package mgr

import (
	"context"
	"fmt"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/channel"
	"golang.52tt.com/clients/channelol"
	"golang.52tt.com/clients/configserver"
	game_pal "golang.52tt.com/clients/game-pal"
	gangup_channel_cli "golang.52tt.com/clients/gangup-channel"
	new_user_reception "golang.52tt.com/clients/new-user-reception"
	tcTab "golang.52tt.com/clients/topic-channel/tab"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channelPB "golang.52tt.com/protocol/app/channel"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
	channelolPB "golang.52tt.com/protocol/services/channelol"
	channelSvrPB "golang.52tt.com/protocol/services/channelsvr"
	configserverPB "golang.52tt.com/protocol/services/configserver"
	gamePalPB "golang.52tt.com/protocol/services/game-pal"
	gangUpPb "golang.52tt.com/protocol/services/gangup-channel"
	receptionPb "golang.52tt.com/protocol/services/new-user-reception"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/bots/userline-robot/config"
	"golang.52tt.com/services/bots/userline-robot/feishu"
	"golang.52tt.com/services/bots/userline-robot/model"
	"regexp"
	"sort"
	"strings"
	"time"
)

const (
	dayLayout  = "2006-01-02"
	timeLayout = "2006-01-02 15:04:05"
)

type Manager struct {
	FeiShu              *feishu.FeiShuServer
	gangupChannelClient gangup_channel_cli.IClient
	topicTabClient      tcTab.IClient
	accountCli          account.IClient
	channelClient       channel.IClient
	channelolClient     channelol.IClient
	receptionClient     *new_user_reception.Client
	refreshTabInfoFre   uint32

	configServerClient *configserver.Client
	gamePalClient      game_pal.IClient
}

type TabInfoCache struct {
	TabNameToId map[string]uint32
	TabIdMap    map[uint32]*tabPB.Tab
	AllTabIds   []uint32
}

var tabInfoCache = &TabInfoCache{}

func setTabInfoCache(tmpCache *TabInfoCache) {
	tabInfoCache = tmpCache
}

func NewManager(feishuServer *feishu.FeiShuServer, refreshTabInfoFre uint32) (*Manager, error) {
	ctx := context.Background()
	gangupChannelClient, err := gangup_channel_cli.NewClient()
	if err != nil {
		log.Errorf("NewManager new GangupChannelClient err: %v", err)
		return nil, err
	}

	topicTabClient, err := tcTab.NewClient()
	if err != nil {
		log.Errorf("NewManager new TopicTabClient err: %v", err)
		return nil, err
	}

	accountClient, err := account.NewClient()
	if err != nil {
		log.Errorf("NewManager new AccountClient err: %v", err)
		return nil, err
	}
	channelClient := channel.NewClient()
	channelolClient := channelol.NewClient()

	newUserRecptionClient, err := new_user_reception.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "new_user_reception.NewClient err %v", err)
		return nil, err
	}
	configServerClient, err := configserver.NewClient()
	if err != nil {
		log.Errorf("NewManager new configServerClient err: %v", err)
		return nil, err
	}
	gamePalClient, err := game_pal.NewClient()
	if err != nil {
		log.Errorf("NewManager new gamePalClient err: %v", err)
		return nil, err
	}
	m := &Manager{
		FeiShu:              feishuServer,
		gangupChannelClient: gangupChannelClient,
		topicTabClient:      topicTabClient,
		accountCli:          accountClient,
		channelClient:       channelClient,
		channelolClient:     channelolClient,
		receptionClient:     newUserRecptionClient,
		refreshTabInfoFre:   refreshTabInfoFre,
		configServerClient:  configServerClient,
		gamePalClient:       gamePalClient,
	}
	return m, nil
}

func (s *Manager) SyncTabInfo() {
	duration := s.refreshTabInfoFre
	if duration == 0 {
		duration = 600 // 10分钟刷新一次tab信息
	}
	ticker := time.NewTicker(time.Duration(duration) * time.Second)
	for range ticker.C {
		ctx, cancel := context.WithTimeout(context.Background(), time.Second)
		s.timerRefreshTabInfoCache(ctx)
		cancel()
	}
}

func (s *Manager) timerRefreshTabInfoCache(ctx context.Context) {
	defer func() {
		err := recover()
		if err != nil {
			log.Errorf("timerRefreshTabInfoCache RefreshTabInfoCache panic err: %v", err)
		}
	}()
	if err := s.RefreshTabInfoCache(ctx); err != nil {
		log.ErrorWithCtx(ctx, "RefreshTabInfoCache err: %v", err)
	}
}

func (s *Manager) RefreshTabInfoCache(ctx context.Context) error {
	tabInfos, err := s.topicTabClient.Tabs(ctx, 0, 1000)
	if err != nil {
		log.ErrorWithCtx(ctx, "manager RefreshTabInfoCache err: %v", err)
		return err
	}
	tabIdMap := make(map[uint32]*tabPB.Tab)
	tabNameToId := make(map[string]uint32)
	allTabIds := make([]uint32, 0, len(tabInfos.Tabs))
	for _, tab := range tabInfos.GetTabs() {
		tabIdMap[tab.GetId()] = tab
		tabNameToId[tab.GetName()] = tab.GetId()
		allTabIds = append(allTabIds, tab.GetId())
	}
	tmpCache := &TabInfoCache{
		TabNameToId: tabNameToId,
		TabIdMap:    tabIdMap,
		AllTabIds:   allTabIds,
	}
	setTabInfoCache(tmpCache)
	log.DebugWithCtx(ctx, "manager RefreshTabInfoCache success, len(tabNameToId):%d, len(tabIdMap):%d, len(allTabIds):%d",
		len(tmpCache.TabNameToId), len(tmpCache.TabIdMap), len(tmpCache.AllTabIds))
	return nil
}

// QueryBusiness 根据命令查询业务数据
func (s *Manager) QueryBusiness(ctx context.Context, command string, senderOpenId, chatId string) (content string, msgType string) {
	/*
		// 根据前缀划分业务
		if strings.HasPrefix(command, "【游戏房间】") {
			return s.processReleasingRoomCmd(ctx, command)

		} else if strings.HasPrefix(command, "【房间人数】") {
			if strings.Contains(command, "白名单") {
				return s.processWhitelistRoomMemberCountCmd(ctx)
			}
			return s.processRoomMemberCountCmd(ctx, command)

		} else if command == "help" {
			content = model.IntroduceMsg
			msgType = feishu.MsgType_interactive
		} else {
			content = model.QueryFailMsg
			msgType = feishu.MsgType_text
		}
	*/
	if command == "help" {
		if chatId == config.UserLineRobotDynamicConfig.GetFallBackSwitchChatId() {
			content = model.FallBackSwitchIntroduceMsg
		} else {
			content = model.IntroduceMsg
		}
		msgType = feishu.MsgType_interactive
		return
	}
	//命令格式固定【标识】，用正则表达式获取标识符
	var cmd string
	regex := regexp.MustCompile("【(.*?)】")
	match := regex.FindStringSubmatch(command)
	if len(match) > 1 {
		cmd = match[1]
	}
	switch cmd {
	case "游戏房间":
		return s.processReleasingRoomCmd(ctx, command)
	case "房间人数":
		if strings.Contains(command, "白名单") {
			return s.processWhitelistRoomMemberCountCmd(ctx)
		}
		return s.processRoomMemberCountCmd(ctx, command)
	case "业务兜底开关":
		return s.processUpdateFallBackSwitchCmd(ctx, command, senderOpenId, chatId)
	case "搭子卡数量":
		return s.processPolishedGamePalCardCmd(ctx, command)
	default:
		content = model.QueryFailMsg
		msgType = feishu.MsgType_text
	}
	return
}

// processReleasingRoomCmd 查询当前发布中的实时房间数
func (s *Manager) processReleasingRoomCmd(ctx context.Context, command string) (content string, msgType string) {
	var err error
	command = strings.TrimPrefix(command, "【游戏房间】")
	if command == "all" { // 查询所有tab
		msgType = feishu.MsgType_file
		content, err = s.getAllTabRoomCount(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "processReleasingRoomCmd getAllTabRoomCount err: %v", err)
			return model.ServerErrorMsg, feishu.MsgType_text
		}
		return
	} else {
		// 获取合法tab名称
		tabNames := strings.Split(command, "、")
		log.InfoWithCtx(ctx, "processReleasingRoomCmd tabNames: %v", tabNames)
		tabIds := make([]uint32, 0)
		for _, tabName := range tabNames {
			if tabId, ok := tabInfoCache.TabNameToId[tabName]; ok {
				tabIds = append(tabIds, tabId)
			}
		}
		log.InfoWithCtx(ctx, "processReleasingRoomCmd tabIds: %v", tabIds)
		if len(tabIds) == 0 {
			return model.QueryFailMsg, feishu.MsgType_text
		}
		// 查询命令中包含的合法tab
		msgType = feishu.MsgType_interactive
		content, err = s.getTabRoomCount(ctx, tabIds)
		if err != nil {
			log.ErrorWithCtx(ctx, "processReleasingRoomCmd getTabRoomCount err: %v", err)
			return model.ServerErrorMsg, feishu.MsgType_text
		}
	}
	return
}

// getTabRoomCount 获取发布中房间数量
func (s *Manager) getTabRoomCount(ctx context.Context, tabIds []uint32) (string, error) {
	resp, err := s.gangupChannelClient.GetReleasingChannelCountByTabIds(ctx, &gangUpPb.GetReleasingChannelCountByTabIdsReq{
		TabIds: tabIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getTabRoomCount gangupChannelClient.GetReleasingChannelCountByTabIds err: %v", err)
		return "", err
	}
	var data strings.Builder
	for tabId, count := range resp.GetCountMap() {
		data.WriteString(fmt.Sprintf(`**%s：%d**\n`, tabInfoCache.TabIdMap[tabId].GetName(), count))
	}
	log.InfoWithCtx(ctx, "getTabRoomCount data: %s", data.String())
	msg := model.InteractiveMsg
	msg = strings.ReplaceAll(msg, "title_content", "当前发布中的实时房间数")
	msg = strings.ReplaceAll(msg, "data", data.String())
	msg = strings.ReplaceAll(msg, "current_time", time.Now().Format(timeLayout))

	return msg, nil
}

// 获取所有发布中房间数量
func (s *Manager) getAllTabRoomCount(ctx context.Context) (string, error) {
	resp, err := s.gangupChannelClient.GetReleasingChannelCountByTabIds(ctx, &gangUpPb.GetReleasingChannelCountByTabIdsReq{
		TabIds: tabInfoCache.AllTabIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getAllTabRoomCount gangupChannelClient.GetReleasingChannelCountByTabIds err: %v", err)
		return "", err
	}
	// 1. 获取所有发布中房间数据
	title := []string{"TabId", "Tab名称", "实时发布房间数"}
	tabRoomInfos := make([]*model.ReleasingChannelCountInfo, 0, len(resp.GetCountMap()))
	for tabId, count := range resp.GetCountMap() {
		info := &model.ReleasingChannelCountInfo{
			TabId:   tabId,
			TabName: tabInfoCache.TabIdMap[tabId].GetName(),
			Count:   count,
		}
		tabRoomInfos = append(tabRoomInfos, info)
	}

	// 2. 按tabId排序
	sort.Slice(tabRoomInfos, func(i, j int) bool {
		return tabRoomInfos[i].TabId < tabRoomInfos[j].TabId
	})
	log.InfoWithCtx(ctx, "getAllTabRoomCount tabRoomInfos: %+v", tabRoomInfos)

	// 3. 转化为[][]interface{}类型
	data := make([][]interface{}, 0, len(tabRoomInfos))
	for _, info := range tabRoomInfos {
		row := []interface{}{info.TabId, info.TabName, info.Count}
		data = append(data, row)
	}

	return s.uploadFile(ctx, "实时发布房间数", title, data)
}

func (s *Manager) processWhitelistRoomMemberCountCmd(ctx1 context.Context) (string, string) {
	t := time.Now()
	// 获取白名单
	resp, err := s.receptionClient.GetUserReceptionGroupWhitelist(ctx1, &receptionPb.GetUserReceptionGroupWhitelistReq{})
	if err != nil {
		log.ErrorWithCtx(ctx1, "GetUserReceptionGroupWhitelist fail, err: %v", err)
		return model.QueryFailMsg, feishu.MsgType_text
	}

	title := []string{"ttid", "user_id", "channel_id", "channel_display_id", "member_count", "new_user_count", "new_user_detail"}
	type Data struct {
		ttid             string
		uid              uint32
		channelId        uint32
		channelDisplayId uint32
		memberCount      uint32
		newUserCount     uint32
		newUserDetail    []uint32
	}

	log.InfoWithCtx(ctx1, "GetUserReceptionGroupWhitelist info, len(resp.GetItems()):%d", len(resp.GetItems()))

	data := make([][]interface{}, 0, len(resp.GetItems()))
	pool := NewGoroutinePool(10)
	for _, tInfo := range resp.GetItems() {
		pool.Add()
		go func(uid uint32) {
			ctx, cancel := context.WithTimeout(context.Background(), time.Second)
			defer func() {
				pool.Done()
				cancel()
			}()

			d := Data{uid: uid}

			userInfo, err1 := s.accountCli.GetUserByUid(ctx, d.uid)
			if err1 != nil {
				log.ErrorWithCtx(ctx, "GetUserByUid failed, uid: %d, err: %v", d.uid, err1)
				return
			}
			d.ttid = userInfo.GetUsername()

			d.channelId, d.channelDisplayId, err = s.getChannelInfoByUid(ctx, d.uid)
			if err != nil {
				log.ErrorWithCtx(ctx, "processRoomMemberCountCmd getChannelInfoByUid(%d) failed, err: %v", d.uid, err)
				return
			}

			// 批量查询多个房间内成员信息
			resp, err := s.channelolClient.BatchGetChannelMemberList(ctx, 0, &channelolPB.BatchGetChannelMemberListReq{
				ChannelIdList: []uint32{d.channelId},
				MemberCount:   100,
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "getChannelMemberCountInfo BatchGetChannelMemberList err: %v", err)
				return
			}

			for _, channelMemberInfo := range resp.GetChannelmemberinfoList() {
				// 获取房内新用户个数和uid列表
				newUserCount, uids, err := s.getChannelNewUserCount(ctx, channelMemberInfo.GetMemberList())
				if err != nil {
					log.ErrorWithCtx(ctx, "getChannelMemberCountInfo getChannelNewUserCount failed, channelId: %d, err: %v", channelMemberInfo.ChannelId, err)
					continue
				}
				d.memberCount = channelMemberInfo.GetAllSize()
				d.newUserCount = newUserCount
				d.newUserDetail = uids
			}

			data = append(data, []interface{}{d.ttid, d.uid, d.channelId, d.channelDisplayId, d.memberCount, d.newUserCount, d.newUserDetail})

		}(tInfo.Uid)

	}
	pool.Wait()

	msg, err := s.uploadFile(ctx1, "room_member_info", title, data)
	if err != nil {
		return model.QueryFailMsg, feishu.MsgType_text
	}
	log.InfoWithCtx(ctx1, "processWhitelistRoomMemberCountCmd data success, cost:%d", time.Since(t)/1e6)
	return msg, feishu.MsgType_file
}

// processRoomMemberCmd 根据房主ttid查询房间人数信息
func (s *Manager) processRoomMemberCountCmd(ctx context.Context, command string) (string, string) {
	command = strings.TrimPrefix(command, "【房间人数】")

	// 根据输入获取ttid列表
	ttidList := strings.Split(command, "、")
	log.InfoWithCtx(ctx, "processRoomMemberCountCmd ttidList: %v", ttidList)

	var data strings.Builder
	channelOwnerTTid := make(map[uint32]string)
	channelDisplayId := make(map[uint32]uint32)
	channelIdList := make([]uint32, 0, 10)
	for _, ttid := range ttidList {
		if ttid == "" {
			continue
		}
		// 1. 根据ttid获取uid
		uid, _, err := s.accountCli.GetUidByName(ctx, ttid)
		if err != nil {
			log.ErrorWithCtx(ctx, "processRoomMemberCountCmd GetUidByName(%s) failed, err: %v", ttid, err)
			data.WriteString(fmt.Sprintf(`无效的ttid: %s\n`, ttid))
			continue
		}
		log.DebugWithCtx(ctx, "processRoomMemberCountCmd accountCli.GetUidByName ttid: %s, uid: %d", ttid, uid)

		// 2. 根据房主uid获取ugc房的channelId和displayId
		channelId, displayId, err2 := s.getChannelInfoByUid(ctx, uid)
		if err2 != nil {
			log.ErrorWithCtx(ctx, "processRoomMemberCountCmd getChannelInfoByUid(%d) failed, err: %v", uid, err2)
			data.WriteString(fmt.Sprintf(`找不到属于用户(ttid: %s, uid: %d)的ugc房间\n`, ttid, uid))
			continue
		}
		channelIdList = append(channelIdList, channelId)
		channelOwnerTTid[channelId] = ttid
		channelDisplayId[channelId] = displayId

		// 3. 查询房间人数数据
		if len(channelIdList) == 10 {
			str, err := s.getChannelMemberCountInfo(ctx, channelIdList, channelOwnerTTid, channelDisplayId)
			if err != nil {
				log.ErrorWithCtx(ctx, "processRoomMemberCountCmd getChannelMemberCountInfo err: %v", err)
				return model.ServerErrorMsg, feishu.MsgType_text
			}
			data.WriteString(str)
			channelIdList = channelIdList[:0]
		}
	}
	if len(channelIdList) > 0 {
		str, err := s.getChannelMemberCountInfo(ctx, channelIdList, channelOwnerTTid, channelDisplayId)
		if err != nil {
			log.ErrorWithCtx(ctx, "processRoomMemberCountCmd getChannelMemberCountInfo err: %v", err)
			return model.ServerErrorMsg, feishu.MsgType_text
		}
		data.WriteString(str)
	}
	if data.Len() == 0 {
		return model.QueryFailMsg, feishu.MsgType_text
	}
	log.InfoWithCtx(ctx, "processRoomMemberCountCmd data: %s", data.String())

	msg := model.InteractiveMsg
	msg = strings.ReplaceAll(msg, "title_content", "房间实时人数")
	msg = strings.ReplaceAll(msg, "data", data.String())
	msg = strings.ReplaceAll(msg, "current_time", time.Now().Format(timeLayout))

	return msg, feishu.MsgType_interactive
}

// getChannelInfoByUid 根据uid查询用户ugc房的channelId
func (s *Manager) getChannelInfoByUid(ctx context.Context, uid uint32) (channelId uint32, displayId uint32, err error) {
	// 获取用户个人房
	roleListResp, err := s.channelClient.GetUserChannelRoleList(ctx, uid, uid, uint32(channelSvrPB.ChannelAdminRole_CHANNEL_OWNER))
	if err != nil {
		log.ErrorWithCtx(ctx, "getChannelInfoByUid GetUserChannelRoleList err: %v", err)
		return
	}

	for _, role := range roleListResp.GetRoleList() {
		if role.GetRole() == uint32(channelSvrPB.ChannelAdminRole_CHANNEL_OWNER) &&
			role.GetChannelInfo().GetChannelBindType() == uint32(channelPB.ChannelType_USER_CHANNEL_TYPE) {
			channelId = role.GetChannelInfo().GetChannelBaseinfo().GetChannelId()
			displayId = role.GetChannelInfo().GetChannelBaseinfo().GetDisplayId()
			break
		}
	}
	log.DebugWithCtx(ctx, "getChannelInfoByUid uid: %d, channelId: %d, displayId: %d", uid, channelId, displayId)
	return
}

// getChannelMemberCountInfo 一次获取多个房间的房内人数和新用户人数信息
func (s *Manager) getChannelMemberCountInfo(ctx context.Context, channelIdList []uint32,
	channelOwnerTTid map[uint32]string, channelDisplayId map[uint32]uint32) (string, error) {
	// 批量查询多个房间内成员信息
	resp, err := s.channelolClient.BatchGetChannelMemberList(ctx, 0, &channelolPB.BatchGetChannelMemberListReq{
		ChannelIdList: channelIdList,
		MemberCount:   100,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getChannelMemberCountInfo BatchGetChannelMemberList err: %v", err)
		return "", err
	}
	var builder strings.Builder
	for _, channelMemberInfo := range resp.GetChannelmemberinfoList() {
		// 获取房内新用户个数和uid列表
		newUserCount, uids, err := s.getChannelNewUserCount(ctx, channelMemberInfo.GetMemberList())
		if err != nil {
			log.ErrorWithCtx(ctx, "getChannelMemberCountInfo getChannelNewUserCount failed, channelId: %d, err: %v", channelMemberInfo.ChannelId, err)
			return "", err
		}
		channelId := channelMemberInfo.GetChannelId()
		builder.WriteString(fmt.Sprintf(`用户(ttid: %s)的ugc房间(displayId: %d)当前房内总人数为: %d, 新用户人数为: %d, uids: %v\n`,
			channelOwnerTTid[channelId], channelDisplayId[channelId], channelMemberInfo.GetAllSize(), newUserCount, uids))
	}
	return builder.String(), nil
}

// getChannelNewUserCount 获取房内新用户个数和uid列表
func (s *Manager) getChannelNewUserCount(ctx context.Context, channelMembers []*channelolPB.ChannelMember) (uint32, []uint32, error) {
	newUserIds := make([]uint32, 0)
	tmpUids := make([]uint32, 0, len(channelMembers))
	for _, member := range channelMembers {
		tmpUids = append(tmpUids, member.GetUid())
	}
	uidInfoMap, err := s.accountCli.BatGetUserByUid(ctx, tmpUids...)
	if err != nil {
		log.ErrorWithCtx(ctx, "getChannelNewUserCount BatGetUserByUid fail, uids: %v, err: %v", tmpUids, err)
	}
	for _, member := range channelMembers {
		// 当天注册新用户
		if s.IsEqualDay(int64(uidInfoMap[member.GetUid()].GetRegisteredAt()), time.Now().Unix()) {
			newUserIds = append(newUserIds, member.GetUid())
		}
	}
	return uint32(len(newUserIds)), newUserIds, nil
}

func (s *Manager) IsEqualDay(unixA, unixB int64) bool {
	return time.Unix(unixA, 0).Format(dayLayout) == time.Unix(unixB, 0).Format(dayLayout)
}

//修改业务兜底开关
func (s *Manager) processUpdateFallBackSwitchCmd(ctx context.Context, command string, operatorId, chatId string) (content string, msgType string) {
	msgType = feishu.MsgType_text
	if config.UserLineRobotDynamicConfig.GetFallBackSwitchChatId() != chatId {
		content = model.NoAccessMsg
		return
	}
	args := strings.Split(command, " ")
	if len(args) < 2 {
		content = model.QueryFailMsg
		return
	}
	var switchType configserverPB.SwitchBusinessType
	switchEnv := configserverPB.SwitchEnv_SWITCH_DEFAULT
	switch args[1] {
	case model.GameChannelList, model.MixChannelList:
		switchType = configserverPB.SwitchBusinessType_GAME_MIX_CHANNEL_LIST
		content = s.updateFallBackSwitch(ctx, switchEnv, switchType, operatorId, args)
	case model.MusicChannelList:
		switchType = configserverPB.SwitchBusinessType_MUSIC_CHANNEL_LIST
		content = s.updateFallBackSwitch(ctx, switchEnv, switchType, operatorId, args)
	case model.UgcQuickFormTeam:
		switchType = configserverPB.SwitchBusinessType_GAME_UGC_QUICK_MATCH
		content = s.updateFallBackSwitch(ctx, switchEnv, switchType, operatorId, args)
	case model.TempQuickFormTeam:
		switchType = configserverPB.SwitchBusinessType_GAME_TEMP_QUICK_MATCH
		content = s.updateFallBackSwitch(ctx, switchEnv, switchType, operatorId, args)
	case model.StagingMixChannelList, model.StagingGameChannelList:
		switchType = configserverPB.SwitchBusinessType_GAME_MIX_CHANNEL_LIST
		switchEnv = configserverPB.SwitchEnv_SWITCH_STAGING
		content = s.updateFallBackSwitch(ctx, switchEnv, switchType, operatorId, args)
	case model.GetAllSwitchStatus:
		content = s.getAllSwitchStatus(ctx)
	default:
		content = model.QueryFailMsg
		return
	}

	return
}

// 修改开关状态
func (s *Manager) updateFallBackSwitch(ctx context.Context, switchEnv configserverPB.SwitchEnv, switchType configserverPB.SwitchBusinessType,
	operatorId string, args []string) (content string) {
	if switchType == configserverPB.SwitchBusinessType_INVALID_TYPE || len(args) != 3 {
		content = model.QueryFailMsg
		return
	}
	var switchStatus uint32
	if args[2] == "开启" {
		switchStatus = 1
	} else if args[2] == "关闭" {
		switchStatus = 0
	} else {
		content = model.QueryFailMsg
		return
	}
	operator, err := s.FeiShu.GetUserNameByOpenId(ctx, operatorId)
	if err != nil {
		log.ErrorWithCtx(ctx, "processUpdateFallBackSwitchCmd GetUserNameByOpenId  args:%v, operatorId:%s, err:%v", args, operatorId, err)
		content = model.ServerErrorMsg
		return
	}
	_, err = s.configServerClient.SetSwitchStatusByBusinessType(ctx, configserverPB.SetSwitchStatusByBusinessTypeReq{
		Types:     switchType,
		Operator:  operator,
		Status:    switchStatus,
		SwitchEnv: switchEnv,
	})
	if err != nil {
		if serr, ok := err.(protocol.ServerError); ok && serr.Code() == status.ErrFallBackSwitchCantSet {
			log.InfoWithCtx(ctx, "processUpdateFallBackSwitchCmd SetSwitchStatusByBusinessType args:%v, operator:%s, 使用动态配置开关",
				args, operator)
			content = model.FallBackFunctionCantUse
			return
		}
		log.ErrorWithCtx(ctx, "processUpdateFallBackSwitchCmd SetSwitchStatusByBusinessType args:%v, operator:%s, err:%v",
			args, operator, err)
		content = model.QueryFailMsg
		return
	}
	content = model.UpdateSuccessMsg
	log.InfoWithCtx(ctx, "processUpdateFallBackSwitchCmd args:%s, operator:%s, switchType:%v, switchStatus:%d", args, operator, switchType, switchStatus)
	return
}

// 获取开关状态
func (s *Manager) getAllSwitchStatus(ctx context.Context) (content string) {
	resp, err := s.configServerClient.GetFallBackSwitchByTypes(ctx, configserverPB.GetFallBackSwitchByTypesReq{
		Types: []configserverPB.SwitchBusinessType{
			configserverPB.SwitchBusinessType_GAME_MIX_CHANNEL_LIST, configserverPB.SwitchBusinessType_GAME_UGC_QUICK_MATCH,
			configserverPB.SwitchBusinessType_MUSIC_CHANNEL_LIST, configserverPB.SwitchBusinessType_GAME_TEMP_QUICK_MATCH,
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getAllSwitchStatus err:%v", err)
		content = model.QueryFailMsg
		return
	}
	content = genSwitchStatusContent(resp.GetSwitchMap())
	return
}

func genSwitchStatusContent(switchStatusMap map[int32]bool) (content string) {

	for k, v := range model.FallBackBusinessMap {
		if switchStatusMap[k] {
			content += fmt.Sprintf("%s 兜底开关状态：开启\\n", v)
		} else {
			content += fmt.Sprintf("%s 兜底开关状态：关闭\\n", v)
		}
	}
	content = fmt.Sprintf(model.TextFormatMsg, content)
	return
}

// processPolishedGamePalCardCmd 查询当前擦亮状态的搭子卡数量
func (s *Manager) processPolishedGamePalCardCmd(ctx context.Context, command string) (content string, msgType string) {
	var err error
	command = strings.TrimPrefix(command, "【搭子卡数量】")
	if command == "all" { // 查询所有tab
		msgType = feishu.MsgType_file
		content, err = s.getAllTabGamePalCardCount(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "processPolishedGamePalCardCmd getAllTabGamePalCardCount err: %v", err)
			return model.ServerErrorMsg, feishu.MsgType_text
		}
		return
	} else {
		// 获取合法tab名称
		tabNames := strings.Split(command, "、")
		log.InfoWithCtx(ctx, "processPolishedGamePalCardCmd tabNames: %v", tabNames)
		tabIds := make([]uint32, 0)
		tabIdMap := make(map[uint32]struct{})
		for _, tabName := range tabNames {
			if tabId, ok := tabInfoCache.TabNameToId[tabName]; ok {
				// 去重
				if _, exist := tabIdMap[tabId]; !exist {
					tabIds = append(tabIds, tabId)
					tabIdMap[tabId] = struct{}{}
				}
			}
		}
		log.InfoWithCtx(ctx, "processPolishedGamePalCardCmd tabIds: %v", tabIds)
		if len(tabIds) == 0 {
			return model.QueryFailMsg, feishu.MsgType_text
		}
		// 查询命令中包含的合法tab
		msgType = feishu.MsgType_interactive
		content, err = s.getTabGamePalCardCount(ctx, tabIds)
		if err != nil {
			log.ErrorWithCtx(ctx, "processPolishedGamePalCardCmd getTabGamePalCardCount err: %v", err)
			return model.ServerErrorMsg, feishu.MsgType_text
		}
	}
	return
}

// 获取一起开黑下所有玩法的实时擦亮中搭子卡数量
func (s *Manager) getAllTabGamePalCardCount(ctx context.Context) (string, error) {
	// 获取所有玩法擦亮中搭子卡数据
	resp, err := s.gamePalClient.GetTabPolishedCardCountMap(ctx, &gamePalPB.GetTabPolishedCardCountMapReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "getAllTabGamePalCardCount gamePalClient.GetTabPolishedCardCountMap err: %v", err)
		return "", err
	}

	gamePalCardCountMap := resp.GetTabPolishedCardCountMap()
	if gamePalCardCountMap == nil {
		gamePalCardCountMap = make(map[uint32]uint32)
	}

	title := []string{"TabId", "Tab名称", "当前擦亮中的搭子卡数量"}
	data := make([][]interface{}, 0)
	for _, tabId := range tabInfoCache.AllTabIds {
		tab, ok := tabInfoCache.TabIdMap[tabId]
		// 过滤掉不是一起开黑下的玩法
		if !ok || tab.GetCategoryMapping() != uint32(topic_channel.CategoryType_Gangup_type) {
			continue
		}
		row := []interface{}{tab.GetId(), tab.GetName(), gamePalCardCountMap[tab.GetId()]}
		data = append(data, row)
	}

	log.InfoWithCtx(ctx, "getAllTabGamePalCardCount len(data): %d, gamePalCardCountMap: %v", len(data), gamePalCardCountMap)

	return s.uploadFile(ctx, "当前擦亮中搭子卡数量", title, data)
}

// getTabGamePalCardCount 获取指定玩法的实时擦亮中搭子卡数量
func (s *Manager) getTabGamePalCardCount(ctx context.Context, tabIds []uint32) (string, error) {
	// 获取所有玩法擦亮中搭子卡数据
	resp, err := s.gamePalClient.GetTabPolishedCardCountMap(ctx, &gamePalPB.GetTabPolishedCardCountMapReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "getAllTabGamePalCardCount gamePalClient.GetTabPolishedCardCountMap err: %v", err)
		return "", err
	}

	gamePalCardCountMap := resp.GetTabPolishedCardCountMap()
	if gamePalCardCountMap == nil {
		gamePalCardCountMap = make(map[uint32]uint32)
	}

	log.InfoWithCtx(ctx, "gamePalCardCountMap: %v", gamePalCardCountMap)

	var data strings.Builder
	for _, tabId := range tabIds {
		tab, ok := tabInfoCache.TabIdMap[tabId]
		if !ok {
			continue
		}
		data.WriteString(fmt.Sprintf(`**%s：%d**\n`, tab.GetName(), gamePalCardCountMap[tab.GetId()]))
	}

	log.InfoWithCtx(ctx, "getTabGamePalCardCount data: %s", data.String())
	msg := model.InteractiveMsg
	msg = strings.ReplaceAll(msg, "title_content", "当前擦亮中的搭子卡数量")
	msg = strings.ReplaceAll(msg, "data", data.String())
	msg = strings.ReplaceAll(msg, "current_time", time.Now().Format(timeLayout))

	return msg, nil
}
