package feishu

import (
	"context"
	"fmt"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/bots/userline-robot/config"
	"io"
)

const (
	MsgType_text        = "text"
	MsgType_interactive = "interactive"
	MsgType_file        = "file"
	MsgType_image       = "image"
	UserType_user_id    = "user_id"
	UserType_chat_id    = "chat_id"
)

type FeiShuServer struct {
	LarkClient *lark.Client
	Config     *config.FeiShuConfig
}

func NewFeiShuServer(config *config.FeiShuConfig) *FeiShuServer {
	larkClient := lark.NewClient(config.AppId, config.AppSecret)
	s := &FeiShuServer{
		LarkClient: larkClient,
		Config:     config,
	}
	return s
}

func (s *FeiShuServer) SendMessage(ctx context.Context, receiveIdType string, receiveId string, msgType string, content string) error {
	// 创建请求对象
	req := larkim.NewCreateMessageReqBuilder().
		ReceiveIdType(receiveIdType).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(receiveId).
			MsgType(msgType).
			Content(content).
			Build()).
		Build()
	// 发起请求
	resp, err := s.LarkClient.Im.Message.Create(ctx, req)

	// 处理错误
	if err != nil {
		log.ErrorWithCtx(ctx, "SendMessage msg %v to %v, err: %v", content, receiveId, err)
		return err
	}
	// 服务端错误处理
	if !resp.Success() {
		log.WarnWithCtx(ctx, "SendMessage msg %v to %v, resp: %+v ", content, receiveId, resp)
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
	}
	log.InfoWithCtx(ctx, "SendMessage resp: %+v", resp)

	return nil
}

func (s *FeiShuServer) UploadImage(ctx context.Context, image io.Reader) (imageKey string, err error) {
	// 创建请求对象
	req := larkim.NewCreateImageReqBuilder().
		Body(larkim.NewCreateImageReqBodyBuilder().
			ImageType(larkim.ImageTypeMessage).
			Image(image).
			Build()).
		Build()
	// 发起请求
	resp, err := s.LarkClient.Im.Image.Create(ctx, req)

	// 处理错误
	if err != nil {
		log.ErrorWithCtx(ctx, "UploadImage err: %v", err)
		return
	}
	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
	}
	log.InfoWithCtx(ctx, "UploadImage resp: %+v", resp)

	imageKey = *resp.Data.ImageKey

	return
}

func (s *FeiShuServer) UploadFile(ctx context.Context, file io.Reader, fileType string, fileName string) (fileKey string, err error) {
	// 创建请求对象
	req := larkim.NewCreateFileReqBuilder().
		Body(larkim.NewCreateFileReqBodyBuilder().
			FileType(fileType).
			FileName(fileName).
			File(file).
			Build()).
		Build()
	// 发起请求
	resp, err := s.LarkClient.Im.File.Create(ctx, req)

	// 处理错误
	if err != nil {
		log.ErrorWithCtx(ctx, "UploadFile err: %v", err)
		return
	}
	// 服务端错误处理
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
	}
	log.InfoWithCtx(ctx, "UploadFile resp: %+v", resp)

	fileKey = *resp.Data.FileKey

	return
}

func (s *FeiShuServer) GetUserNameByOpenId(ctx context.Context, openId string) (string, error) {
	req := larkcontact.NewGetUserReqBuilder().UserId(openId).UserIdType("open_id").
		DepartmentIdType("open_department_id").Build()
	resp, err := s.LarkClient.Contact.User.Get(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "FeiShuServer GetUserNameByOpenId openId:%s, err:%v", openId, err)
		return openId, err
	}
	// 服务端错误处理
	if !resp.Success() {
		log.ErrorWithCtx(ctx, "GetUserNameByOpenId fail openId:%s, resp.Code:%d, resp.Msg:%s, resp.RequestId():%s",
			openId, resp.Code, resp.Msg, resp.RequestId())
		return openId, nil
	}
	return *resp.Data.User.Name, nil

}
