package manager

import (
	"bou.ke/monkey"
	"context"
	"fmt"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/pkg/settlement/alert"
	gold_commission "golang.52tt.com/protocol/services/gold-commission"
	Guild "golang.52tt.com/protocol/services/guildsvr"
	settlement_bill "golang.52tt.com/protocol/services/settlement-bill"
	"golang.52tt.com/services/gold/common/model"
	"golang.52tt.com/services/gold/gold-settlement/mocks"
	"golang.52tt.com/services/gold/gold-settlement/mysql"
	"reflect"
	"testing"
	"time"
)

func TestSettleYuyinExtra_Settle(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()
	nowTime := time.Now()
	monkey.Patch(time.Now, func() time.Time {
		return nowTime
	})
	billType := settlement_bill.SettlementBillType_MonthMiddle
	settleStatus := gold_commission.ReqSettleStatus_ReqSettleStatusWait
	mockManager := mocks.NewMockIManager(ctl)
	mockStore := mocks.NewMockIGoldCommissionMysql(ctl)

	guildId := uint32(999)
	settleUid := uint32(123456)
	income := uint64(10000)
	billId := "bill_id"
	guildInfo := &Guild.GuildResp{
		GuildId: guildId,
		Name:    "公会1",
	}
	dateKey := fmt.Sprintf("%s_%d", nowTime.Format("20060102"), guildId)

	start := time.Date(nowTime.Year(), nowTime.Month()-1, 1, 0, 0, 0, 0, time.Local)
	end := time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local)

	list := []*model.YuyinGoldExtraIncome{
		{
			GuildId:               guildId,
			MonthRecordIncome:     income,
			ValidAnchorIncome:     income,
			ValidAnchorAddIncome:  income,
			PotentialAnchorIncome: income,
		},
	}

	monkey.PatchInstanceMethod(reflect.TypeOf(&alert.Feishu{}), "SendInfo", func(c *alert.Feishu,
		text string) {
		return
	})
	gomock.InOrder(
		mockManager.EXPECT().GetSettleMonthRange(nowTime).Return(start, end),
		mockStore.EXPECT().GetYuyinExtraAggIncome(ctx, start, settleStatus).Return(list, nil),
		mockManager.EXPECT().GetAlert().Return(&alert.Feishu{}),
		mockManager.EXPECT().IsSkipGuild(guildId).Return(false),
		mockManager.EXPECT().GetGuildInfo(ctx, guildId).Return(guildInfo, false, nil),
		mockManager.EXPECT().HandleCorporate(ctx, gomock.Any()).Return(false, settleUid, nil),
		mockManager.EXPECT().CreateBill(ctx, billType, settleUid, start, end).Return(billId, nil),
		mockManager.EXPECT().GenDayKey(nowTime, guildId).Return(dateKey),
		mockStore.EXPECT().YuyinExtraSettlement(ctx, gomock.Any(), gomock.Any()).Return(nil),
		mockStore.EXPECT().SetExtraSettleReconcileInfo(ctx, guildId, start, gomock.Any()).Return(nil),
	)
	type fields struct {
		ctx      context.Context
		billType settlement_bill.SettlementBillType
		mgr      IManager
		store    mysql.IGoldCommissionMysql
		abort    bool
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "Settle",
			fields: fields{
				ctx:      ctx,
				billType: billType,
				mgr:      mockManager,
				store:    mockStore,
				abort:    false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettleYuyinExtra{
				ctx:      tt.fields.ctx,
				billType: tt.fields.billType,
				mgr:      tt.fields.mgr,
				store:    tt.fields.store,
				abort:    tt.fields.abort,
			}
			if err := s.Settle(); (err != nil) != tt.wantErr {
				t.Errorf("Settle() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
