package manager

import (
	"bou.ke/monkey"
	"context"
	"fmt"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/pkg/settlement"
	"golang.52tt.com/pkg/settlement/alert"
	gold_commission "golang.52tt.com/protocol/services/gold-commission"
	Guild "golang.52tt.com/protocol/services/guildsvr"
	settlement_bill "golang.52tt.com/protocol/services/settlement-bill"
	"golang.52tt.com/services/gold/common"
	"golang.52tt.com/services/gold/common/model"
	"golang.52tt.com/services/gold/gold-settlement/mocks"
	"golang.52tt.com/services/gold/gold-settlement/mysql"
	"os"
	"reflect"
	"testing"
	"time"
)

func TestSettleInteractGameGold_Aggregate(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()
	nowTime := time.Now()
	monkey.Patch(time.Now, func() time.Time {
		return nowTime
	})
	billType := settlement_bill.SettlementBillType_InteractGameCommission
	settleStatus := gold_commission.ReqSettleStatus_ReqSettleStatusWait
	mockManager := mocks.NewMockIManager(ctl)
	mockStore := mocks.NewMockIGoldCommissionMysql(ctl)
	guildId := uint32(999)
	guildIds := []uint32{guildId}
	guildInfo := &Guild.GuildResp{
		GuildId: guildId,
		Name:    "公会1",
	}
	settleUid := uint32(123456)
	kFee := uint64(10000)
	dateKey := fmt.Sprintf("%s_%d", nowTime.Format("20060102"), guildId)

	start := time.Date(nowTime.Year(), nowTime.Month()-1, 1, 0, 0, 0, 0, time.Local)
	end := time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local)

	gomock.InOrder(
		mockManager.EXPECT().GetSettleMonthRange(nowTime).Return(start, end),
		// mockManager.EXPECT().GetStore(channelType).Return(mockStore, nil),
		mockStore.EXPECT().GetDistinctGuildIds(ctx, start, end, settleStatus).Return(guildIds, nil),
		mockManager.EXPECT().IsSkipGuild(guildId).Return(false),
		mockManager.EXPECT().GetGuildInfo(ctx, guildId).Return(guildInfo, false, nil),
		mockStore.EXPECT().GetGuildGoldTotal(ctx, guildId, start, end, gold_commission.SourceType_SourceTypeInteractGame, settleStatus).
			Return(kFee, nil),
		mockManager.EXPECT().HandleCorporate(ctx, gomock.Any()).Return(false, settleUid, nil),
		mockManager.EXPECT().GenDayKey(nowTime, guildId).Return(dateKey),
		mockStore.EXPECT().Aggregation(ctx, gomock.Any()).Return(nil),
	)
	type fields struct {
		ctx      context.Context
		billType settlement_bill.SettlementBillType
		goldType gold_commission.GoldType
		mgr      IManager
		store    mysql.IGoldCommissionMysql
		abort    bool
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "Aggregate",
			fields: fields{
				ctx:      ctx,
				billType: billType,
				mgr:      mockManager,
				store:    mockStore,
				abort:    false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettleInteractGameGold{
				ctx:      tt.fields.ctx,
				billType: tt.fields.billType,
				mgr:      tt.fields.mgr,
				store:    tt.fields.store,
				abort:    tt.fields.abort,
			}
			if err := s.Aggregate(); (err != nil) != tt.wantErr {
				t.Errorf("Aggregate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestSettleInteractGameGold_Report(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()
	nowTime := time.Now()
	monkey.Patch(time.Now, func() time.Time {
		return nowTime
	})
	billType := settlement_bill.SettlementBillType_InteractGameCommission
	settleStatus := gold_commission.ReqSettleStatus_ReqSettleStatusFinished
	mockManager := mocks.NewMockIManager(ctl)
	mockStore := mocks.NewMockIGoldCommissionMysql(ctl)
	guildId := uint32(999)
	settleUid := uint32(123456)
	pFee := uint64(10000)

	start := time.Date(nowTime.Year(), nowTime.Month()-1, 1, 0, 0, 0, 0, time.Local)
	end := time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local)

	list := []*model.GoldSettleInfo{
		{
			CommonSettleInfo: model.CommonSettleInfo{
				BillType:  billType,
				GuildId:   guildId,
				SettleUid: settleUid,
				Fee:       pFee,
				Income:    common.InteractGameConvIncome(pFee),
				Start:     start,
				End:       end,
			},
		},
	}
	tpName := settlement.BillTypeLabel.Text(billType)
	title := tpName + "-" + nowTime.Format("2006-01-02")

	defer func() {
		f := fmt.Sprintf("%s.xlsx", title)
		_ = os.Remove(f)
	}()

	gomock.InOrder(
		mockManager.EXPECT().GetSettleMonthRange(nowTime).Return(start, end),
		// mockManager.EXPECT().GetStore(channelType).Return(mockStore, nil),
		mockStore.EXPECT().GetInteractGameAggIncome(ctx, start, settleStatus).Return(list, nil),
		mockManager.EXPECT().GetGuildInfo(ctx, guildId).Return(nil, false, nil),
		mockManager.EXPECT().CreateEmailTitle(tpName, nowTime).Return(title),
		mockManager.EXPECT().SendEmail(ctx, title, "见附件", gomock.Any()),
	)
	type fields struct {
		ctx      context.Context
		billType settlement_bill.SettlementBillType
		goldType gold_commission.GoldType
		mgr      IManager
		store    mysql.IGoldCommissionMysql
		abort    bool
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "Report",
			fields: fields{
				ctx:      ctx,
				billType: billType,
				mgr:      mockManager,
				store:    mockStore,
				abort:    false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettleInteractGameGold{
				ctx:      tt.fields.ctx,
				billType: tt.fields.billType,
				mgr:      tt.fields.mgr,
				store:    tt.fields.store,
				abort:    tt.fields.abort,
			}
			if err := s.Report(); (err != nil) != tt.wantErr {
				t.Errorf("Report() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestSettleInteractGameGold_Settle(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()
	nowTime := time.Now()
	monkey.Patch(time.Now, func() time.Time {
		return nowTime
	})
	billType := settlement_bill.SettlementBillType_InteractGameCommission
	settleStatus := gold_commission.ReqSettleStatus_ReqSettleStatusWait
	mockManager := mocks.NewMockIManager(ctl)
	mockStore := mocks.NewMockIGoldCommissionMysql(ctl)
	guildId := uint32(999)
	settleUid := uint32(123456)
	pFee := uint64(10000)
	billId := "bill_id"

	start := time.Date(nowTime.Year(), nowTime.Month()-1, 1, 0, 0, 0, 0, time.Local)
	end := time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local)

	list := []*model.GoldSettleInfo{
		{
			CommonSettleInfo: model.CommonSettleInfo{
				BillType:  billType,
				GuildId:   guildId,
				SettleUid: settleUid,
				Fee:       pFee,
				Income:    common.InteractGameConvIncome(pFee),
				Start:     start,
				End:       end,
			},
		},
	}

	monkey.PatchInstanceMethod(reflect.TypeOf(&alert.Feishu{}), "SendInfo", func(c *alert.Feishu,
		text string) {
		return
	})
	gomock.InOrder(
		mockManager.EXPECT().GetSettleMonthRange(nowTime).Return(start, end),
		// mockManager.EXPECT().GetStore(channelType).Return(mockStore, nil),
		mockStore.EXPECT().GetInteractGameAggIncome(ctx, start, settleStatus).Return(list, nil),
		mockManager.EXPECT().GetAlert().Return(&alert.Feishu{}),
		mockManager.EXPECT().CreateBill(ctx, billType, settleUid, start, end).Return(billId, nil),
		mockStore.EXPECT().Settlement(ctx, gomock.Any(), gomock.Any()).Return(nil),
		mockStore.EXPECT().SetSettleReconcileInfo(ctx, guildId, start, gomock.Any()).Return(nil),
	)

	type fields struct {
		ctx      context.Context
		billType settlement_bill.SettlementBillType
		goldType gold_commission.GoldType
		mgr      IManager
		store    mysql.IGoldCommissionMysql
		abort    bool
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "Settle",
			fields: fields{
				ctx:      ctx,
				billType: billType,
				mgr:      mockManager,
				store:    mockStore,
				abort:    false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettleInteractGameGold{
				ctx:      tt.fields.ctx,
				billType: tt.fields.billType,
				mgr:      tt.fields.mgr,
				store:    tt.fields.store,
				abort:    tt.fields.abort,
			}
			if err := s.Settle(); (err != nil) != tt.wantErr {
				t.Errorf("Settle() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
