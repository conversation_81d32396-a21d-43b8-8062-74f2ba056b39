package manager

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/commission"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/settlement"
	"golang.52tt.com/pkg/settlement/export"
	pb "golang.52tt.com/protocol/services/gold-commission"
	Guild "golang.52tt.com/protocol/services/guildsvr"
	settlementBill "golang.52tt.com/protocol/services/settlement-bill"
	"golang.52tt.com/services/gold/common"
	"golang.52tt.com/services/gold/common/model"
	"golang.52tt.com/services/gold/gold-settlement/mysql"
	"time"
)

type SettleInteractGameGold struct {
	ctx      context.Context
	billType settlementBill.SettlementBillType
	goldType pb.GoldType
	mgr      IManager
	store    mysql.IGoldCommissionMysql
	abort    bool
}

func (s *SettleInteractGameGold) GetBillType() settlementBill.SettlementBillType { return s.billType }
func (s *SettleInteractGameGold) GetGoldType() pb.GoldType                       { return s.goldType }
func (s *SettleInteractGameGold) GetContext() context.Context                    { return s.ctx }
func (s *SettleInteractGameGold) WasAbort() bool                                 { return s.abort }
func (s *SettleInteractGameGold) Abort()                                         { s.abort = true }

func NewSettleInteractGameGold(ctx context.Context, mgr IManager) *SettleInteractGameGold {
	billType := settlementBill.SettlementBillType_InteractGameCommission
	goldType := pb.GoldType_INTERACT_GAME_GOLD
	store, err := mgr.GetStore(goldType)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewSettleInteractGameGold %s GetStore err:%s", billType, err)
	}
	return &SettleInteractGameGold{
		ctx:      ctx,
		billType: billType,
		goldType: goldType,
		mgr:      mgr,
		store:    store,
	}
}

func (s *SettleInteractGameGold) Aggregate() error {
	ctx := s.ctx
	billType := s.GetBillType()
	settleStatus := pb.ReqSettleStatus_ReqSettleStatusWait
	nowTime := time.Now()

	// 结算时间区间
	start, end := s.mgr.GetSettleMonthRange(nowTime)

	// 获取公会列表
	guildIds, err := s.store.GetDistinctGuildIds(ctx, start, end, settleStatus)
	if err != nil {
		log.ErrorWithCtx(ctx, "Aggregate %s GetDistinctGuildIds err:%s", billType, err)
		return err
	}

	log.Infof("Aggregate %s settlement guild ids: %+v", billType, guildIds)

	if len(guildIds) == 0 {
		s.Abort()
		return nil
	}

	desc := fmt.Sprintf("汇总于:%s 前的互动游戏会长金钻收益", end.Format("2006-01-02 00:00:00"))

	for _, guildId := range guildIds {

		if s.mgr.IsSkipGuild(guildId) {
			log.Infof("Settle %s skip guild id: %+v", billType, guildId)
			continue
		}

		settleInfo := &model.GoldSettleInfo{
			CommonSettleInfo: model.CommonSettleInfo{
				BillType: billType,
				Now:      nowTime,
				Start:    start,
				End:      end,
				GuildId:  guildId,
			},
		}

		handle := func() (err error) {
			// 填充公会信息
			{
				var guildInfo *Guild.GuildResp
				var guildDismiss bool
				guildInfo, guildDismiss, err = s.mgr.GetGuildInfo(ctx, guildId)
				if err != nil {
					log.ErrorWithCtx(ctx, "Aggregate %s GetGuildInfo err:%s", billType, billType, err)
					return
				}
				// 公会不存在，不重试
				if guildDismiss {
					log.WarnWithCtx(ctx, "Aggregate %s guildDismiss, guild_id:%d", billType, guildId)
					settleInfo.NotSettle = true
					settleInfo.NotSettleReason = "公会已解散"

					return
				}
				settleInfo.GuildUid = guildInfo.GetOwner()
				settleInfo.GuildName = guildInfo.GetName()
				settleInfo.GuildDismiss = guildDismiss
			}

			_, income, err := s.GetFeeIncome(ctx, settleInfo, settleStatus)
			if err != nil {
				log.ErrorWithCtx(ctx, "Aggregate %s GetFeeIncome err:%s", billType, err)
				return
			}

			if income == 0 {
				log.ErrorWithCtx(ctx, "Aggregate %s income = 0, guild_id = %d", billType, guildId)
				return
			}

			// 处理对公
			isCorporate, settleUid, err := s.mgr.HandleCorporate(ctx, &settleInfo.CommonSettleInfo)
			if err != nil {
				log.ErrorWithCtx(ctx, "Aggregate %s HandleCorporate err:%s", billType, err)
				return
			}

			dateKey := s.mgr.GenDayKey(nowTime, guildId)
			settleInfo.IsCorporate = isCorporate
			settleInfo.SettleUid = settleUid
			settleInfo.DateKey = dateKey
			settleInfo.Remark = desc

			if err = s.store.Aggregation(ctx, settleInfo); err != nil {
				log.ErrorWithCtx(ctx, "Aggregate %s Settlement err:%s, settleUid:%d", billType, err, settleUid)
				// 如果汇总记录已存在，不重试
				if model.IsDuplicate(err) {
					return nil
				}
				return err
			}
			return
		}

		err = common.Retry(10, time.Second*1, handle)
		if err != nil {
			log.ErrorWithCtx(ctx, "Aggregate %s retry err:%s", billType, err)
			settleInfo.Err = err.Error()
		}
	}
	return nil
}

func (s *SettleInteractGameGold) Settle() error {
	ctx := s.ctx
	settleStatus := pb.ReqSettleStatus_ReqSettleStatusWait
	nowTime := time.Now()
	// 结算时间区间
	start, _ := s.mgr.GetSettleMonthRange(nowTime)

	ResetSettledUidList()
	defer ResetSettledUidList()

	// 取汇总结果
	guildSettleInfos, err := s.store.GetInteractGameAggIncome(ctx, start, settleStatus)
	if err != nil {
		log.ErrorWithCtx(ctx, "Settle %s GetInteractGameAggIncome err:%s", s.GetBillType(), err)
		return err
	}

	defer s.mgr.GetAlert().SendInfo(fmt.Sprintf("结算完成 互动游戏会长佣金 共%d条", len(guildSettleInfos)))

	if len(guildSettleInfos) == 0 {
		s.Abort()
		return nil
	}

	for _, gi := range guildSettleInfos {
		if gi.NotSettle {
			continue
		}

		err = common.Retry(10, time.Second*1, func() error {
			return s.SettleHandle(gi)
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "Settle %s retry err:%s", s.GetBillType(), err)
			gi.Err = err.Error()
		}
	}

	return nil
}

func (s *SettleInteractGameGold) SettleHandle(gi *model.GoldSettleInfo) error {
	var err error
	ctx := s.ctx
	billType := s.GetBillType()
	// 创建结算单
	gi.BillId, err = s.mgr.CreateBill(ctx, billType, gi.SettleUid, gi.Start, gi.End)
	if err != nil {
		log.ErrorWithCtx(ctx, "Settle %s CreateBill err:%s, settleUid:%d", billType, err, gi.SettleUid)
		return err
	}

	// 结算事务
	var comResp *commission.SettlementDataResponse
	if err = s.store.Settlement(ctx, gi, func() error {
		cli := s.mgr.GetCommissionClient(billType)
		comResp, err = cli.Settlement(ctx, gi.SettleUid, gi.Income, gi.DateKey, gi.Remark)
		if err != nil {
			log.ErrorWithCtx(ctx, "Settle %s Settlement err:%s, settleUid:%d, income:%d", billType, err, gi.SettleUid, gi.Income)
			if !IsSettleFatal(err) {
				return nil
			}
			time.Sleep(100 * time.Millisecond)
			return err
		}
		return nil
	}); err != nil {
		log.ErrorWithCtx(ctx, "Settle %s Settlement err:%s, settleUid:%d", billType, err, gi.SettleUid)
		return err
	}

	// 记录货币服务器时间与订单ID
	_ = s.store.SetSettleReconcileInfo(ctx, gi.GuildId, gi.Start, comResp)

	return nil
}

func (s *SettleInteractGameGold) Report() error {
	ctx := s.ctx
	billType := s.GetBillType()
	settleStatus := pb.ReqSettleStatus_ReqSettleStatusFinished
	nowTime := time.Now()
	start, _ := s.mgr.GetSettleMonthRange(nowTime)
	tableRows := make([][]interface{}, 0)

	// 取汇总结果
	guildSettleInfos, err := s.store.GetInteractGameAggIncome(ctx, start, settleStatus)
	if err != nil {
		log.ErrorWithCtx(ctx, "Report %s GetInteractGameAggIncome err:%s", billType, err)
		return err
	}

	for _, gi := range guildSettleInfos {
		if gi.Income == 0 {
			continue
		}

		var errInfo string
		if gi.NotSettle {
			errInfo = gi.NotSettleReason
		}

		guildInfo, _, err := s.mgr.GetGuildInfo(ctx, gi.GuildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "Report %s GetGuildInfo err:%s, guildId:%d", billType, err, gi.GuildId)
		}

		// 公会ID，会长UID，工会名称，互动游戏流水，总流水，佣金收益元
		printLine := []interface{}{
			gi.GuildId,
			gi.SettleUid,
			gi.InteractGameFee,
			gi.Fee,
			settlement.CentToMoney(gi.Income),
			guildInfo.GetName(),
			errInfo,
		}

		fmt.Printf("Report %s settlement info: %+v \n", billType, printLine)

		tableRows = append(tableRows, printLine)
	}

	if len(tableRows) == 0 {
		return nil
	}

	tpName := settlement.BillTypeLabel.Text(billType)
	title := tpName + "-" + nowTime.Format("2006-01-02")
	e := export.CreateReportTableHeader(title, []string{"公会ID", "会长UID", "互动游戏流水（T豆）", "总流水（T豆）", "佣金收益（元）", "公会名称"})
	e.PushRowsV2(tableRows)
	f := fmt.Sprintf("%s.xlsx", title)
	e.Save(f)
	s.mgr.SendEmail(ctx, s.mgr.CreateEmailTitle(tpName, nowTime), "见附件", []string{f})
	return nil
}

func (s *SettleInteractGameGold) GetFeeIncome(ctx context.Context, settleInfo *model.GoldSettleInfo, settleStatus pb.ReqSettleStatus) (
	fee, income uint64, err error) {
	guildId, start, end := settleInfo.GuildId, settleInfo.Start, settleInfo.End
	interactGameFee, err := s.store.GetGuildGoldTotal(ctx, guildId, start, end, pb.SourceType_SourceTypeInteractGame, settleStatus)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFeeIncome %s GetGuildGoldTotal err:%s", s.GetBillType(), err)
		return
	}

	// 总流水
	fee = interactGameFee

	// 金钻
	income = common.InteractGameConvIncome(interactGameFee)

	settleInfo.InteractGameFee = interactGameFee
	settleInfo.InteractGameIncome = income
	settleInfo.Fee = fee
	settleInfo.Income = income
	return fee, income, nil
}
