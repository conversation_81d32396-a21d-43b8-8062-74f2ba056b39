package main

import (
	"context"
	"flag"
	"fmt"
	"golang.52tt.com/services/gold/common/model"
	"golang.52tt.com/services/gold/gold-settlement/conf"
	"golang.52tt.com/services/gold/gold-settlement/manager"
	_ "golang.52tt.com/services/recommend-dialog/tools/grpc_proxy/enable"
)

// 删除会长结算记录并重新结算 仅限测试环境使用！

var (
	mgr *manager.Manager
)

func initMain() {
	var err error

	sc := &conf.ServiceConfigT{}

	err = sc.Parse("/home/<USER>/etc/server/gold-settlement.json")
	if err != nil {
		fmt.Println(err)
		return
	}

	if !sc.IsTest() {
		panic("仅限测试环境使用！")
	}

	mgr, err = manager.NewManager(sc)
	if err != nil {
		fmt.Println(err)
		return
	}
}

func main() {

	u := flag.Int("uid", 0, "uid")
	flag.Parse()
	uid := *u
	if uid == 0 {
		panic("uid 不能为空")
	}

	initMain()
	ctx := context.Background()
	//ctx = context.WithValue(ctx, settlement.SpecialEmailTo, "<EMAIL>")

	f := manager.NewSettleESportGold(ctx, mgr)

	store, err := mgr.GetStore(f.GetGoldType())
	if err != nil {
		panic(err)
	}

	if err = store.GetDb().Model(&model.ESportGoldIncome{}).
		Where("guild_owner = ?", uid).Delete(&model.ESportGoldIncome{}).Error; err != nil {
		panic(err)
	}

	manager.NewFlow(f)
}
