package model

import (
	"fmt"
	"time"

	pb "golang.52tt.com/protocol/services/gold-commission"
)

const (
	AmuseGoldTbl              = "amuse_gold"
	AmuseGoldIncomeTbl        = "amuse_gold_income"
	AmuseGoldSpecialIncomeTbl = "amuse_gold_special_income"
	AmuseReconcileTbl         = "amuse_gold_reconcile"
	AmuseSettleRecord         = "amuse_gold_settle_record"
	AmuseChamberDayTbl        = "amuse_gold_day_stat"
	AmuseChamberMonthTbl      = "amuse_gold_month_stat"
	AmuseExtraTbl             = "amuse_extra_income"
	AmusePaidCntTbl           = "amuse_paid_cnt_stat"
)

// AmuseMysql
type AmuseMysql struct{}

func (a *AmuseMysql) GetGoldSettleRecordTbl() string {
	return AmuseSettleRecord
}

func (a *AmuseMysql) GetGoldExtraTbl() string {
	return AmuseExtraTbl
}

func (a *AmuseMysql) GetDaySumTbl() string {
	return AmuseChamberDayTbl
}

func (a *AmuseMysql) GetMonthSumTbl() string {
	return AmuseChamberMonthTbl
}

// GetGoldReconcileTbl 获取对账表名
func (a *AmuseMysql) GetGoldReconcileTbl(t time.Time) string {
	return fmt.Sprintf("%s_%s", AmuseReconcileTbl, t.Format("200601"))
}

// GetGoldType 获取类型
func (a *AmuseMysql) GetGoldType() pb.GoldType {
	return pb.GoldType_AMUSE_GOLD
}

// GetGoldTbl 获取流水表名
func (a *AmuseMysql) GetGoldTbl(t time.Time) string {
	return fmt.Sprintf("%s_%s", AmuseGoldTbl, t.Format("200601"))
}

func (a *AmuseMysql) GetGoldIncomeTbl() string {
	return AmuseGoldIncomeTbl
}

func (a *AmuseMysql) GetGoldSpecialIncomeTbl() string {
	return AmuseGoldSpecialIncomeTbl
}

func (a *AmuseMysql) GetPaidCntTbl() string {
	return AmusePaidCntTbl
}

// AmuseGoldIncome 多人互动会长佣金收益汇总
type AmuseGoldIncome struct {
	GoldIncome
	WereWolfFee    uint64 `gorm:"column:werewolf_fee"`    // 本月狼人杀流水（T豆）
	WereWolfIncome uint64 `gorm:"column:werewolf_income"` // 本月狼人杀佣金收益（金钻）
}

func (m *AmuseGoldIncome) TableName() string {
	return AmuseGoldIncomeTbl
}

// AmuseExtraIncome 多人互动额外奖励月度汇总
type AmuseExtraIncome struct {
	GuildId          uint32    `gorm:"primary_key;column:guild_id"`             // 公会id
	SettlementDate   uint32    `gorm:"primary_key;column:settlement_date"`      // 结算月份
	GuildOwner       uint32    `gorm:"column:guild_owner"`                      // 对公会长uid
	IsCorp           bool      `gorm:"column:is_corp"`                          // 是否是企业版
	ThisMonthFee     uint64    `gorm:"column:this_month_fee"`                   // 本月房间流水
	LastMonthFee     uint64    `gorm:"column:last_month_fee"`                   // 上月房间流水
	GrowRate         float64   `gorm:"column:grow_rate"`                        // 房间流水增长率
	SettlementMoney  uint64    `gorm:"column:settlement_money"`                 // 总结算金额（分）
	PrepaidMoney     uint64    `gorm:"column:prepaid_money"`                    // 预付金额（分）
	SettlementStatus uint8     `gorm:"column:settlement_status"`                // 结算状态 1 未结算 2 已结算
	SettlementBillID string    `gorm:"column:settlement_bill_id"`               // 归属结算单ID
	Remark           string    `gorm:"column:remark"`                           // 备注
	ReconcileTime    time.Time `gorm:"column:reconcile_time"`                   // 货币确认结算时间，用于对账
	ReconcileOrderID string    `gorm:"column:reconcile_order_id"`               // 货币确认结算订单ID，用于对账
	CreateTime       time.Time `gorm:"column:create_time;default:current_time"` // 创建时间
	UpdateTime       time.Time `gorm:"column:update_time;default:current_time"` // 更新时间
	Operator         string    `gorm:"column:operator"`                         // 操作人
}

func (m *AmuseExtraIncome) TableName() string {
	return "amuse_extra_income"
}
