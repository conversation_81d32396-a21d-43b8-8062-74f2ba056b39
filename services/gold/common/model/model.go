package model

import (
	"time"

	pb "golang.52tt.com/protocol/services/gold-commission"
)

// Gold 流水明细表通用结构
type Gold struct {
	ID         uint32 `gorm:"column:id"`
	GuildId    uint32 `gorm:"column:guild_id;primary_key"`    // 工会ID
	SourceType uint8  `gorm:"column:source_type;primary_key"` // 来源类型
	UniqSign   string `gorm:"column:uniq_sign;primary_key"`   // 唯一签名标识
	PaidUid    uint32 `gorm:"column:paid_uid"`                // 用户uid
	RoomId     uint32 `gorm:"column:room_id"`                 // 房间ID
	AnchorId   uint32 `gorm:"column:anchor_id"`               // 主播ID
	OrderId    string `gorm:"column:order_id"`                // 订单ID
	Fee        uint64 `gorm:"column:fee"`                     // 流水
	Status     uint32 `gorm:"column:status"`                  // 状态
	BoughtTime uint64 `gorm:"column:bought_time"`             // 购买时间
}

// GoldIncome 佣金汇总结算表通用结构
type GoldIncome struct {
	IsCorp           bool      `gorm:"column:is_corp"`                          // 是否对公 0 否 1 是
	GuildOwner       uint32    `gorm:"column:guild_owner"`                      // 对公会长uid
	GuildID          uint32    `gorm:"column:guild_id"`                         // 公会id
	SettlementDate   uint32    `gorm:"column:settlement_date"`                  // 结算月份 202201
	DateKey          string    `gorm:"column:date_key"`                         // 用于货币date_key 日期+公会ID
	Fee              uint64    `gorm:"column:fee"`                              // 本月流水（T豆）
	Income           uint64    `gorm:"column:income"`                           // 本月佣金收益（金钻）
	PresentFee       uint64    `gorm:"column:present_fee"`                      // 本月送礼流水（T豆）
	PresentIncome    uint64    `gorm:"column:present_income"`                   // 本月送礼佣金收益（金钻）
	SettlementMoney  uint64    `gorm:"column:settlement_money"`                 // 本月结算金额（分）
	Remark           string    `gorm:"column:remark"`                           // 备注
	SettlementStatus bool      `gorm:"column:settlement_status"`                // 结算状态 0 未结算 1 已结算
	SettlementBillID string    `gorm:"column:settlement_bill_id"`               // 归属结算单ID
	NotSettle        bool      `gorm:"column:not_settle"`                       // 是否不结算 0 结算 1 不结算
	NotSettleReason  string    `gorm:"column:not_settle_reason"`                // 不结算原因
	SettleStart      time.Time `gorm:"column:settle_start"`                     // 结算开始时间
	SettleEnd        time.Time `gorm:"column:settle_end"`                       // 结算结束时间
	ReconcileTime    time.Time `gorm:"column:reconcile_time"`                   // 货币确认结算时间，用于对账
	ReconcileOrderID string    `gorm:"column:reconcile_order_id"`               // 货币确认结算订单ID，用于对账
	CreateTime       time.Time `gorm:"column:create_time;default:current_time"` // 创建时间
	UpdateTime       time.Time `gorm:"column:update_time;default:current_time"` // 更新时间
}

func (m *GoldIncome) ConvSettleInfo() *GoldSettleInfo {
	var info = CommonSettleInfo{
		IsCorporate:     m.IsCorp,
		Now:             m.CreateTime,
		Start:           m.SettleStart,
		End:             m.SettleEnd,
		GuildId:         m.GuildID,
		SettleUid:       m.GuildOwner,
		Fee:             m.Fee,
		Income:          m.Income,
		DateKey:         m.DateKey,
		Remark:          m.Remark,
		NotSettle:       m.NotSettle,
		NotSettleReason: m.NotSettleReason,
	}
	if info.IsCorporate {
		info.MasterUid = m.GuildOwner
	} else {
		info.GuildUid = m.GuildOwner
	}
	return &GoldSettleInfo{
		CommonSettleInfo: info,
	}
}

// GoldSettleRecord 结算通用结构
type GoldSettleRecord struct {
	ID         uint32    `gorm:"column:id"`
	SettleUid  uint32    `gorm:"column:settle_uid"` // 结算uid
	UniqSign   string    `gorm:"column:uniq_sign"`  // 唯一
	GuildId    uint32    `gorm:"column:guild_id"`   // 公会id
	Fee        uint64    `gorm:"column:fee"`        // 总流水
	Income     uint64    `gorm:"column:income"`     // 结算金钻收益
	Status     uint8     `gorm:"column:status"`     // 预留 1
	BillId     string    `gorm:"bill_id"`           // 结算单ID
	Extend     string    `gorm:"column:extend"`
	CreateTime time.Time `gorm:"column:create_time;default:current_time"`
	UpdateTime time.Time `gorm:"column:update_time;default:current_time"`
}

// GoldStat 日/月纬度汇总数据通用结构
type GoldStat struct {
	Date       time.Time `gorm:"column:date"`                             // 日/月整点时间戳
	GuildId    uint32    `gorm:"column:guild_id"`                         // 工会ID
	ChannelId  uint32    `gorm:"column:channel_id"`                       // 房间ID
	AnchorId   uint32    `gorm:"column:anchor_id"`                        // 主播ID
	Source     uint32    `gorm:"column:source"`                           // 类别：0、汇总；1、礼物；5、狼人杀
	Fee        uint64    `gorm:"column:fee"`                              // 类别流水
	Income     uint64    `gorm:"column:income"`                           // 类别收益
	UpdateTime time.Time `gorm:"column:update_time;default:current_time"` // 更新时间
}

// PaidCntStat 付费人数统计
type PaidCntStat struct {
	ID         uint32    `gorm:"column:id"`                                    // id
	Dimension  string    `gorm:"column:dimension"`                             // 维度
	Date       time.Time `gorm:"column:date"`                                  // 日期
	GuildId    uint32    `gorm:"column:guild_id"`                              // 公会id
	IncrId     uint32    `gorm:"column:incr_id"`                               // cid or uid
	PaidCnt    uint32    `gorm:"column:paid_cnt"`                              // 付费人数
	UpdateTime time.Time `gorm:"column:update_time;default:current_timestamp"` // 更新时间
}

// 付费人数统计-维度
type PaidCntStatDimension string

const (
	DimensionGuildDaily       PaidCntStatDimension = "gd"  // guild daily 公会日付费人数
	DimensionGuildMonthly     PaidCntStatDimension = "gm"  // guild monthly 公会月付费人数
	DimensionGuildIncrDaily   PaidCntStatDimension = "gid" // incr daily 房间日付费人数（娱乐房cid，语音房uid）
	DimensionGuildIncrMonthly PaidCntStatDimension = "gim" // incr monthly 房间月付费人数（娱乐房cid，语音房uid）
)

type Table interface {
	GetGoldType() pb.GoldType
	GetGoldTbl(t time.Time) string
	GetGoldIncomeTbl() string
	GetGoldSpecialIncomeTbl() string
	GetGoldSettleRecordTbl() string
	GetGoldExtraTbl() string
	GetDaySumTbl() string
	GetMonthSumTbl() string
	GetGoldReconcileTbl(t time.Time) string
	GetPaidCntTbl() string
}
