package com_kafka

import (
    "math/rand"
    "sync"

	"context"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
)

const (
    defaultConcurrenceSize = 10
    defaultQueueSize       = 2
)

func defaultHashFunc(msg *subscriber.ConsumerMessage) uint32 {
    return rand.Uint32()
}

type KafkaConcurrenceOrderlySub struct {
    kfkSub          subscriber.Subscriber
    concurrenceSize uint32
    queue           []chan *subscriber.ConsumerMessage
    queueSize       uint32
    hashFunc        func(message *subscriber.ConsumerMessage) uint32
    processor       func(message *subscriber.ConsumerMessage) (error, bool)
    wg              sync.WaitGroup
}

type Option func(sub *KafkaConcurrenceOrderlySub)

func WithConcurrenceSize(size uint32) Option {
    return func(sub *KafkaConcurrenceOrderlySub) {
        sub.concurrenceSize = size
    }
}

func WithQueueSize(size uint32) Option {
    return func(sub *KafkaConcurrenceOrderlySub) {
        sub.queueSize = size
    }
}

func WithHashFunc(f func(message *subscriber.ConsumerMessage) uint32) Option {
    return func(sub *KafkaConcurrenceOrderlySub) {
        sub.hashFunc = f
    }
}

func NewKafkaConcurrenceOrderlySub(kfkCfg *config.KafkaConfig,
    processor func(message *subscriber.ConsumerMessage) (error, bool),
    opts ...Option) *KafkaConcurrenceOrderlySub {
    kmos := &KafkaConcurrenceOrderlySub{
        concurrenceSize: defaultConcurrenceSize,
        queueSize:       defaultQueueSize,
        hashFunc:        defaultHashFunc,
        processor:       processor,
    }
    for _, opt := range opts {
        opt(kmos)
    }
    kmos.initQueue()

    log.Infof("new KafkaConcurrenceOrderlySub, config:%+v", kfkCfg)
    cfg := kafka.DefaultConfig()
    cfg.ClientID = kfkCfg.ClientID
    cfg.Consumer.Offsets.Initial = kafka.OffsetNewest
    cfg.Consumer.Return.Errors = true

    //kafkaSub, err := eventV2.NewKafkaSub(kCfg.Topics, kCfg.BrokerList(), kCfg.GroupID, kCfg.TopicList(), cfg)
    //if err != nil {
    //	log.Errorf("NewKafkaConcurrenceOrderlySub.NewKafkaSub, error: %v", err)
    //	return nil
    //}
    //kafkaSub.SetMessageProcessor(func(msg *sarama.ConsumerMessage) (error, bool) {
    //	kmos.submitTask(msg)
    //	return nil, false
    //})
    kafkaSub, err := kafka.NewSubscriber(kfkCfg.BrokerList(), cfg, subscriber.WithMaxRetryTimes(3))
    if err != nil {
        panic(err)
    }

    err = kafkaSub.SubscribeContext(kfkCfg.GroupID, []string{kfkCfg.Topics}, subscriber.ProcessorContextFunc(
        func(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
            kmos.submitTask(msg)
            return nil, false
        }))
    if err != nil {
        panic(err)
    }

    kmos.kfkSub = kafkaSub

    return kmos
}

func (sub *KafkaConcurrenceOrderlySub) initQueue() {
    for i := 0; i < int(sub.concurrenceSize); i++ {
        sub.queue = append(sub.queue, make(chan *subscriber.ConsumerMessage, sub.queueSize))
    }
}

func (sub *KafkaConcurrenceOrderlySub) submitTask(msg *subscriber.ConsumerMessage) {
    hashKey := sub.hashFunc(msg)
    idx := int(hashKey) % len(sub.queue)
    sub.queue[idx] <- msg
}

func (sub *KafkaConcurrenceOrderlySub) concurrenceProcess() {
    for i := 0; i < int(sub.concurrenceSize); i++ {
        sub.wg.Add(1)
        go func(idx int) {
            defer sub.wg.Done()
            for msg := range sub.queue[idx] {
                if err, _ := sub.processor(msg); err != nil {
                    log.Errorf("worker_%d consume error: %v", err)
                }
                //log.Infof("worker_%d consumer msg: %v", idx, msg)
            }
        }(i)
    }
}

func (sub *KafkaConcurrenceOrderlySub) stopWorker() {
    for _, que := range sub.queue {
        close(que)
    }

    sub.wg.Wait()
}

func (sub *KafkaConcurrenceOrderlySub) Start() error {
    //err := sub.kfkSub.Start()
    //if err != nil {
    //	return err
    //}

    sub.concurrenceProcess()

    return nil
}

func (sub *KafkaConcurrenceOrderlySub) Stop() {
    sub.kfkSub.Stop()
    sub.stopWorker()
}
