// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/expsvr/cache (interfaces: IExpCache)

// Package mocks is a generated GoMock package.
package mocks

import (
	"golang.52tt.com/services/expsvr/internal/cache"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	expsvr "golang.52tt.com/protocol/services/expsvr"
)

// MockIExpCache is a mock of IExpCache interface.
type MockIExpCache struct {
	ctrl     *gomock.Controller
	recorder *MockIExpCacheMockRecorder
}

// MockIExpCacheMockRecorder is the mock recorder for MockIExpCache.
type MockIExpCacheMockRecorder struct {
	mock *MockIExpCache
}

// NewMockIExpCache creates a new mock instance.
func NewMockIExpCache(ctrl *gomock.Controller) *MockIExpCache {
	mock := &MockIExpCache{ctrl: ctrl}
	mock.recorder = &MockIExpCacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIExpCache) EXPECT() *MockIExpCacheMockRecorder {
	return m.recorder
}

// AddAsyncTaskQueue mocks base method.
func (m *MockIExpCache) AddAsyncTaskQueue(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAsyncTaskQueue", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddAsyncTaskQueue indicates an expected call of AddAsyncTaskQueue.
func (mr *MockIExpCacheMockRecorder) AddAsyncTaskQueue(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAsyncTaskQueue", reflect.TypeOf((*MockIExpCache)(nil).AddAsyncTaskQueue), arg0)
}

// ConsumerAsyncTask mocks base method.
func (m *MockIExpCache) ConsumerAsyncTask() (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConsumerAsyncTask")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConsumerAsyncTask indicates an expected call of ConsumerAsyncTask.
func (mr *MockIExpCacheMockRecorder) ConsumerAsyncTask() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConsumerAsyncTask", reflect.TypeOf((*MockIExpCache)(nil).ConsumerAsyncTask))
}

// DelUserSpeedUpStatus mocks base method.
func (m *MockIExpCache) DelUserSpeedUpStatus(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserSpeedUpStatus", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserSpeedUpStatus indicates an expected call of DelUserSpeedUpStatus.
func (mr *MockIExpCacheMockRecorder) DelUserSpeedUpStatus(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserSpeedUpStatus", reflect.TypeOf((*MockIExpCache)(nil).DelUserSpeedUpStatus), arg0)
}

// GetBatchUserExp mocks base method.
func (m *MockIExpCache) GetBatchUserExp(arg0 []uint32) ([]*expsvr.UserExp, []uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBatchUserExp", arg0)
	ret0, _ := ret[0].([]*expsvr.UserExp)
	ret1, _ := ret[1].([]uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetBatchUserExp indicates an expected call of GetBatchUserExp.
func (mr *MockIExpCacheMockRecorder) GetBatchUserExp(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBatchUserExp", reflect.TypeOf((*MockIExpCache)(nil).GetBatchUserExp), arg0)
}

// GetLogCleanStatus mocks base method.
func (m *MockIExpCache) GetLogCleanStatus(arg0 uint32, arg1 time.Time) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLogCleanStatus", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLogCleanStatus indicates an expected call of GetLogCleanStatus.
func (mr *MockIExpCacheMockRecorder) GetLogCleanStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLogCleanStatus", reflect.TypeOf((*MockIExpCache)(nil).GetLogCleanStatus), arg0, arg1)
}

// GetUserExp mocks base method.
func (m *MockIExpCache) GetUserExp(arg0 uint32) (int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserExp", arg0)
	ret0, _ := ret[0].(int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserExp indicates an expected call of GetUserExp.
func (mr *MockIExpCacheMockRecorder) GetUserExp(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExp", reflect.TypeOf((*MockIExpCache)(nil).GetUserExp), arg0)
}

// GetUserOnlineFields mocks base method.
func (m *MockIExpCache) GetUserOnlineFields(arg0 uint32, arg1 time.Time) (*cache.UserOnlineData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserOnlineFields", arg0, arg1)
	ret0, _ := ret[0].(*cache.UserOnlineData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserOnlineFields indicates an expected call of GetUserOnlineFields.
func (mr *MockIExpCacheMockRecorder) GetUserOnlineFields(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserOnlineFields", reflect.TypeOf((*MockIExpCache)(nil).GetUserOnlineFields), arg0, arg1)
}

// GetUserOnlineTime mocks base method.
func (m *MockIExpCache) GetUserOnlineTime(arg0 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserOnlineTime", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserOnlineTime indicates an expected call of GetUserOnlineTime.
func (mr *MockIExpCacheMockRecorder) GetUserOnlineTime(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserOnlineTime", reflect.TypeOf((*MockIExpCache)(nil).GetUserOnlineTime), arg0)
}

// GetUserSpeedUpStatus mocks base method.
func (m *MockIExpCache) GetUserSpeedUpStatus(arg0 uint32) (int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSpeedUpStatus", arg0)
	ret0, _ := ret[0].(int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSpeedUpStatus indicates an expected call of GetUserSpeedUpStatus.
func (mr *MockIExpCacheMockRecorder) GetUserSpeedUpStatus(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSpeedUpStatus", reflect.TypeOf((*MockIExpCache)(nil).GetUserSpeedUpStatus), arg0)
}

// SaveLogCleanStatus mocks base method.
func (m *MockIExpCache) SaveLogCleanStatus(arg0 uint32, arg1 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveLogCleanStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveLogCleanStatus indicates an expected call of SaveLogCleanStatus.
func (mr *MockIExpCacheMockRecorder) SaveLogCleanStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveLogCleanStatus", reflect.TypeOf((*MockIExpCache)(nil).SaveLogCleanStatus), arg0, arg1)
}

// SaveUserOnlineData mocks base method.
func (m *MockIExpCache) SaveUserOnlineData(arg0 *cache.UserOnlineData) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveUserOnlineData", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveUserOnlineData indicates an expected call of SaveUserOnlineData.
func (mr *MockIExpCacheMockRecorder) SaveUserOnlineData(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveUserOnlineData", reflect.TypeOf((*MockIExpCache)(nil).SaveUserOnlineData), arg0)
}

// SetBatchUserExp mocks base method.
func (m *MockIExpCache) SetBatchUserExp(arg0 []*expsvr.UserExp) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBatchUserExp", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetBatchUserExp indicates an expected call of SetBatchUserExp.
func (mr *MockIExpCacheMockRecorder) SetBatchUserExp(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBatchUserExp", reflect.TypeOf((*MockIExpCache)(nil).SetBatchUserExp), arg0)
}

// SetUserExp mocks base method.
func (m *MockIExpCache) SetUserExp(arg0 uint32, arg1 int32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserExp", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserExp indicates an expected call of SetUserExp.
func (mr *MockIExpCacheMockRecorder) SetUserExp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserExp", reflect.TypeOf((*MockIExpCache)(nil).SetUserExp), arg0, arg1)
}

// SetUserSpeedUpStatus mocks base method.
func (m *MockIExpCache) SetUserSpeedUpStatus(arg0 uint32, arg1 int32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserSpeedUpStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserSpeedUpStatus indicates an expected call of SetUserSpeedUpStatus.
func (mr *MockIExpCacheMockRecorder) SetUserSpeedUpStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSpeedUpStatus", reflect.TypeOf((*MockIExpCache)(nil).SetUserSpeedUpStatus), arg0, arg1)
}

// UserOnlineDataExpire mocks base method.
func (m *MockIExpCache) UserOnlineDataExpire(arg0 *cache.UserOnlineData, arg1 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserOnlineDataExpire", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UserOnlineDataExpire indicates an expected call of UserOnlineDataExpire.
func (mr *MockIExpCacheMockRecorder) UserOnlineDataExpire(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserOnlineDataExpire", reflect.TypeOf((*MockIExpCache)(nil).UserOnlineDataExpire), arg0, arg1)
}

// UserOnlineTimeIncr mocks base method.
func (m *MockIExpCache) UserOnlineTimeIncr(arg0 *cache.UserOnlineData, arg1 int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserOnlineTimeIncr", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UserOnlineTimeIncr indicates an expected call of UserOnlineTimeIncr.
func (mr *MockIExpCacheMockRecorder) UserOnlineTimeIncr(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserOnlineTimeIncr", reflect.TypeOf((*MockIExpCache)(nil).UserOnlineTimeIncr), arg0, arg1)
}
