package cache

import (
	"context"
	"errors"
	"fmt"
	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/channel-live-mgr/conf"
	"strconv"
	"strings"
	"time"
)

var (
	ErrSecretHadUsed = errors.New("secret had used")
)

func getVirtualAnchorPerKey(uid uint32) string {
	return fmt.Sprintf("virtual_anchor_per_%d", uid)
}

func getVirtualChannelSecretKey(uid uint32, channel_id uint32) string {
	return fmt.Sprintf("virtual_live_chn_secret_%d_%d", uid, channel_id)
}
func getVirtualLiveSecretStartKey(uid, cid uint32) string {
	return fmt.Sprintf("virtual_live_secret_start_%d_%d", uid, cid)
}
func getVirtualLiveInfoKey(secret interface{}) string {
	return fmt.Sprintf("virtual_live_secret_info_%v", secret)
}
func getVirtualLiveSecretRefreshKey(uid, cid uint32, t time.Time) string {
	return fmt.Sprintf("virtual_live_secret_fresh_%d_%d_%s", uid, cid, t.Format("20060102"))
}

func (c *ChannelLiveMgrCache) SetVirtualAnchorPer(uid, perVal, expireTs uint32) error {
	key := getVirtualAnchorPerKey(uid)

	err := c.RedisClient.Set(key, perVal, time.Duration(expireTs)*time.Second).Err()
	if err != nil {
		return err
	}

	return nil
}

func (c *ChannelLiveMgrCache) GetVirtualAnchorPer(uid uint32) (bool, bool, error) {
	key := getVirtualAnchorPerKey(uid)

	perVal, err := c.RedisClient.Get(key).Uint64()
	if err != nil && err != redis.Nil {
		return false, false, err
	}

	if err == redis.Nil {
		return false, false, nil
	}

	return true, perVal == 1, nil
}

func (c *ChannelLiveMgrCache) DelVirtualAnchorPer(uid uint32) error {
	key := getVirtualAnchorPerKey(uid)

	err := c.RedisClient.Del(key).Err()
	return err
}

//获取虚拟主播房间密钥
func (c *ChannelLiveMgrCache) GetVirtualLiveChannelSecret(ctx context.Context, uid, cid uint32) (string, error) {
	key := getVirtualChannelSecretKey(uid, cid)
	val, err := c.RedisClient.Get(key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return "", nil
		}
		log.ErrorWithCtx(ctx, "GetVirtualLiveChannelSecret key:%s err:%v", key, err)
		return "", err
	}

	startKey := getVirtualLiveSecretStartKey(uid, cid)
	startVal, err := c.RedisClient.Get(startKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return "", nil
		}
		log.ErrorWithCtx(ctx, "GetVirtualLiveChannelSecret start key:%s err:%v", startKey, err)
		return "", err
	}

	startT, _ := strconv.Atoi(startVal)
	startTs := uint32(startT)
	nowTs := uint32(time.Now().Unix())
	if nowTs > startTs && nowTs-uint32(startTs) >= conf.GetVirtualLiveSecretExpire() { //过期(这里判断，主要是防止配置过期时间变小的情况)
		return "", nil
	}

	return val, nil
}

//根据房间密钥获取房间信息(uid, cid)
func (c *ChannelLiveMgrCache) GetVirtualLiveInfoBySecret(ctx context.Context, secret string) (uint32, uint32, error) {
	key := getVirtualLiveInfoKey(secret)
	val, err := c.RedisClient.Get(key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return 0, 0, nil
		}
		log.ErrorWithCtx(ctx, "GetVirtualLiveInfoBySecret key:%s err:%v", key, err)
		return 0, 0, err
	}
	arr := strings.Split(val, ",")
	uid, _ := strconv.Atoi(arr[0])
	cid, _ := strconv.Atoi(arr[1])
	// startT, _ := strconv.Atoi(arr[2])
	// startTs := uint32(startT)
	// nowTs := uint32(time.Now().Unix())
	// if nowTs > startTs && nowTs-startTs >= conf.GetVirtualLiveSecretExpire() { //过期
	// 	return 0, 0, nil
	// }
	return uint32(uid), uint32(cid), nil
}

func (c *ChannelLiveMgrCache) SetVirtualLiveChannelSecret(ctx context.Context, uid, cid uint32, secret uint64, isFresh bool) error {
	key := getVirtualChannelSecretKey(uid, cid)
	startKey := getVirtualLiveSecretStartKey(uid, cid)
	infoKey := getVirtualLiveInfoKey(secret)
	now := time.Now()
	startTs := uint32(now.Unix())
	info := fmt.Sprintf("%d,%d,%d", uid, cid, startTs)
	expireTime := time.Duration(conf.GetVirtualLiveSecretExpire()) * time.Second * 2

	todayFreshKey := getVirtualLiveSecretRefreshKey(uid, cid, now)
	//获取旧的secret
	oldSecret, err := c.RedisClient.Get(key).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		log.ErrorWithCtx(ctx, "SetVirtualLiveChannelSecret Get key:%s err:%v", key, err)
		return err
	}

	_, err = c.RedisClient.TxPipelined(func(pip redis.Pipeliner) error {
		// ok, e := pip.SetNX(infoKey, info, expireTime).Result()
		n, e := pip.Exists(infoKey).Result()
		if e != nil {
			return e
		}
		if n != 0 {
			return ErrSecretHadUsed
		}
		e = pip.Set(infoKey, info, expireTime).Err()
		if e != nil {
			return e
		}

		e = pip.Set(startKey, startTs, expireTime).Err()
		if e != nil {
			return e
		}
		if len(oldSecret) > 0 { //删除旧的secret
			oldInfoKey := getVirtualLiveInfoKey(oldSecret)
			_ = pip.Del(oldInfoKey)
		}
		if isFresh { //设置今日已刷新
			_ = pip.Set(todayFreshKey, secret, time.Hour*25)
		}
		e = pip.Set(key, secret, expireTime).Err()
		return e
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "SetVirtualLiveChannelSecret uid:%d, cid:%d, secret:%d err:%v", uid, cid, secret, err)
		return err
	}
	return nil
}

//检查今天是否刷新过房间密钥
func (c *ChannelLiveMgrCache) CheckVirtualLiveSecretTodayRefresh(ctx context.Context, uid, cid uint32) (bool, error) {
	now := time.Now()
	todayFreshKey := getVirtualLiveSecretRefreshKey(uid, cid, now)
	_, err := c.RedisClient.Get(todayFreshKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return false, nil
		}
		log.ErrorWithCtx(ctx, "CheckVirtualLiveSecretTodayRefresh key:%s err:%v", todayFreshKey, err)
		return false, err
	}
	return true, nil
}

func (c *ChannelLiveMgrCache) ClearVirtualLiveSecretRefreshLimit(ctx context.Context, uid, cid uint32) error {
	now := time.Now()
	todayFreshKey := getVirtualLiveSecretRefreshKey(uid, cid, now)
	_, err := c.RedisClient.Del(todayFreshKey).Result()
	log.InfoWithCtx(ctx, "CheckVirtualLiveSecretTodayRefresh key:%s err:%v", todayFreshKey, err)
	return err
}
