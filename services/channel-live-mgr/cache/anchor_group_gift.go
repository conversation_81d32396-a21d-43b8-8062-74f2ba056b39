package cache

import (
	"fmt"
	"golang.52tt.com/pkg/log"
	"strconv"
	"time"
)

const gift_value_key = "anchor_%d_group_%d_%s_value"

// 更新今日礼物值
func (c *ChannelLiveMgrCache) UpdateGiftValue(targetUid, sendUid, value uint32) error {
	key := fmt.Sprintf(gift_value_key, targetUid, sendUid, time.Now().Format("20060102"))
	result, err := c.RedisClient.IncrBy(key, int64(value)).Result()
	if result == int64(value) {
		c.RedisClient.Expire(key, 25*time.Hour)
	}
	return err
}

// 获取今日礼物值//
func (c *ChannelLiveMgrCache) BatchGetGiftValue(anchorUid uint32, uidList []uint32) ([]uint32, error) {
	valueList := make([]uint32, len(uidList))
	strList := make([]string, len(uidList))
	today := time.Now().Format("20060102")
	for i, uid := range uidList {
		key := fmt.Sprintf(gift_value_key, anchorUid, uid, today)
		strList[i] = key
	}

	if len(strList) == 0 {
		return valueList, nil
	}

	result, err := c.RedisClient.MGet(strList...).Result()
	if err != nil {
		return valueList, err
	}

	for i, value := range result {
		if value != nil {
			v, err := strconv.Atoi(value.(string))
			if err != nil {
				log.Errorf("Failed to Atoi err(%s) value(%s)", err.Error(), value.(string))
			}
			valueList[i] = uint32(v)
		}
	}

	return valueList, nil
}
