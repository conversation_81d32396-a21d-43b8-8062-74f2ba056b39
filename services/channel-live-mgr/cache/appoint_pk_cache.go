package cache

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channellivemgr"
)

// AnchorAppointPkBase 主播维度的 AppointId 的索引信息
// 完整的AppointPk信息存储在 pb.AppointPkInfo 中
type AnchorAppointPkBase struct {
	Uid       uint32 `json:"uid"` // 用户ID，可以是发起者 或者是 挑战者
	AppointId uint32 `json:"appoint_id"`
	BeginTs   uint32 `json:"begin_ts"`
	EndTs     uint32 `json:"end_ts"`
}

// getAnchorAppointPkBaseKey cache中记录 AnchorAppointPkBase 的key
func getAnchorAppointPkBaseKey(uid uint32) string {
	return fmt.Sprintf("anchor_appointpk_%d", uid)
}

// getAppointPkInfoKey cache中记录 完整 AppointPkInfo 的key
func getAppointPkInfoKey(appointId uint32) string {
	return fmt.Sprintf("appointpk_%d", appointId)
}

func getNeedProcAppointPkKey(index uint32) string {
	return fmt.Sprintf("np_appoint_pk_%d", index)
}

func getWaitingAppointPkKey(index uint32) string {
	return fmt.Sprintf("wait_appoint_pk_%d", index)
}

func getAnchorAppointPkPushKey(uid uint32) string {
	return fmt.Sprintf("appointpk_push_%d", uid)
}

func getWaitingAppointPkPushKey(index uint32) string {
	return fmt.Sprintf("waiting_pk_push_%d", index)
}

func getAcceptAppointPkFlagKey(myUid, otherUid, appointId uint32) string {
	return fmt.Sprintf("flag_%d_%d_%d", myUid, otherUid, appointId)
}

func getAppointPkDownTsKey(myUid, otherUid, appointId uint32) string {
	return fmt.Sprintf("down_%d_%d_%d", myUid, otherUid, appointId)
}

func getAppointPkPushKey(appointId uint32) string {
	return fmt.Sprintf("id_push_%d", appointId)
}

func (c *ChannelLiveMgrCache) GetAppointPkLock(key string, expireTs uint32) bool {
	success, err := c.RedisClient.SetNX(key, "1", time.Duration(expireTs)*time.Second).Result()
	if err != nil {
		log.Errorf("GetAppointPkLock RedisClient.SetNX failed key:%s err:%v", key, err)
		return false
	}

	return success
}

func (c *ChannelLiveMgrCache) ReleaseAppointPkLock(key string) error {
	err := c.RedisClient.Del(key).Err()
	if err != nil {
		return err
	}

	return nil
}

// SetAppointPkInfo 记录 AppointPkInfo 信息
func (c *ChannelLiveMgrCache) SetAppointPkInfo(info *pb.AppointPkInfo) error {

	nowTs := uint32(time.Now().Unix())
	var expireTs int64 = 24 * 3600
	if info.EndTs > nowTs {
		expireTs = int64(info.EndTs-nowTs) + 7*24*3600
	}

	key := getAppointPkInfoKey(info.GetAppointId())

	byteData, err := proto.Marshal(info)
	if err != nil {
		return err
	}

	err = c.RedisClient.Set(key, byteData, time.Duration(expireTs)*time.Second).Err()
	if err != nil {
		return err
	}

	return nil
}

func (c *ChannelLiveMgrCache) BatchGetAppointPkInfo(idList []uint32) (map[uint32]*pb.AppointPkInfo, error) {
	mapId2Info := make(map[uint32]*pb.AppointPkInfo, 0)

	if len(idList) == 0 {
		return mapId2Info, nil
	}

	keyList := make([]string, 0)
	for _, id := range idList {
		keyList = append(keyList, getAppointPkInfoKey(id))
	}

	items, err := c.RedisClient.MGet(keyList...).Result()
	if err != nil {
		return mapId2Info, err
	}

	for _, item := range items {
		if str, ok := item.(string); ok {
			value := []byte(str)
			if value != nil {
				info := &pb.AppointPkInfo{}
				err = proto.Unmarshal(value, info)
				if err != nil {
					continue
				}
				mapId2Info[info.AppointId] = info
			}
		}
	}

	return mapId2Info, nil
}

func (c *ChannelLiveMgrCache) CheckAppointPkInfoIsExist(appointId uint32) (bool, error) {
	key := getAppointPkInfoKey(appointId)

	intVal, err := c.RedisClient.Exists(key).Result()
	if err != nil {
		return true, err
	}

	return intVal == 1, nil
}

func (c *ChannelLiveMgrCache) DelAppointPkInfo(appointId uint32) error {
	key := getAppointPkInfoKey(appointId)

	err := c.RedisClient.Del(key).Err()
	if err != nil {
		return err
	}

	return nil
}

func (c *ChannelLiveMgrCache) AddNeedProcAppointPk(appointId, beginTs, index uint32) error {
	key := getNeedProcAppointPkKey(index)

	err := c.RedisClient.ZAdd(key, redis.Z{
		Score:  float64(beginTs),
		Member: appointId,
	}).Err()

	return err
}

func (c *ChannelLiveMgrCache) GetNeedProcAppointPkList(beginTs, endTs, index uint32) (map[uint32]uint32, error) {
	key := getNeedProcAppointPkKey(index)

	mapId2BeginTs := make(map[uint32]uint32, 0)

	results, err := c.RedisClient.ZRangeByScoreWithScores(key, redis.ZRangeBy{
		Min:    fmt.Sprintf("%d", beginTs),
		Max:    fmt.Sprintf("%d", endTs),
		Offset: 0,
		Count:  1024,
	}).Result()
	if err != nil {
		return mapId2BeginTs, err
	}

	for _, result := range results {
		memStr, ok := result.Member.(string)
		if !ok {
			log.Errorf("GetNeedProcAppointPkList interface to string err, info:%v", result)
			continue
		}

		id, err := strconv.ParseInt(memStr, 10, 32)
		if err != nil {
			log.Errorf("GetNeedProcAppointPkList strconv.ParseInt failed info:%v err:%v", result, err)
			continue
		}

		mapId2BeginTs[uint32(id)] = uint32(result.Score)
	}

	return mapId2BeginTs, nil
}

func (c *ChannelLiveMgrCache) DelNeedProcAppointPk(appointId, index uint32) error {
	key := getNeedProcAppointPkKey(index)

	err := c.RedisClient.ZRem(key, appointId).Err()

	return err
}

// SetAnchorAppointPkBase 记录主播维度的 AppointPk 基本信息（AppointPk的ID索引）
func (c *ChannelLiveMgrCache) SetAnchorAppointPkBase(info *AnchorAppointPkBase) error {
	key := getAnchorAppointPkBaseKey(info.Uid)

	member, err := json.Marshal(info)
	if err != nil {
		return err
	}

	err = c.RedisClient.ZAdd(key, redis.Z{
		Score:  float64(info.EndTs),
		Member: member,
	}).Err()

	return err
}

func (c *ChannelLiveMgrCache) GetAnchorAppointPkBaseList(uid, nowTs uint32) ([]*AnchorAppointPkBase, error) {
	key := getAnchorAppointPkBaseKey(uid)

	infoList := make([]*AnchorAppointPkBase, 0)

	// AppointPk的score 是 EndTs
	results, err := c.RedisClient.ZRangeByScore(key, redis.ZRangeBy{
		Min:    fmt.Sprintf("%d", nowTs),             // 结束时间 在当前时间之后的
		Max:    fmt.Sprintf("%d", nowTs+600*24*3600), // 结束时间 在60天之后
		Offset: 0,
		Count:  1024,
	}).Result()
	if err != nil {
		return infoList, err
	}

	for _, result := range results {
		info := &AnchorAppointPkBase{}
		err := json.Unmarshal([]byte(result), info)
		if err != nil {
			continue
		}

		infoList = append(infoList, info)
	}

	return infoList, nil
}

func (c *ChannelLiveMgrCache) GetAnchorAppointPkBaseListByTime(uids []uint32, startTs, finishTs uint32) ([]*AnchorAppointPkBase, error) {

	infoList := make([]*AnchorAppointPkBase, 0)

	// 使用Pipeline批量操作
	pipe := c.RedisClient.Pipeline()

	// AnchorAppointPkBase 的 score 是 AppointPk-EndTs
	for _, uid := range uids {
		key := getAnchorAppointPkBaseKey(uid)
		pipe.ZRangeByScore(key, redis.ZRangeBy{
			Min:    fmt.Sprintf("%d", startTs),
			Max:    fmt.Sprintf("%d", finishTs),
			Offset: 0,
			Count:  100,
		})
	}

	resCmd, errPip := pipe.Exec()
	if errPip != nil {
		log.Errorf("GetAnchorAppointPkBaseListByTime uid %v pipe.Exec fail %v", uids, errPip)
		return infoList, errPip
	}

	for _, cmdItem := range resCmd {
		cmdValue, cmdErr := cmdItem.(*redis.StringSliceCmd).Result()
		if cmdErr != nil {
			log.Errorf("GetAnchorAppointPkBaseListByTime uid %v ZRangeByScore fail %v",
				uids, cmdErr)
			continue
		}
		for _, result := range cmdValue {
			info := &AnchorAppointPkBase{}
			err := json.Unmarshal([]byte(result), info)
			if err != nil {
				log.Errorf("GetAnchorAppointPkBaseListByTime uid %v AnchorAppointPkBase Unmarshal fail %v",
					uids, err)
				continue
			}

			infoList = append(infoList, info)
		}

	}

	return infoList, nil
}

func (c *ChannelLiveMgrCache) DelAnchorAppointPkBaseByTime(uid, endTs uint32) error {
	key := getAnchorAppointPkBaseKey(uid)

	strMinScore := strconv.FormatUint(uint64(0), 10)
	strMaxScore := strconv.FormatUint(uint64(endTs), 10)

	_, err := c.RedisClient.ZRemRangeByScore(key, strMinScore, strMaxScore).Result()

	return err
}

func (c *ChannelLiveMgrCache) AddWaitingAppointPk(appointId, myUid, otherUid, endTs, index uint32) error {
	key := getWaitingAppointPkKey(index)

	member := fmt.Sprintf("%d_%d_%d", appointId, myUid, otherUid)
	err := c.RedisClient.ZAdd(key, redis.Z{
		Score:  float64(endTs),
		Member: member,
	}).Err()
	if err != nil {
		log.Errorf("AddWaitingAppointPk RedisClient.ZAdd failed %d %d %d %d %d err:%v", appointId, myUid, otherUid, endTs, index, err)
	}

	return err
}

func (c *ChannelLiveMgrCache) GetWaitingAppointPkList(beginTs, endTs, index uint32) ([]string, error) {
	key := getWaitingAppointPkKey(index)

	memberList := make([]string, 0)

	results, err := c.RedisClient.ZRangeByScore(key, redis.ZRangeBy{
		Min:    fmt.Sprintf("%d", beginTs),
		Max:    fmt.Sprintf("%d", endTs),
		Offset: 0,
		Count:  512,
	}).Result()
	if err != nil {
		return memberList, err
	}

	// memberList = append(memberList, results...)

	return results, nil
}

func (c *ChannelLiveMgrCache) DelWaitingAppointPk(appointId, myUid, otherUid, index uint32) (bool, error) {
	key := getWaitingAppointPkKey(index)

	member := fmt.Sprintf("%d_%d_%d", appointId, myUid, otherUid)

	intVal, err := c.RedisClient.ZRem(key, member).Result()
	if err != nil {
		log.Errorf("DelWaitingAppointPk failed key:%v mem:%v err:%v", key, member, err)
		return false, err
	}

	return intVal == 1, nil
}

func (c *ChannelLiveMgrCache) CheckWaitingAppointPkIsExist(appointId, myUid, otherUid, index uint32) (bool, error) {
	key := getWaitingAppointPkKey(index)
	member := fmt.Sprintf("%d_%d_%d", appointId, myUid, otherUid)

	_, err := c.RedisClient.ZRank(key, member).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return false, err
	}

	if errors.Is(err, redis.Nil) {
		return false, nil
	}

	return true, nil
}

func (c *ChannelLiveMgrCache) AddAnchorAppointPkPushEvent(info *pb.AppointPkEvent, expireTs uint32) error {
	key := getAnchorAppointPkPushKey(info.GetMyUid())

	byteData, err := proto.Marshal(info)
	if err != nil {
		return err
	}

	err = c.RedisClient.Set(key, byteData, time.Duration(expireTs)*time.Second).Err()
	if err != nil {
		return err
	}

	return nil
}

func (c *ChannelLiveMgrCache) GetAnchorAppointPkPushEvent(uid uint32) (*pb.AppointPkEvent, error) {
	key := getAnchorAppointPkPushKey(uid)

	pkEvent := &pb.AppointPkEvent{}

	strVal, err := c.RedisClient.Get(key).Result()
	if err != nil {
		return pkEvent, err
	}

	proto.Unmarshal([]byte(strVal), pkEvent)

	return pkEvent, nil
}

func (c *ChannelLiveMgrCache) DelAnchorAppointPkPushEvent(uid uint32) error {
	key := getAnchorAppointPkPushKey(uid)

	_, err := c.RedisClient.Del(key).Result()

	return err
}

func (c *ChannelLiveMgrCache) AddWaitingAppointPkPush(myUid, otherUid, endTs, index uint32) error {
	key := getWaitingAppointPkPushKey(index)

	member := fmt.Sprintf("%d_%d", myUid, otherUid)

	err := c.RedisClient.ZAdd(key, redis.Z{
		Score:  float64(endTs),
		Member: member,
	}).Err()

	return err
}

func (c *ChannelLiveMgrCache) GetWaitingAppointPkPushList(beginTs, endTs, index uint32) ([]string, error) {
	key := getWaitingAppointPkPushKey(index)

	memberList := make([]string, 0)

	results, err := c.RedisClient.ZRangeByScore(key, redis.ZRangeBy{
		Min:    fmt.Sprintf("%d", beginTs),
		Max:    fmt.Sprintf("%d", endTs),
		Offset: 0,
		Count:  512,
	}).Result()
	if err != nil {
		return memberList, err
	}

	//memberList = append(memberList, results...)

	return results, nil
}

func (c *ChannelLiveMgrCache) DelWaitingAppointPkPush(myUid, otherUid, index uint32) error {
	key := getWaitingAppointPkPushKey(index)

	member := fmt.Sprintf("%d_%d", myUid, otherUid)

	err := c.RedisClient.ZRem(key, member).Err()

	return err
}

func (c *ChannelLiveMgrCache) DelWaitingAppointPkPushByScore(minScore, maxScore, index uint32) error {
	key := getWaitingAppointPkPushKey(index)

	strMinScore := strconv.FormatUint(uint64(minScore), 10)
	strMaxScore := strconv.FormatUint(uint64(maxScore), 10)

	_, err := c.RedisClient.ZRemRangeByScore(key, strMinScore, strMaxScore).Result()

	return err
}

func (c *ChannelLiveMgrCache) IncrAcceptAppointPkFlag(myUid, otherUid, appointId uint32) (uint32, error) {
	key := getAcceptAppointPkFlagKey(myUid, otherUid, appointId)

	newVal, err := c.RedisClient.Incr(key).Result()

	return uint32(newVal), err
}

func (c *ChannelLiveMgrCache) DelAcceptAppointPkFlag(myUid, otherUid, appointId uint32) error {
	key := getAcceptAppointPkFlagKey(myUid, otherUid, appointId)

	err := c.RedisClient.Del(key).Err()

	return err
}

func (c *ChannelLiveMgrCache) AddAppointPkDownEndTs(myUid, otherUid, appointId, downEndTs, expireTs uint32) error {
	key := getAppointPkDownTsKey(myUid, otherUid, appointId)

	err := c.RedisClient.Set(key, downEndTs, time.Duration(expireTs)*time.Second).Err()
	if err != nil {
		return err
	}

	return nil
}

func (c *ChannelLiveMgrCache) GetAppointPkDownEndTs(myUid, otherUid, appointId uint32) (uint32, error) {
	key := getAppointPkDownTsKey(myUid, otherUid, appointId)

	strVal, err := c.RedisClient.Get(key).Result()
	if err != nil {
		return 0, err
	}

	downTs, _ := strconv.Atoi(strVal)

	return uint32(downTs), nil
}

func (c *ChannelLiveMgrCache) IncrAppointPkFlag(appointId uint32) (uint32, error) {
	key := getAppointPkPushKey(appointId)

	newVal, err := c.RedisClient.Incr(key).Result()

	return uint32(newVal), err
}
