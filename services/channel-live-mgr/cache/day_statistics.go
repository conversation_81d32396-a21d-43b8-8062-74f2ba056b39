package cache

import (
	"errors"
	"fmt"
	"strconv"
	"time"
)

const day_value_key = "anchor_%d_day_%s_%s"
const Audience_type = "audience"
const Pay_type = "pay"

// 每日机器人观众数统计
const Robot_audience_day_value_key = "robot_anchor_%d_day_%s"

func (c *ChannelLiveMgrCache) AddDayStatPF(targetUid, sendUid uint32, keyType string) (int64, error) {
	if keyType != Audience_type && keyType != Pay_type {
		return 0, errors.New("error key type")
	}

	key := fmt.Sprintf(day_value_key, targetUid, time.Now().Format("20060102"), keyType)
	count, err := c.RedisClient.PFAdd(key, sendUid).Result()
	if err == nil || count <= 1 {
		// 40天过期
		c.RedisClient.Expire(key, time.Hour*24*40)
	}

	return count, err
}

func (c *ChannelLiveMgrCache) CountDayStatPF(targetUid uint32, date string, keyType string) (int64, error) {
	if keyType != Audience_type && keyType != Pay_type {
		return 0, errors.New("error key type")
	}

	robotCnt := 0
	if keyType == Audience_type {
		key := fmt.Sprintf(Robot_audience_day_value_key, targetUid, date)
		str, err := c.RedisClient.Get(key).Result()
		if err == nil {
			robotCnt, _ = strconv.Atoi(str)
		}
	}

	key := fmt.Sprintf(day_value_key, targetUid, date, keyType)
	audience, err := c.RedisClient.PFCount(key).Result()
	audience += int64(robotCnt)
	return audience, err
}

func (c *ChannelLiveMgrCache) SetDayRobotAudienceCnt(uid, cnt uint32) error {
	key := fmt.Sprintf(Robot_audience_day_value_key, uid, time.Now().Format("20060102"))
	count, err := c.RedisClient.IncrBy(key, int64(cnt)).Result()
	if err == nil || count <= int64(cnt) {
		// 40天过期
		c.RedisClient.Expire(key, time.Hour*24*40)
	}
	return err
}
