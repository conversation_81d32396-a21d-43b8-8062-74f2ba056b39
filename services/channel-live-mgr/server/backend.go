package server

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"sort"
	"sync"
	"time"

	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/mapreduce"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channelpb "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/common/status"
	anchorcontractGoPb "golang.52tt.com/protocol/services/anchorcontract-go"
	apiCenterPb "golang.52tt.com/protocol/services/apicentergo"
	pb "golang.52tt.com/protocol/services/channellivemgr"
	channelMicPb "golang.52tt.com/protocol/services/channelmicsvr"
	channelsvrPb "golang.52tt.com/protocol/services/channelsvr"
	greenBabaPB "golang.52tt.com/protocol/services/greenBaba"
	"golang.52tt.com/services/channel-live-mgr/conf"
	"golang.52tt.com/services/channel-live-mgr/metrics"
	"golang.52tt.com/services/channel-live-mgr/mysql"
)

//直播后台相关接口

func LoopFunc(f func(), t time.Duration) {
	ticker := time.NewTicker(t)
	defer ticker.Stop()

	for range ticker.C {
		func() {
			defer func() {
				if r := recover(); r != nil {
					log.Errorf("LoopFunc r:%v", r)
				}
			}()

			log.Debugf("LoopFunc begin")

			f()
		}()
	}

	/*
		for {
			select {
			case <-time.After(t):
				func() {
					defer func() {
						if r := recover(); r != nil {
							log.Errorf("LoopFunc r:%v", r)
						}
					}()

					f()
				}()
			}
		}
	*/
}

// 取所有有权限的主播， 数据量太大，不建议使用，可以调用新的分页获取接口
func (s *ChannelLiveMgrServer) GetAllAnchor(ctx context.Context, in *pb.GetAllAnchorReq) (out *pb.GetAllAnchorResp, err error) {
	log.DebugWithCtx(ctx, "GetAllAnchor in:%v", in)

	ekey := "get_all_anchor"
	v, ok := easyCache.Get(ekey)
	if ok && v != nil {
		resp, ok := v.(*pb.GetAllAnchorResp)
		if ok && resp != nil {
			return resp, nil
		}
	}

	out = &pb.GetAllAnchorResp{}

	anchorList := make([]*pb.AnchorInfo, 0)
	allInfo, _, err := s.liveMgrCache.GetAllChannelLiveInfo()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllAnchor GetAllChannelLiveInfo failed  err:%v", err)
	}

	if len(allInfo) == 0 {
		off, count := 0, 1024
		for i := 0; i < 1024; i++ {
			channelLiveinfoArr := make([]*pb.ChannelLiveInfo, 0)
			err := s.mysqlStore.GetAllChannelLiveInfo(ctx, &channelLiveinfoArr, off, count)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetAllAnchor GetAllChannelLiveInfo err:%v", err)
				break
			}
			if len(channelLiveinfoArr) == 0 {
				break
			}
			for _, ch := range channelLiveinfoArr {
				s.liveMgrCache.SetChannelLiveInfo(ch.Uid, ch)

				allInfo[ch.Uid] = ch

			}
			if len(channelLiveinfoArr) < count {
				break
			}
			off = off + count
		}
	}

	ChannelIDList := make([]uint32, 0)

	for _, item := range allInfo {
		if item.ChannelId == 0 || item.TagId != 0 {
			continue
		}
		ChannelIDList = append(ChannelIDList, item.ChannelId)
	}

	mapCid2Tag, err := s.aclExtLogicApi.GetChannelTagList(ctx, 1024, ChannelIDList)

	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllAnchor BatchGetChannelTag err:%v", err)
	}

	for _, item := range allInfo {
		tagID := item.TagId
		if tmpTagID, ok := mapCid2Tag[item.ChannelId]; ok {
			item.TagId = tmpTagID
			s.liveMgrCache.SetChannelLiveInfo(item.Uid, item)
		}

		anchorList = append(anchorList, &pb.AnchorInfo{
			Uid:       item.Uid,
			ChannelId: item.ChannelId,
			OperUser:  item.OperName,
			OperTime:  item.CreateTime,
			TagId:     tagID,
			Authority: item.Authority,
		})
	}

	out.AnchorList = anchorList

	easyCache.Set(ekey, out, 180)

	return out, nil
}

func (s *ChannelLiveMgrServer) GetAnchorList(ctx context.Context, in *pb.GetAnchorListReq) (*pb.GetAnchorListResp, error) {
	out := &pb.GetAnchorListResp{}

	log.DebugWithCtx(ctx, "GetAnchorList begin in:%v")

	infoList := make([]*pb.ChannelLiveInfo, 0)
	err := s.mysqlStore.GetValidAnchorList(ctx, &infoList, int((in.GetPage()-1)*in.GetPageSize()), int(in.GetPageSize()), in.GetUidList(), in.GetBeginTs(), in.GetEndTs())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorList GetAllChannelLiveInfo failed in:%v err:%v", in, err)
		return out, err
	}

	totalCnt, err := s.mysqlStore.GetValidAnchorTotalCnt(ctx, in.GetUidList(), in.GetBeginTs(), in.GetEndTs())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorList GetAnchorTotalCnt failed in:%v err:%v", in, err)
		return out, err
	}

	anchorUidList := make([]uint32, 0)
	tmpInfoList := make([]*pb.ChannelLiveInfo, 0)
	mapCid2TagId := make(map[uint32]uint32, 0)

	for index, info := range infoList {
		anchorUidList = append(anchorUidList, info.GetUid())
		tmpInfoList = append(tmpInfoList, info)
		if len(anchorUidList) == 500 || index == len(infoList)-1 {
			// BatchGetChannelTag耗时长，减少查询数量, 查缓存
			mapUid2LiveInfo, err := s.liveMgrCache.GetAnchorByUidList(anchorUidList...)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetAnchorList GetAnchorByUidList failed in:%v err:%v", in, err)
			}

			tmpChannelIdList := make([]uint32, 0)
			for _, tmpInfo := range tmpInfoList {
				if liveInfo, ok := mapUid2LiveInfo[tmpInfo.GetUid()]; ok {
					if liveInfo.GetTagId() != 0 {
						mapCid2TagId[liveInfo.GetChannelId()] = liveInfo.GetTagId()
						continue
					}
				}
				tmpChannelIdList = append(tmpChannelIdList, tmpInfo.GetChannelId())
			}

			if len(tmpChannelIdList) != 0 {
				mapTmpCid2TagID, err := s.aclExtLogicApi.GetChannelTagList(ctx, 0, tmpChannelIdList)
				if err != nil {
					log.ErrorWithCtx(ctx, "GetAnchorList BatchGetChannelTag in:%v err:%v", in, err)
				} else {
					for cid_, tagID_ := range mapTmpCid2TagID {
						mapCid2TagId[cid_] = tagID_
					}
				}
			}

			anchorUidList = anchorUidList[0:0]
			tmpInfoList = tmpInfoList[0:0]
		}
	}

	for _, info := range infoList {
		var tagId uint32 = 0
		if id, ok := mapCid2TagId[info.GetChannelId()]; ok {
			tagId = id
		}

		out.AnchorList = append(out.AnchorList, &pb.AnchorInfo{
			Uid:       info.GetUid(),
			ChannelId: info.GetChannelId(),
			OperUser:  info.GetOperName(),
			OperTime:  info.GetCreateTime(),
			TagId:     tagId,
			Authority: info.GetAuthority(),
		})
	}

	out.TotalCnt = totalCnt
	out.NextPage = in.GetPage() + 1
	if len(infoList) < int(in.GetPageSize()) {
		out.NextPage = 0
	}

	log.DebugWithCtx(ctx, "GetAnchorList end in:%v out:%v", in, out)
	return out, nil
}

func (s *ChannelLiveMgrServer) BatchGetChannelLiveTotalData(ctx context.Context, in *pb.BatchGetChannelLiveTotalDataReq) (*pb.BatchGetChannelLiveTotalDataResp, error) {
	out := &pb.BatchGetChannelLiveTotalDataResp{}

	log.DebugWithCtx(ctx, "BatchGetChannelLiveTotalData in:%v", in)

	mapRecords, err := s.mysqlStore.GetChannelLiveRecordsByTime(ctx, in.UidList, in.BeginTime, in.EndTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to get GetChannelLiveRecordsByTime err(%s) in(%+v)", err.Error(), in)
	}

	mapTotalRc, err := s.mysqlStore.BatchGetAnchorTotalData(ctx, in.UidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to get BatchGetAnchorTotalData err(%s) in(%+v)", err.Error(), in)
	}

	for uid, records := range mapRecords {
		dataList := make([]*pb.LiveDayData, 0)
		mapRecData := make(map[string]*pb.LiveDayData)
		for _, rc := range records {
			currDay := time.Unix(int64(rc.BeginTime), 0).Format("20060102")
			if _, ok := mapRecData[currDay]; !ok {
				mapRecData[currDay] = &pb.LiveDayData{
					Date: currDay,
				}
			}
			mapRecData[currDay].BeginTime = rc.BeginTime
			mapRecData[currDay].GiftValue = mapRecData[currDay].GiftValue + rc.Score
			mapRecData[currDay].SendGiftUserCnt = mapRecData[currDay].SendGiftUserCnt + rc.SendGiftUserCnt
			mapRecData[currDay].LiveTime = mapRecData[currDay].LiveTime + (rc.EndTime - rc.BeginTime)
			mapRecData[currDay].AudienceCnt = mapRecData[currDay].AudienceCnt + rc.Audiences
			mapRecData[currDay].AnchorGiftValue = mapRecData[currDay].AnchorGiftValue + rc.AnchorGiftValue
			mapRecData[currDay].AddFans = mapRecData[currDay].AddFans + rc.AddFans
			mapRecData[currDay].AddGroupFans = mapRecData[currDay].AddGroupFans + rc.AddGroupFans
		}
		for _, rc := range mapRecData {
			dataList = append(dataList, rc)
		}
		var totalScore uint64 = 0
		if totalInfo, ok := mapTotalRc[uid]; ok {
			totalScore = uint64(totalInfo.TotalScore)
		}
		out.DataList = append(out.DataList, &pb.LiveTotalDayData{
			Uid:            uid,
			DayDateList:    dataList,
			TotalGiftValue: totalScore,
		})
	}
	log.DebugWithCtx(ctx, "BatchGetChannelLiveTotalData req(%+v) resp(%+v)", in, out)

	return out, nil
}

// 开播记录
func (s *ChannelLiveMgrServer) GetChannelLiveHistoryRecord(ctx context.Context, in *pb.GetChannelLiveHistoryRecordReq) (out *pb.GetChannelLiveHistoryRecordResp, err error) {
	out = &pb.GetChannelLiveHistoryRecordResp{}

	log.DebugWithCtx(ctx, "GetChannelLiveHistoryRecord in:%v", in)

	mapRecord, err := s.mysqlStore.GetChannelLiveRecordsByTime(ctx, []uint32{in.Uid}, in.BeginTime, in.EndTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveHistoryRecord err:%v", err)
		return out, err
	}

	records, ok := mapRecord[in.Uid]
	if !ok {
		return out, err
	}

	for _, rc := range records {
		record := &pb.ChannelLiveHistoryRecord{
			Uid:       rc.Uid,
			ChannelId: rc.ChannelId,
			BeginTime: rc.BeginTime,
			EndTime:   rc.EndTime - rc.BeginTime,
		}
		out.RecordList = append(out.RecordList, record)
	}
	log.DebugWithCtx(ctx, "GetChannelLiveHistoryRecord out:%v", out)

	return out, nil
}

// pk记录（废弃该接口）
func (s *ChannelLiveMgrServer) GetChannelLivePKRecord(ctx context.Context, in *pb.GetChannelLivePKRecordReq) (out *pb.GetChannelLivePKRecordResp, err error) {
	out = &pb.GetChannelLivePKRecordResp{
		RecordList: make([]*pb.ChannelLivePKRecord, 0),
	}

	// 废弃该接口,报错
	return out, protocol.NewExactServerError(nil,
		status.ErrCommApiScrap, "接口已经废弃")

	/*
	   log.DebugWithCtx(ctx, "GetChannelLivePKRecordS in:%v", in)

	   pkRecord, err := s.mysqlStore.GetChannelLivePkRecoreds(ctx, in.Uid)

	   	if err != nil {
	   		log.ErrorWithCtx(ctx, "GetChannelLivePKRecord GetChannelLivePkRecoreds failed in:%v err:%v", in, err)
	   	}

	   	for _, record := range pkRecord {
	   		record := &pb.ChannelLivePKRecord{
	   			Uid:             record.Uid,
	   			Account:         record.Account,
	   			Nick:            "",
	   			ChannelId:       record.ChannelId,
	   			TargetUid:       record.TargetUid,
	   			TargetAccount:   record.TargetAccount,
	   			TargetChannelId: record.TargetChannelId,
	   			CreateTime:      record.CreateTime,
	   		}
	   		out.RecordList = append(out.RecordList, record)
	   	}

	   log.DebugWithCtx(ctx, "GetChannelLivePKRecordS out:%v in:%v", out, in)

	   return out, nil
	*/
}

// 主播页接口
func (s *ChannelLiveMgrServer) GetChannelLiveTotalData(ctx context.Context, in *pb.GetChannelLiveTotalDataReq) (out *pb.GetChannelLiveTotalDataResp, err error) {
	out = &pb.GetChannelLiveTotalDataResp{
		TotalGiftValue: 0,
		DayDateList:    make([]*pb.LiveDayData, 0),
	}

	log.DebugWithCtx(ctx, "GetChannelLiveTotalData begin in:%v", in)

	mapRecord, err := s.mysqlStore.GetChannelLiveRecordsByTime(ctx, []uint32{in.Uid}, in.BeginTime, in.EndTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to get GetChannelLiveRecords err(%s) in(%+v)", err.Error(), in)
	}

	records, ok := mapRecord[in.Uid]
	log.DebugWithCtx(ctx, "GetChannelLiveTotalData in:%v mapRecord:%v ok:%v", in, records, ok)
	if !ok {
		return out, err
	}

	mapAnchor, err := s.mysqlStore.BatchGetAnchorTotalData(ctx, []uint32{in.Uid})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveTotalData BatchGetAnchorTotalData in:%v err:%v", in, err)
	}

	log.DebugWithCtx(ctx, "GetChannelLiveTotalData in:%v mapAnchor:%v", in, mapAnchor)

	lastDay := ""
	dayRec := &pb.LiveDayData{}

	for _, rc := range records {
		currDay := time.Unix(int64(rc.BeginTime), 0).Format("20060102")
		if lastDay == currDay {
			//合并
			dayRec.Date = currDay
			dayRec.GiftValue = dayRec.GiftValue + rc.Score
			dayRec.SendGiftUserCnt = dayRec.SendGiftUserCnt + rc.SendGiftUserCnt
			dayRec.LiveTime = dayRec.LiveTime + (rc.EndTime - rc.BeginTime)
			dayRec.AudienceCnt = dayRec.AudienceCnt + rc.Audiences
			dayRec.AnchorGiftValue = dayRec.AnchorGiftValue + rc.AnchorGiftValue
			dayRec.AddFans = dayRec.AddFans + rc.AddFans
			dayRec.AddGroupFans = dayRec.AddGroupFans + rc.AddGroupFans

		} else {
			dayRec = &pb.LiveDayData{
				Date:            currDay,
				GiftValue:       rc.Score,
				SendGiftUserCnt: rc.SendGiftUserCnt,
				LiveTime:        rc.EndTime - rc.BeginTime,
				AudienceCnt:     rc.Audiences,
				AnchorGiftValue: rc.AnchorGiftValue,
				AddFans:         rc.AddFans,
				AddGroupFans:    rc.AddGroupFans,
			}

			out.DayDateList = append(out.DayDateList, dayRec)
		}
		lastDay = currDay
	}

	/* 直接以mysql为准，不读缓存了
		// 获取每日的计数，如果没有就优雅降级
		for i, record := range out.DayDateList {
	    	audienceCnt, _ := s.liveMgrCache.CountDayStatPF(in.Uid, record.Date, cache.Audience_type)
			if audienceCnt != 0 {
				out.DayDateList[i].AudienceCnt = uint32(audienceCnt)
			}
	    	sendGiftUserCnt, _ := s.liveMgrCache.CountDayStatPF(in.Uid, record.Date, cache.Pay_type)
			if sendGiftUserCnt != 0 {
				out.DayDateList[i].SendGiftUserCnt = uint32(sendGiftUserCnt)
			}
			log.DebugWithCtx(ctx,"CountDayStatPF uid(%d) audienceCnt(%d) sendGiftUserCnt(%d)", in.Uid, audienceCnt, sendGiftUserCnt)
		}
	*/

	if an, ok := mapAnchor[in.Uid]; ok {
		out.TotalGiftValue = uint64(an.TotalScore)
	}

	log.DebugWithCtx(ctx, "GetChannelLiveTotalData req(%+v) resp(%+v)", in, out)
	return out, nil
}

func (s *ChannelLiveMgrServer) BatchGetAnchorTotalData(ctx context.Context, req *pb.BatchGetAnchorTotalDataReq) (*pb.BatchGetAnchorTotalDataResp, error) {
	resp := &pb.BatchGetAnchorTotalDataResp{}

	if len(req.GetUidList()) == 0 {
		return resp, nil
	}

	mapRes, err := s.mysqlStore.BatchGetAnchorTotalData(ctx, req.GetUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAnchorTotalData fail. req:%+v, err:%v", req, err)
		return resp, err
	}

	resp.MapTotalData = make(map[uint32]*pb.AnchorTotalData)
	for uid, totalData := range mapRes {
		resp.MapTotalData[uid] = &pb.AnchorTotalData{
			Uid:           totalData.Uid,
			ChannelId:     totalData.ChannelId,
			TotalScore:    totalData.TotalScore,
			FirstLiveTime: totalData.FirstLiveTs,
		}
	}

	return resp, nil
}

func (s *ChannelLiveMgrServer) BatchGetChannelLiveRecord(ctx context.Context, in *pb.BatchGetChannelLiveRecordReq) (out *pb.BatchGetChannelLiveRecordResp, err error) {
	out = &pb.BatchGetChannelLiveRecordResp{
		RecordList: make([]*pb.ChannelLiveRecord, 0),
	}

	fBeginTs := time.Now()
	defer func() {
		subTs := time.Since(fBeginTs)
		if subTs >= 100*time.Millisecond {
			log.DebugWithCtx(ctx, "BatchGetChannelLiveRecord_warning in(+%v), subTs(+%v)", in, subTs)
		}
	}()

	log.DebugWithCtx(ctx, "BatchGetChannelLiveRecord in:%v", in)

	if in.BeginTime == 0 && in.EndTime == 0 {
		in.EndTime = uint32(time.Now().Unix())
	}

	mapRecord, _ := s.mysqlStore.GetChannelLiveRecordsByTime(ctx, in.UidList, in.BeginTime, in.EndTime)
	totalDatas, _ := s.mysqlStore.BatchGetAnchorTotalData(ctx, in.UidList)

	for anchorUid, records := range mapRecord {
		//TODO
		mapEachDayLiveTime := make(map[int]uint32)
		rec := pb.ChannelLiveRecord{Uid: anchorUid}
		var firsLiveTs uint32 = 0
		if an, ok := totalDatas[anchorUid]; ok {
			firsLiveTs = an.FirstLiveTs
		}
		rec.FirstLiveTime = int64(firsLiveTs)
		for _, re := range records {
			currDay := time.Unix(int64(re.BeginTime), 0).YearDay()
			mapEachDayLiveTime[currDay] = mapEachDayLiveTime[currDay] + (re.EndTime - re.BeginTime)
			rec.LiveTime = rec.LiveTime + (re.EndTime - re.BeginTime)
			rec.FirstLive = rec.FirstLive || (re.BeginTime == firsLiveTs)
		}

		var validDay uint32 = 0
		for _, v := range mapEachDayLiveTime {
			if v >= 3600 {
				validDay = validDay + 1
			}
		}
		rec.ValidDay = validDay
		out.RecordList = append(out.RecordList, &rec)
	}

	log.DebugWithCtx(ctx, "BatchGetChannelLiveRecord out:%v", out)

	return out, nil
}

func (s *ChannelLiveMgrServer) GetChannelLiveAnchorScoreLog(ctx context.Context, in *pb.GetChannelLiveAnchorScoreLogReq) (*pb.GetChannelLiveAnchorScoreLogResp, error) {
	out := &pb.GetChannelLiveAnchorScoreLogResp{}

	now := time.Now()
	thisMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	lastMonth := thisMonth.AddDate(0, -1, 0)

	logs, err := s.mysqlStore.GetAnchorScoreMonthLog(ctx, in.GetUid(), in.GetBegin(), in.GetLimit(), in.GetSourceList(), now)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveAnchorScoreLog fail to GetAnchorScoreMonthLog.uid:%d err:%v", in.GetUid(), err)
		return out, err
	}

	// 本月记录数不足，从上月取
	if now.After(time.Date(2021, 2, 1, 0, 0, 0, 0, time.Local)) &&
		uint32(len(logs)) < in.GetLimit() {
		cnt, err := s.mysqlStore.GetAnchorScoreMonthLogCnt(ctx, in.GetUid(), thisMonth)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelLiveAnchorScoreLog fail to GetAnchorScoreMonthLogCnt.uid:%d err:%v", in.GetUid(), err)
			return out, err
		}

		begin := in.GetBegin()
		if begin >= cnt {
			begin = begin - cnt
		} else {
			begin = 0
		}

		logs2, err := s.mysqlStore.GetAnchorScoreMonthLog(ctx, in.GetUid(), begin, in.GetLimit()-uint32(len(logs)), in.GetSourceList(), lastMonth)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelLiveAnchorScoreLog fail to GetAnchorScoreMonthLog.uid:%d err:%v", in.GetUid(), err)
			return out, err
		}

		logs = append(logs, logs2...)
	}

	for _, log := range logs {
		out.LogList = append(out.LogList, &pb.AnchorScoreLog{
			OrderId:    log.OrderId,
			SourceType: log.SourceType,
			AddScore:   log.AddScore,
			FinalScore: log.FinalScore,
			CreateTime: log.CreateTime,
		})
	}

	log.DebugWithCtx(ctx, "GetChannelLiveAnchorScoreLog in:%+v out:%+v", in, out)
	return out, nil
}

// 权限申请黑名单，如果在黑名单中则不能发起申请
func (s *ChannelLiveMgrServer) CheckIsAnchorInBackList(ctx context.Context, req *pb.CheckIsAnchorInBackListReq) (*pb.CheckIsAnchorInBackListResp, error) {
	resp := &pb.CheckIsAnchorInBackListResp{}
	res := s.liveMgrCache.CheckIsAnchorInBackList(req.Uid)
	resp.IsInBacklist = res

	log.DebugWithCtx(ctx, "CheckIsAnchorInBackList uid:%v res:%v", req.Uid, res)

	return resp, nil
}

func (s *ChannelLiveMgrServer) AddAnchorInBackList(ctx context.Context, req *pb.AddAnchorInBackListReq) (*pb.AddAnchorInBackListResp, error) {
	resp := &pb.AddAnchorInBackListResp{}
	s.liveMgrCache.AddAnchorInBackList(req.UidList)
	return resp, nil
}

func (s *ChannelLiveMgrServer) GetAnchorBackList(ctx context.Context, req *pb.GetAnchorBackListReq) (*pb.GetAnchorBackListResp, error) {
	resp := &pb.GetAnchorBackListResp{}
	uidList := s.liveMgrCache.GetAnchorBackList(int64(req.Off), int64(req.Count))
	resp.UidList = uidList
	return resp, nil
}

func (s *ChannelLiveMgrServer) DelAnchorBackList(ctx context.Context, req *pb.DelAnchorBackListReq) (*pb.DelAnchorBackListResp, error) {
	resp := &pb.DelAnchorBackListResp{}

	s.liveMgrCache.DelAnchorBackList(req.UidList)

	return resp, nil
}

// 排麦权限
func (s *ChannelLiveMgrServer) SetAuthFlag(ctx context.Context, req *pb.SetAuthFlagReq) (*pb.SetAuthFlagResp, error) {
	resp := &pb.SetAuthFlagResp{}

	log.DebugWithCtx(ctx, "SetAuthFlag in:%v", *req)

	if len(req.UidList) > 200 {
		log.ErrorWithCtx(ctx, "SetAuthFlag invalid sz:%v", len(req.UidList))
		return resp, errors.New("UID列表长度超出范围")
	}

	failUidList, _ := s.mgr.SetAuthFlag(req)
	resp.FailUidList = failUidList

	return resp, nil
}

func (s *ChannelLiveMgrServer) PushTest(ctx context.Context, req *pb.PushTestReq) (*pb.PushTestResp, error) {
	resp := &pb.PushTestResp{}

	toUser, err := s.aclExtLogicApi.GetUserInfo(ctx, req.ToUid)
	if nil != err {
		log.ErrorWithCtx(ctx, "PushTest GetUserInfo err:%v uid:%v", err, req.ToUid)
		return resp, err
	}

	fromUser, err := s.aclExtLogicApi.GetUserInfo(ctx, req.FromUid)
	if nil != err {
		log.ErrorWithCtx(ctx, "PushTest GetUserInfo err:%v uid:%v", err, req.FromUid)
		return resp, err
	}
	pErr := s.aclExtLogicApi.PushReliableChannelMsg(ctx,
		req.Cid, req.Cmd,
		fromUser, toUser,
		req.NormalData, req.PbData, req.Content)

	if nil != pErr {

		log.ErrorWithCtx(ctx,
			"PushTest PushReliableChannelMsg fail cid:%v cmd:%v err:%v",
			req.Cid, req.Cmd, pErr)
	}

	return resp, nil
}

func (s *ChannelLiveMgrServer) GetAnchorByUidList(ctx context.Context, req *pb.GetAnchorByUidListReq) (*pb.GetAnchorByUidListResp, error) {
	resp := &pb.GetAnchorByUidListResp{}

	if len(req.UidList) > 200 {
		return resp, errors.New("参数数量超出限制")
	}

	uid2Info, err := s.liveMgrCache.GetAnchorByUidList(req.UidList...)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetAnchorByUidList GetAllChannelLiveInfo err:%v", err)
	}

	userMap, err := s.aclExtLogicApi.GetUserInfoMap(ctx, req.UidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorByUidList GetUsersMap err:%v", err)
	}

	for _, i := range uid2Info {
		user, ok := userMap[i.Uid]
		if !ok {
			continue
		}
		resp.AnchorList = append(resp.AnchorList, &pb.AnchorInfo{
			Uid:       i.Uid,
			OperUser:  i.OperName,
			Account:   user.Username,
			Nickname:  user.Nickname,
			Authority: i.Authority,
			ChannelId: i.ChannelId,
			TagId:     i.TagId,
		})
	}

	log.InfoWithCtx(ctx, "GetAnchorByUidList AnchorList:%v", resp.AnchorList)

	return resp, nil
}

// 开启主播权限
func (s *ChannelLiveMgrServer) SetChannelLiveInfoForTest(ctx context.Context, in *pb.SetChannelLiveInfoForTestReq) (out *pb.SetChannelLiveInfoForTestResp, err error) {
	out = &pb.SetChannelLiveInfoForTestResp{}

	//暂时没有时间段需求，先给个大的时间
	in.ChannelLiveInfo.BeginTime = uint32(time.Now().Unix())
	in.ChannelLiveInfo.EndTime = **********

	channelID := in.ChannelLiveInfo.ChannelId
	if channelID == 0 {
		channelID, err = s.GenChannelLiveChannelID(ctx, in.ChannelLiveInfo.Uid)
		if err != nil || channelID == 0 {
			log.ErrorWithCtx(ctx, "SetChannelLiveInfo GenChannelLiveChannelID channelID==0 err:%v", err)
			return out, err
		}
	}
	out.ChannelId = channelID

	//清空密码
	var pwdFlag, recommen uint32 = 0, 0x2 //开启推荐位
	pwd := ""
	s.channelClient.ModifyChannel(ctx, in.ChannelLiveInfo.Uid, &channelsvrPb.ModifyChannelReq{
		ChannelId:        &channelID,
		OpUid:            &in.ChannelLiveInfo.Uid,
		PwdFlag:          &pwdFlag,
		Passwd:           &pwd,
		SwitchFlagBitmap: &recommen,
	})

	_, err = s.channelmicClient.SetChannelMicMode(ctx, in.ChannelLiveInfo.Uid, &channelMicPb.SetChannelMicModeReq{
		Uid:       in.ChannelLiveInfo.Uid,
		ChannelId: channelID,
		MicMode:   uint32(channelpb.EChannelMicMode_LIVE_MIC_SPACE_MODE),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo SetChannelMicMode in:%v err:%v", in, err)
		//return out, err
	}

	in.ChannelLiveInfo.ChannelId = channelID

	err = s.mysqlStore.SetChannelLiveInfo(ctx, in.GetChannelLiveInfo())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo DB  err:%v in:%v", err, in)
		return out, err
	}

	err = s.liveMgrCache.SetChannelLiveInfo(in.ChannelLiveInfo.Uid, in.GetChannelLiveInfo())
	log.DebugWithCtx(ctx, "SetChannelLiveInfo cache err(%s) uid(%d) channelLiveInfo(%+v)", err, in.ChannelLiveInfo.Uid, in.GetChannelLiveInfo())

	//清掉没有直播权限标志
	s.liveMgrCache.SetNoAuthFlag(0, AuthFlagKey(pb.EnumIDType_USER_ID, in.ChannelLiveInfo.Uid), AuthFlagKey(pb.EnumIDType_CHANNEL_ID, in.ChannelLiveInfo.ChannelId))

	log.DebugWithCtx(ctx, "SetChannelLiveInfoForTest PushToUsers uid:%v", in.ChannelLiveInfo.Uid)

	return out, nil
}

func (s *ChannelLiveMgrServer) checkAppointPkTs(ctx context.Context, info *pb.AppointPkInfo) (lastPK []*pb.AppointPkInfo, err error) {
	lastPK = make([]*pb.AppointPkInfo, 0)

	_, pkTs := conf.GetAppointPkTsConf()
	if pkTs == 0 {
		return lastPK, protocol.NewExactServerError(nil, -2, "指定pk每场pk时间为0")
	}
	nowTs := uint32(time.Now().Unix())

	log.DebugWithCtx(ctx, "checkAppointPkTs begin in:%v pkTs:%d nowTs:%d", info, pkTs, nowTs)

	infoList, err := s.mysqlStore.GetAnchorValidAppointPkList(ctx, info.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "checkAppointPkTs GetAnchorValidAppointPkList failed in:%v err:%v", info, err)
		return lastPK, err
	}

	for _, pkInfo := range infoList {
		if info.GetAppointId() != 0 && info.GetAppointId() == pkInfo.AppointId {
			continue
		}

		if (info.GetEndTs() >= pkInfo.EndTs && info.GetBeginTs() <= pkInfo.EndTs) ||
			(info.GetEndTs() <= pkInfo.EndTs && info.GetEndTs() >= pkInfo.BeginTs) {
			log.ErrorWithCtx(ctx, "checkAppointPkTs anchor ts appeated in:%v pkinfo:%v", info, pkInfo)
			lastPK = append(lastPK, &pb.AppointPkInfo{
				AppointId: pkInfo.AppointId,
				Uid:       pkInfo.Uid,
				BeginTs:   pkInfo.BeginTs,
				EndTs:     pkInfo.EndTs,
			})
		}
	}

	uidList := make([]uint32, 0)
	uidList = append(uidList, info.GetUid())
	for _, rivalInfo := range info.GetRivalList() {
		uidList = append(uidList, rivalInfo.GetUid())
	}

	rivalInfoList, err := s.mysqlStore.GetPkRivalDetailInfoListByUids(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkAppointPkTs GetPkRivalDetailInfoListByUids failed in:%v err:%v", info, err)
		return lastPK, err
	}

	mapUid2RivalInfo := make(map[uint32][]*mysql.PkRivalDetailInfo, 0)
	for _, rivalInfo := range rivalInfoList {
		mapUid2RivalInfo[rivalInfo.Uid] = append(mapUid2RivalInfo[rivalInfo.Uid], rivalInfo)
	}

	for _, uid := range uidList {
		if rivalInfoList, ok := mapUid2RivalInfo[uid]; ok {
			for _, rivalInfo := range rivalInfoList {
				if info.GetAppointId() != 0 && info.GetAppointId() == rivalInfo.AppointId {
					continue
				}

				if (rivalInfo.EndTs >= info.GetBeginTs() && rivalInfo.EndTs <= info.GetEndTs()) ||
					(info.GetEndTs() >= rivalInfo.BeginTs && info.GetEndTs() <= rivalInfo.EndTs) {
					log.ErrorWithCtx(ctx, "checkAppointPkTs anchor ts appeated2 in:%v pkinfo:%v", info, rivalInfo)
					lastPK = append(lastPK, &pb.AppointPkInfo{
						AppointId: rivalInfo.AppointId,
						Uid:       rivalInfo.Uid,
						BeginTs:   rivalInfo.BeginTs,
						EndTs:     rivalInfo.EndTs,
					})
				}
			}
		}
	}

	if len(lastPK) > 0 {
		log.ErrorWithCtx(ctx, "checkAppointPkTs anchor ts appeated in:%v pkinfo:%v", info, lastPK)
		return lastPK, protocol.NewExactServerError(nil, -2, "主播在该时间段已有指定pk赛事")
	}

	return lastPK, nil
}

func (s *ChannelLiveMgrServer) checkUpcomeingAppointPkTs(ctx context.Context, uids []uint32) (comingPkUser map[uint32]bool, err error) {
	comingPkUser = make(map[uint32]bool, 0)

	nowTs := uint32(time.Now().Unix())

	log.DebugWithCtx(ctx, "checkAppointPkTs begin in:%v nowTs:%d", uids, nowTs)

	infoList, err := s.mysqlStore.GetAnchorUpcomingAppointPkList(ctx, uids, nowTs, nowTs+600)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkAppointPkTs GetAnchorValidAppointPkList failed in:%v err:%v", uids, err)
		return comingPkUser, err
	}
	log.DebugWithCtx(ctx, "checkAppointPkTs begin in:%v infoList:%+v", uids, infoList)
	for _, pkInfo := range infoList {
		comingPkUser[pkInfo.Uid] = true
	}

	pkList, err := s.mysqlStore.GetAnchorUpcomingPKRivalAppointPkList(ctx, uids, nowTs, nowTs+600)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkAppointPkTs GetAnchorValidAppointPkList failed in:%v err:%v", uids, err)
		return comingPkUser, err
	}

	log.DebugWithCtx(ctx, "checkAppointPkTs PKRivalAppointPkList in:%v pkList:%+v", uids, pkList)
	for _, pkInfo := range pkList {
		comingPkUser[pkInfo.Uid] = true
	}
	log.DebugWithCtx(ctx, "checkAppointPkTs PKRivalAppointPkList in:%v comingPkUser:%+v", uids, comingPkUser)

	return comingPkUser, nil
}

func (s *ChannelLiveMgrServer) AddAppointPkInfo(ctx context.Context, in *pb.AddAppointPkInfoReq) (*pb.AddAppointPkInfoResp, error) {
	out := &pb.AddAppointPkInfoResp{}

	_, pkTs := conf.GetAppointPkTsConf()
	if pkTs == 0 {
		return out, error(protocol.NewExactServerError(nil, -2, "指定pk每场pk时间为0"))
	}

	nowTs := uint32(time.Now().Unix())

	log.DebugWithCtx(ctx, "SetAppointPkInfo begin in:%v pkTs:%d nowTs:%d", in, pkTs, nowTs)

	//  判断同一个对手是否在同时间内有多个对手。
	lastPK, err := s.checkAppointPkTs(ctx, in.GetInfo())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetAppointPkInfo checkAppointPkTs in:%v err:%v", in, err)
		out.ErrorList = lastPK
		return out, err
	}

	err = s.mysqlStore.Transaction(ctx, func(tx *sql.Tx) error {
		appointId, sqlErr := s.mysqlStore.AddAppointPkInfo(ctx, tx, in.GetInfo().GetUid(), in.GetInfo().GetBeginTs(), in.GetInfo().GetEndTs(), nowTs, in.GetInfo().GetOperator())
		if sqlErr != nil {
			log.ErrorWithCtx(ctx,
				"SetAppointPkInfo mysqlStore.SetAppointPkInfo failed in:%v err:%v",
				in, sqlErr)
			return sqlErr
		}

		for _, rivalInfo := range in.GetInfo().GetRivalList() {
			sqlErr = s.mysqlStore.AddAppointPkRivalInfo(ctx, tx, appointId, rivalInfo.GetUid(), rivalInfo.GetPkBeginTs(), nowTs)
			if sqlErr != nil {
				log.ErrorWithCtx(ctx, "SetAppointPkInfo mysqlStore.AddAppointPkRivalInfo failed in:%v err:%v", in, sqlErr)
				return sqlErr
			}
		}
		out.AppointId = appointId
		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetAppointPkInfo mysqlStore.Transaction failed in:%v err:%v", in, err)
		return out, err
	}

	log.DebugWithCtx(ctx, "SetAppointPkInfo end in:%v out:%v", in, out)
	return out, nil
}

func (s *ChannelLiveMgrServer) UpdateAppointPkInfo(ctx context.Context, in *pb.UpdateAppointPkInfoReq) (*pb.UpdateAppointPkInfoResp, error) {
	out := &pb.UpdateAppointPkInfoResp{}

	_, pkTs := conf.GetAppointPkTsConf()
	if pkTs == 0 {
		return out, protocol.NewExactServerError(nil, -2, "指定pk每场pk时间为0")
	}

	nowTs := uint32(time.Now().Unix())
	// 的指定pk，已经发了推送通知。需要重新发推送通知
	mapId2PkInfo, err := s.liveMgrCache.BatchGetAppointPkInfo([]uint32{in.GetInfo().GetAppointId()})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAppointPkInfo BatchGetAppointPkInfo failed in:%v err:%v", in, err)
		return out, err
	}

	pkInfo, err := s.mysqlStore.GetAppointPkInfoById(ctx, in.GetInfo().GetAppointId())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAppointPkInfo GetAppointPkInfoById failed in:%v err:%v", in, err)
		return out, err
	}

	if pkInfo.BeginTs <= (nowTs+60) && pkInfo.EndTs >= nowTs {
		log.ErrorWithCtx(ctx, "UpdateAppointPkInfo unable update in:%v", in)
		return out, error(protocol.NewExactServerError(nil, -2, "不能进行更新操作，离开始时间少于1分钟"))
	}

	//  判断同一个对手是否在同时间内有多个对手。
	_, err = s.checkAppointPkTs(ctx, in.GetInfo())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAppointPkInfo checkAppointPkTs in:%v err:%v", in, err)

		return out, err
		//log.DebugWithCtx(ctx, "UpdateAppointPkInfo begin in:%v pkTs:%d nowTs:%d", in, pkTs, nowTs)
	}

	err = s.mysqlStore.Transaction(ctx, func(tx *sql.Tx) error {
		sqlErr := s.mysqlStore.UpdateAppointPkInfo(ctx, tx, in.GetInfo().GetAppointId(), in.GetInfo().GetUid(), in.GetInfo().GetBeginTs(), in.GetInfo().GetEndTs(),
			nowTs, in.GetInfo().GetOperator())
		if sqlErr != nil {
			log.ErrorWithCtx(ctx, "UpdateAppointPkInfo mysqlStore.UpdateAppointPkInfo failed in:%v sqlErr:%v", in, sqlErr)
			return sqlErr
		}

		sqlErr = s.mysqlStore.DelAppointPkRivalInfo(ctx, tx, in.GetInfo().GetAppointId())
		if sqlErr != nil {
			log.ErrorWithCtx(ctx, "UpdateAppointPkInfo mysqlStore.DelAppointPkRivalInfo failed in:%v sqlErr:%v", in, sqlErr)
			return sqlErr
		}

		for _, rivalInfo := range in.GetInfo().GetRivalList() {
			sqlErr = s.mysqlStore.AddAppointPkRivalInfo(ctx, tx, in.GetInfo().GetAppointId(), rivalInfo.GetUid(), rivalInfo.GetPkBeginTs(), nowTs)
			if sqlErr != nil {
				log.ErrorWithCtx(ctx, "UpdateAppointPkInfo mysqlStore.AddAppointPkRivalInfo failed in:%v err:%v", in, sqlErr)
				return sqlErr
			}
		}

		cacheErr := s.liveMgrCache.DelAppointPkInfo(in.GetInfo().GetAppointId())
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateAppointPkInfo DelAppointPkInfo failed in:%v err:%v", in, cacheErr)
			return cacheErr
		}
		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAppointPkInfo mysqlStore.Transaction in:%v err:%v", in, err)
		return out, err
	}

	if pkInfo, ok := mapId2PkInfo[in.GetInfo().GetAppointId()]; ok {
		mapUid2NewPkRival := make(map[uint32]*metrics.AppointPkRivalInfo, 0)
		newPkRivalList := make([]*metrics.AppointPkRivalInfo, 0)
		for _, rivalInfo := range in.GetInfo().GetRivalList() {
			tmpRivalInfo := &metrics.AppointPkRivalInfo{
				Uid:     rivalInfo.GetUid(),
				BeginTs: rivalInfo.GetPkBeginTs(),
			}
			mapUid2NewPkRival[rivalInfo.Uid] = tmpRivalInfo
			newPkRivalList = append(newPkRivalList, tmpRivalInfo)
		}

		mapUid2OldPkRival := make(map[uint32]*metrics.AppointPkRivalInfo, 0)
		oldPkRivalList := make([]*metrics.AppointPkRivalInfo, 0)
		for _, rivalInfo := range pkInfo.GetRivalList() {
			tmpRivalInfo := &metrics.AppointPkRivalInfo{
				Uid:     rivalInfo.GetUid(),
				BeginTs: rivalInfo.GetPkBeginTs(),
			}
			mapUid2OldPkRival[rivalInfo.Uid] = tmpRivalInfo
			oldPkRivalList = append(oldPkRivalList, tmpRivalInfo)
		}

		mapType2RivalList := make(map[uint32][]*metrics.AppointPkRivalInfo, 0)
		for uid, newPkInfo := range mapUid2NewPkRival {
			if oldPkInfo, ok := mapUid2OldPkRival[uid]; ok {
				if newPkInfo.BeginTs != oldPkInfo.BeginTs {
					mapType2RivalList[0] = append(mapType2RivalList[0], newPkInfo)
				}
			}
		}

		for uid, oldPkInfo := range mapUid2OldPkRival {
			if _, ok := mapUid2NewPkRival[uid]; !ok {
				mapType2RivalList[1] = append(mapType2RivalList[1], oldPkInfo)
			}
		}

		sort.Slice(newPkRivalList, func(i, j int) bool { return newPkRivalList[i].BeginTs <= newPkRivalList[j].BeginTs })
		sort.Slice(oldPkRivalList, func(i, j int) bool { return oldPkRivalList[i].BeginTs <= oldPkRivalList[j].BeginTs })

		mapUid2NewRivalMsg, err := s.aclExtLogicApi.AssemblePkRivalMsg(ctx, in.GetInfo().GetUid(), newPkRivalList)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateAppointPkInfo getAnchorPushRivalMsg failed in:%v, err:%v", in.GetInfo(), err)
			return out, err
		}

		mapUid2OldRivalMsg, err := s.aclExtLogicApi.AssemblePkRivalMsg(ctx, pkInfo.GetUid(), oldPkRivalList)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateAppointPkInfo getAnchorPushRivalMsg failed in:%v, err:%v", pkInfo, err)
			return out, err
		}

		for _, rival := range mapType2RivalList[0] {
			if oldMsg, ok := mapUid2OldRivalMsg[rival.Uid]; ok {
				log.DebugWithCtx(ctx, "DelAppointPkInfo rival:%v msg:%s", rival, oldMsg)
				tm := time.Unix(int64(pkInfo.BeginTs), 0)
				newTm := time.Unix(int64(in.GetInfo().GetBeginTs()), 0)
				pkTm := time.Unix(int64(rival.BeginTs), 0)
				pushMsg := fmt.Sprintf("【重要赛事通知】你在%02d月%02d日%s的PK赛事时间已调整为%02d月%02d日%02d:%02d，详情可找官方人员咨询，点击跳转>",
					tm.Month(), tm.Day(), oldMsg, newTm.Month(), newTm.Day(), pkTm.Hour(), pkTm.Minute())
				s.aclExtLogicApi.PushAnchorFuWuHaoMsg(ctx, rival.Uid, pushMsg, true)
			}
		}

		for _, rival := range mapType2RivalList[1] {
			if oldMsg, ok := mapUid2OldRivalMsg[rival.Uid]; ok {
				log.DebugWithCtx(ctx, "DelAppointPkInfo rival:%v msg:%s", rival, oldMsg)
				tm := time.Unix(int64(pkInfo.BeginTs), 0)
				pushMsg := fmt.Sprintf("【重要赛事通知】你在%02d月%02d日%s的PK赛事已取消，详情可找官方人员咨询，点击跳转>", tm.Month(), tm.Day(), oldMsg)
				s.aclExtLogicApi.PushAnchorFuWuHaoMsg(ctx, rival.Uid, pushMsg, true)
			}
		}

		if oldMsg, oka := mapUid2OldRivalMsg[pkInfo.GetUid()]; oka {
			if newMsg, okb := mapUid2NewRivalMsg[pkInfo.GetUid()]; okb {
				tm := time.Unix(int64(in.GetInfo().GetBeginTs()), 0)
				newTm := time.Unix(int64(in.GetInfo().GetBeginTs()), 0)
				pushMsg := fmt.Sprintf("【重要赛事通知】你在%02d月%02d日%s的PK赛事时间已调整为%02d月%02d日%s，详情可找官方人员咨询，点击跳转>",
					tm.Month(), tm.Day(), oldMsg, newTm.Month(), newTm.Day(), newMsg)
				s.aclExtLogicApi.PushAnchorFuWuHaoMsg(ctx, pkInfo.GetUid(), pushMsg, true)
			}
		}
	}

	log.DebugWithCtx(ctx, "UpdateAppointPkInfo end in:%v out:%v", in, out)
	return out, nil
}

func (s *ChannelLiveMgrServer) DelAppointPkInfo(ctx context.Context, in *pb.DelAppointPkInfoReq) (*pb.DelAppointPkInfoResp, error) {
	out := &pb.DelAppointPkInfoResp{}

	// 取消的指定pk，已经发了推送通知。需要重新发推送通知
	mapId2PkInfo, err := s.liveMgrCache.BatchGetAppointPkInfo([]uint32{in.GetAppointId()})
	if err != nil {
		log.ErrorWithCtx(ctx, "DelAppointPkI BatchGetAppointPkInfo failed in:%v err:%v", in, err)
		return out, err
	}

	nowTs := uint32(time.Now().Unix())
	pkInfo, err := s.mysqlStore.GetAppointPkInfoById(ctx, in.GetAppointId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DelAppointPkInfo GetAppointPkInfoById failed in:%v err:%v", in, err)
		return out, err
	}

	if pkInfo.BeginTs <= (nowTs+60) && pkInfo.EndTs >= nowTs {
		log.ErrorWithCtx(ctx, "DelAppointPkInfo unable delete in:%v", in)
		return out, error(protocol.NewExactServerError(nil, -2, "不能进行删除操作，离开始时间少于1分钟"))
	}

	log.DebugWithCtx(ctx, "DelAppointPkInfo begin in:%v", in)
	err = s.mysqlStore.Transaction(ctx, func(tx *sql.Tx) error {
		sqlErr := s.mysqlStore.DelAppointPkInfo(ctx, tx, in.GetAppointId())
		if sqlErr != nil {
			log.ErrorWithCtx(ctx, "DelAppointPkInfo mysqlStore.DelAppointPkInfo failed in:%v sqlErr:%v", in, sqlErr)
			return sqlErr
		}

		sqlErr = s.mysqlStore.DelAppointPkRivalInfo(ctx, tx, in.GetAppointId())
		if sqlErr != nil {
			log.ErrorWithCtx(ctx, "DelAppointPkInfo mysqlStore.DelAppointPkRivalInfo failed in:%v sqlErr:%v", in, sqlErr)
			return sqlErr
		}

		cacheErr := s.liveMgrCache.DelAppointPkInfo(in.GetAppointId())
		if err != nil {
			log.ErrorWithCtx(ctx, "DelAppointPkInfo DelAppointPkInfo failed in:%v err:%v", in, cacheErr)
			return cacheErr
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "DelAppointPkInfo mysqlStore.Transaction failed in:%v sqlErr:%v", in, err)
		return out, err
	}

	if pkInfo, ok := mapId2PkInfo[in.GetAppointId()]; ok {
		if pkInfo.GetEndTs() >= nowTs {
			pkRivalList := make([]*metrics.AppointPkRivalInfo, 0)
			for _, rivalInfo := range pkInfo.GetRivalList() {
				pkRivalList = append(pkRivalList, &metrics.AppointPkRivalInfo{
					Uid:     rivalInfo.GetUid(),
					BeginTs: rivalInfo.GetPkBeginTs(),
				})
			}

			sort.Slice(pkRivalList, func(i, j int) bool { return pkRivalList[i].BeginTs <= pkRivalList[j].BeginTs })
			mapUid2RivalMsg, err := s.aclExtLogicApi.AssemblePkRivalMsg(ctx, pkInfo.Uid, pkRivalList)
			if err != nil {
				log.ErrorWithCtx(ctx, "DelAppointPkInfo getAnchorPushRivalMsg info:%v err:%v", pkInfo, err)
				return out, err
			}

			for uid, msg := range mapUid2RivalMsg {
				log.DebugWithCtx(ctx, "DelAppointPkInfo uid:%d msg:%s", uid, msg)
				tm := time.Unix(int64(pkInfo.BeginTs), 0)
				pushMsg := fmt.Sprintf("【重要赛事通知】你在%02d月%02d日%s的PK赛事已取消，详情可找官方人员咨询，点击跳转>", tm.Month(), tm.Day(), msg)
				s.aclExtLogicApi.PushAnchorFuWuHaoMsg(ctx, uid, pushMsg, true)
			}
		}
	}

	log.DebugWithCtx(ctx, "DelAppointPkInfo end in:%v out:%v", in, out)
	return out, nil
}

func (s *ChannelLiveMgrServer) GetAppointPkInfoList(ctx context.Context, in *pb.GetAppointPkInfoListReq) (*pb.GetAppointPkInfoListResp, error) {
	out := &pb.GetAppointPkInfoListResp{}

	log.DebugWithCtx(ctx, "GetAppointPkInfoList begin in:%v", in)
	pkInfoList, err := s.mysqlStore.GetAppointPkInfoList(ctx, in.GetUid(), in.GetBeginTs(), in.GetEndTs(), in.GetPage()*in.GetPageSize(),
		in.GetPageSize(), pb.GetAppointPkInfoListReq_QueryType(in.GetQueryType()))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAppointPkInfoList mysqlStore.GetAppointPkInfoList failed in:%v err:%v", in, err)
		return out, err
	}

	totalCnt, err := s.mysqlStore.GetAppointPkInfoTotalCnt(ctx, in.GetUid(), in.GetBeginTs(), in.GetEndTs(), pb.GetAppointPkInfoListReq_QueryType(in.GetQueryType()))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAppointPkInfoList mysqlStore.GetAppointPkInfoTotalCnt failed in:%v err:%v", in, err)
		return out, err
	}

	var count uint32 = 0
	idList := make([]uint32, 0)
	mapId2RivalList := make(map[uint32][]*metrics.AppointPkRivalInfo, 0)
	for index, pkInfo := range pkInfoList {
		idList = append(idList, pkInfo.AppointId)
		count++

		if count == 5 || index == len(pkInfoList)-1 {
			rivalInfoList, err := s.mysqlStore.GetAppointPkRivalListByIds(ctx, idList)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetAppointPkInfoList mysqlStore.GetAppointPkRivalListByIds failed in:%v list:%v err:%v", in, idList, err)
				return out, err
			}

			for _, rivalInfo := range rivalInfoList {
				mapId2RivalList[rivalInfo.AppointId] = append(mapId2RivalList[rivalInfo.AppointId], rivalInfo)
			}

			count = 0
			idList = idList[0:0]
		}
	}

	for _, pkInfo := range pkInfoList {
		rivalList := make([]*pb.PkRivalInfo, 0)
		if list, ok := mapId2RivalList[pkInfo.AppointId]; ok {
			for _, rival := range list {
				tmpInfo := &pb.PkRivalInfo{
					Uid:       rival.Uid,
					PkBeginTs: rival.BeginTs,
				}
				rivalList = append(rivalList, tmpInfo)
			}
		}

		tmpInfo := &pb.AppointPkInfo{
			AppointId: pkInfo.AppointId,
			Uid:       pkInfo.Uid,
			BeginTs:   pkInfo.BeginTs,
			EndTs:     pkInfo.EndTs,
			RivalList: rivalList,
			UpdateTs:  pkInfo.UpdateTs,
			Operator:  pkInfo.Operator,
		}

		out.InfoList = append(out.InfoList, tmpInfo)
	}

	out.NextPage = in.GetPage() + 1
	if uint32(len(pkInfoList)) < in.GetPageSize() {
		out.NextPage = 0
	}
	out.TotalCnt = totalCnt

	log.DebugWithCtx(ctx, "GetAppointPkInfoList end in:%v out:%v", in, out)
	return out, nil
}

func (s *ChannelLiveMgrServer) GetAnchorOperRecord(ctx context.Context, req *pb.GetAnchorOperRecordReq) (*pb.GetAnchorOperRecordResp, error) {
	resp := &pb.GetAnchorOperRecordResp{}

	recordList, err := s.mysqlStore.GetAnchorOperLogList(ctx, req.GetGuildId(), req.GetOperateType(), req.GetUidList(), (req.GetPage()-1)*req.GetPageSize(), req.GetPageSize())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorOperRecord GetAnchorOperLogList failed req:%+v err:%+v", req, err)
		return resp, err
	}

	totalCnt, err := s.mysqlStore.GetAnchorOperLogTotalCnt(ctx, req.GetGuildId(), req.GetOperateType(), req.GetUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorOperRecord GetAnchorOperLogTotalCnt failed req:%+v err:%+v", req, err)
		return resp, err
	}

	for _, record := range recordList {
		resp.RecordList = append(resp.RecordList, &pb.AnchorOperRecord{
			Ttid:        record.Tid,
			Uid:         record.Uid,
			Nickname:    record.NickName,
			GuildId:     record.GuildId,
			GuildName:   record.GuildName,
			TagId:       record.TagId,
			OperateType: record.OperType,
			OperateTs:   uint32(record.UpdateTime.Unix()),
			OperateName: record.OperUser,
		})
	}

	resp.TotalCnt = totalCnt
	resp.NextPage = req.GetPage() + 1
	if len(recordList) < int(req.GetPageSize()) {
		resp.NextPage = 0
	}

	log.DebugWithCtx(ctx, "GetAnchorOperRecord end req:%v resp:%v", req, resp)
	return resp, nil
}

func (s *ChannelLiveMgrServer) GetAnchorScoreList(ctx context.Context, req *pb.GetAnchorScoreListReq) (*pb.GetAnchorScoreListResp, error) {
	resp := &pb.GetAnchorScoreListResp{}

	beginTm := time.Unix(int64(req.GetBeginTs()), 0)
	endTm := time.Unix(int64(req.GetEndTs()), 0)

	if beginTm.Day() != 1 {
		log.ErrorWithCtx(ctx, "GetAnchorScoreList invalid req req:%v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	lastMonthTm := beginTm.AddDate(0, 0, -1)

	lastMonthScoreList, err := s.mysqlStore.GetAnchorMonthScore(ctx, req.GetUidList(), lastMonthTm)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorScoreList GetAnchorMonthScore failed req:%v err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	mapUid2LastMonthScore := make(map[uint32]uint32, 0)
	for _, lastMonthScore := range lastMonthScoreList {
		mapUid2LastMonthScore[lastMonthScore.Uid] = lastMonthScore.Score
	}

	for _, uid := range req.GetUidList() {
		orderList, err := s.mysqlStore.GetAnchorScoreOrder(ctx, uid, beginTm, endTm)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorScoreList GetAnchorScoreOrderList failed req:%v err:%v", req, err)
			return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
		}

		var newScore, exchangeMoneyScore, guildScore, exchangeMoneyFailReturnScore int32
		var officialGrantScore, officialReclaimScore, remainScore, remainExchangeMoney int32

		for _, order := range orderList {
			switch pb.AddChannelLiveAnchorScoreReq_SourceType(order.SourceType) {
			case pb.AddChannelLiveAnchorScoreReq_DayAnchorTimeMission, pb.AddChannelLiveAnchorScoreReq_WeekAnchorTimeMission,
				pb.AddChannelLiveAnchorScoreReq_DayAnchorIncomeMission, pb.AddChannelLiveAnchorScoreReq_WeekAnchorIncomeMission, pb.AddChannelLiveAnchorScoreReq_MonthAnchorIncomeMission:
				newScore += order.AddScore
			case pb.AddChannelLiveAnchorScoreReq_ScoreExchange:
				exchangeMoneyScore += -order.AddScore
			case pb.AddChannelLiveAnchorScoreReq_GuildChangePrivate, pb.AddChannelLiveAnchorScoreReq_GuildQuit, pb.AddChannelLiveAnchorScoreReq_GuildOfficalRecycle,
				pb.AddChannelLiveAnchorScoreReq_GuildExchange:
				guildScore += -order.AddScore
			case pb.AddChannelLiveAnchorScoreReq_ScoreExchangeReturn:
				exchangeMoneyFailReturnScore += order.AddScore
			case pb.AddChannelLiveAnchorScoreReq_OfficialReward:
				officialGrantScore += order.AddScore
			case pb.AddChannelLiveAnchorScoreReq_ReclaimScore:
				officialReclaimScore += -order.AddScore
			}
		}

		remainScore = int32(mapUid2LastMonthScore[uid]) + newScore - exchangeMoneyScore - guildScore - officialReclaimScore + exchangeMoneyFailReturnScore + officialGrantScore

		if remainScore >= 10000 {
			remainExchangeMoney = remainScore / 100
		}

		if req.GetIsHasExchage() && exchangeMoneyScore <= 0 && guildScore <= 0 {
			continue
		}

		if remainScore < 0 {
			log.ErrorWithCtx(ctx, "GetAnchorScoreList invalid score uid:%d req:%v remainScore:%d", uid, req, remainScore)
			continue
		}

		resp.List = append(resp.List, &pb.AnchorScore{
			Uid:                          uid,
			LastMonthRemainScore:         mapUid2LastMonthScore[uid],
			NewScore:                     uint32(newScore),
			TotalScore:                   mapUid2LastMonthScore[uid] + uint32(newScore),
			ExchangeMoneyScore:           uint32(exchangeMoneyScore),
			GuildScore:                   uint32(guildScore),
			ExchangeMoneyFailReturnScore: uint32(exchangeMoneyFailReturnScore),
			OfficialGrantScore:           uint32(officialGrantScore),
			OfficialReclaimScore:         uint32(officialReclaimScore),
			RemainScore:                  uint32(remainScore),
			RemainExchangeMoney:          uint32(remainExchangeMoney),
		})

	}

	log.DebugWithCtx(ctx, "GetAnchorScoreList end req:%v resp:%v", req, resp)
	return resp, nil
}

func (s *ChannelLiveMgrServer) GetAnchorMonthScoreList(ctx context.Context, req *pb.GetAnchorMonthScoreListReq) (*pb.GetAnchorMonthScoreListResp, error) {
	resp := &pb.GetAnchorMonthScoreListResp{}

	if req.GetPage() < 1 {
		log.ErrorWithCtx(ctx, "GetAnchorMonthScoreList invalid req req:%v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	tm := time.Unix(int64(req.GetTs()), 0)

	scoreList, err := s.mysqlStore.GetAnchorMonthScoreList(ctx, (req.GetPage()-1)*req.GetPageSize(), req.GetPageSize(), tm)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorMonthScoreList GetAnchorMonthScoreList failed req:%v err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	for _, list := range scoreList {
		resp.List = append(resp.List, &pb.AnchorMonthScore{
			Uid:   list.Uid,
			Score: list.Score,
		})
	}

	log.DebugWithCtx(ctx, "GetAnchorMonthScoreList end req:%v resp:%v", req, resp)
	return resp, nil
}

func (s *ChannelLiveMgrServer) GetAnchorScoreOrderList(ctx context.Context, req *pb.GetAnchorScoreOrderListReq) (*pb.GetAnchorScoreOrderListResp, error) {
	resp := &pb.GetAnchorScoreOrderListResp{}

	if req.GetPage() < 1 {
		log.ErrorWithCtx(ctx, "GetAnchorScoreOrderList invalid req req:%v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	beginTm := time.Unix(int64(req.GetBeginTs()), 0)
	endTm := time.Unix(int64(req.GetEndTs()), 0)

	orderList, err := s.mysqlStore.GetAnchorScoreOrderList(ctx, (req.GetPage()-1)*req.GetPageSize(), req.GetPageSize(), beginTm, endTm)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorScoreOrderList GetAnchorScoreOrderList failed req:%v err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	for _, order := range orderList {
		resp.List = append(resp.List, &pb.AnchorScoreLog{
			OrderId:    order.OrderId,
			AddScore:   order.AddScore,
			FinalScore: order.FinalScore,
			SourceType: order.SourceType,
			CreateTime: order.CreateTime,
			Uid:        order.Uid,
		})
	}

	log.DebugWithCtx(ctx, "GetAnchorScoreOrderList end req:%v resp:%v", req, resp)
	return resp, nil
}

// GetAnchorListMgr 获取主播直播权限列表
// apiCenter-go 迁移接口
func (s *ChannelLiveMgrServer) GetAnchorListMgr(ctx context.Context, req *apiCenterPb.GetAnchorListReq) (*apiCenterPb.GetAnchorListResp, error) {
	resp := &apiCenterPb.GetAnchorListResp{}

	uidList := req.GetUidList()
	var err error
	if len(req.GetTtidList()) != 0 {
		uidList, err = s.aclExtLogicApi.GetUidListByAlias(ctx, req.GetTtidList())

		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorList GetUidListByAlias failed req:%v err:%v", req, err)
			return resp, err
		}

		if len(uidList) == 0 {
			// 无效tid
			return resp, nil
		}
	}

	liveResp, err := s.GetAnchorList(ctx, &pb.GetAnchorListReq{
		Page:     req.GetPage(),
		PageSize: req.GetPageSize(),
		UidList:  uidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorList GetAnchorListV2 failed req:%v err:%v", req, err)
		return resp, err
	}

	cidList := make([]uint32, 0)
	resUidList := make([]uint32, 0)
	mapUid2LiveInfo := make(map[uint32]*pb.AnchorInfo, 0)
	for _, info := range liveResp.GetAnchorList() {
		cidList = append(cidList, info.GetChannelId())
		resUidList = append(resUidList, info.GetUid())
		mapUid2LiveInfo[info.GetUid()] = info
	}

	funcList := make([]func() error, 0)

	mapUid2User := make(map[uint32]*metrics.UserSimpleInfo, 0)
	funcList = append(funcList, func() error {
		var err error
		mapUid2User, err = s.aclExtLogicApi.GetUserInfoMap(ctx, resUidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorList GetUsersMap failed req:%v err:%v", req, err)
			return err
		}
		return nil
	})

	mapCid2ChInfo := make(map[uint32]*channelsvrPb.ChannelSimpleInfo, 0)
	funcList = append(funcList, func() error {
		var err error
		mapCid2ChInfo, err = s.batchGetChannelInfo(ctx, cidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorList batchGetChannelInfo failed req:%v err:%v", req, err)
			return err
		}
		return nil
	})

	mapCid2IsBanned := make(map[uint32]bool, 0)
	funcList = append(funcList, func() error {
		var err error
		mapCid2IsBanned, err = s.batchGetCurrBannedStat(ctx, cidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorList batchGetCurrBannedStat failed req:%v err:%v", req, err)
			return err
		}
		return nil
	})

	mapUid2Contract := make(map[uint32]*anchorcontractGoPb.ContractCacheInfo)
	guildIdList := make([]uint32, 0)
	funcList = append(funcList, func() error {
		var err error
		mapUid2Contract, guildIdList, err = s.batchGetUserContract(ctx, resUidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorList batchGetUserContract failed req:%v err:%v", req, err)
			return err
		}
		return nil
	})

	mapUid2IsGreat := make(map[uint32]bool, 0)
	funcList = append(funcList, func() error {
		var err error
		mapUid2IsGreat, err = s.batCheckIsGreatAnchor(ctx, resUidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorList batCheckIsGreatAnchor failed req:%v err:%v", req, err)
			return err
		}
		return nil
	})

	mapUid2FirstTs := make(map[uint32]uint32, 0)
	funcList = append(funcList, func() error {
		var err error
		mapUid2FirstTs, err = s.batGetAnchorFirstLiveTs(ctx, resUidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorList batGetAnchorFirstLiveTs failed req:%v err:%v", req, err)
			return err
		}
		return nil
	})

	mapUid2FirstSignTs := make(map[uint32]uint32, 0)
	mapUid2LatestSignTs := make(map[uint32]uint32, 0)
	funcList = append(funcList, func() error {
		var err error
		mapUid2LatestSignTs, mapUid2FirstSignTs, err = s.batGetUserApplySignRecord(ctx, resUidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorList batGetUserApplySignRecord failed req:%v err:%v", req, err)
			return err
		}
		return nil
	})

	err = mapreduce.Finish(funcList...)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorList mapreduce.Finish failed req:%v err:%v", req, err)
	}

	mapId2GuildInfo, err := s.aclExtLogicApi.GetGuildList(ctx, guildIdList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorList batGetGuildInfo failed req:%v err:%v", req, err)
		return resp, err
	}

	mapTagId2Name, _, err := s.aclExtLogicApi.GetAllConfTagID2NameMap(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorList batchGetAllChannelTagNameMap failed req:%v err:%v", req, err)
		return resp, err
	}

	for _, uid := range resUidList {
		if liveInfo, ok := mapUid2LiveInfo[uid]; ok {
			banStatus := uint32(apiCenterPb.BanStatusType_BanStatusNo)
			if mapCid2IsBanned[liveInfo.GetChannelId()] {
				banStatus = uint32(apiCenterPb.BanStatusType_BanStatusYes)
			}

			signIndentity := ""
			var signGuildId uint32
			signGuildName := ""
			if contract, ok := mapUid2Contract[uid]; ok {
				for _, identify := range contract.AnchorIdentityList {
					if identify == uint32(anchorcontractGoPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER) {
						if len(signIndentity) > 0 {
							signIndentity += "+"
						}
						signIndentity += "成员"
					} else if identify == uint32(anchorcontractGoPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
						if len(signIndentity) > 0 {
							signIndentity += "+"
						}
						signIndentity += "主播"
					} else if identify == uint32(anchorcontractGoPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS) {
						if len(signIndentity) > 0 {
							signIndentity += "+"
						}
						signIndentity += "大神"
					} else {
						log.DebugWithCtx(ctx, "GetAnchorList unknown sign identity:%d", identify)
					}
				}

				if guildInfo, ok := mapId2GuildInfo[contract.GetContract().GetGuildId()]; ok {
					signGuildId = guildInfo.GuildId
					signGuildName = guildInfo.GuildName
					if guildInfo.GuildShortId != 0 {
						signGuildId = guildInfo.GuildShortId
					}
				}
			}

			info := &apiCenterPb.AnchorInfo{
				Uid:           liveInfo.GetUid(),
				ChannelId:     liveInfo.GetChannelId(),
				Ttid:          mapUid2User[liveInfo.GetUid()].Alias,
				Nickname:      mapUid2User[liveInfo.GetUid()].Nickname,
				DisplayId:     mapCid2ChInfo[liveInfo.GetChannelId()].GetDisplayId(),
				BanStatus:     banStatus,
				SignIdentity:  signIndentity,
				SignGuildId:   signGuildId,
				SignGuildName: signGuildName,
				TagName:       mapTagId2Name[liveInfo.GetTagId()],
				IsGreat:       mapUid2IsGreat[liveInfo.GetUid()],
				FirstLiveTs:   mapUid2FirstTs[liveInfo.GetUid()],
				LatestSignTs:  mapUid2LatestSignTs[liveInfo.GetUid()],
				FirstSignTs:   mapUid2FirstSignTs[liveInfo.GetUid()],
				OperUser:      liveInfo.GetOperUser(),
				ChannelViewId: mapCid2ChInfo[liveInfo.GetChannelId()].GetChannelViewId(),
			}

			resp.AnchorList = append(resp.AnchorList, info)
		}
	}

	resp.TotalCnt = liveResp.GetTotalCnt()
	resp.NextPage = liveResp.GetNextPage()

	log.DebugWithCtx(ctx, "GetAnchorList end req:%v resp:%v", req, resp)
	return resp, nil
}

// BatDelChannelLiveInfoMgr 批量回收主播直播权限
// apiCenter-go 迁移接口
func (s *ChannelLiveMgrServer) BatDelChannelLiveInfoMgr(ctx context.Context, req *apiCenterPb.BatDelChannelLiveInfoReq) (*apiCenterPb.BatDelChannelLiveInfoResp, error) {
	resp := &apiCenterPb.BatDelChannelLiveInfoResp{}

	//if len(req.GetInfoList()) > 30 {
	//	log.ErrorWithCtx(ctx, "BatDelChannelLiveInfo bat del too much req:%v", req)
	//	return resp, protocol.NewExactServerError(nil,-2, "一次最多批量处理30个主播")
	//}

	if len(req.GetInfoList()) == 0 {
		log.ErrorWithCtx(ctx, "BatDelChannelLiveInfo list is empty req:%v", req)
		return resp, protocol.NewExactServerError(nil, -2, "主播数量不能为0")
	}

	ttidList := make([]string, 0)
	for _, info := range req.GetInfoList() {
		ttidList = append(ttidList, info.GetTtid())
	}

	mapTTid2Uid, err := s.aclExtLogicApi.GetUidMapByAlias(ctx, ttidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatDelChannelLiveInfo BatchQueryUidList failed in:%v err:%v", req, err)
		return resp, err
	}

	var dataLock sync.Mutex
	var wg sync.WaitGroup
	noValidTTidList := make([]string, 0)
	for _, info := range req.GetInfoList() {
		tmpTid := info.GetTtid()
		tmpReason := info.GetReason()
		tmpOperUser := info.GetOperUser()
		if mapTTid2Uid[tmpTid] != 0 {
			wg.Add(1)
			go func(tid, reason, operUser string) {
				defer wg.Done()

				_, err := s.DelChannelLiveInfo(ctx, &pb.DelChannelLiveInfoReq{
					Uid:      mapTTid2Uid[tid],
					Reason:   reason,
					OperUser: operUser,
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "BatDelChannelLiveInfo DelChannelLiveInfo failed req:%v uid:%d err:%v", req, mapTTid2Uid[tid], err)

					dataLock.Lock()
					defer dataLock.Unlock()
					resp.ErrInfoList = append(resp.ErrInfoList, &apiCenterPb.ErrInfo{
						Ttid: tid,
						Msg:  err.Error(),
					})
				}
			}(tmpTid, tmpReason, tmpOperUser)
		} else {
			noValidTTidList = append(noValidTTidList, tmpTid)
		}
	}

	wg.Wait()

	for _, ttid := range noValidTTidList {
		resp.ErrInfoList = append(resp.ErrInfoList, &apiCenterPb.ErrInfo{
			Ttid: ttid,
			Msg:  "无效TTID",
		})
	}

	log.DebugWithCtx(ctx, "BatDelChannelLiveInfo end req:%v resp:%s", req, resp)

	return resp, nil
}

// SetChannelLiveTagMgr 设置主播直播权限
// apiCenter-go 迁移接口
func (s *ChannelLiveMgrServer) SetChannelLiveTagMgr(ctx context.Context, req *apiCenterPb.SetChannelLiveTagReq) (*apiCenterPb.SetChannelLiveTagResp, error) {
	resp := &apiCenterPb.SetChannelLiveTagResp{}

	_, mapName2TagId, err := s.aclExtLogicApi.GetAllConfTagID2NameMap(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveTagMgr GetAllConfTagID2NameMap failed req:%v err:%v", req, err)
		return resp, err
	}
	tagId := mapName2TagId[req.GetTagName()]

	if tagId == 0 {
		log.ErrorWithCtx(ctx, "SetChannelLiveTag invalid tag req:%v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrSys, "无效标签")
	}

	_, err = s.SetChannelLiveTag(ctx, &pb.SetChannelLiveTagReq{
		ChannelId: req.GetChannelId(),
		TagId:     tagId,
		Uid:       req.GetUid(),
		OperUser:  req.GetOperUser(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveTag failed req:%v err:%v", req, err)
		return resp, err
	}

	log.DebugWithCtx(ctx, "SetChannelLiveTag end req:%v resp:%v", req, resp)
	return resp, nil
}

// GetAnchorOperRecordMgr 主播直播权限修改记录
// apiCenter-go 迁移接口
func (s *ChannelLiveMgrServer) GetAnchorOperRecordMgr(ctx context.Context, req *apiCenterPb.GetAnchorOperRecordReq) (*apiCenterPb.GetAnchorOperRecordResp, error) {
	resp := &apiCenterPb.GetAnchorOperRecordResp{}

	uidList := req.GetUidList()
	var err error
	if len(req.GetTtidList()) != 0 {
		uidList, err = s.aclExtLogicApi.GetUidListByAlias(ctx, req.GetTtidList())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorOperRecord GetUidListByAlias failed req:%v err:%v", req, err)
			return resp, err
		}

		if len(uidList) == 0 {
			// 无效tid
			return resp, nil
		}
	}

	reqGuildId := req.GetGuildId()
	if req.GetGuildId() != 0 {
		// 可能是靓号查询，需要查长号
		guildInfo, err := s.aclExtLogicApi.GetGuild(ctx, req.GetGuildId())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorOperRecord GetGuild failed req:%v err:%v", req, err)
			return resp, err
		}

		reqGuildId = guildInfo.GuildId
	}

	recordResp, err := s.GetAnchorOperRecord(ctx, &pb.GetAnchorOperRecordReq{
		OperateType: req.GetOperateType(),
		GuildId:     reqGuildId,
		UidList:     uidList,
		Page:        req.GetPage(),
		PageSize:    req.GetPageSize(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorOperRecord GetAnchorOperRecord failed req:%v err:%v", req, err)
		return resp, err
	}

	guildList := make([]uint32, 0)
	for _, record := range recordResp.GetRecordList() {
		guildList = append(guildList, record.GetGuildId())
	}

	mapId2GuildInfo, errList := s.aclExtLogicApi.GetGuildList(ctx, guildList)
	if errList != nil {
		log.ErrorWithCtx(ctx, "GetAnchorOperRecord GetGuildList failed req:%v err:%v",
			req, errList)
		return resp, errList
	}

	mapTagId2Name, _, err := s.aclExtLogicApi.GetAllConfTagID2NameMap(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveTagMgr batchGetAllChannelTagNameMap failed req:%v err:%v", req, err)
		return resp, err
	}

	for _, record := range recordResp.GetRecordList() {
		guildId := record.GetGuildId()
		if guildInfo, ok := mapId2GuildInfo[guildId]; ok {
			if guildInfo.GuildShortId != 0 {
				guildId = guildInfo.GuildShortId
			}
			resp.RecordList = append(resp.RecordList, &apiCenterPb.AnchorOperRecord{
				Ttid:        record.GetTtid(),
				Uid:         record.GetUid(),
				Nickname:    record.GetNickname(),
				GuildId:     guildId,
				GuildName:   record.GetGuildName(),
				TagName:     mapTagId2Name[record.GetTagId()],
				OperateType: record.GetOperateType(),
				OperateTs:   record.GetOperateTs(),
				OperateName: record.GetOperateName(),
			})
		}

	}

	resp.TotalCnt = recordResp.GetTotalCnt()
	resp.NextPage = recordResp.GetNextPage()

	log.DebugWithCtx(ctx, "GetAnchorOperRecord end req:%v resp:%v", req, resp)
	return resp, nil
}

// BatchAddAnchorMgr 批量添加主播直播权限
// apiCenter-go 迁移接口
func (s *ChannelLiveMgrServer) BatchAddAnchorMgr(ctx context.Context, req *apiCenterPb.BatchAddAnchorReq) (*apiCenterPb.BatchAddAnchorResp, error) {
	resp := &apiCenterPb.BatchAddAnchorResp{}

	if len(req.GetAnchorList()) > 30 {
		log.ErrorWithCtx(ctx, "BatchAddAnchor bat del too much req:%v", req)
		return resp, protocol.NewExactServerError(nil, -2, "一次最多批量处理30个主播")
	}

	if len(req.GetAnchorList()) == 0 {
		log.ErrorWithCtx(ctx, "BatchAddAnchor list is empty req:%v", req)
		return resp, protocol.NewExactServerError(nil, -2, "主播数量不能为0")
	}

	uidList := make([]uint32, 0)
	for _, info := range req.GetAnchorList() {
		uidList = append(uidList, info.GetUid())
	}

	// 获取权限列表
	liveResp, err := s.BatGetChannelLiveInfo(ctx, &pb.BatGetChannelLiveInfoReq{
		UidList: uidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchAddAnchor BatGetChannelLiveInfo failed req:%v err:%v", req, err)
		return resp, err
	}

	nowTs := uint32(time.Now().Unix())
	mapUid2IsAnchor := make(map[uint32]bool, 0)
	for _, liveInfo := range liveResp.GetInfoList() {
		if liveInfo.GetEndTime() >= nowTs {
			mapUid2IsAnchor[liveInfo.GetUid()] = true
		}
	}

	_, mapName2TagId, err := s.aclExtLogicApi.GetAllConfTagID2NameMap(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveTagMgr GetAllConfTagID2NameMap failed req:%v err:%v", req, err)
		return resp, err
	}

	var dataLock sync.Mutex
	var wg sync.WaitGroup
	for _, info := range req.GetAnchorList() {
		if !mapUid2IsAnchor[info.GetUid()] {
			tmpUid := info.GetUid()
			tmpTagName := info.GetTagName()
			tmpOperUser := info.GetOperUser()
			tmpTid := info.GetTtid()
			wg.Add(1)
			go func(uid uint32, tid, tagName, operUser string) {
				defer wg.Done()

				_, err := s.SetChannelLiveInfo(ctx, &pb.SetChannelLiveInfoReq{
					ChannelLiveInfo: &pb.ChannelLiveInfo{
						Uid:      uid,
						OperName: operUser,
						TagId:    mapName2TagId[tagName],
					},
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "BatchAddAnchor SetChannelLiveInfo failed req:%v uid:%d err:%v", req, uid, err)

					dataLock.Lock()
					defer dataLock.Unlock()
					resp.ErrInfoList = append(resp.ErrInfoList, &apiCenterPb.ErrInfo{
						Ttid: tid,
						Msg:  err.Error(),
					})
				}
			}(tmpUid, tmpTid, tmpTagName, tmpOperUser)
		} else {
			dataLock.Lock()
			resp.ErrInfoList = append(resp.ErrInfoList, &apiCenterPb.ErrInfo{
				Ttid: info.GetTtid(),
				Msg:  "该用户已经有主播权限",
			})
			dataLock.Unlock()
		}

	}

	wg.Wait()

	log.InfoWithCtx(ctx, "BatchAddAnchor end req:%+v resp:%+v", req, resp)
	return resp, nil
}

func (s *ChannelLiveMgrServer) batchGetChannelInfo(ctx context.Context, cidList []uint32) (map[uint32]*channelsvrPb.ChannelSimpleInfo, protocol.ServerError) {
	tmpCidList := make([]uint32, 0)
	mapCid2Info := make(map[uint32]*channelsvrPb.ChannelSimpleInfo, 0)

	for index, cid := range cidList {
		tmpCidList = append(tmpCidList, cid)
		if len(tmpCidList) == 100 || index == (len(cidList)-1) {
			mapId2Ch, err := s.channelClient.BatchGetChannelSimpleInfo(ctx, 0, tmpCidList)
			if err != nil {
				return mapCid2Info, err
			}

			for _, info := range mapId2Ch {
				mapCid2Info[info.GetChannelId()] = info
			}

			tmpCidList = tmpCidList[0:0]
		}
	}

	return mapCid2Info, nil
}

func (s *ChannelLiveMgrServer) batchGetCurrBannedStat(ctx context.Context, cidList []uint32) (map[uint32]bool, error) {
	mapCid2IsBanned := make(map[uint32]bool, 0)
	tmpCidList := make([]uint32, 0)

	for index, cid := range cidList {
		tmpCidList = append(tmpCidList, cid)
		if len(tmpCidList) == 100 || index == len(cidList)-1 {
			statList, err := s.greenCli.BatchGetCurrBannedStatById(ctx, 0, uint32(greenBabaPB.ENUM_TARGET_TYPE_E_TARGET_TYPE_CHANNEL), tmpCidList)
			if nil != err {
				return mapCid2IsBanned, err
			}

			for _, info := range statList {
				for _, sanctionInfo := range info.BannedList {
					if sanctionInfo.TargetType == uint32(greenBabaPB.ENUM_TARGET_TYPE_E_TARGET_TYPE_CHANNEL) &&
						sanctionInfo.BannedType == uint32(greenBabaPB.ENUM_BANNED_TYPE_E_BANNED_CHANNEL) {
						mapCid2IsBanned[info.GetId()] = true
					}
				}
			}

			tmpCidList = tmpCidList[0:0]
		}
	}

	return mapCid2IsBanned, nil
}

func (s *ChannelLiveMgrServer) batchGetUserContract(ctx context.Context, uidList []uint32) (map[uint32]*anchorcontractGoPb.ContractCacheInfo, []uint32, error) {
	contractMap := make(map[uint32]*anchorcontractGoPb.ContractCacheInfo)
	tmpUidList := make([]uint32, 0)
	guildList := make([]uint32, 0)
	mapGuildIdIsProc := make(map[uint32]bool, 0)
	nowTs := uint32(time.Now().Unix())

	for index, uid := range uidList {
		tmpUidList = append(tmpUidList, uid)
		if len(tmpUidList) == 100 || index == len(uidList)-1 {
			contractresp, err := s.anchorContractGoCli.BatchGetUserContractCacheInfo(ctx, &anchorcontractGoPb.BatchGetUserContractCacheInfoReq{Uids: tmpUidList})
			if err != nil {
				return contractMap, guildList, err
			}

			for k, v := range contractresp.GetUid2Contractinfo() {
				if v.GetContract().GetExpireTime() >= nowTs {
					contractMap[k] = v
					if !mapGuildIdIsProc[v.GetContract().GetGuildId()] {
						guildList = append(guildList, v.GetContract().GetGuildId())
						mapGuildIdIsProc[v.GetContract().GetGuildId()] = true
					}
				}
			}

			tmpUidList = tmpUidList[0:0]
		}
	}

	return contractMap, guildList, nil
}

// 判断是否是大主播
func (s *ChannelLiveMgrServer) batCheckIsGreatAnchor(ctx context.Context, uidList []uint32) (map[uint32]bool, error) {
	mapUidIsGreat := make(map[uint32]bool, 0)
	tmpUidList := make([]uint32, 0)
	now := time.Now()

	monthTimeList := make([]uint32, 0)
	monthStrList := make([]string, 0)
	for i := 0; i < 6; i++ {
		monthTm := now.AddDate(0, -i, 0)
		monthTimeList = append(monthTimeList, uint32(monthTm.Unix()))
		monthStrList = append(monthStrList, monthTm.Format("2006-01"))
	}

	mapUidMonth2Income := make(map[string]uint32, 0)
	for index, uid := range uidList {
		tmpUidList = append(tmpUidList, uid)
		if len(tmpUidList) == 100 || index == len(uidList)-1 {
			for _, monthTs := range monthTimeList {
				statsResp, err := s.chLiveStats.BatchGetAnchorMonthlyStatsByUid(ctx, tmpUidList, monthTs)
				if err != nil {
					return mapUidIsGreat, err
				}

				for _, stats := range statsResp.GetList() {
					mapUidMonth2Income[fmt.Sprintf("%d-%s", stats.GetAnchorUid(), stats.GetYearmonth())] = stats.GetAnchorIncome()
				}
			}

			tmpUidList = tmpUidList[0:0]
		}
	}

	// 大主播定义
	// 近3个月（包含当月）中，有某一个月在自己的直播间的主播收礼值≥20000元的主播
	// 近6个月（包含当月）中， 月均主播收礼值≥20000元的主播
	for _, uid := range uidList {
		var totalIncome uint64 = 0
		for index, monthStr := range monthStrList {
			income := mapUidMonth2Income[fmt.Sprintf("%d-%s", uid, monthStr)]

			// 近3个月（包含当月）中，有某一个月在自己的直播间的主播收礼值≥20000元的主播
			if index < 3 && income >= 20000*100 {
				mapUidIsGreat[uid] = true
			}

			totalIncome += uint64(income)
		}

		if !mapUidIsGreat[uid] {
			//近6个月（包含当月）中， 月均主播收礼值≥20000元的主播
			mapUidIsGreat[uid] = (totalIncome / 6) >= (20000 * 100)
		}

	}

	return mapUidIsGreat, nil
}

func (s *ChannelLiveMgrServer) batGetAnchorFirstLiveTs(ctx context.Context, uidList []uint32) (map[uint32]uint32, error) {
	mapUid2FirstTs := make(map[uint32]uint32, 0)
	tmpUidList := make([]uint32, 0)

	for index, uid := range uidList {
		tmpUidList = append(tmpUidList, uid)
		if len(tmpUidList) == 100 || index == len(uidList)-1 {
			statsResp, err := s.chLiveStats.BatchGetAnchorBaseInfo(ctx, 0, tmpUidList)
			if err != nil {
				return mapUid2FirstTs, err
			}

			for _, stats := range statsResp.GetInfoList() {
				mapUid2FirstTs[stats.GetUid()] = stats.GetFirstLiveTs()
			}

			tmpUidList = tmpUidList[0:0]
		}
	}

	return mapUid2FirstTs, nil
}

func (s *ChannelLiveMgrServer) batGetUserApplySignRecord(ctx context.Context, uidList []uint32) (map[uint32]uint32, map[uint32]uint32, error) {
	mapUid2FirstTs := make(map[uint32]uint32, 0)
	mapUid2LatestTs := make(map[uint32]uint32, 0)
	tmpUidList := make([]uint32, 0)

	for index, uid := range uidList {
		tmpUidList = append(tmpUidList, uid)
		if len(tmpUidList) == 100 || index == len(uidList)-1 {
			multiResp, err := s.anchorContractGoCli.BatchGetUserApplySignRecord(ctx, 0, &anchorcontractGoPb.BatchGetUserApplySignRecordReq{
				UidList:        tmpUidList,
				AnchorIdentity: uint32(anchorcontractGoPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER),
				StatusList:     []uint32{uint32(anchorcontractGoPb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PASS)},
			})
			if err != nil {
				return mapUid2LatestTs, mapUid2FirstTs, err
			}

			mapUid2MutiList := make(map[uint32][]*anchorcontractGoPb.ApplySignRecord, 0)
			for _, info := range multiResp.GetRecordList() {
				mapUid2MutiList[info.GetUid()] = append(mapUid2MutiList[info.GetUid()], info)
			}

			for k, v := range mapUid2MutiList {
				sort.Slice(v, func(i, j int) bool {
					return v[i].GetChangeTime() > v[j].GetChangeTime()
				})
				mapUid2LatestTs[k] = v[0].GetChangeTime()
				mapUid2FirstTs[k] = v[len(v)-1].GetChangeTime()
			}

			liveResp, err := s.anchorContractGoCli.BatchGetUserApplySignRecord(ctx, 0, &anchorcontractGoPb.BatchGetUserApplySignRecordReq{
				UidList:        tmpUidList,
				AnchorIdentity: uint32(anchorcontractGoPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE),
				StatusList:     []uint32{uint32(anchorcontractGoPb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PASS)},
			})
			if err != nil {
				return mapUid2LatestTs, mapUid2FirstTs, err
			}

			mapUid2LiveList := make(map[uint32][]*anchorcontractGoPb.ApplySignRecord, 0)
			for _, info := range liveResp.GetRecordList() {
				mapUid2LiveList[info.GetUid()] = append(mapUid2LiveList[info.GetUid()], info)
			}

			for k, v := range mapUid2LiveList {
				sort.Slice(v, func(i, j int) bool {
					return v[i].GetChangeTime() > v[j].GetChangeTime()
				})
				if v[0].GetChangeTime() > mapUid2LatestTs[k] {
					mapUid2LatestTs[k] = v[0].GetChangeTime()
				}
				if mapUid2FirstTs[k] == 0 || v[len(v)-1].GetChangeTime() < mapUid2FirstTs[k] {
					mapUid2FirstTs[k] = v[len(v)-1].GetChangeTime()
				}
			}

			tmpUidList = tmpUidList[0:0]
		}
	}

	return mapUid2LatestTs, mapUid2FirstTs, nil
}
