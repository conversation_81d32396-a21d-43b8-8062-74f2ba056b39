package server

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"

	channellivestats "golang.52tt.com/clients/channel-live-stats"
	"golang.52tt.com/clients/greenbaba"
	channel_base_api "golang.52tt.com/protocol/services/channel-base-api"
	anti_corruption_layer "golang.52tt.com/services/channel-live-mgr/model/anti-corruption-layer"

	"github.com/go-redis/redis"
	"github.com/jmoiron/sqlx"
	"github.com/opentracing/opentracing-go"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	activeFans "golang.52tt.com/clients/active-fans"
	"golang.52tt.com/clients/anchorcontract"
	anchorcontract_go "golang.52tt.com/clients/anchorcontract-go"
	"golang.52tt.com/clients/channel"
	channel_follow "golang.52tt.com/clients/channel-follow"
	channellivefans "golang.52tt.com/clients/channel-live-fans"
	"golang.52tt.com/clients/channelmic"
	"golang.52tt.com/clients/channelrobot"
	"golang.52tt.com/clients/friendol"
	"golang.52tt.com/clients/nobility"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	tracing "golang.52tt.com/pkg/tracing/jaeger"
	channelpb "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/common/status"
	anchorcontract_go2 "golang.52tt.com/protocol/services/anchorcontract-go"
	"golang.52tt.com/services/channel-live-mgr/kafka_prod"
	"golang.52tt.com/services/channel-live-mgr/match"
	"golang.52tt.com/services/channel-live-mgr/pk"

	pbLogic "golang.52tt.com/protocol/app/channel-live-logic"

	active_fans "golang.52tt.com/clients/active-fans"
	channel_live_pk "golang.52tt.com/clients/channel-live-pk"
	friendshipCli "golang.52tt.com/clients/ugc/friendship"
	channellivepkPB "golang.52tt.com/protocol/services/channel-live-pk"
	channel_scheme_middle "golang.52tt.com/protocol/services/channel-scheme-middle"
	liveFansPb "golang.52tt.com/protocol/services/channellivefans"
	pb "golang.52tt.com/protocol/services/channellivemgr"
	channelMicPb "golang.52tt.com/protocol/services/channelmicsvr"
	channelsvrPb "golang.52tt.com/protocol/services/channelsvr"
	"golang.52tt.com/services/channel-live-mgr/cache"
	"golang.52tt.com/services/channel-live-mgr/conf"
	"golang.52tt.com/services/channel-live-mgr/event"
	"golang.52tt.com/services/channel-live-mgr/manager"
	"golang.52tt.com/services/channel-live-mgr/mysql"
)

var easyCache = cache.NewEasyCache()     //通用回包缓存
var easyCacheFlag = cache.NewEasyCache() //没权限标志缓存

var HeartBeats = sync.Map{}

const (
	WatchAudieCountLimit = 100 // 观看排行榜获取观众数量
)

var MapTagId2Name = map[uint32]string{
	0:    "",
	2003: "大神带飞",
	2010: "相亲交友",
	2002: "点唱厅",
	2001: "扩列",
	2012: "Pia戏",
	2005: "踢保",
	2007: "小故事",
	2016: "一起玩",
	2015: "大神带飞",
	2014: "CP战",
	2013: "派对",
	3001: "音乐",
	3002: "情感",
	3003: "二次元",
	3004: "故事",
	3005: "脱口秀",
	3006: "虚拟互动",
}

type ChannelLiveMgrServer struct {
	sc             *conf.ServiceConfigT
	liveMgrCache   *cache.ChannelLiveMgrCache
	redisClient    *redis.Client
	mysqlStore     *mysql.Store
	mgr            *manager.Manager
	stop           chan bool
	ctx            context.Context
	aclExtLogicApi anti_corruption_layer.IExtLogicApi // 防腐层接口 用于封装外部逻辑接口

	channelLiveProd kafka_prod.LiveEventPublisher
	pkApplyProd     kafka_prod.PkApplyEventPublisher

	channelKfkSub *event.KafkaChannelSubscriber
	presentKfkSub *event.KafkaPresentSubscriber
	micKfkSub     *event.KafkaMicEventSubscriber
	knightKfkSub  *event.KnightGroupSub
	ykwKfkSub     *event.UkwSub
	extGameKfkSub *event.RevenueExtGameSub

	channelClient    *channel.Client
	channelmicClient *channelmic.Client

	liveFansClient *channellivefans.Client

	channelFollowClient *channel_follow.Client
	activeCli           *activeFans.Client
	friendShipCli       *friendshipCli.Client

	channelBaseApiCli   channel_base_api.ChannelBaseApiClient
	robotCli            *channelrobot.Client
	nobilityClient      *nobility.Client
	activeFanClient     *active_fans.Client
	friendOLClient      *friendol.Client
	anchorContractGoCli *anchorcontract_go.Client

	channeLivePkCli        *channel_live_pk.Client
	channelSchemeMiddleCli *channel_scheme_middle.Client
	anchorContractClient   *anchorcontract.Client
	greenCli               greenbaba.IClient
	chLiveStats            channellivestats.IClient
}

func GetHeartBeat(cid uint32) int64 {
	tmp, ok := HeartBeats.Load(cid)
	if !ok {
		return 0
	}
	return tmp.(int64)
}

func SetHeartBeat(cid uint32, ts int64) {
	HeartBeats.Store(cid, ts)
}

func NewChannelLiveMgrServer(ctx context.Context, cfg config.Configer) (*ChannelLiveMgrServer, error) {

	sc := &conf.ServiceConfigT{}

	cfgPath := ctx.Value("configfile").(string)
	if cfgPath == "" {
		return nil, errors.New("configfile  not exist")
	}
	err := sc.Parse(cfgPath)
	if err != nil {
		return nil, err
	}

	log.ErrorWithCtx(ctx, "NewChannelLiveMgrServer sc:%v", sc)

	redisClient := redis.NewClient(&redis.Options{
		Network:            sc.GetRedisConfig().Protocol,
		Addr:               sc.GetRedisConfig().Addr(),
		PoolSize:           sc.GetRedisConfig().PoolSize,
		IdleCheckFrequency: sc.GetRedisConfig().IdleCheckFrequency(),
		DB:                 sc.GetRedisConfig().DB,
	})

	redisGClient := redis.NewClient(&redis.Options{
		Network:            sc.GetRedisConfigQueueMic().Protocol,
		Addr:               sc.GetRedisConfigQueueMic().Addr(),
		PoolSize:           sc.GetRedisConfigQueueMic().PoolSize,
		IdleCheckFrequency: sc.GetRedisConfigQueueMic().IdleCheckFrequency(),
		DB:                 sc.GetRedisConfigQueueMic().DB,
	})

	redisFlagClient := redis.NewClient(&redis.Options{
		Network:            sc.GetFlagRedis().Protocol,
		Addr:               sc.GetFlagRedis().Addr(),
		PoolSize:           sc.GetFlagRedis().PoolSize,
		IdleCheckFrequency: sc.GetFlagRedis().IdleCheckFrequency(),
		DB:                 sc.GetFlagRedis().DB,
	})

	log.DebugWithCtx(ctx, "Initialized redis connection pool to %s://%s/%d", sc.GetRedisConfig().Protocol, sc.GetRedisConfig().Addr(), sc.GetRedisConfig().DB)
	redisTracer := tracing.Init("channel-live-mgr_redis")
	cacheClient := cache.NewChannelLiveMgrCache(redisClient, nil, redisFlagClient, redisTracer)

	mysqlDb, err := sqlx.Connect("mysql", sc.GetMysqlConnectionString())
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create mysql %v", err)
		return nil, err
	}

	readonlyMysqlDb, err := sqlx.Connect("mysql", sc.GetReadonlyMysqlConnectionString())
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create mysql %v", err)
		return nil, err
	}

	mysqlStore := mysql.NewMysql(mysqlDb, readonlyMysqlDb)
	_ = mysqlStore.CreateTables()

	// 初始化外部接口 模块
	aclExtLogic_, err := anti_corruption_layer.NewExtLogicMgr(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer fail to anti_corruption_layer.NewExtLogicMgr, err:%v", err)
		return nil, err
	}

	liveFansClient, _ := channellivefans.NewClient()
	channelClient := channel.NewClient()
	channelmicClient := channelmic.NewClient()
	friendShipCli, _ := friendshipCli.NewClient()

	activeCli, _ := activeFans.NewClient()
	robotCli := channelrobot.NewClient()
	nobilityClient, _ := nobility.NewClient()
	activeFanClient, _ := active_fans.NewTracedClient(opentracing.GlobalTracer())
	friendOLClient := friendol.NewTracedClient(opentracing.GlobalTracer())
	channelFollowClient, _ := channel_follow.NewTracedClient(opentracing.GlobalTracer())

	channeLivePkCli, _ := channel_live_pk.NewClient()
	channelSchemeMiddleCli, _ := channel_scheme_middle.NewClient(ctx)

	anchorContractGoCli, _ := anchorcontract_go.NewClient()
	anchorContractClient := anchorcontract.NewClient()

	greenCli_ := greenbaba.NewIClient()
	chLiveStats_ := channellivestats.NewIClient()

	channelBaseApiClient, err := channel_base_api.NewClient(context.Background())
	if err != nil {
		log.Errorf("channel_base_api.NewClient() failed err:%v", err)
		return nil, err
	}

	log.DebugWithCtx(ctx, "Initialized kafka %v", sc.GetChannelLiveKafkaConfig())

	channelLiveProd, err := kafka_prod.NewKafkaProduce(sc.GetChannelLiveKafkaConfig().BrokerList(), sc.GetChannelLiveKafkaConfig().ClientID, sc.GetChannelLiveKafkaConfig().Topics)
	if err != nil {
		log.Errorf("kafka_prod.NewKafkaProduce failed err:%v", err)
		return nil, err
	}

	pkApplyProd, err := kafka_prod.NewKafkaProduce(sc.GetPKApplyKafkaConfig().BrokerList(), sc.GetPKApplyKafkaConfig().ClientID, sc.GetPKApplyKafkaConfig().Topics)
	if err != nil {
		log.Errorf("kafka_prod.NewKafkaProduce failed err:%v", err)
		return nil, err
	}

	mgr := manager.NewManager(cacheClient, mysqlStore, redisGClient, aclExtLogic_, sc, channelLiveProd, ctx)

	presentKfkSub, err := event.NewPresentKafkaSubscriber(sc.GetPresentKafkaConfig(), mgr, sc.GetJoinGroupGiftID())
	if err != nil {
		log.Errorf("event.NewPresentKafkaSubscriber failed err:%v", err)
		return nil, err
	}

	knightKfkSub, err := event.NewKnightGroupSub(sc.GetKnightKafkaConfig(), mgr)
	if err != nil {
		log.Errorf("event.NewKnightGroupSub failed err:%v", err)
		return nil, err
	}

	ykwKfkSub, err := event.NewUkwSub(sc.GetYkwKafkaConfig(), mgr)
	if err != nil {
		log.Errorf("event.NewUkwSub failed err:%v", err)
		return nil, err
	}

	extGameSub, err := event.NewRevenueExtGameSub(sc.GetExtGameKfkConfig(), mgr)
	if err != nil {
		log.Errorf("event.NewRevenueExtGameSub failed err:%v", err)
		return nil, err
	}

	channelKfkSub, err := event.NewChannelKafkaSubscriber(sc.GetChannelKafkaConfig(), mgr)
	if err != nil {
		log.Errorf("event.NewChannelKafkaSubscriber failed err:%v", err)
		return nil, err
	}

	channelMicKfkSub, err := event.NewMicKafkaSubscriber(sc.GetChannelMicKafkaConfig(), mgr)
	if err != nil {
		log.Errorf("event.NewMicKafkaSubscriber failed err:%v", err)
		return nil, err
	}

	server := &ChannelLiveMgrServer{
		sc:             sc,
		mysqlStore:     mysqlStore,
		liveMgrCache:   cacheClient,
		redisClient:    redisGClient,
		mgr:            mgr,
		stop:           make(chan bool),
		ctx:            ctx,
		aclExtLogicApi: aclExtLogic_,

		// MQ Pub
		channelLiveProd: channelLiveProd,
		pkApplyProd:     pkApplyProd,
		// MQ Sub
		presentKfkSub: presentKfkSub,
		channelKfkSub: channelKfkSub,
		knightKfkSub:  knightKfkSub,
		ykwKfkSub:     ykwKfkSub,
		extGameKfkSub: extGameSub,
		micKfkSub:     channelMicKfkSub,

		liveFansClient:      liveFansClient,
		channelClient:       channelClient,
		channelmicClient:    channelmicClient,
		channelFollowClient: channelFollowClient,

		activeCli:     activeCli,
		friendShipCli: friendShipCli,

		robotCli: robotCli,

		nobilityClient:      nobilityClient,
		friendOLClient:      friendOLClient,
		activeFanClient:     activeFanClient,
		anchorContractGoCli: anchorContractGoCli,

		channeLivePkCli:        channeLivePkCli,
		channelSchemeMiddleCli: channelSchemeMiddleCli,
		anchorContractClient:   anchorContractClient,
		greenCli:               greenCli_,
		chLiveStats:            chLiveStats_,
		channelBaseApiCli:      channelBaseApiClient,
	}

	//匹配
	match.InitOptimizedMatchers(server.liveMgrCache)
	matchFunc := func(ctx context.Context, uida, uidb, channelida, chanenlidb, scoreA, scoreB uint32, source int) error {
		_, err := server.StartPk(ctx, uida, uidb, channelida, chanenlidb, scoreA, scoreB, source, 0)
		return err
	} //

	go LoopFunc(server.mgr.HandlerPkProcess, time.Second)
	go LoopFunc(func() { match.MatchTick(server.liveMgrCache, matchFunc) }, time.Second)
	go LoopFunc(server.mgr.GenMonthAnchorScoreProcess, time.Second)

	server.StartAppointPkTimer()

	return server, nil
}

func (s *ChannelLiveMgrServer) GetHeartBeatTimeOut(ctx context.Context, in *pb.GetHeartBeatTimeOutReq) (*pb.GetHeartBeatTimeOutResp, error) {
	out := &pb.GetHeartBeatTimeOutResp{
		UidList: make([]uint32, 0),
	}

	bLock, _ := s.liveMgrCache.GetLock("channel_live_HandlerLiveHeartBeatTimeOut")

	if !bLock {
		return out, nil
	}

	uids := s.liveMgrCache.GetTimeOutHeartBeatList(in.Expire, in.Del)

	s.liveMgrCache.UnLock("channel_live_HandlerLiveHeartBeatTimeOut")

	out.UidList = uids

	return out, nil
}

func (s *ChannelLiveMgrServer) GenChannelLiveChannelID(ctx context.Context, uid uint32) (uint32, error) {
	var channelID uint32 = 0

	channelLiveinfo := &pb.ChannelLiveInfo{}
	err := s.mysqlStore.GetChannelLiveInfo(ctx, channelLiveinfo, uid)
	if err == nil && channelLiveinfo.ChannelId != 0 {
		channelID = channelLiveinfo.ChannelId
		return channelID, nil
	}

	resp, err := s.channelClient.GetUserChannelRoleList(ctx, uid, uid, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenChannelLiveChannelID GetUserChannelRoleList err:%v", err)
		return 0, err
	}

	for _, roleInfo := range resp.RoleList {
		if roleInfo.ChannelInfo == nil {
			continue
		}
		if (*roleInfo.ChannelInfo.ChannelBindType) == 7 && (*roleInfo.ChannelInfo.CreaterUid) == uid {
			channelID = *roleInfo.ChannelInfo.ChannelBaseinfo.ChannelId
			break
		}
	}

	if channelID == 0 {
		cresp, err := s.channelClient.CreateChannel(ctx, uid, uid, uint32(channelpb.ChannelType_RADIO_LIVE_CHANNEL_TYPE),
			0, "听听房间", "")
		if err != nil {
			log.ErrorWithCtx(ctx, "GenChannelLiveChannelID CreateChannel err:%v", err)
			return channelID, err
		}
		channelID = *cresp.CreateResp.ChannelId

		_, setSchemeErr := s.channelSchemeMiddleCli.SetCurChannelScheme(ctx, &channel_scheme_middle.SetCurChannelSchemeReq{
			OpUid:                uid,
			Cid:                  channelID,
			SchemeId:             10010,
			Source:               channel_scheme_middle.Source_SET_RADIO_LIVE_CREATE,
			AppId:                0,
			MarketId:             0,
			NotSetMicAndMinigame: true,
		})
		if setSchemeErr != nil {
			log.ErrorWithCtx(ctx, "GenChannelLiveChannelID SetCurChannelScheme uid:%d cid:%d err:%v",
				uid, channelID, setSchemeErr)
			return 0, setSchemeErr
		}
	}

	return channelID, nil
}

// 增加操作记录
func (s *ChannelLiveMgrServer) addAnchorOperRecord(anchorUid, cid, tagId, operType uint32, operUser string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	anchorInfo, err := s.aclExtLogicApi.GetAnchorInfo(ctx, anchorUid)
	if err != nil {
		log.ErrorWithCtx(ctx,
			"addAnchorOperRecord GetAnchorInfo failed anchorUid:%d err:%+v", anchorUid, err)
		return err
	}

	if tagId == 0 {
		tagID_, err := s.aclExtLogicApi.GetChannelTag(ctx, anchorUid, cid)
		if err != nil {
			log.ErrorWithCtx(ctx,
				"addAnchorOperRecord GetChannelTag failed anchorUid:%d err:%+v", anchorUid, err)
		} else {
			tagId = tagID_
		}
	}

	Err := s.mysqlStore.AddAnchorOperLog(ctx, mysql.AnchorOperLog{
		Uid:       anchorUid,
		Tid:       anchorInfo.AnchorAlias,
		NickName:  anchorInfo.AnchorNickname,
		GuildId:   anchorInfo.GuildId,
		GuildName: anchorInfo.GuildName,
		TagId:     tagId,
		OperType:  operType,
		OperUser:  operUser,
	})
	if Err != nil {
		log.ErrorWithCtx(ctx, "addAnchorOperRecord AddAnchorOperLog failed anchorUid:%d err:%+v", anchorUid, Err)
	}

	log.DebugWithCtx(ctx, "addAnchorOperRecord end anchorUid:%d operType:%d", anchorUid, operType)
	return nil
}

// 开启主播权限
func (s *ChannelLiveMgrServer) SetChannelLiveInfo(ctx context.Context, in *pb.SetChannelLiveInfoReq) (out *pb.SetChannelLiveInfoResp, err error) {
	out = &pb.SetChannelLiveInfoResp{}

	//暂时没有时间段需求，先给个大的时间
	in.ChannelLiveInfo.BeginTime = uint32(time.Now().Unix())
	in.ChannelLiveInfo.EndTime = 1993315950

	channelID := in.ChannelLiveInfo.ChannelId
	if channelID == 0 {
		channelID, err = s.GenChannelLiveChannelID(ctx, in.ChannelLiveInfo.Uid)
		if err != nil || channelID == 0 {
			log.ErrorWithCtx(ctx, "SetChannelLiveInfo GenChannelLiveChannelID failed in:%+v id:%d err:%+v", in, channelID, err)
			return out, err
		}
	}

	//清空密码
	var pwdFlag, recommen uint32 = 0, 0x2 //开启推荐位
	pwd := ""
	_, err = s.channelClient.ModifyChannel(ctx, in.ChannelLiveInfo.Uid, &channelsvrPb.ModifyChannelReq{
		ChannelId:        &channelID,
		OpUid:            &in.ChannelLiveInfo.Uid,
		PwdFlag:          &pwdFlag,
		Passwd:           &pwd,
		SwitchFlagBitmap: &recommen,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo ModifyChannel failed in:%v err:%v", in, err)
	}

	_, err = s.channelmicClient.SetChannelMicMode(ctx, in.ChannelLiveInfo.Uid, &channelMicPb.SetChannelMicModeReq{
		Uid:       in.ChannelLiveInfo.Uid,
		ChannelId: channelID,
		MicMode:   uint32(channelpb.EChannelMicMode_LIVE_MIC_SPACE_MODE),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo SetChannelMicMode in:%v err:%v", in, err)
	}

	in.ChannelLiveInfo.ChannelId = channelID
	err = s.mysqlStore.SetChannelLiveInfo(ctx, in.GetChannelLiveInfo())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo DB  err:%v in:%v", err, in)
		return out, err
	}

	//清缓存
	err = s.liveMgrCache.DelChannelLiveInfo(in.ChannelLiveInfo.Uid, in.ChannelLiveInfo.ChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo DelChannelLiveInfo failed in:%v err:%v", in, err)
	}

	// 操作记录，直接起协程
	go s.addAnchorOperRecord(in.GetChannelLiveInfo().GetUid(), in.GetChannelLiveInfo().GetChannelId(),
		in.GetChannelLiveInfo().GetTagId(), uint32(pb.OperateType_AddAnchor), in.GetChannelLiveInfo().GetOperName())

	log.DebugWithCtx(ctx, "SetChannelLiveInfo cache err(%s) uid(%d) channelLiveInfo(%+v)", err, in.ChannelLiveInfo.Uid, in.GetChannelLiveInfo())

	//清掉没有直播权限标志
	s.liveMgrCache.SetNoAuthFlag(0, AuthFlagKey(pb.EnumIDType_USER_ID, in.ChannelLiveInfo.Uid), AuthFlagKey(pb.EnumIDType_CHANNEL_ID, in.ChannelLiveInfo.ChannelId))

	//关闭排麦开关
	simple, err := s.channelClient.GetChannelSimpleInfo(ctx, in.ChannelLiveInfo.Uid, channelID)
	if nil != err {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo GetChannelSimpleInfo uid:%v cid:%v err:%v", in.ChannelLiveInfo.Uid, channelID, err)
	} else {
		if (*simple.SwitchFlag)|manager.OPEN_NORMAL_QUEUE_UP_MIC > 0 {
			flagBit := (*simple.SwitchFlag) & (^manager.OPEN_NORMAL_QUEUE_UP_MIC)
			s.mgr.SwitchMicQueue(channelID, in.ChannelLiveInfo.Uid, flagBit)
		}
	}

	//创建粉丝团
	_, err = s.liveFansClient.CreateFansGroup(s.ctx, in.ChannelLiveInfo.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo CreateFansGroup in:%v err:%v", in, err)
	}

	err = s.aclExtLogicApi.FollowOrUnFollowYuYinFuWuHao(ctx, in.ChannelLiveInfo.Uid, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo FollowOrUnFollowYuYinFuWuHao in:%v err:%v", in, err)
	}

	var level uint32 = 4
	serr := s.aclExtLogicApi.AutoAddLivePrepareChannel(ctx, in.ChannelLiveInfo.Uid,
		in.ChannelLiveInfo.TagId, level, in.ChannelLiveInfo.ChannelId)
	if serr != nil {
		log.ErrorWithCtx(ctx,
			"SetChannelLiveInfo AutoAddLivePrepareChannel failed in:%+v serr:%v", in, serr)
	}

	//TT语音助手消息
	msg := "恭喜获得[开启听听]权限，在娱乐界面进入听听房间（5.3.0以上版本支持），点此去开启>"
	_ = s.aclExtLogicApi.SendTTAssistantMsgWithUrl(ctx, in.ChannelLiveInfo.Uid, msg, "点此去开启>", "tt://m.52tt.com/home?main_tab=channel&second_tab=sec_entertainment")

	// 推送 开启事件给客户端
	perr := s.aclExtLogicApi.PushLivePermission(ctx, in.ChannelLiveInfo.Uid, in.ChannelLiveInfo.ChannelId, true)
	if perr != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo u %d c %d PushLivePermission err:%v",
			in.ChannelLiveInfo.Uid, in.ChannelLiveInfo.ChannelId, perr)
	}

	log.InfoWithCtx(ctx, "SetChannelLiveInfo end in:%+v out:%+v", in, out)

	return out, nil
}

func (s *ChannelLiveMgrServer) SetChannelLiveTag(ctx context.Context, in *pb.SetChannelLiveTagReq) (out *pb.SetChannelLiveTagResp, err error) {
	out = &pb.SetChannelLiveTagResp{}

	log.DebugWithCtx(ctx, "SetChannelLiveTag AutoAddLivePrepareChannel in:%v", in)

	if in.Level == 0 {
		in.Level = 4
	}

	serr := s.aclExtLogicApi.AutoAddLivePrepareChannel(ctx, in.Uid,
		in.TagId, in.Level, in.ChannelId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveTag AutoAddLivePrepareChannel fail. in:%v err:%v", in, serr)
		return out, serr
	}

	chLiveInfo, err := s.liveMgrCache.GetChannelLiveInfo(in.ChannelId, pb.EnumIDType_CHANNEL_ID)
	if err != nil || chLiveInfo == nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveTag GetChannelLiveInfo failed in:%v err:%v",
			in, err)
	}
	if chLiveInfo.ChannelId != 0 {
		chLiveInfo.TagId = in.TagId
		_ = s.liveMgrCache.SetChannelLiveInfo(chLiveInfo.Uid, chLiveInfo)
	}

	// 操作记录，直接起协程
	go s.addAnchorOperRecord(chLiveInfo.GetUid(), in.GetChannelId(), in.GetTagId(), uint32(pb.OperateType_ModifyTag), in.GetOperUser())

	msg := fmt.Sprintf("亲爱的达人：您目前的听听标签已更换为【%s】，请按照平台规范继续开启听听，感谢您的支持与配合!", MapTagId2Name[in.GetTagId()])
	_ = s.aclExtLogicApi.SendTTAssistantMsgWithUrl(ctx, chLiveInfo.GetUid(), msg, "", "")
	return out, nil
}

// 删除主播权限
func (s *ChannelLiveMgrServer) DelChannelLiveInfo(ctx context.Context, in *pb.DelChannelLiveInfoReq) (out *pb.DelChannelLiveInfoResp, err error) {
	out = &pb.DelChannelLiveInfoResp{}

	chLiveInfo, err := s.liveMgrCache.GetChannelLiveInfo(in.Uid, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelChannelLiveInfo GetChannelLiveInfo failed in:%v err:%v", in, err)
	}

	chStatus, err := s.liveMgrCache.GetChannelLiveStatus(in.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelChannelLiveInfo GetChannelLiveStatus failed in:%v err:%v", in, err)
	}

	//更新权限过期周期
	err = s.mysqlStore.DelChannelLiveInfo(ctx, in.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelChannelLiveInfo err:%v", err)
		return out, err
	}

	channelID := in.ChannelId
	if channelID == 0 {
		channelID = chLiveInfo.ChannelId
	}
	s.liveMgrCache.DelChannelLiveInfo(in.Uid, channelID)

	// 操作记录，直接起协程
	go s.addAnchorOperRecord(in.GetUid(), channelID, chLiveInfo.GetTagId(), uint32(pb.OperateType_DelAnchor), in.GetOperUser())

	//设置没有权限标志
	s.liveMgrCache.SetNoAuthFlag(1, AuthFlagKey(pb.EnumIDType_USER_ID, in.Uid), AuthFlagKey(pb.EnumIDType_CHANNEL_ID, in.ChannelId))

	serr := s.anchorContractClient.SetContractLiveActorFlag(ctx, in.Uid, 1)
	if serr != nil {
		log.ErrorWithCtx(ctx, "DelChannelLiveInfo SetContractLiveActorFlag fail. err:%v", err)
	}

	contractResp, serr := s.anchorContractGoCli.GetUserContract(ctx, in.GetUid(), in.GetUid())
	if serr != nil {
		// retry
		contractResp, serr = s.anchorContractGoCli.GetUserContract(ctx, in.GetUid(), in.GetUid())
		if serr != nil {
			log.ErrorWithCtx(ctx, "DelChannelLiveInfo GetUserContract fail. %+v err:%v", in, serr)
		}
	}

	if serr == nil {
		_, serr = s.anchorContractGoCli.ReclaimAnchorIdentity(ctx, in.GetUid(), &anchorcontract_go2.ReclaimAnchorIdentityReq{
			Uid:            in.GetUid(),
			GuildId:        contractResp.GetContract().GetGuildId(),
			AnchorIdentity: uint32(anchorcontract_go2.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE),
			Handler:        in.GetReason(),
		})
		if serr != nil {
			log.ErrorWithCtx(ctx, "DelChannelLiveInfo ReclaimAnchorIdentity fail. %+v err:%v", in, serr)
		}
	}

	msg := fmt.Sprintf("由于【%s】，已暂时回收您的听听权限。", in.Reason)
	s.aclExtLogicApi.SendTTAssistantMsgWithUrl(ctx, in.Uid, msg, "", "")

	//设置心跳值，过期，logic层推送结束状态
	if chStatus.Status != pb.EnumChannelLiveStatus_CLOSE {
		s.liveMgrCache.UpdateChannelLiveHeartBeat(in.Uid, 10000)

		//s.apiCli.KickoutChannelMember(ctx, in.Uid, []uint32{in.Uid}, channelID, 1, 1, "听听权限被回收")
		_, kerr := s.channelBaseApiCli.KickOutChannelMember(ctx, &channel_base_api.KickOutChannelMemberReq{
			ChannelId:      channelID,
			OpUid:          in.Uid,
			BanEnterSecond: 1,
			TargetUidList:  []uint32{in.Uid},
			KickType:       1,
			KickText:       "听听权限被回收",
			OpSource:       "channel-live-mgr",
		})
		if kerr != nil {
			log.WarnWithCtx(ctx, "kickout channel member failed. err:%v, uid:%d, cid:%d", kerr, in.Uid, channelID)
		}

		log.DebugWithCtx(ctx, "DelChannelLiveInfo set heart beat uid:%v", in.Uid)
	}

	// 回收推荐库信息
	serr = s.aclExtLogicApi.DeletePrepareChannel(ctx, in.GetChannelId())
	if serr != nil {
		log.ErrorWithCtx(ctx, "DelChannelLiveInfo DeletePrepareChannel failed in:%v err:%v", in, err)
	}

	// 重置粉丝团一些信息
	_, serr = s.liveFansClient.ResetFansGroup(ctx, &liveFansPb.ResetFansGroupReq{AnchorUid: in.GetUid()})
	if serr != nil {
		log.ErrorWithCtx(ctx, "DelChannelLiveInfo ResetFansGroup failed in:%v err:%v", in, err)
	}

	// 推送关闭事件给客户端
	perr := s.aclExtLogicApi.PushLivePermission(ctx, in.Uid, channelID, false)
	if perr != nil {
		log.ErrorWithCtx(ctx, "DelChannelLiveInfo u %d c %d PushLivePermission err:%v",
			in.Uid, channelID, perr)
	}

	// 取消关注
	err = s.aclExtLogicApi.FollowOrUnFollowYuYinFuWuHao(ctx, in.Uid, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelChannelLiveInfo FollowOrUnFollowYuYinFuWuHao err:%v", err)
	}

	log.DebugWithCtx(ctx, "DelChannelLiveInfo PushToUsers success uid:%v", in.Uid)

	return out, nil
}

func AuthFlagKey(ty pb.EnumIDType, id uint32) string {
	return fmt.Sprintf("%v_%v", ty, id)
}

// 获取直播权限
func (s *ChannelLiveMgrServer) GetChannelLiveInfo(ctx context.Context, in *pb.GetChannelLiveInfoReq) (out *pb.GetChannelLiveInfoResp, err error) {
	log.DebugWithCtx(ctx, "GetChannelLiveInfo in(%+v)", in)

	// 内存缓存key
	ekey := fmt.Sprintf("get_channel_live_info_%v", in.Uid)
	if in.GetUid() == 0 {
		// 房间id查询
		ekey = fmt.Sprintf("get_channel_live_info_ch_%d", in.GetChannelId())
	}

	eVal, ok := easyCache.Get(ekey)
	if ok && eVal != nil && !in.Expire {
		resp, ok := eVal.(*pb.GetChannelLiveInfoResp)
		if ok && resp != nil {
			log.DebugWithCtx(ctx, "GetChannelLiveInfo mem cache in:%v out:%v", in, resp)
			var err error = nil
			if resp.ChannelLiveInfo.ChannelId == 0 {
				err = protocol.NewExactServerError(nil, status.ErrChannelLiveNotAuthority, "没有听听权限")
			}
			return resp, err
		}
	}

	out = &pb.GetChannelLiveInfoResp{
		ChannelLiveInfo: &pb.ChannelLiveInfo{},
	}

	nowTs := uint32(time.Now().Unix())
	var id = in.Uid
	ty := pb.EnumIDType_USER_ID
	if in.Uid == 0 {
		id = in.ChannelId
		ty = pb.EnumIDType_CHANNEL_ID
	}

	//没权限标志
	noAuthKey := AuthFlagKey(ty, id)
	if s.liveMgrCache.GetNoAuthFlag(noAuthKey) && !in.Expire {
		easyCache.Set(ekey, out, conf.GetLiveCacheTs()*100)

		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveNotAuthority, "没有听听权限")
	}

	chLiveInfo, err := s.liveMgrCache.GetChannelLiveInfo(id, ty)
	if err == nil {
		if !in.Expire && chLiveInfo.EndTime <= nowTs {
			easyCache.Set(ekey, out, conf.GetLiveCacheTs()*100)

			return out, nil
		}

		if chLiveInfo.TagId == 0 {
			tagID_, serr := s.aclExtLogicApi.GetChannelTag(ctx, in.Uid, chLiveInfo.ChannelId)

			if serr != nil {
				log.ErrorWithCtx(ctx, "GetChannelLiveInfo GetChannelTag serr:%v in:%v", serr, in)
			} else {
				chLiveInfo.TagId = tagID_
				s.liveMgrCache.SetChannelLiveInfo(in.Uid, chLiveInfo)
			}
		}

		log.DebugWithCtx(ctx, "GetChannelLiveInfo redis cache in:%v out:%+v", in, out)

		out.ChannelLiveInfo = chLiveInfo
		easyCache.Set(ekey, out, conf.GetLiveCacheTs()*100)
		return out, nil
	} else {
		log.ErrorWithCtx(ctx, "GetChannelLiveInfo failed in:%v err:%v", in, err)
	}

	chLiveInfo = &pb.ChannelLiveInfo{}
	if in.GetUid() != 0 {
		err = s.mysqlStore.GetChannelLiveInfo(ctx, chLiveInfo, in.Uid)
	} else {
		err = s.mysqlStore.GetChannelLiveInfoByCid(ctx, chLiveInfo, in.GetChannelId())
	}
	if err != nil {
		//清掉没有权限标志
		if strings.Contains(err.Error(), "no rows") {
			s.liveMgrCache.SetNoAuthFlag(1, noAuthKey)
		} else {
			log.ErrorWithCtx(ctx, "GetChannelLiveInfo fail err:%v in:%v", err, in)
		}
		easyCache.Set(ekey, out, conf.GetLiveCacheTs()*100)
		//没有权限的不写入缓存，避免缓存过大
		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveNotAuthority, "没有听听权限")
	}

	if chLiveInfo.TagId == 0 {
		tagID_, serr := s.aclExtLogicApi.GetChannelTag(ctx, in.Uid, chLiveInfo.ChannelId)

		if serr != nil {
			log.ErrorWithCtx(ctx, "GetChannelLiveInfo GetChannelTag serr:%v", serr)
		} else {
			chLiveInfo.TagId = tagID_
		}
	}

	err = s.liveMgrCache.SetChannelLiveInfo(in.Uid, chLiveInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveInfo SetChannelLiveInfo in:%v err:%v", in, err)
	}

	if !in.Expire && chLiveInfo.EndTime <= nowTs {
		return out, nil
	}

	out.ChannelLiveInfo = chLiveInfo

	easyCache.Set(ekey, out, conf.GetLiveCacheTs()*100)

	log.InfoWithCtx(ctx, "GetChannelLiveInfo req:%v rsp:%v", in, out)
	return out, nil
}

// 搜索主播
func (s *ChannelLiveMgrServer) SearchAnchor(ctx context.Context, in *pb.SearchAnchorReq) (out *pb.SearchAnchorResp, err error) {

	log.DebugWithCtx(ctx, "SearchAnchorX in(%+v)", in)

	out = &pb.SearchAnchorResp{
		AnchorInfo: &pb.AnchorInfo{},
	}

	chInfo, err := s.liveMgrCache.GetChannelLiveInfo(in.Uid, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchAnchorX GetChannelLiveInfo err:%v in:%v", err, in)
		return out, err
	}

	out.AnchorInfo.Uid = chInfo.Uid
	out.AnchorInfo.ChannelId = chInfo.ChannelId
	out.AnchorInfo.ChannelLiveStatus = 0   //TODO
	out.AnchorInfo.ChannelLivePkStatus = 0 //TODO

	if chInfo.ChannelId > 0 {
		chStatus, err := s.liveMgrCache.GetChannelLiveStatus(chInfo.Uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "SearchAnchorX GetChannelLiveStatus err:%v in:%v", err, in)
		} else {
			out.AnchorInfo.ChannelLiveStatus = uint32(chStatus.Status)
		}
	}

	pkstate := pk.PkState{
		ChannelID_A: chInfo.ChannelId,
	}
	state, err := pkstate.GetPkStage()
	if err == nil {
		out.AnchorInfo.ChannelLivePkStatus = uint32(state)
	}

	log.DebugWithCtx(ctx, "SearchAnchorX out:%v in:%v", out, in)

	return out, nil
}

func (s *ChannelLiveMgrServer) ChannelLiveFinish(ctx context.Context, in *pb.SetChannelLiveStatusReq, out *pb.SetChannelLiveStatusResp) {
	//从心跳队列清理
	s.liveMgrCache.DelChannelLiveHeartBeat(in.Uid)

	chLiveInfo := s.liveMgrCache.GetChannelLiveData(in.ChannelId)

	// 添加机器人观众数
	subCtx, cancel := context.WithTimeout(ctx, time.Millisecond*200)
	defer cancel()

	robotCnt, err := s.robotCli.GetChannelRobotSize(subCtx, in.Uid, in.ChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveStatus Failed to GetChannelRobotSize err(%s) in.Uid(%d) in.ChannelId(%d)", err.Error(), in.Uid, in.ChannelId)
	}
	chLiveInfo.AudienceCnt += robotCnt
	// 添加机器人数量到每天的观众记录
	serr := s.liveMgrCache.SetDayRobotAudienceCnt(in.Uid, robotCnt)
	if serr != nil && robotCnt != 0 {
		log.ErrorWithCtx(ctx, "SetChannelLiveStatus Failed to SetDayRobotAudienceCnt err(%s) in.Uid(%d) robotCnt(%d)", serr.Error(), in.Uid, robotCnt)
	}

	addFans := uint32(0)
	if in.FansCnt > uint32(out.ChannelLiveStatus.FansCnt) {
		addFans = in.FansCnt - uint32(out.ChannelLiveStatus.FansCnt)
	}

	addGroupFans := uint32(0)
	if in.GroupFansCnt > uint32(out.ChannelLiveStatus.GroupFansCnt) {
		addGroupFans = in.GroupFansCnt - uint32(out.ChannelLiveStatus.GroupFansCnt)
	}

	//记录每场数据到MYSQL
	serr = s.mysqlStore.AddChannelLiveRecordWhenFinish(ctx, in.ChannelLiveId, uint32(chLiveInfo.GiftValue), addFans, addGroupFans,
		chLiveInfo.AudienceCnt, chLiveInfo.SendGiftAudienceCnt, uint32(chLiveInfo.AnchorGiftValue), uint32(chLiveInfo.KnightValue),
		uint32(chLiveInfo.GameGiftValue), uint32(chLiveInfo.GameTs))
	if serr != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveStatus AddChannelLiveRecordWhenFinish serr:%v", serr)
	}

	ts := time.Unix(int64(chLiveInfo.BeginTime), 0).Format("2006-01-02 15:04:05")
	s.mysqlStore.UpdateAnchorTotalData(in.Uid, in.ChannelId, uint32(chLiveInfo.GiftValue), ts)

	s.liveMgrCache.RecordChannelLiveValue(in.Uid, uint32(chLiveInfo.BeginTime), uint32(chLiveInfo.GiftValue))
}

// 主播设置直播状态
func (s *ChannelLiveMgrServer) SetChannelLiveStatus(ctx context.Context, in *pb.SetChannelLiveStatusReq) (out *pb.SetChannelLiveStatusResp, err error) {
	log.DebugWithCtx(ctx, "SetChannelLiveStatus in:%v", in)

	out = &pb.SetChannelLiveStatusResp{}

	//直播基本信息，不容易变动的部分
	channelLiveStatus := &pb.ChannelLiveStatus{
		Uid:            in.Uid,
		Account:        in.Account,
		Nickname:       in.Nick,
		Sex:            in.Sex,
		ChannelId:      in.ChannelId,
		ChannelLiveId:  in.ChannelLiveId,
		ChannelMicList: []*pb.PkMicSpace{},
		Status:         in.Status,
		FansCnt:        int64(in.FansCnt),
		GroupFansCnt:   int64(in.GroupFansCnt),
		BeginTime:      uint32(time.Now().Unix()),
	}

	pkState := pk.PkState{ChannelID_A: in.ChannelId}
	pkInfo, err := pkState.GetPkInfo()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveStatus GetPkInfo failed in:%v err:%v", in, err)
		return
	}

	if pkInfo.TChannelID != 0 && in.Status == pb.EnumChannelLiveStatus_CLOSE {
		tmpPkStage, _ := pkState.GetPkStage()
		if tmpPkStage < pbLogic.EnumChannelLivePKStatus_PUNISH && tmpPkStage != pbLogic.EnumChannelLivePKStatus_IDLE {
			log.ErrorWithCtx(ctx, "SetChannelLiveStatus is pk in:%v pkInfo:%v", in, pkInfo)
			return out, protocol.NewExactServerError(nil, status.ErrChannelLiveCloseLimit, "正在PK，不可以离开房间喔")
		}
	}

	oldStatus := pb.EnumChannelLiveStatus_CLOSE
	oldLiveStatus, err := s.liveMgrCache.GetChannelLiveStatus(in.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveStatus GetChannelLiveStatus in:%v err:%v", in, err)
		return
	}

	if err == nil && (in.ChannelLiveId > 0 && oldLiveStatus.ChannelLiveId > in.ChannelLiveId) {
		in.ChannelLiveId = oldLiveStatus.ChannelLiveId //修复客户端传旧版channelliveID
	}

	channelLiveId := in.ChannelLiveId
	var anchorType uint32
	if err == nil && oldLiveStatus != nil && oldLiveStatus.ChannelLiveId == in.ChannelLiveId {
		oldStatus = oldLiveStatus.Status
		channelLiveStatus.BeginTime = oldLiveStatus.BeginTime
		channelLiveStatus.FansCnt = oldLiveStatus.FansCnt
		channelLiveStatus.GroupFansCnt = oldLiveStatus.GroupFansCnt
		anchorType = oldLiveStatus.GetAnchorType()
		if in.GetStatus() != pb.EnumChannelLiveStatus_CLOSE {
			channelLiveStatus.AnchorType = oldLiveStatus.GetAnchorType()
			channelLiveStatus.VirtualLiveInfo = oldLiveStatus.GetVirtualLiveInfo()
		}

	}

	//生成新的channelliveID
	if in.Status == pb.EnumChannelLiveStatus_OPEN {
		if oldStatus == pb.EnumChannelLiveStatus_CLOSE {
			channelLiveId, err = s.mgr.GenChannelLiveID(ctx)
			if err != nil {
				log.ErrorWithCtx(ctx, "SetChannelLiveStatus GenChannelLiveID err:%v channelLiveId:%v", err, channelLiveId)
				return out, err
			}
			//添加开播记录
			go func() {
				subCtx, cancel := context.WithTimeout(context.Background(), time.Second)
				defer cancel()

				err := s.mysqlStore.AddChannelLiveRecord(subCtx, in.Uid, in.ChannelId, in.GetAnchorType(), channelLiveId)
				if err != nil {
					log.Errorf("SetChannelLiveStatus AddChannelLiveRecord failed in:%v err:%v", in, err)
				}
			}()
			//清理直播数据
			s.liveMgrCache.OnChannelLiveFinish(in.ChannelId)

			anchorType = in.GetAnchorType()
			channelLiveStatus.AnchorType = in.GetAnchorType()
			channelLiveStatus.VirtualLiveInfo = &pb.VirtualLiveInfo{
				ScreenType: in.GetVirtualLiveInfo().GetScreenType(),
			}
		}
		go func() {
			err := s.liveMgrCache.UpdateChannelLiveHeartBeat(in.Uid, 0)
			if err != nil {
				log.Errorf("SetChannelLiveStatus UpdateChannelLiveHeartBeat failed in:%v err:%v", in, err)
			}
		}()
	}

	channelLiveStatus.ChannelLiveId = channelLiveId
	s.liveMgrCache.SetChannelLiveStatus(in.Uid, in.ChannelId, channelLiveStatus, channelLiveId != in.ChannelLiveId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveStatus  failed in:%v err:%v", in, err)
	}

	//匹配状态的恢复或暂停，matchFlag只有结束直播的时候删掉
	var matchValue uint32 = 0
	if in.Status == pb.EnumChannelLiveStatus_OPEN {
		matchValue, _ = s.mgr.GetPKMatchValue(in.Uid, conf.GetMatchType(uint32(time.Now().Unix())))
	}
	channelLiveStatus.PkMatchState = match.UpdateMatchState(s.liveMgrCache, in.Uid, in.ChannelId, matchValue, in.Status)

	/*
		// 记录主播操作日志(废弃）
			go func() {
				//subCtx, cancel := context.WithTimeout(context.Background(), time.Second)
				//defer cancel()
				//s.mysqlStore.AddOperLog(subCtx, in.Uid, in.ChannelId, uint32(in.Status), channelLiveId)
			}()
	*/

	out = &pb.SetChannelLiveStatusResp{ChannelLiveStatus: channelLiveStatus}
	//数据上报
	if (in.Status == pb.EnumChannelLiveStatus_CLOSE || in.Status == pb.EnumChannelLiveStatus_PAUSE) && oldStatus == pb.EnumChannelLiveStatus_OPEN {
		go func() {
			s.mgr.DataCenterReport("************", in.ChannelId, map[string]interface{}{
				"uid":       in.Uid,
				"channelId": in.ChannelId,
				"liveId":    channelLiveId,
			})
		}()
	}

	//直播结束，开始统计直播数据
	if in.Status == pb.EnumChannelLiveStatus_CLOSE {
		s.ChannelLiveFinish(ctx, in, out)
	}

	if in.Status == pb.EnumChannelLiveStatus_PAUSE || in.Status == pb.EnumChannelLiveStatus_OPEN {
		statePushMsg := &pbLogic.PkChannelLiveStatusPushMsg{
			ChannelId: in.ChannelId,
			Status:    pbLogic.EnumChannelLiveStatus(in.Status),
		}
		statePushMsgBin, _ := proto.Marshal(statePushMsg)

		err = s.aclExtLogicApi.PushChannelMsg(ctx, []uint32{in.ChannelId, pkInfo.TChannelID},
			uint32(channelpb.ChannelMsgType_CHANNEL_LIVE_PK_TARGET_CHANNEL_STATUS_MSG), statePushMsgBin)
		if err != nil {
			log.ErrorWithCtx(ctx,
				"SetChannelLiveStatus PushChannelMsg cid %d err:%v", pkInfo.TChannelID, err)
		}

		log.DebugWithCtx(ctx, "SetChannelLiveStatus push statePushMsg:%v, TChannelID:%d", statePushMsg, pkInfo.TChannelID)
	}

	if in.OrigStatus == pb.EnumChannelLiveStatus_OPEN {
		s.redisClient.Del(fmt.Sprintf("ZSET_QUEUE_MIC_APPLY_%v", in.ChannelId))
	}

	err = s.channelLiveProd.ProduceChannelLiveEvent(&pbLogic.ChannelLiveKafkaEvent{
		ChannelLiveStatus: pbLogic.EnumChannelLiveStatus(in.Status),
		AnchorUid:         in.Uid,
		ChannelId:         in.ChannelId,
		ChannelLiveId:     int64(channelLiveId),
		Ty:                pbLogic.ChannelLiveKafkaEventType_ChannelLiveType,
		CreateTime:        time.Now().Unix(),
		AnchorType:        anchorType,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveStatus ProduceChannelLiveEvent err:%v", err)
	}

	log.InfoWithCtx(ctx, "SetChannelLiveStatus out:%v in:%v oldLiveStatus:%v", out, in, oldLiveStatus)
	return out, nil
}

// 主播心跳，没有PK的情况下用户监控直播中主播的在线状态
func (s *ChannelLiveMgrServer) ChannelLiveHeartbeat(ctx context.Context, in *pb.ChannelLiveHeartbeatReq) (out *pb.ChannelLiveHeartbeatResp, err error) {
	out = &pb.ChannelLiveHeartbeatResp{}

	log.DebugWithCtx(ctx, "ChannelLiveHeartbeatS in:%v", in)

	/*	currChLiveStatus, err := s.liveMgrCache.GetChannelLiveStatus(in.Uid)

		if err != nil {
			log.ErrorWithCtx(ctx,"ChannelLiveHeartbeatSX GetChannelLiveStatus err:%v in:%v", err, in)
			return out, err
		}*/

	/*	out.IsChange = false
		out.ChannelStatus = &pb.ChannelLiveStatus{
			Uid:            in.Uid,
			Account:        in.Account,
			ChannelId:      in.ChannelId,
			ChannelLiveId:  in.ChannelLiveId,
			ChannelMicList: make([]*pb.PkMicSpace, 0),
			Status:         currChLiveStatus.Status,
		}*/

	//定时更新数据
	nowTs := time.Now().Unix()
	lastHeartTs := GetHeartBeat(in.ChannelId)

	if (nowTs - lastHeartTs) <= 2 {
		return out, nil
	}

	err = s.liveMgrCache.UpdateChannelLiveHeartBeat(in.Uid, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelLiveHeartbeatSX UpdateChannelLiveHeartBeat err:%v in:%v", err, in)
	}

	if (nowTs - lastHeartTs) >= 30 {
		SetHeartBeat(in.ChannelId, nowTs)
		chLiveInfo := s.liveMgrCache.GetChannelLiveData(in.ChannelId)

		go func() {

			sta, err := s.liveMgrCache.GetChannelLiveStatus(in.Uid)
			if nil == err && sta.ChannelLiveId > in.ChannelLiveId { //修复客户端传旧版channelliveID
				in.ChannelLiveId = sta.ChannelLiveId
			}

			s.mysqlStore.AddChannelLiveRecordWhenFinish(context.Background(), in.ChannelLiveId, uint32(chLiveInfo.GiftValue), 0, 0,
				chLiveInfo.AudienceCnt, chLiveInfo.SendGiftAudienceCnt, uint32(chLiveInfo.AnchorGiftValue), uint32(chLiveInfo.KnightValue),
				uint32(chLiveInfo.GameGiftValue), uint32(chLiveInfo.GameTs))
		}()
	}

	log.DebugWithCtx(ctx, "ChannelLiveHeartbeatS out:%v in:%v", out, in)

	return out, nil
}

// 包括PK信息
func (s *ChannelLiveMgrServer) GetChannelLiveStatus(ctx context.Context, in *pb.GetChannelLiveStatusReq) (out *pb.GetChannelLiveStatusResp, err error) {

	log.DebugWithCtx(ctx, "GetChannelLiveStatus in:%v", in)

	//先读取缓存
	ekey := fmt.Sprintf("get_channel_live_status_%v", in.Uid)
	if !in.IgnoreMemCache {
		eVal, ok := easyCache.Get(ekey)
		if ok && eVal != nil {
			resp := eVal.(*pb.GetChannelLiveStatusResp)
			if resp != nil {
				log.DebugWithCtx(ctx, "GetChannelLiveStatus cache out:%v", resp)
				return resp, nil
			}
		}
	}

	out = &pb.GetChannelLiveStatusResp{}

	chLiveStatus, err := s.liveMgrCache.GetChannelLiveStatus(in.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveStatus fail err:%v", err)
	}

	pkstate := pk.PkState{
		ChannelID_A: in.ChannelId,
		ChannelID_B: 0,
	}

	pkstatus, _ := pkstate.GetPkStage()
	chLiveStatus.PkStatus = pb.EnumChannelLivePKStatus(pkstatus)
	matchFlag := match.GetMatchFlag(s.liveMgrCache, in.Uid)
	chLiveStatus.PkMatchState = matchFlag

	opCidMap := s.liveMgrCache.BatchGetOpPkChannelID([]uint32{in.GetChannelId()})
	if opCidMap != nil && opCidMap[in.GetChannelId()] != 0 {
		chLiveStatus.PkChannelId = opCidMap[in.GetChannelId()]
	}

	// 查询多人pk状态
	if chLiveStatus.PkStatus == pb.EnumChannelLivePKStatus_IDLE {
		resp, err := s.channeLivePkCli.BatCheckIsInMultiPk(ctx, &channellivepkPB.BatCheckIsInMultiPkReq{
			UidList: []uint32{in.GetUid()},
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelLiveStatus BatCheckIsInMultiPk fail in:%v err:%v", in, err)
		}

		if resp.GetMapIdIspk()[in.GetUid()] {
			chLiveStatus.PkStatus = pb.EnumChannelLivePKStatus_BEGIN
		}
	}
	out.ChannelLiveInfo = &pb.ChannelLiveStatusInfo{
		ChannelLiveStatus: chLiveStatus,
	}

	//缓存
	easyCache.Set(ekey, out, conf.GetLiveCacheTs())

	log.DebugWithCtx(ctx, "GetChannelLiveStatus out:%v", out)

	return out, nil
}

func (s *ChannelLiveMgrServer) BatchGetChannelLiveStatusSimple(ctx context.Context, in *pb.BatchGetChannelLiveStatusSimpleReq) (out *pb.BatchGetChannelLiveStatusSimpleResp, err error) {
	out = &pb.BatchGetChannelLiveStatusSimpleResp{ChannelLiveStatusList: make([]*pb.ChannelLiveStatusSimple, 0)}

	if len(in.ChannelList) > 0 {
		channelLiveStatusList, err := s.liveMgrCache.GetChannelLiveStatusSimple(in.ChannelList, true)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGetChannelLiveStatusSimple GetChannelLiveStatusSimple failed len:%d err:%v in:%v", len(in.GetChannelList()), err, in)
			return out, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
		}

		livePkResp, err := s.channeLivePkCli.BatCheckIsInMultiPk(ctx, &channellivepkPB.BatCheckIsInMultiPkReq{
			CidList:      in.GetChannelList(),
			IsNeedPkInfo: true,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGetChannelLiveStatusSimple BatCheckIsInMultiPk failed len:%d err:%v in:%v", len(in.GetChannelList()), err, in)
			return out, err
		}

		opCidMap := s.liveMgrCache.BatchGetOpPkChannelID(in.ChannelList)
		for _, c := range channelLiveStatusList {
			c.PkChannelIdList = make([]uint32, 0)
			c.PkChannelId = opCidMap[c.ChannelId]
			if c.PkChannelId != 0 {
				log.DebugWithCtx(ctx, "BatchGetChannelLiveStatusSimple opCidMap:%v", opCidMap)
				c.PkChannelIdList = append(c.PkChannelIdList, c.PkChannelId)
			}
			if c.GetPkChannelId() != 0 {
				c.PkType = uint32(pb.EChannelPkType_ENUM_PK_TYPE_SINGLE)
			}
			multiPkSimpleInfo := livePkResp.GetMapCidInfo()[c.GetChannelId()]
			if c.GetPkChannelId() == 0 && livePkResp.GetMapIdIspk()[c.GetChannelId()] && multiPkSimpleInfo != nil {
				c.PkType = func(pkType uint32) uint32 {
					switch channellivepkPB.MultiPkType(pkType) {
					case channellivepkPB.MultiPkType_MULTI_PK_TYPE_UNSPECIFIED:
						// 连麦阶段还没选择模式，默认是1v1
						return uint32(pb.EChannelPkType_ENUM_PK_TYPE_MULTI_1V1)
					case channellivepkPB.MultiPkType_MULTI_PK_TYPE_SINGLE:
						return uint32(pb.EChannelPkType_ENUM_PK_TYPE_MULTI_1V1)
					case channellivepkPB.MultiPkType_MULTI_PK_TYPE_TEAM:
						return uint32(pb.EChannelPkType_ENUM_PK_TYPE_MULTI_2V2)
					default:
						return 0
					}
				}(multiPkSimpleInfo.GetPkType())
				// 在多人pk
				for _, cid := range multiPkSimpleInfo.GetChannelIdList() {
					if cid != c.GetChannelId() {
						// 随便取一个对手
						c.PkChannelId = cid
						break
					}
				}
				for _, cid := range multiPkSimpleInfo.GetChannelIdList() {
					if cid != c.GetChannelId() {
						c.PkChannelIdList = append(c.PkChannelIdList, cid)
					}
				}
			}
			out.ChannelLiveStatusList = append(out.ChannelLiveStatusList, c)
		}

	}

	log.DebugWithCtx(ctx, "BatchGetChannelLiveStatusSimple end in:%v out:%v", in, out)
	return out, err
}

// 批量取直播状态
func (s *ChannelLiveMgrServer) BatchGetChannelLiveStatus(ctx context.Context, in *pb.BatchGetChannelLiveStatusReq) (out *pb.BatchGetChannelLiveStatusResp, err error) {
	out = &pb.BatchGetChannelLiveStatusResp{
		ChannelLiveInfoList: make([]*pb.ChannelLiveStatusInfo, 0),
	}

	log.DebugWithCtx(ctx, "BatchGetChannelLiveStatus in:%v", in)

	if len(in.UidList) == 0 && len(in.ChannelList) == 0 {
		return out, nil
	}

	//不支持channelID查询
	if len(in.ChannelList) > 0 || len(in.UidList) > 128 {
		log.ErrorWithCtx(ctx, "BatchGetChannelLiveStatus invalid para %v %v", len(in.UidList), len(in.ChannelList))
		return out, errors.New("invalid para")
	}

	//先筛选出有直播权限的UID列表
	mapNoAuthUid := make(map[uint32]bool)
	ableUidList, lackUidList := make([]uint32, 0, 128), make([]uint32, 0, 128)
	for _, uid := range in.UidList {
		//AuthFlagKey(pb.EnumIDType_USER_ID, uid) 缓存没有权限的UID列表
		v, ok := easyCacheFlag.Get(AuthFlagKey(pb.EnumIDType_USER_ID, uid))
		if ok && v != nil {
			mapNoAuthUid[uid] = true
		} else {
			lackUidList = append(lackUidList, uid)
		}
	}

	//不在内存缓存的查Redis
	authKey := make([]string, 0, len(lackUidList))
	for _, uid := range lackUidList {
		authKey = append(authKey, AuthFlagKey(pb.EnumIDType_USER_ID, uid))
	}

	items, err := s.liveMgrCache.RedisFlagClient.MGet(authKey...).Result()
	if nil == err && items != nil {
		for index, uid := range lackUidList {
			if index > len(items) {
				continue
			}
			if s, ok := items[index].(string); ok {
				if s == "1" { //缓存没有权限列表
					easyCacheFlag.Set(AuthFlagKey(pb.EnumIDType_USER_ID, uid), s, conf.GetLiveCacheTs()*100)
					mapNoAuthUid[uid] = true
				}
			}
		}
	}

	for _, uid := range in.UidList {
		if mapNoAuthUid[uid] {
			continue
		}
		ableUidList = append(ableUidList, uid)
	}

	mapChannelLiveStatus, err := s.liveMgrCache.BatchGetChannelLiveStatus(ableUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetChannelLiveStatus BatchGetChannelLiveStatus failed in:%v err:%v",
			in, err)
	}

	cidList := make([]uint32, 0)
	for _, liveStatus := range mapChannelLiveStatus {
		cidList = append(cidList, liveStatus.GetChannelId())
	}

	livePkResp, err := s.channeLivePkCli.BatCheckIsInMultiPk(ctx, &channellivepkPB.BatCheckIsInMultiPkReq{
		CidList:      cidList,
		IsNeedPkInfo: true,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetChannelLiveStatus BatCheckIsInMultiPk fail in:%v err:%v", in, err)
	}

	for _, uid := range in.UidList {
		chLiveStatus, ok := mapChannelLiveStatus[uid]
		if !ok {
			continue
		}

		chLiveStatusInfo := &pb.ChannelLiveStatusInfo{}
		if chLiveStatus.Status != pb.EnumChannelLiveStatus_CLOSE {
			pkstate := pk.PkState{ChannelID_A: chLiveStatus.ChannelId}
			pks, _ := pkstate.GetPkStage()
			chLiveStatus.PkStatus = pb.EnumChannelLivePKStatus(pks)
			if chLiveStatus.GetPkStatus() != pb.EnumChannelLivePKStatus_IDLE {
				chLiveStatus.PkType = uint32(pb.EChannelPkType_ENUM_PK_TYPE_SINGLE)
			}

			if chLiveStatus.GetPkStatus() == pb.EnumChannelLivePKStatus_IDLE && livePkResp.GetMapIdIspk()[chLiveStatus.GetChannelId()] {
				chLiveStatus.PkStatus = pb.EnumChannelLivePKStatus_BEGIN
				multiPkSimpleInfo := livePkResp.GetMapCidInfo()[chLiveStatus.GetChannelId()]
				chLiveStatus.PkType = func(pkType uint32) uint32 {
					switch channellivepkPB.MultiPkType(pkType) {
					case channellivepkPB.MultiPkType_MULTI_PK_TYPE_UNSPECIFIED:
						// 连麦阶段还没选择模式，默认是1v1
						return uint32(pb.EChannelPkType_ENUM_PK_TYPE_MULTI_1V1)
					case channellivepkPB.MultiPkType_MULTI_PK_TYPE_SINGLE:
						return uint32(pb.EChannelPkType_ENUM_PK_TYPE_MULTI_1V1)
					case channellivepkPB.MultiPkType_MULTI_PK_TYPE_TEAM:
						return uint32(pb.EChannelPkType_ENUM_PK_TYPE_MULTI_2V2)
					default:
						return 0
					}
				}(multiPkSimpleInfo.GetPkType())
			}
		}

		chLiveStatusInfo.ChannelLiveStatus = chLiveStatus
		out.ChannelLiveInfoList = append(out.ChannelLiveInfoList, chLiveStatusInfo)
	}

	log.DebugWithCtx(ctx, "BatchGetChannelLiveStatus in:%v out:%v", in, out)
	return out, nil
}

// 获取我的道具
func (s *ChannelLiveMgrServer) GetMyToolList(ctx context.Context, in *pb.GetMyToolListReq) (out *pb.GetMyToolListResp, err error) {
	out = &pb.GetMyToolListResp{
		Items: make([]*pb.ToolItem, 0),
	}

	log.DebugWithCtx(ctx, "GetMyToolList in:%v", in)

	pkstate := pk.PkState{
		ChannelID_A: in.ChannelId,
		ChannelID_B: 0,
	}

	pkinfo, serr := pkstate.GetPkInfo()
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetMyToolList GetPkInfo in:%v err:%v", in, serr)
	}

	if serr != nil {
		return out, nil
	}

	if pkinfo.TChannelID == 0 {
		log.DebugWithCtx(ctx, "GetMyToolList in:%v TChannelID==0", in)
		return out, nil
	}

	items, err := s.liveMgrCache.GetUserItemList(in.ChannelId, pkinfo.FBeginTime, in.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMyToolList GetUserItemList in:%v err:%v", in, err)
	}

	out.Items = append(out.Items, items...)

	log.DebugWithCtx(ctx, "GetMyToolList in:%v out:%v", in, out)

	return out, nil
}

func (s *ChannelLiveMgrServer) GetItemConfig(ctx context.Context, in *pb.GetItemConfigReq) (out *pb.GetItemConfigResp, err error) {
	out = &pb.GetItemConfigResp{
		ItemConfList: make([]*pb.ItemConfig, 0),
	}

	bAll := in.ItemIdList == nil || len(in.ItemIdList) == 0
	mapItem := make(map[string]bool)
	if !bAll {
		for _, itemId := range in.ItemIdList {
			mapItem[itemId] = true
		}
	}
	//TODO fill
	for _, item := range s.sc.ItemConfigList {
		if !(bAll || mapItem[item.ItemId]) {
			continue
		}
		itemConf := &pb.ItemConfig{
			ItemId:          item.ItemId,
			Desc:            item.Desc,
			Icon:            item.Icon,
			EffectUrl:       item.EffectUrl,
			TargetEffectUrl: item.TargetEffectUrl,
			Msg:             item.UseMsg,
			TargetMsg:       item.TargetMsg,
			GainMsg:         item.GainMsg,
			Ty:              pb.ItemType(item.Type),
			Value:           item.Value,
			Name:            item.Name,
			Version:         0,
			MilestoneList:   nil,
		}
		if itemConf.Ty == pb.ItemType_Effect_Type {
			itemConf.MilestoneList = make([]*pb.EffectItemMilestone, 0)
			map2per := s.sc.GetCnt2Dec()
			for cnt, per := range map2per {
				itemConf.MilestoneList = append(itemConf.MilestoneList, &pb.EffectItemMilestone{
					Count:   cnt,
					Percent: per,
				})
			}
		}
		out.ItemConfList = append(out.ItemConfList, itemConf)
	}

	log.DebugWithCtx(ctx, "GetItemConfig out:%v", out)

	return out, nil
}

// PK送礼列表
func (s *ChannelLiveMgrServer) GetChanneLivePkRankUser(ctx context.Context, in *pb.GetChanneLivePkRankUserReq) (out *pb.GetChanneLivePkRankUserResp, err error) {
	out = &pb.GetChanneLivePkRankUserResp{
		UserList: make([]*pb.SendGiftUserInfo, 0),
	}

	log.DebugWithCtx(ctx, "GetChanneLivePkRankUser in:%v", in)

	pkstate := pk.PkState{
		ChannelID_A: in.ChannelId,
		ChannelID_B: 0,
	}

	pkInfo, _ := pkstate.GetPkInfo()
	pkstate.ChannelID_B = pkInfo.TChannelID
	firstStrUidInfo, cid := pkstate.GetFirstShootUid()
	firstUidInfo := s.mgr.ParseStrUidInfo(cid, firstStrUidInfo)
	if cid != in.ChannelId {
		firstUidInfo.Uid = 0
	}

	ranks, serr := pkstate.GetPkRank(int64(pkInfo.FBeginTime), int64(in.Off), int64(in.Cnt))
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetChanneLivePkRankUser serr:%v", serr)
		return out, serr
	}

	out.AnchorUid = pkInfo.MUID
	out.ChannelId = in.ChannelId

	for _, k := range ranks {
		userInfo := s.mgr.ParseStrUidInfo(in.GetChannelId(), k.StrUidInfo)
		if userInfo.GetUid() != 0 {
			out.UserList = append(out.UserList, &pb.SendGiftUserInfo{
				Uid:       userInfo.GetUid(),
				Score:     k.Score,
				FirstKill: firstUidInfo.GetUid() == userInfo.GetUid() && firstUidInfo.GetAccount() == userInfo.GetAccount(),
				UkwInfo:   userInfo,
			})
		}
	}

	log.DebugWithCtx(ctx, "GetChanneLivePkRankUser in:%v out:%v", in, out)

	return out, nil
}

// applyPkOptimized 优化版本：并发检查，减少Redis操作
func (s *ChannelLiveMgrServer) applyPkOptimized(ctx context.Context, in *pb.ApplyPkReq) (out *pb.ApplyPkResp, err error) {
	out = &pb.ApplyPkResp{}

	log.InfoWithCtx(ctx, "applyPkOptimized in:%v", in)

	// 并发执行独立的检查项，提升性能
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make([]error, 0)

	// 1. 并发检查PK次数限制
	wg.Add(1)
	go func() {
		defer wg.Done()
		mapUid2Challenger := map[uint32]bool{
			in.Uid:       true,
			in.TargetUid: false,
		}
		if err := s.mgr.BatchCheckIsPkDayRangeTimeLimit(mapUid2Challenger); err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
		}
	}()

	// 2. 并发检查是否已申请（使用优化的快速检查）
	wg.Add(1)
	go func() {
		defer wg.Done()
		applied, err := s.liveMgrCache.HasApplied(in.Uid, in.TargetChannelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "applyPkOptimized HasApplied check failed in:%v err:%v", in, err)
			// 检查失败时降级到原有逻辑
			return
		}
		if applied {
			mu.Lock()
			errors = append(errors, protocol.NewExactServerError(nil, status.ErrChannelLivePkRepeatedApply, "不能重复发起PK申请"))
			mu.Unlock()
		}
	}()

	wg.Wait()

	// 检查是否有错误
	if len(errors) > 0 {
		return out, errors[0]
	}

	// 继续优化后的流程...
	return s.applyPkOptimizedContinue(ctx, in)
}

// 发起PK申请（原有版本，保留兼容性）
func (s *ChannelLiveMgrServer) ApplyPk(ctx context.Context, in *pb.ApplyPkReq) (out *pb.ApplyPkResp, err error) {
	// 优先使用优化版本
	return s.applyPkOptimized(ctx, in)
}

// applyPkLegacy 原有的ApplyPk逻辑，作为降级方案
func (s *ChannelLiveMgrServer) applyPkLegacy(ctx context.Context, in *pb.ApplyPkReq) (out *pb.ApplyPkResp, err error) {
	out = &pb.ApplyPkResp{}

	log.InfoWithCtx(ctx, "applyPkLegacy fallback in:%v", in)

	// 检查申请方的PK次数限制（20:00-22:00期间限制）
	if err := s.mgr.CheckIsPkDayRangeTimeLimit(in.Uid, true); nil != err {
		log.ErrorWithCtx(ctx, "applyPkLegacy uid req:%v", in)
		return out, err
	}

	// 检查目标主播的PK次数限制（20:00-22:00期间限制）
	if err := s.mgr.CheckIsPkDayRangeTimeLimit(in.TargetUid, false); nil != err {
		log.ErrorWithCtx(ctx, "applyPkLegacy target req:%v", in)
		return out, err
	}

	// 获取当前时间戳，用于检查预约PK时间冲突
	nowTs := uint32(time.Now().Unix())

	// 获取申请方的预约PK信息列表
	pkInfoList, err := s.liveMgrCache.GetAnchorAppointPkInfoList(in.GetUid(), nowTs)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk GetAnchorAppointPkInfoList failed in:%v err:%v", in, err)
		return out, err
	}

	// 获取目标主播的预约PK信息列表
	targetPkInfoList, err := s.liveMgrCache.GetAnchorAppointPkInfoList(in.GetTargetUid(), nowTs)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk GetAnchorAppointPkInfoList failed in:%v err:%v", in, err)
		return out, err
	}

	// 检查申请方是否在预约PK时间段内，如果是则不允许发起普通PK申请
	for _, pkInfo := range pkInfoList {
		log.DebugWithCtx(ctx, "ApplyPk in:%v Info:%v ts:%d", in, pkInfo, nowTs)
		// 判断当前时间是否在预约PK的时间范围内
		if nowTs >= pkInfo.BeginTs && nowTs <= pkInfo.EndTs {
			// 检查预约PK是否真实存在（防止缓存脏数据）
			isExist, err := s.liveMgrCache.CheckAppointPkInfoIsExist(pkInfo.AppointId)
			if err != nil {
				log.ErrorWithCtx(ctx, "ApplyPk CheckAppointPkInfoIsExist failed in:%v Info:%v err:%v", in, pkInfo, err)
				continue
			}
			if isExist {
				log.ErrorWithCtx(ctx, "ApplyPk anchor is in appoint ok in:%v pkInfo:%v", in, pkInfo)
				// 申请方正在预约PK时间段内，不允许发起普通PK
				return out, protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoLaunchPk)
			}
		}
	}

	// 检查目标主播是否在预约PK时间段内，如果是则不允许接受普通PK申请
	for _, pkInfo := range targetPkInfoList {
		log.DebugWithCtx(ctx, "ApplyPk in:%v Info:%v ts:%d", in, pkInfo, nowTs)
		// 判断当前时间是否在预约PK的时间范围内
		if nowTs >= pkInfo.BeginTs && nowTs <= pkInfo.EndTs {
			// 检查预约PK是否真实存在（防止缓存脏数据）
			isExist, err := s.liveMgrCache.CheckAppointPkInfoIsExist(pkInfo.AppointId)
			if err != nil {
				log.ErrorWithCtx(ctx, "ApplyPk CheckAppointPkInfoIsExist failed in:%v Info:%v err:%v", in, pkInfo, err)
				continue
			}
			if isExist {
				log.ErrorWithCtx(ctx, "ApplyPk anchor is in appoint ok in:%v pkInfo:%v", in, pkInfo)
				// 目标主播正在预约PK时间段内，不允许接受普通PK申请
				return out, protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoAcceptPk)
			}

		}
	}

	// 检查发起方是否在指定PK开始前10分钟内（避免主播在即将到指定PK场次时还有正在对战的PK）
	mapComingPkUser, err := s.checkUpcomeingAppointPkTs(ctx, []uint32{in.GetUid(), in.GetTargetUid()})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk checkUpcomeingAppointPkTs failed in:%v err:%v", in, err)
		return out, err
	}

	if _, ok := mapComingPkUser[in.GetUid()]; ok {
		log.ErrorWithCtx(ctx, "ApplyPk anchor has upcoming appoint pk within 10 minutes in:%v", in)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoLaunchPk, "即将有指定PK场次，不能发起PK")
	}

	// 检查目标方是否在指定PK开始前10分钟内
	if _, ok := mapComingPkUser[in.GetTargetUid()]; ok {
		log.ErrorWithCtx(ctx, "ApplyPk target anchor has upcoming appoint pk within 10 minutes in:%v", in)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoAcceptPk, "对方即将有一场指定PK场次，不能发起PK")
	}

	targetChLiveStatus, err := s.liveMgrCache.GetChannelLiveStatus(in.TargetUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk GetChannelLiveStatus fail err:%v in:%v", err, in)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveNotOpen)
	}

	// 检查目标主播是否有有效的频道ID
	if targetChLiveStatus.ChannelId == 0 {
		log.ErrorWithCtx(ctx, "ApplyPk ChannelId==0 in:%v", in)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveNotOpen)
	}

	// 检查目标主播的直播状态是否为开启状态
	if targetChLiveStatus.Status == pb.EnumChannelLiveStatus_CLOSE {
		log.ErrorWithCtx(ctx, "ApplyPk Status==close in:%v", in)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveNotOpen)
	}

	// 优化：批量检查双方PK状态，减少Redis操作次数
	pkStatusMap, err := pk.BatchGetPKBasicInfo([]uint32{in.ChannelId, in.TargetChannelId})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk BatchGetPKBasicInfo fail err:%v in:%v", err, in)
		return out, err
	}

	// 检查申请方是否正在PK中
	if pkInfo, exists := pkStatusMap[in.ChannelId]; exists && pkInfo.TChannelID != 0 {
		log.ErrorWithCtx(ctx, "ApplyPk applicant pking in:%v targetCid:%v", in, pkInfo.TChannelID)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLivePkIng, "正在PK")
	}

	// 检查目标主播是否正在PK中
	if pkInfo, exists := pkStatusMap[in.TargetChannelId]; exists && pkInfo.TChannelID != 0 {
		log.ErrorWithCtx(ctx, "ApplyPk target pking in:%v targetCid:%v", in, pkInfo.TChannelID)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLivePkIng, "对方正在PK")
	}

	// 如果是PK活动榜中的主播，需要进行QPS限制检查，防止频繁申请
	if conf.IsPkActivityAnchor(in.Uid) {
		if !s.liveMgrCache.CheckPkApplyQPSLimit(in.Uid, in.TargetUid) {
			log.ErrorWithCtx(ctx, "ApplyPk CheckQPSLimic in:%v", in)
			return out, protocol.NewExactServerError(nil, status.ErrChannelLivePkRepeatedApply, "PK申请频率太频繁")
		}
	}

	// 获取目标频道的PK申请列表，检查是否存在重复申请
	applys, err := s.liveMgrCache.GetApplyList(in.TargetChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk GetApplyList failed in:%v err:%v", in, err)
	}

	// 遍历申请列表，检查当前用户是否已经向目标主播发起过PK申请
	for _, apply := range applys {
		if apply.Uid == in.Uid {
			log.ErrorWithCtx(ctx, "ApplyPk repeated in:%v", in)
			return out, protocol.NewExactServerError(nil, status.ErrChannelLivePkRepeatedApply, "不能重复发起PK申请")
		}
	}

	// 生成全局唯一的PK申请ID
	applyId := s.liveMgrCache.GenApplyId()

	// 获取当前时间戳作为申请时间
	ts := time.Now().Unix()

	// 将PK申请信息存储到缓存中
	err = s.liveMgrCache.ApplyPk(in.Uid, in.ChannelId, uint32(in.ChannelLiveId), in.TargetChannelId, applyId, ts)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk cache repeated in:%v", in)
		return out, err
	}

	// 发布PK申请事件到Kafka，用于通知其他服务（如推送服务）
	s.pkApplyProd.ProdPkApplyEvent(&pbLogic.PkApplyKafkaEvent{
		ApplyUid:  in.Uid,
		TargetUid: in.TargetUid,
		EventTy:   pbLogic.EnumApply_apply,
		ApplyTime: uint32(ts),
		ApplyId:   applyId,
	})

	log.DebugWithCtx(ctx, "ApplyPkS in:%v out:%v", in, out)

	return out, nil
}

// applyPkOptimizedContinue 优化版本的后续流程
func (s *ChannelLiveMgrServer) applyPkOptimizedContinue(ctx context.Context, in *pb.ApplyPkReq) (out *pb.ApplyPkResp, err error) {
	out = &pb.ApplyPkResp{}

	// 获取当前时间戳，用于检查预约PK时间冲突
	nowTs := uint32(time.Now().Unix())

	// 并发获取预约PK信息和其他检查
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make([]error, 0)

	var bothAppointPkInfoList []*cache.AnchorAppointPkInfo
	var targetChLiveStatus *pb.ChannelLiveStatus

	// 1. 获取双方的预约PK信息
	wg.Add(1)
	go func() {
		defer wg.Done()
		startTs := nowTs - 600     // 10分钟前
		finishTs := nowTs + 3600*5 // 5小时后
		list, err := s.liveMgrCache.GetAnchorAppointPkInfoListByTime([]uint32{in.GetUid(), in.GetTargetUid()},
			startTs, finishTs)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		bothAppointPkInfoList = list
	}()

	/*
		var myAppointPkInfoList []*cache.AnchorAppointPkInfo
		var targetAppointPkInfoList []*cache.AnchorAppointPkInfo
		// 1. 并发获取申请方的预约PK信息
		wg.Add(1)
		go func() {
			defer wg.Done()
			list, err := s.liveMgrCache.GetAnchorAppointPkInfoList(in.GetUid(), nowTs)
			if err != nil {
				mu.Lock()
				errors = append(errors, err)
				mu.Unlock()
				return
			}
			myAppointPkInfoList = list
		}()

		// 2. 并发获取目标主播的预约PK信息
		wg.Add(1)
		go func() {
			defer wg.Done()
			list, err := s.liveMgrCache.GetAnchorAppointPkInfoList(in.GetTargetUid(), nowTs)
			if err != nil {
				mu.Lock()
				errors = append(errors, err)
				mu.Unlock()
				return
			}
			targetAppointPkInfoList = list
		}()
	*/

	// 3. 并发获取目标主播直播状态
	wg.Add(1)
	go func() {
		defer wg.Done()
		liveStatus, err := s.liveMgrCache.GetChannelLiveStatus(in.TargetUid)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		targetChLiveStatus = liveStatus
	}()

	// 4. 并发检查PK状态（已在前面完成）
	var pkStatusMap map[uint32]*pk.PKBasicInfo
	wg.Add(1)
	go func() {
		defer wg.Done()
		statusMap, err := pk.BatchGetPKBasicInfo([]uint32{in.ChannelId, in.TargetChannelId})
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		pkStatusMap = statusMap
	}()

	wg.Wait()

	// 检查是否有错误
	if len(errors) > 0 {
		log.ErrorWithCtx(ctx, "applyPkOptimizedContinue concurrent checks failed in:%v err:%v", in, errors[0])
		return out, errors[0]
	}

	// 检查申请方是否正在PK中
	if pkInfo, exists := pkStatusMap[in.ChannelId]; exists && pkInfo.TChannelID != 0 {
		log.ErrorWithCtx(ctx, "applyPkOptimizedContinue applicant pking in:%v targetCid:%v", in, pkInfo.TChannelID)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLivePkIng, "正在PK")
	}

	// 检查目标主播是否正在PK中
	if pkInfo, exists := pkStatusMap[in.TargetChannelId]; exists && pkInfo.TChannelID != 0 {
		log.ErrorWithCtx(ctx, "applyPkOptimizedContinue target pking in:%v targetCid:%v", in, pkInfo.TChannelID)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLivePkIng, "对方正在PK")
	}

	// 检查目标主播直播状态
	if targetChLiveStatus.ChannelId == 0 {
		log.ErrorWithCtx(ctx, "applyPkOptimizedContinue ChannelId==0 in:%v", in)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveNotOpen)
	}

	if targetChLiveStatus.Status == pb.EnumChannelLiveStatus_CLOSE {
		log.ErrorWithCtx(ctx, "applyPkOptimizedContinue Status==close in:%v", in)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveNotOpen)
	}

	// 检查预约PK时间冲突（双方）
	for _, pkInfo := range bothAppointPkInfoList {
		if nowTs >= pkInfo.BeginTs && nowTs <= pkInfo.EndTs {
			isExist, err := s.liveMgrCache.CheckAppointPkInfoIsExist(pkInfo.AppointId)
			if err != nil {
				log.ErrorWithCtx(ctx, "applyPkOptimizedContinue CheckAppointPkInfoIsExist failed in:%v Info:%v err:%v", in, pkInfo, err)
				continue
			}
			if isExist {
				if pkInfo.Uid == in.Uid {
					log.ErrorWithCtx(ctx, "applyPkOptimizedContinue anchor is in appoint pk in:%v pkInfo:%v", in, pkInfo)
					return out, protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoLaunchPk)
				} else if pkInfo.Uid == in.TargetUid {
					log.ErrorWithCtx(ctx, "applyPkOptimizedContinue target anchor is in appoint pk in:%v pkInfo:%v", in, pkInfo)
					return out, protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoAcceptPk)
				}

			}
		}
	}

	/*
		// 检查预约PK时间冲突（申请方）
		for _, pkInfo := range myAppointPkInfoList {
			if nowTs >= pkInfo.BeginTs && nowTs <= pkInfo.EndTs {
				isExist, err := s.liveMgrCache.CheckAppointPkInfoIsExist(pkInfo.AppointId)
				if err != nil {
					log.ErrorWithCtx(ctx, "applyPkOptimizedContinue CheckAppointPkInfoIsExist failed in:%v Info:%v err:%v", in, pkInfo, err)
					continue
				}
				if isExist {
					log.ErrorWithCtx(ctx, "applyPkOptimizedContinue anchor is in appoint pk in:%v pkInfo:%v", in, pkInfo)
					return out, protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoLaunchPk)
				}
			}
		}

		// 检查预约PK时间冲突（目标方）
		for _, pkInfo := range targetAppointPkInfoList {
			if nowTs >= pkInfo.BeginTs && nowTs <= pkInfo.EndTs {
				isExist, err := s.liveMgrCache.CheckAppointPkInfoIsExist(pkInfo.AppointId)
				if err != nil {
					log.ErrorWithCtx(ctx, "applyPkOptimizedContinue CheckAppointPkInfoIsExist failed in:%v Info:%v err:%v", in, pkInfo, err)
					continue
				}
				if isExist {
					log.ErrorWithCtx(ctx, "applyPkOptimizedContinue target anchor is in appoint pk in:%v pkInfo:%v", in, pkInfo)
					return out, protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoAcceptPk)
				}
			}
		}
	*/

	// 检查即将到来的预约PK
	mapComingPkUser, err := s.checkUpcomeingAppointPkTs(ctx, []uint32{in.GetUid(), in.GetTargetUid()})
	if err != nil {
		log.ErrorWithCtx(ctx, "applyPkOptimizedContinue checkUpcomeingAppointPkTs failed in:%v err:%v", in, err)
		return out, err
	}

	if _, ok := mapComingPkUser[in.GetUid()]; ok {
		log.ErrorWithCtx(ctx, "applyPkOptimizedContinue anchor has upcoming appoint pk within 10 minutes in:%v", in)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoLaunchPk, "即将有指定PK场次，不能发起PK")
	}

	if _, ok := mapComingPkUser[in.GetTargetUid()]; ok {
		log.ErrorWithCtx(ctx, "applyPkOptimizedContinue target anchor has upcoming appoint pk within 10 minutes in:%v", in)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoAcceptPk, "对方即将有一场指定PK场次，不能发起PK")
	}

	// QPS限制检查
	if conf.IsPkActivityAnchor(in.Uid) {
		if !s.liveMgrCache.CheckPkApplyQPSLimit(in.Uid, in.TargetUid) {
			log.ErrorWithCtx(ctx, "applyPkOptimizedContinue CheckPkApplyQPSLimit in:%v", in)
			return out, protocol.NewExactServerError(nil,
				status.ErrChannelLivePkRepeatedApply, "PK申请频率太频繁")
		}
	}

	// 生成申请ID和时间戳
	applyId := s.liveMgrCache.GenApplyId()
	ts := time.Now().Unix()

	// 使用优化的ApplyPk方法
	err = s.liveMgrCache.ApplyPkOptimized(in.Uid, in.ChannelId,
		uint32(in.ChannelLiveId), in.TargetChannelId, applyId, ts)
	if err != nil {
		log.ErrorWithCtx(ctx, "applyPkOptimizedContinue applyPkOptimized failed in:%v err:%v", in, err)
		return out, err
	}

	// 发布PK申请事件到Kafka
	s.pkApplyProd.ProdPkApplyEvent(&pbLogic.PkApplyKafkaEvent{
		ApplyUid:  in.Uid,
		TargetUid: in.TargetUid,
		EventTy:   pbLogic.EnumApply_apply,
		ApplyTime: uint32(ts),
		ApplyId:   applyId,
	})

	log.InfoWithCtx(ctx, "applyPkOptimizedContinue success in:%v applyId:%v", in, applyId)

	return out, nil
}

// StartPk 启动PK的核心逻辑（优化版本）
// 第一代PK接口：处理PK开始的所有逻辑，包括状态检查、PK创建、消息推送等
func (s *ChannelLiveMgrServer) StartPk(ctx context.Context, uida, uidb, channelIda, channelIdb, scoreA, scoreB uint32, source, appointId int) (uint32, error) {

	log.DebugWithCtx(ctx, "StartPk uida:%v uidb:%v channelIda:%v channelIdb:%v source:%v", uida, uidb, channelIda, channelIdb, source)

	// 优化：批量检查双方PK状态，减少Redis操作次数
	pkStatusMap, err := pk.BatchGetPKBasicInfo([]uint32{channelIda, channelIdb})
	if err != nil {
		log.ErrorWithCtx(ctx, "StartPk BatchGetPKBasicInfo failed err:%v", err)
		// 降级到原有逻辑
		return s.StartPkLegacy(ctx, uida, uidb, channelIda, channelIdb, scoreA, scoreB, source, appointId)
	}

	// 检查主播A是否正在进行PK
	if pkInfoA, exists := pkStatusMap[channelIda]; exists && pkInfoA.IsPKing() {
		log.DebugWithCtx(ctx, "StartPk user A is pking uid:%v channelId:%v targetChannelId:%v", uida, channelIda, pkInfoA.TChannelID)
		// 如果主播A正在PK，更新匹配状态为关闭，并返回错误
		match.UpdateMatchState(s.liveMgrCache, uida, channelIda, 0, pb.EnumChannelLiveStatus_CLOSE)
		return 0, protocol.NewExactServerError(nil, status.ErrChannelLiveNotAuthority, "当前还有正在进行的PK，无法应战")
	}

	// 检查主播B是否正在进行PK
	if pkInfoB, exists := pkStatusMap[channelIdb]; exists && pkInfoB.IsPKing() {
		log.DebugWithCtx(ctx, "StartPk user B is pking uid:%v channelId:%v targetChannelId:%v", uidb, channelIdb, pkInfoB.TChannelID)
		// 如果主播B正在PK，更新匹配状态为关闭，并返回错误
		match.UpdateMatchState(s.liveMgrCache, uidb, channelIdb, 0, pb.EnumChannelLiveStatus_CLOSE)
		return 0, fmt.Errorf("user is pking %v", uidb)
	}

	// 创建完整的PK状态对象，包含双方信息
	objPKstate := &pk.PkState{
		ChannelID_A: channelIda,    //in.ChannelId,
		UID_A:       uida,          //in.Uid,
		ChannelID_B: channelIdb,    //in.ApplyChannelId,
		UID_B:       uidb,          //in.ApplyUid,
		MatchSource: int32(source), // PK来源（匹配、申请等）
	}

	// 启动PK，获取PK开始时间戳（使用优化后的StartPK方法）
	beginTs, perr := objPKstate.StartPK()
	pkBeginTime := uint32(beginTs)
	if perr != nil {
		log.ErrorWithCtx(ctx, "StartPk StartPK err:%v", perr)
		return 0, perr
	}

	log.InfoWithCtx(ctx, "StartPk success uida:%v uidb:%v channelIda:%v channelIdb:%v beginTs:%v",
		uida, uidb, channelIda, channelIdb, beginTs)

	// 优化：异步处理后续操作，提升响应速度
	go s.handlePKStartAsync(ctx, uida, uidb, channelIda, channelIdb, uint32(beginTs), source, appointId)

	// 立即返回，不等待异步操作完成
	return pkBeginTime, nil
}

// handlePKStartAsync 异步处理PK启动后的操作
func (s *ChannelLiveMgrServer) handlePKStartAsync(ctx context.Context, uida, uidb, channelIda, channelIdb, beginTs uint32, source, appointId int) {
	// 设置超时上下文
	asyncCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 添加PK状态变更的定时任务到定时队列
	// 计算下一个PK阶段的触发时间（BEGIN阶段的持续时间）
	triggerTime := uint32(time.Now().Unix()) + conf.Sec2State[uint32(pb.EnumChannelLivePKStatus_BEGIN)]
	err := s.liveMgrCache.AddTaskToTick(&cache.TickTask{
		AUid:        uidb,
		BUid:        uida,
		AChannelId:  channelIdb,
		BChannelId:  channelIda,
		Status:      uint32(pbLogic.EnumChannelLivePKStatus_BEGIN),
		BeginTs:     beginTs,
		Source:      uint32(source),
		MatchSource: int32(source),
		AppointId:   uint32(appointId),
	}, triggerTime)

	if err != nil {
		log.ErrorWithCtx(asyncCtx, "handlePKStartAsync AddTaskToTick failed err:%v", err)
	}

	// 构建PK开始的推送消息内容
	pkStatusPush := s.mgr.FillStartPkInfo(uida, uidb, channelIda, channelIdb, beginTs)

	// 将PK开始消息序列化为二进制格式
	pkStatusPushBin, _ := proto.Marshal(pkStatusPush)

	// 向双方频道推送PK开始消息，通知房间内的用户PK已开始
	perr := s.aclExtLogicApi.PushChannelMsg(asyncCtx, []uint32{channelIda, channelIdb},
		uint32(channelpb.ChannelMsgType_CHANNEL_LIVE_PK_STATUS_MGS), pkStatusPushBin)
	if perr != nil {
		log.ErrorWithCtx(asyncCtx, "handlePKStartAsync PushChannelMsg cid (%d %d) fail err:%v",
			channelIda, channelIdb, perr)
	}

	// 发布PK开始事件到Kafka，供其他服务消费（如数据统计、推荐系统等）
	perr = s.channelLiveProd.ProduceChannelLiveEvent(&pbLogic.ChannelLiveKafkaEvent{
		ChannelLiveStatus: pbLogic.EnumChannelLiveStatus_OPEN,
		AnchorUid:         uida,
		ChannelId:         channelIda,
		OppAnchorUid:      uidb,
		OppChannelId:      channelIdb,
		ChannelPkStatus:   pbLogic.EnumChannelLivePKStatus_BEGIN,
		ApplyId:           int64(uidb),
		Ty:                pbLogic.ChannelLiveKafkaEventType_ChannelLivePkType,
		CreateTime:        time.Now().Unix(),
		MatchModel:        pbLogic.ChannelLivePKMatchType(source),
		MatchType:         int64(source),
	})

	if perr != nil {
		log.ErrorWithCtx(asyncCtx, "handlePKStartAsync ProduceChannelLiveEvent perr:%v", perr)
	}

	// 清除双方的PK匹配状态，防止重复匹配
	match.UpdateMatchState(s.liveMgrCache, uida, channelIda, 0, pb.EnumChannelLiveStatus_CLOSE)
	match.UpdateMatchState(s.liveMgrCache, uidb, channelIdb, 0, pb.EnumChannelLiveStatus_CLOSE)

	log.DebugWithCtx(asyncCtx, "handlePKStartAsync completed for channels %d-%d", channelIda, channelIdb)
}

// StartPkLegacy 原有的StartPk逻辑，作为降级方案
func (s *ChannelLiveMgrServer) StartPkLegacy(ctx context.Context, uida, uidb, channelIda, channelIdb, scoreA, scoreB uint32, source, appointId int) (uint32, error) {
	log.DebugWithCtx(ctx, "StartPkLegacy fallback for uida:%v uidb:%v", uida, uidb)

	// 创建主播A的PK状态对象，检查是否正在PK中
	objPKstateA := &pk.PkState{
		ChannelID_A: channelIda,
	}

	// 检查主播A是否正在进行PK
	isPKing := objPKstateA.IsPKing()
	if isPKing {
		match.UpdateMatchState(s.liveMgrCache, uida, channelIda, 0, pb.EnumChannelLiveStatus_CLOSE)
		return 0, protocol.NewExactServerError(nil, status.ErrChannelLiveNotAuthority, "当前还有正在进行的PK，无法应战")
	}

	// 创建主播B的PK状态对象，检查是否正在PK中
	objPKstateB := &pk.PkState{
		ChannelID_A: channelIdb,
	}
	// 检查主播B是否正在进行PK
	isPKing = objPKstateB.IsPKing()
	if isPKing {
		match.UpdateMatchState(s.liveMgrCache, uidb, channelIdb, 0, pb.EnumChannelLiveStatus_CLOSE)
		return 0, fmt.Errorf("user is pking %v", uidb)
	}

	// 创建完整的PK状态对象，包含双方信息
	objPKstate := &pk.PkState{
		ChannelID_A: channelIda,
		UID_A:       uida,
		ChannelID_B: channelIdb,
		UID_B:       uidb,
		MatchSource: int32(source),
	}

	// 使用原有的StartPK方法
	beginTs, perr := objPKstate.StartPKLegacy()
	pkBeginTime := uint32(beginTs)
	if perr != nil {
		log.ErrorWithCtx(ctx, "StartPkLegacy StartPKLegacy err:%v", perr)
		return 0, perr
	}

	// 后续处理逻辑保持不变
	go s.handlePKStartAsync(ctx, uida, uidb, channelIda, channelIdb, uint32(beginTs), source, appointId)

	// 如果PK来源是匹配系统（source >= 0），记录匹配成功日志
	if source >= 0 {
		//s.mysqlStore.AddMatchLog(ctx, int64(uida), int64(pb.EnumPkMatch_PKM_Match_Success), int64(source), int64(scoreA), int64(uidb), int64(scoreB))
	}
	return pkBeginTime, nil
}

// HandlerApply 处理PK申请的响应（接受/拒绝）
// 第一代PK接口：处理目标主播对PK申请的响应操作
func (s *ChannelLiveMgrServer) HandlerApply(ctx context.Context, in *pb.HandlerApplyReq) (out *pb.HandlerApplyResp, err error) {
	out = &pb.HandlerApplyResp{}

	log.DebugWithCtx(ctx, "HandlerApplyS in:%v", in)

	// 删除PK申请记录，无论是接受还是拒绝都需要清理申请记录
	// ok表示申请是否在有效期内，apply包含申请的详细信息
	ok, apply, err := s.liveMgrCache.DelApply(in.ChannelId, in.ApplyChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlerApplySX DelApply err:%v in:%v", err, in)
		return out, err
	}

	// 初始化申请ID和申请时间，用于事件发布
	var applyId int64 = 0
	var applyTime uint32 = 0

	// 如果申请记录存在，提取申请ID和时间
	if apply != nil {
		applyId = apply.ApplyID
		applyTime = apply.ApplyTime
	}

	// 异步发布PK申请处理事件到Kafka，通知其他服务申请的处理结果
	go func() {
		s.pkApplyProd.ProdPkApplyEvent(&pbLogic.PkApplyKafkaEvent{
			ApplyUid:  in.ApplyUid,
			TargetUid: in.Uid,
			EventTy:   pbLogic.EnumApply(in.Oper), // 操作类型：接受/拒绝
			ApplyId:   applyId,
			ApplyTime: applyTime,
		})
	}()

	// 如果操作不是接受（即拒绝或其他操作），直接返回，不启动PK
	if in.Oper != pb.EnumApply_accept {
		//除了接受操作，其他delete后直接返回
		return out, nil
	}

	// 检查申请是否在有效期内
	if !ok {
		log.ErrorWithCtx(ctx, "HandlerApplySX apply timeout in:%v", in)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveIdInvalid, "PK申请已过期")
	}

	// 如果是接受操作且申请有效，启动PK
	// source设为-1表示这是通过申请方式启动的PK（非匹配系统）
	out.PkBeginTime, err = s.StartPk(ctx, in.Uid, in.ApplyUid, in.ChannelId, in.ApplyChannelId, 0, 0, -1, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlerApplySX StartPk err:%v in:%v", err, in)
	}

	log.DebugWithCtx(ctx, "HandlerApplyS in:%v out:%v", in, out)

	return out, nil
}

// CancelPKApply 取消PK申请
// 第一代PK接口：允许申请方主动取消已发起的PK申请
func (s *ChannelLiveMgrServer) CancelPKApply(ctx context.Context, in *pb.CancelPKApplyReq) (out *pb.CancelPKApplyResp, err error) {
	out = &pb.CancelPKApplyResp{}

	log.InfoWithCtx(ctx, "CancelPKApply in:%v", in)

	// 删除PK申请记录，获取申请的详细信息用于事件发布
	_, apply, _ := s.liveMgrCache.DelApply(in.ChannelId, in.ApplyChannelId)

	// 初始化申请ID和申请时间
	var applyId int64 = 0
	var applyTime uint32 = 0

	// 如果申请记录存在，提取申请ID和时间
	if apply != nil {
		applyId = apply.ApplyID
		applyTime = apply.ApplyTime
	}

	// 发布PK申请取消事件到Kafka，通知其他服务申请已被取消
	s.pkApplyProd.ProdPkApplyEvent(&pbLogic.PkApplyKafkaEvent{
		ApplyUid:  in.ApplyUid,
		TargetUid: in.Uid,
		EventTy:   pbLogic.EnumApply_cancel, // 事件类型：取消
		ApplyId:   applyId,
		ApplyTime: applyTime,
	})

	return out, nil
}

// GetApplyList 获取PK申请列表
// 第一代PK接口：获取指定频道收到的所有PK申请列表
func (s *ChannelLiveMgrServer) GetApplyList(ctx context.Context, in *pb.GetApplyListReq) (out *pb.GetApplyListResp, err error) {
	out = &pb.GetApplyListResp{
		ApplyList: make([]*pb.Apply, 0),
	}

	// 从缓存中获取指定频道的PK申请列表
	applyList, err := s.liveMgrCache.GetApplyList(in.ChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetApplyList err:%v", err)
	}

	// 将缓存中的申请信息转换为响应格式
	for _, apply := range applyList {
		out.ApplyList = append(out.ApplyList, &pb.Apply{
			ApplyUid:       apply.Uid,       // 申请方用户ID
			ApplyChannelId: apply.ChannelId, // 申请方频道ID
			ApplyTime:      apply.ApplyTime, // 申请时间
		})
	}

	return out, nil
}

// SetPkStatus 设置PK状态
// 第一代PK接口：处理PK状态变更，主要用于PK结束时的状态清理
func (s *ChannelLiveMgrServer) SetPkStatus(ctx context.Context, in *pb.SetPkStatusReq) (out *pb.SetPkStatusResp, err error) {
	out = &pb.SetPkStatusResp{}

	log.DebugWithCtx(ctx, "SetPkStatus in:%v", in)

	// 创建PK状态对象，获取当前频道的PK信息
	pkstate := pk.PkState{
		ChannelID_A: in.ChannelId,
	}
	pkinfoA, err := pkstate.GetPkInfo()
	if err == nil {
		out.TargetChannelId = pkinfoA.TChannelID
	}

	// 如果当前频道正在PK中（有对手频道）
	if pkinfoA.TChannelID != 0 {
		// 设置对手频道ID，准备结束PK
		pkstate.ChannelID_B = pkinfoA.TChannelID

		// 获取对手频道的PK状态，确认双方PK关系
		pkstateB := pk.PkState{
			ChannelID_A: pkinfoA.TChannelID,
		}
		pkinfoB, _ := pkstateB.GetPkInfo()

		// 如果对手频道的PK对象确实是当前频道，则结束PK
		if pkinfoB.TChannelID == in.ChannelId {
			pkstate.FinishPK()
		}
	}

	// 设置响应数据
	out.BeginTime = pkinfoA.BeginTime
	out.TargetChannelId = pkinfoA.TChannelID

	// 发布PK状态变更事件到Kafka，通知其他服务PK已结束
	perr := s.channelLiveProd.ProduceChannelLiveEvent(&pbLogic.ChannelLiveKafkaEvent{
		ChannelLiveStatus: pbLogic.EnumChannelLiveStatus_OPEN,
		AnchorUid:         in.Uid,
		ChannelId:         in.ChannelId,
		OppAnchorUid:      pkinfoA.TUID,
		OppChannelId:      pkinfoA.TChannelID,
		ChannelPkStatus:   pbLogic.EnumChannelLivePKStatus_IDLE, // 状态设为空闲
		Ty:                pbLogic.ChannelLiveKafkaEventType_ChannelLivePkType,
		CreateTime:        time.Now().Unix(),
		MatchType:         int64(pkinfoA.MatchSource),
		MatchModel:        pbLogic.ChannelLivePKMatchType(pkinfoA.MatchSource),
	})

	if perr != nil {
		log.ErrorWithCtx(ctx, "StartPk ProduceChannelLiveEvent perr:%v", perr)
	}

	log.DebugWithCtx(ctx, "SetPkStatus in:%v out:%v", in, out)

	return out, nil
}

// 直播送礼榜
func (s *ChannelLiveMgrServer) GetChannelLiveRankUser(ctx context.Context, in *pb.GetChannelLiveRankUserReq) (out *pb.GetChannelLiveRankUserResp, err error) {
	out = &pb.GetChannelLiveRankUserResp{
		UserList: make([]*pb.SendGiftUserInfo, 0),
	}

	ekey := fmt.Sprintf("get_channel_live_rank_user_%v", in.ChannelId)
	v, ok := easyCache.Get(ekey)
	if ok && v != nil {
		resp, ok := v.(*pb.GetChannelLiveRankUserResp)
		if ok && resp != nil {
			return resp, nil
		}
	}

	rankUser, _ := s.liveMgrCache.GetSendGiftRank(in.ChannelId)

	for _, rank := range rankUser {
		sendGiftUser := &pb.SendGiftUserInfo{
			Uid:   rank.Uid,
			Score: rank.Score,
		}
		out.UserList = append(out.UserList, sendGiftUser)
	}

	easyCache.Set(ekey, out, conf.GetLiveCacheTs())

	log.DebugWithCtx(ctx, "GetChannelLiveRankUser out:%v", out)

	return out, nil
}

type WatchRankStu struct {
	Uid   uint32
	Score uint32
}

type WatchRankStus []WatchRankStu

func (w WatchRankStus) Len() int {
	return len(w)
}

func (w WatchRankStus) Less(i, j int) bool {
	return w[i].Score > w[j].Score
}

func (w WatchRankStus) Swap(i, j int) {
	w[i], w[j] = w[j], w[i]
}

// 直播观看榜
func (s *ChannelLiveMgrServer) GetChannelLiveWatchTimeRankUser(ctx context.Context, in *pb.GetChannelLiveWatchTimeRankUserReq) (out *pb.GetChannelLiveWatchTimeRankUserResp, err error) {
	out = &pb.GetChannelLiveWatchTimeRankUserResp{
		UserList: make([]*pb.WatchUserInfo, 0),
	}

	log.DebugWithCtx(ctx, "GetChannelLiveWatchTimeRankUser in:%v", in)

	watchRank, err := s.liveMgrCache.GetChannelLiveWatchRank(in.ChannelId, WatchAudieCountLimit)

	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveWatchTimeRankUser GetChannelLiveWatchRank err:%v", err)
		return out, err
	}

	ww := WatchRankStus{}

	for _, user := range watchRank {
		ww = append(ww, WatchRankStu{
			Uid:   user.Uid,
			Score: user.Score,
		})
	}

	sort.Sort(ww)

	for index, w := range ww {
		if index >= WatchAudieCountLimit {
			break
		}

		out.UserList = append(out.UserList, &pb.WatchUserInfo{
			Uid:     w.Uid,
			Account: "",
			Score:   w.Score,
		})
	}

	log.DebugWithCtx(ctx, "GetChannelLiveWatchTimeRankUser out:%v", out)

	return out, nil
}

// 单场直播数据统计
func (s *ChannelLiveMgrServer) GetChannelLiveData(ctx context.Context, in *pb.GetChannelLiveDataReq) (out *pb.GetChannelLiveDataResp, err error) {
	log.DebugWithCtx(ctx, "GetChannelLiveData req(%+v)", in)
	out = &pb.GetChannelLiveDataResp{}

	channelId := in.ChannelId
	if channelId == 0 {
		chInfo, err := s.liveMgrCache.GetChannelLiveInfo(in.Uid, 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelLiveData GetChannelLiveInfo err:%v", err)
			return out, err
		}
		channelId = chInfo.ChannelId
	}

	ekey := fmt.Sprintf("get_channel_live_data_%v", channelId)
	v, ok := easyCache.Get(ekey)
	if ok && v != nil {
		resp, ok := v.(*pb.GetChannelLiveDataResp)
		if ok && resp != nil {
			return resp, nil
		}
	}

	chData := s.liveMgrCache.GetChannelLiveData(channelId)
	// 添加机器人观众数
	robotCnt, serverErr := s.robotCli.GetChannelRobotSize(ctx, in.Uid, in.ChannelId)
	if serverErr != nil {
		log.ErrorWithCtx(ctx, "Failed to GetChannelRobotSize err(%s) in.Uid(%d) in.ChannelId(%d)", serverErr.Error(), in.Uid, in.ChannelId)
	}
	chData.AudienceCnt += robotCnt

	out.LiveData = &pb.ChannelLiveData{
		AudienceCnt:         chData.AudienceCnt,
		LiveGiftValue:       uint32(chData.GiftValue),
		AnchorGiftValue:     uint32(chData.AnchorGiftValue),
		SendGiftAudienceCnt: chData.SendGiftAudienceCnt,
		BeginTime:           uint32(chData.BeginTime),
		EndTime:             uint32(chData.EndTime),
		KnightValue:         uint32(chData.KnightValue),
		GameFee:             uint32(chData.GameGiftValue),
	}

	easyCache.Set(ekey, out, conf.GetLiveCacheTs()*2)

	log.InfoWithCtx(ctx, "GetChannelLiveData req(%+v) resp(%+v)", in, out)
	return out, nil
}

func (s *ChannelLiveMgrServer) GetChannelLiveTOPN(ctx context.Context, in *pb.GetChannelLiveTOPNReq) (out *pb.GetChannelLiveTOPNResp, err error) {
	out = &pb.GetChannelLiveTOPNResp{
		LiveData: make([]*pb.ChannelLiveData, 0),
	}

	records, serr := s.mysqlStore.GetChannelLiveRecords(ctx, in.Uid, in.Off, in.Count)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveTOPN GetChannelLiveRecords serr:%v", serr)
	}

	for _, re := range records {
		var virtualTs uint32
		var virtualFee uint32
		if re.AnchorType == uint32(pb.AnchorType_Anchor_Type_Virtual) && re.EndTime > re.BeginTime {
			virtualTs = re.EndTime - re.BeginTime
		}
		if re.AnchorType == uint32(pb.AnchorType_Anchor_Type_Virtual) {
			virtualFee = re.Score
		}

		out.LiveData = append(out.LiveData, &pb.ChannelLiveData{
			LiveTime:            re.EndTime - re.BeginTime,
			AudienceCnt:         re.Audiences,
			LiveGiftValue:       re.Score,
			AnchorGiftValue:     re.AnchorGiftValue,
			SendGiftAudienceCnt: re.SendGiftUserCnt,
			AddFans:             re.AddFans,
			AddGroupFans:        re.AddGroupFans,
			BeginTime:           re.BeginTime,
			EndTime:             re.EndTime,
			KnightValue:         re.KnightValue,
			GameFee:             re.GameFee,
			GameTs:              re.GameTs,
			VirtualFee:          virtualFee,
			VirtualTs:           virtualTs,
		})
	}

	return out, nil
}

// 主播积分
func (s *ChannelLiveMgrServer) AddChannelLiveAnchorScore(ctx context.Context, in *pb.AddChannelLiveAnchorScoreReq) (out *pb.AddChannelLiveAnchorScoreResp, err error) {
	out = &pb.AddChannelLiveAnchorScoreResp{}

	log.InfoWithCtx(ctx, "AddChannelLiveAnchorScore in %+v", ctx, in)

	finalScore, err := s.mysqlStore.AddAnchorScore(ctx, in.GetUid(), in.GetScore(), in.GetOrderId(), in.GetSourceType(), in.GetOutsideTime(), in.GetMissionAwardDetail())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddChannelLiveAnchorScore fail. req:%+v, err:%v", in, err)
		return out, err
	}
	out.FinalScore = finalScore

	return out, nil
}

func (s *ChannelLiveMgrServer) GetChannelLiveAnchorScore(ctx context.Context, in *pb.GetChannelLiveAnchorScoreReq) (out *pb.GetChannelLiveAnchorScoreResp, err error) {
	out = &pb.GetChannelLiveAnchorScoreResp{}

	score, err := s.mysqlStore.GetAnchorScore(ctx, in.Uid)

	if err != nil {
		return out, err
	}

	out.Score = score
	return out, nil
}

func (s *ChannelLiveMgrServer) BatchGetAllChannelLive(ctx context.Context, in *pb.BatchGetAllChannelLiveReq) (out *pb.BatchGetAllChannelLiveResp, err error) {
	out = &pb.BatchGetAllChannelLiveResp{}

	log.DebugWithCtx(ctx, "BatchGetAllChannelLive in:%v", in)

	ekey := "batch_get_all_channel_live"
	v, ok := easyCache.Get(ekey)
	if ok && v != nil {
		resp, ok := v.(*pb.BatchGetAllChannelLiveResp)
		if ok && resp != nil {
			return resp, nil
		}
	}

	statList, err := s.liveMgrCache.GetAllChannelLiveStatus()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllChannelLiveStatus failed in:%v err:%v", in, err)
		return out, err
	}

	for _, sta := range statList {
		out.ChannelList = append(out.ChannelList, sta.ChannelId)
		out.LiveChannelList = append(out.LiveChannelList, sta)
	}

	easyCache.Set(ekey, out, conf.GetLiveCacheTs()*50)

	return out, nil
}

func (s *ChannelLiveMgrServer) ShutDown() {
	s.stop <- true
	s.knightKfkSub.Close()
	s.ykwKfkSub.Close()
	s.extGameKfkSub.Close()
	s.channelKfkSub.Close()
	s.micKfkSub.Close()
	s.presentKfkSub.Close()
	s.channelLiveProd.Close()
	s.pkApplyProd.Close()
}

func (s *ChannelLiveMgrServer) BatchGetGroupFansGiftValue(ctx context.Context, req *pb.BatchGetGroupFansGiftValueReq) (*pb.BatchGetGroupFansGiftValueResp, error) {
	log.DebugWithCtx(ctx, "BatchGetGroupFansGiftValue req(%+v)", req)
	resp := &pb.BatchGetGroupFansGiftValueResp{}

	ekey := fmt.Sprintf("batch_get_group_Fans_gift_value_%v", req.AnchorUid)
	v, ok := easyCache.Get(ekey)
	if ok && v != nil {
		resp, ok := v.(*pb.BatchGetGroupFansGiftValueResp)
		if ok && resp != nil {
			return resp, nil
		}
	}

	valueList, err := s.liveMgrCache.BatchGetGiftValue(req.AnchorUid, req.UidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to BatchGetGroupFansGiftValue err(%s) anchorUid(%d) uidList(%v)", err, req.AnchorUid, req.UidList)
	}

	resp.GiftValueList = valueList

	if len(valueList) > 128 {
		easyCache.Set(ekey, resp, 120)
	}

	log.DebugWithCtx(ctx, "BatchGetGroupFansGiftValue resp(%+v)", resp)
	return resp, nil
}

func (s *ChannelLiveMgrServer) GetUserPushCnt(ctx context.Context, req *pb.GetUserPushCntReq) (*pb.GetUserPushCntResp, error) {
	resp := &pb.GetUserPushCntResp{
		Result: false,
	}

	log.DebugWithCtx(ctx, "GetUserPushCnt req:%v", req)

	nowTs := time.Now()
	resp.ValidUidList = s.liveMgrCache.GetValidPushUidList(nowTs, req.UidList, req.AnchorUid)

	log.DebugWithCtx(ctx, "GetUserPushCnt req(%+v) resp(%v)", req, resp)

	return resp, nil
}

// GetPkInfo 获取PK详细信息
// 第一代PK接口：获取正在进行的PK的完整状态信息，包括双方信息、公共信息、麦位信息等
func (s *ChannelLiveMgrServer) GetPkInfo(ctx context.Context, req *pb.GetPkInfoReq) (*pb.GetPkInfoResp, error) {
	resp := &pb.GetPkInfoResp{
		APkInfo:      &pb.PkSingleInfo{},
		BPkInfo:      &pb.PkSingleInfo{},
		PkCommonInfo: &pb.PkCommonInfo{},
	}

	log.DebugWithCtx(ctx, "GetPkInfo in:%v", req)

	// 创建A方（请求方）的PK状态对象
	apkstate := pk.PkState{
		ChannelID_A: req.ChannelId,
		ChannelID_B: 0,
	}
	// 获取A方的PK信息
	apkinfo, serr := apkstate.GetPkInfo()

	if serr != nil {
		log.ErrorWithCtx(ctx, "GetPkInfo req:%v serr:%v", req, serr)
		return resp, serr
	}

	// 检查是否正在PK中，如果TChannelID为0表示没有在PK
	if apkinfo.TChannelID == 0 {
		log.ErrorWithCtx(ctx, "GetPkInfo req:%v TChannelID empty", req)
		return resp, protocol.NewExactServerError(nil, status.ErrChannelLiveIdInvalid)
	}
	// 设置A方的对手频道ID
	apkstate.ChannelID_B = apkinfo.TChannelID

	// 创建B方（对手方）的PK状态对象
	bpkstate := pk.PkState{
		ChannelID_A: apkinfo.TChannelID,
		ChannelID_B: 0,
	}

	// 获取B方的PK信息
	bpkinfo, serr := bpkstate.GetPkInfo()
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetPkInfo req:%v serr:%v", req, serr)
		return resp, serr
	}

	// 验证双方PK关系的一致性，确保B方的对手确实是A方
	if bpkinfo.TChannelID != req.ChannelId {
		log.ErrorWithCtx(ctx, "GetPkInfo pkstate confusion %v %v", bpkinfo.TChannelID, req.ChannelId)
		return resp, protocol.NewExactServerError(nil, status.ErrChannelLiveIdInvalid)
	}

	// 计算PK已进行的时间，用于确定当前PK阶段
	passSec := uint32(time.Now().Unix()) - bpkinfo.BeginTime
	state := conf.GetGConfig().GetState(int(passSec))

	// 获取加时赛配置信息
	extraConf := conf.GetExtraTimeConf()

	// 获取首杀信息（第一个击败对手的用户）
	firstStrUidInfo, cid := apkstate.GetFirstShootUid()
	firstUidInfo := s.mgr.ParseStrUidInfo(cid, firstStrUidInfo)

	// 构建PK公共信息
	commonInfo := &pb.PkCommonInfo{
		BeginTime:        bpkinfo.BeginTime,                 // PK开始时间
		PkStatus:         pb.EnumChannelLivePKStatus(state), // 当前PK状态
		MicList:          make(map[uint32]*pb.PkMicSpace),   // 麦位列表
		FirstKillUid:     firstUidInfo.GetUid(),             // 首杀用户ID
		FirstKillCid:     cid,                               // 首杀频道ID
		IsExtraTime:      bpkinfo.IsExtra,                   // 是否处于加时赛
		PkExtraTimeRule:  extraConf.Rule,                    // 加时赛规则
		ExtraLeftTime:    uint32(extraConf.LeftTs),          // 加时赛剩余时间
		IsOpenExtraTime:  bpkinfo.IsOpenExtra,               // 是否开启加时赛
		FirstKillUwkinfo: firstUidInfo,                      // 首杀用户详细信息
	}

	resp.PkCommonInfo = commonInfo

	// 构建A方（请求方）的PK信息
	aInfo := &pb.PkSingleInfo{
		Uid:           bpkinfo.TUID,                                   // A方用户ID
		ChannelId:     bpkinfo.TChannelID,                             // A方频道ID
		ChannelLiveId: 0,                                              // TODO: 直播ID
		PkScore:       apkinfo.PKScore,                                // A方PK分数
		EffectCnt:     apkinfo.EffectCnt,                              // A方特效数量
		MicFlag:       pb.ChannelLiveOpponentMicFlag(apkinfo.MicFlag), // A方麦克风状态
	}

	// 获取A方的麦位列表并添加到公共信息中
	aMicList, gerr := apkstate.GetMicList()
	if gerr == nil {
		for _, mic := range aMicList {
			commonInfo.MicList[mic.Uid] = &pb.PkMicSpace{
				Uid:                  mic.Uid,
				Account:              "",
				Nick:                 "",
				MicId:                0,
				VoiceId:              mic.VoiceID,
				ChannelVideoClientId: mic.VideoID,
			}
		}
	}

	// 构建B方（对手方）的PK信息
	bInfo := &pb.PkSingleInfo{
		Uid:           apkinfo.TUID,                                   // B方用户ID
		ChannelId:     apkinfo.TChannelID,                             // B方频道ID
		ChannelLiveId: 0,                                              // 直播ID
		PkScore:       bpkinfo.PKScore,                                // B方PK分数
		EffectCnt:     bpkinfo.EffectCnt,                              // B方特效数量
		MicFlag:       pb.ChannelLiveOpponentMicFlag(bpkinfo.MicFlag), // B方麦克风状态
	}

	// 获取B方的麦位列表并添加到公共信息中
	bMicList, gerr := bpkstate.GetMicList()
	if gerr == nil {
		for _, mic := range bMicList {
			commonInfo.MicList[mic.Uid] = &pb.PkMicSpace{
				Uid:                  mic.Uid,
				Account:              "",
				Nick:                 "",
				MicId:                0,
				VoiceId:              mic.VoiceID,
				ChannelVideoClientId: mic.VideoID,
			}
		}
	}

	// 设置响应数据
	resp.APkInfo = aInfo
	resp.BPkInfo = bInfo

	log.DebugWithCtx(ctx, "GetPkInfo req:%v resp:%v", req, resp)

	return resp, nil
}

// ReportClientIDChange 麦位信息变化处理
// 第一代PK接口：处理PK过程中麦位信息的变化，包括语音ID和视频ID的更新
func (s *ChannelLiveMgrServer) ReportClientIDChange(ctx context.Context, req *pb.ReportClientIDChangeReq) (*pb.ReportClientIDChangeResp, error) {
	resp := &pb.ReportClientIDChangeResp{MicList: make([]*pb.PkMicSpace, 0)}

	log.DebugWithCtx(ctx, "ReportClientIDChange in:%v", req)

	// 创建PK状态对象
	pkstate := &pk.PkState{
		ChannelID_A: req.ChannelId,
	}

	// 处理语音ID变化，更新麦位信息
	mapMic, _ := pkstate.OnVoiceIDChange(&pk.MicInfo{
		Uid:       req.Uid,                       // 用户ID
		ChannelID: req.ChannelId,                 // 频道ID
		VoiceID:   req.ClientId,                  // 语音客户端ID
		VideoID:   req.GetChannelVideoClientId(), // 视频客户端ID
	})

	// 获取PK信息，如果正在PK中则推送麦位信息变化到对方频道
	pkInfo, err := pkstate.GetPkInfo()
	if err == nil && pkInfo.TChannelID > 0 {
		go func() { // 异步推送麦位信息到双方频道
			s.mgr.PushMicInfo(req.ChannelId, pkInfo.TChannelID, mapMic)
		}()
	}

	log.InfoWithCtx(ctx, "ReportClientIDChange end req:%v pkInfo:%v resp:%v", req, pkInfo, resp)

	return resp, nil
}

// SetChannelLiveOpponentMicFlag PK的时候屏蔽对面主播声音
// 第一代PK接口：设置PK过程中对对方主播声音的屏蔽状态
func (s *ChannelLiveMgrServer) SetChannelLiveOpponentMicFlag(ctx context.Context, req *pb.SetChannelLiveOpponentMicFlagReq) (*pb.SetChannelLiveOpponentMicFlagResp, error) {
	resp := &pb.SetChannelLiveOpponentMicFlagResp{}

	// 创建PK状态对象
	pkstate := pk.PkState{
		ChannelID_A: req.ChannelId,
	}

	// 获取当前PK信息，确保正在PK中
	pkinfo, err := pkstate.GetPkInfo()
	if err != nil {
		return resp, err
	}

	// 设置麦克风标志位，控制是否屏蔽对方声音
	err = pkstate.SetMicFlag(uint32(req.OptMicFlag))
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveOpponentMicFlag SetMicFlag err:%v", err)
		return resp, err
	}

	// 返回对方频道ID，用于客户端处理
	resp.TargetChannelId = pkinfo.TChannelID

	return resp, nil
}

// StartPkMatch PK匹配
// 第一代PK接口：启动PK匹配功能，将主播加入匹配池等待系统自动匹配对手
func (s *ChannelLiveMgrServer) StartPkMatch(ctx context.Context, req *pb.StartPkMatchReq) (*pb.StartPkMatchResp, error) {
	resp := &pb.StartPkMatchResp{}

	log.DebugWithCtx(ctx, "StartPkMatchS req:%v", req)

	// 检查是否处于预约PK时间段，如果是则不允许进行匹配
	nowTs := uint32(time.Now().Unix())
	pkInfoList, err := s.liveMgrCache.GetAnchorAppointPkInfoList(req.GetUid(), nowTs)
	if err != nil {
		log.ErrorWithCtx(ctx, "AcceptAppointPk GetAnchorAppointPkInfoList failed in:%v err:%v", req, err)
		return resp, err
	}

	// 遍历预约PK列表，检查是否在预约时间段内
	for _, pkInfo := range pkInfoList {
		log.DebugWithCtx(ctx, "StartPkMatch req:%v anchorPkInfo:%v ts:%d", req, pkInfo, nowTs)
		// 如果当前时间在预约PK时间范围内
		if nowTs >= pkInfo.BeginTs && nowTs <= pkInfo.EndTs {
			// 检查预约PK是否真实存在
			isExist, err := s.liveMgrCache.CheckAppointPkInfoIsExist(pkInfo.AppointId)
			if err != nil {
				log.ErrorWithCtx(ctx, "StartPkMatch CheckAppointPkInfoIsExist failed in:%v Info:%v err:%v", req, pkInfo, err)
				continue
			}
			if isExist {
				log.ErrorWithCtx(ctx, "StartPkMatch anchor is in appoint ok req:%v anchorPkInfo:%v", req, pkInfo)
				// 在预约PK时间段内，不允许进行匹配
				return resp, protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoLaunchPk)
			}
		}
	}

	// 检查是否在指定PK开始前10分钟内，如果是则不允许发起任何PK（包括双人PK、四人PK、指定id PK）
	comingPkUser, err := s.checkUpcomeingAppointPkTs(ctx, []uint32{req.GetUid()})
	if err != nil {
		log.ErrorWithCtx(ctx, "StartPkMatch checkUpcomeingAppointPkTs failed in:%v err:%v", req, err)
		return resp, err
	}

	if _, ok := comingPkUser[req.GetUid()]; ok {
		log.ErrorWithCtx(ctx, "StartPkMatch anchor has upcoming appoint pk within 10 minutes req:%v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoLaunchPk, "即将有指定PK场次，不能发起PK")
	}

	// 检查PK次数限制（20:00-22:00期间限制）
	if err := s.mgr.CheckIsPkDayRangeTimeLimit(req.Uid, true); nil != err {
		log.ErrorWithCtx(ctx, "StartPkMatch uid:%v", req.Uid)
		return resp, err //protocol.NewExactServerError(nil, status.ErrChannelLivePkCntLimit, "每日20:00～22:00期间只允许连麦PK两次哦～")
	}

	// 清理之前的匹配状态，防止重复匹配
	match.UpdateMatchState(s.liveMgrCache, req.Uid, req.ChannelId, 0, pb.EnumChannelLiveStatus_CLOSE)

	// 获取当前时间段的匹配类型
	matchTy := conf.GetMatchType(uint32(time.Now().Unix()))
	// 验证客户端请求的匹配类型与服务端一致
	if matchTy != req.MatchType {
		log.ErrorWithCtx(ctx, "StartPkMatchSX invalid match type req:%v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrChannelLivePkMatchInvalidTy, "匹配类型跟服务端不一致")
	}

	// 获取对应的匹配器
	matcher := match.GetMatcher(matchTy)
	if matcher == nil {
		log.ErrorWithCtx(ctx, "StartPkMatchSX GetMatcher match empty")
		return resp, protocol.NewExactServerError(nil, status.ErrChannelLivePkMatchInvalidTy, "没有匹配类型")
	}

	// 获取用户的PK匹配值（用于匹配算法）
	var pkValue, _ = s.mgr.GetPKMatchValue(req.Uid, matchTy)
	// 将用户加入匹配池
	matcher.Add(s.liveMgrCache, req.Uid, req.ChannelId, pkValue)

	// 记录匹配日志
	// s.mysqlStore.AddMatchLog(ctx, int64(req.Uid), int64(pb.EnumPkMatch_PKM_Matching), int64(matchTy), int64(pkValue), 0, 0)

	log.DebugWithCtx(ctx, "StartPkMatchS resp:%v matchTy:%v pkValue:%v", resp, matchTy, pkValue)

	return resp, nil
}

// 检查主播是否在指定PK前10分钟内（供其他服务调用）
func (s *ChannelLiveMgrServer) CheckAnchorUpcomingMatchPk(ctx context.Context, in *pb.CheckAnchorUpcomingMatchPkReq) (out *pb.CheckAnchorUpcomingMatchPkResp, err error) {
	out = &pb.CheckAnchorUpcomingMatchPkResp{
		UidHasUpcomingMatch: make(map[uint32]bool),
		UidErrorMsg:         make(map[uint32]string),
	}

	log.InfoWithCtx(ctx, "CheckAnchorUpcomingMatchPk in:%v", in)

	comingPkUser, err := s.checkUpcomeingAppointPkTs(ctx, in.GetUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckAnchorUpcomingMatchPk checkUpcomeingAppointPkTs failed in:%v err:%v", in, err)
		return out, err
	}

	if len(comingPkUser) > 0 {
		for _, uid := range in.GetUidList() {
			if _, ok := comingPkUser[uid]; ok {
				out.UidHasUpcomingMatch[uid] = true
			}
		}
	}
	log.DebugWithCtx(ctx, "CheckAnchorUpcomingMatchPk out:%v", out)
	return out, nil
}

// CancelPkMatch 取消PK匹配
// 第一代PK接口：取消正在进行的PK匹配，将用户从匹配池中移除
func (s *ChannelLiveMgrServer) CancelPkMatch(ctx context.Context, req *pb.CancelPkMatchReq) (*pb.CancelPkMatchResp, error) {
	resp := &pb.CancelPkMatchResp{}

	// 更新匹配状态为关闭，从匹配池中移除用户
	match.UpdateMatchState(s.liveMgrCache, req.Uid, req.ChannelId, 0, pb.EnumChannelLiveStatus_CLOSE)

	// 记录取消匹配的日志
	//s.mysqlStore.AddMatchLog(ctx, int64(req.Uid), int64(pb.EnumPkMatch_PKM_Match_Close), 0, 0, 0, 0)

	return resp, nil
}

// GetPKMatchInfo 获取PK匹配信息
// 第一代PK接口：获取用户的PK匹配相关信息，包括段位、匹配类型、限制信息等
func (s *ChannelLiveMgrServer) GetPKMatchInfo(ctx context.Context, req *pb.GetPKMatchInfoReq) (*pb.GetPKMatchInfoResp, error) {
	resp := &pb.GetPKMatchInfoResp{}

	log.DebugWithCtx(ctx, "GetPKMatchInfo req:%v", req)

	// 获取当前时间段的匹配类型
	matchTy := conf.GetMatchType(uint32(time.Now().Unix()))
	// 获取用户的匹配值和段位名称
	matchValue, levelName := s.mgr.GetPKMatchValue(req.Uid, matchTy)

	// 构建竞技信息
	resp.CptInfo = &pb.PkCompetitionInfo{
		Level:     matchValue,                                       // 段位分数
		LevelName: fmt.Sprintf("我的段位:%v%v分", levelName, matchValue), // 段位显示名称
	}
	resp.PkMatchTy = matchTy                  // 当前匹配类型
	resp.PkLimitInfo = s.mgr.GetPkLimitInfo() // PK限制信息

	log.DebugWithCtx(ctx, "GetPKMatchInfo resp:%v", resp)

	return resp, nil
}

func (s *ChannelLiveMgrServer) BatGetChannelLiveInfo(ctx context.Context, req *pb.BatGetChannelLiveInfoReq) (*pb.BatGetChannelLiveInfoResp, error) {
	resp := &pb.BatGetChannelLiveInfoResp{}

	if len(req.GetUidList()) > 100 {
		return resp, errors.New("一次批量数量不能超过100")
	}

	// 先读缓存
	uid2Info, err := s.liveMgrCache.GetAnchorByUidList(req.GetUidList()...)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatGetChannelLiveInfo GetAnchorByUidList failed req:%v err:%v", req, err)
		return resp, err
	}

	noCacheList := make([]uint32, 0)
	for _, uid := range req.GetUidList() {
		if _, ok := uid2Info[uid]; !ok {
			noCacheList = append(noCacheList, uid)
		}
	}

	if len(noCacheList) != 0 {
		log.DebugWithCtx(ctx, "BatGetChannelLiveInfo get from db req:%v list:%v", req, noCacheList)

		liveInfoList, err := s.mysqlStore.GetChannelLiveInfoByUidList(ctx, noCacheList)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatGetChannelLiveInfo GetChannelLiveInfoByUidList failed req:%v err:%v", req, err)
			return resp, err
		}

		if len(liveInfoList) != 0 {
			noCacheCidList := make([]uint32, 0)
			for _, info := range liveInfoList {
				noCacheCidList = append(noCacheCidList, info.ChannelID)
			}

			tmpMapCid2TagID, err := s.aclExtLogicApi.GetChannelTagList(ctx, 0, noCacheCidList)
			if err != nil {
				log.ErrorWithCtx(ctx, "BatGetChannelLiveInfo BatchGetChannelTag failed req:%v err:%v", req, err)
				return resp, err
			}

			for _, info := range liveInfoList {
				pbInfo := &pb.ChannelLiveInfo{
					Uid:        info.Uid,
					ChannelId:  info.ChannelID,
					BeginTime:  info.BeginTime,
					EndTime:    info.EndTime,
					CreateTime: info.CreateTime,
					OperName:   info.OperUser,
					TagId:      tmpMapCid2TagID[info.ChannelID],
					Authority:  info.Authority,
				}
				uid2Info[info.Uid] = pbInfo

				// 设置缓存
				err = s.liveMgrCache.SetChannelLiveInfo(info.Uid, pbInfo)
				if err != nil {
					log.ErrorWithCtx(ctx, "BatGetChannelLiveInfo SetChannelLiveInfo failed req:%v info:%v err:%v", req, req, pbInfo, err)
				}
			}
		}

	}

	for _, info := range uid2Info {
		resp.InfoList = append(resp.InfoList, info)
	}

	log.DebugWithCtx(ctx, "BatGetChannelLiveInfo end req:%v resp:%v", req, resp)
	return resp, nil
}
