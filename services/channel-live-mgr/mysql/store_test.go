package mysql

import (
	"context"
	"database/sql"
	"fmt"
	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/jmoiron/sqlx"
	"golang.52tt.com/pkg/config"
	"testing"
	"time"
)

var mysqlStore *Store

func init() {
	mysqlConf := &config.MysqlConfig{
		Host:     "*************",
		Port:     3306,
		Database: "appsvr",
		Charset:  "utf8",
		UserName: "godman",
		Password: "thegodofman",
	}
	mysqlDb, err := sqlx.Connect("mysql", mysqlConf.ConnectionString())
	if err != nil {
		fmt.Errorf("Failed to Connect mysql %v", err)
		return
	}

	mysqlStore = NewMysql(mysqlDb, mysqlDb)
}

func TestStore_GetAnchorUpcomingPKRivalAppointPkList(t *testing.T) {
	ctx := context.Background()
	nowTs := uint32(time.Now().Unix())
	err := mysqlStore.Transaction(ctx, func(tx *sql.Tx) error {
		appointId, sqlErr := mysqlStore.AddAppointPkInfo(ctx, tx, 12345, nowTs+10, nowTs+1000, nowTs, "test")
		if sqlErr != nil {

			return sqlErr
		}

		sqlErr = mysqlStore.AddAppointPkRivalInfo(ctx, tx, appointId, 78910, nowTs+10, nowTs)
		if sqlErr != nil {
			fmt.Println("SetAppointPkInfo mysqlStore.AddAppointPkRivalInfo failed  err", sqlErr)
			return sqlErr
		}
		return nil
	})

	if err != nil {
		fmt.Println("SetAppointPkInfo mysqlStore.AddAppointPkRivalInfo failed  err", err)
		return
	}

	list1, err := mysqlStore.GetAnchorUpcomingAppointPkList(ctx, []uint32{12345, 78910}, nowTs, nowTs+1000)
	fmt.Println("list1 > ", len(list1), err)
	list2, err := mysqlStore.GetAnchorUpcomingPKRivalAppointPkList(ctx, []uint32{12345, 78910}, nowTs, nowTs+1000)
	fmt.Println("list2 >", len(list2), err)
	for _, v := range list2 {
		fmt.Println(v)
	}
}

/*
func TestStore_UpdateNoticeCfg(t *testing.T) {

// go  test -timeout 30s -run ^TestAddAnchorScore$ golang.52tt.com/services/channel-live-mgr/mysql -v -count=1
func TestAddAnchorScore(t *testing.T) {
	// AddAnchorScore(ctx context.Context, uid uint32, addScore int32, orderId string, sourceType uint32, outsideTime uint32, missionDetail *pb.MissionAwardDetail)

	f, err := mysqlStore.AddAnchorScore(context.TODO(), 1, 10, "qx", 1, 1, &channellivemgr.MissionAwardDetail{})
	t.Log(f, err)
}

// go  test -timeout 30s -run ^TestGetAnchorScoreOrderIds$ golang.52tt.com/services/channel-live-mgr/mysql -v -count=1
func TestGetAnchorScoreOrderIds(t *testing.T) {
	//begin := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), time.Now().Hour(), time.Now().Minute()-10, 0, 0, time.Local)
	//end := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), time.Now().Hour(), time.Now().Minute()+1, 0, 0, time.Local)

	list, err := mysqlStore.GetAnchorScoreOrderIds(context.TODO(), uint32(time.Now().Unix()), 1, 10)
	t.Log(list, err)
}

// go  test -timeout 30s -run ^TestGetAwardAnchorScoreTotal$ golang.52tt.com/services/channel-live-mgr/mysql -v -count=1
func TestGetAwardAnchorScoreTotal(t *testing.T) {
	//begin := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), time.Now().Hour(), time.Now().Minute()-10, 0, 0, time.Local)
	//end := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), time.Now().Hour(), time.Now().Minute()+1, 0, 0, time.Local)

	info, err := mysqlStore.GetAwardAnchorScoreTotal(context.TODO(), uint32(time.Now().Unix()), 1, 10)
	t.Log(info, err)
}

type ChannelLiveInfoTT struct {
	Uid       uint32 `db:"uid"`
	ChannelID uint32 `db:"channel_id"`
	Authority uint32 `db:"authority"`
}

func TestGet(t *testing.T) {
	//ctx := context.Background()

	/*	chLive := &ChannelLiveInfoTT{}
		sql := "select uid,channel_id,authority from tbl_channel_live where uid=?"
		err := mysqlStore.db.GetContext(ctx, chLive, sql, 10)

		if strings.Contains(err.Error(), "no rows") {
			fmt.Printf("err:%v chLive:%+v\n", err.Error(), chLive)
		}*/

//mysqlStore.AddMatchLog(ctx, 1024, 1, 2, 30)

//fmt.Printf(time.Now().Format("2006-01-02 00:00:00"))

//beginDru := time.Duration(3600*2+100) * time.Second
//fmt.Printf("%02d:%02d", int(beginDru.Minutes())/60, int(beginDru.Minutes())%60)
//}

/*func TestStore_GetAnchorScore(t *testing.T) {
	ctx := context.Background()
	res, err := mysqlStore.GetAnchorScore( ctx, 2210427)

	fmt.Println(res,err)
}

func TestStore_AddAnchorScore(t *testing.T) {
	ctx := context.Background()
	res, err := mysqlStore.AddAnchorScore( ctx, 2246451,1, "test_8", 0, &pb.MissionAwardDetail{})

	fmt.Println(res,err)
}

func TestStore_GenMonthAnchorScore(t *testing.T) {
	b, err := mysqlStore.GenMonthAnchorScore(time.Now().AddDate(0,-1,0))

	fmt.Println(b, err)
}

func TestStore_AdjustMonthAnchorScore(t *testing.T) {
	err := mysqlStore.AdjustMonthAnchorScore(time.Now().AddDate(0,-1,0), time.Now())

	fmt.Println(err)
}*/
