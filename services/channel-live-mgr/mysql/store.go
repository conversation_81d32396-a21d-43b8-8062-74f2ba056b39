package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/jmoiron/sqlx"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/channellivemgr"
	"golang.52tt.com/services/channel-live-mgr/metrics"
	// "time"
)

const (
	tblAnchorTotal = "tbl_anchor_channel_live_total_data"

	tblChannelLive    = "tbl_channel_live" // 直播权限
	tblChannelLiveLog = "tbl_channel_live_log"

	tblAnchorScore           = "tblAnchorScore"
	tblAnchorScoreOrder      = "tblAnchorScoreOrder"
	tblAnchorScoreMonthOrder = "tblAnchorScoreOrder_%s"

	tblAppointPkInfo      = "tbl_appoint_pk_info"
	tblAppointPkRivalInfo = "tbl_appoint_pk_rival_info"
	channelLiveInfoFiles  = "uid, channel_id,UNIX_TIMESTAMP(create_time) as create_time,UNIX_TIMESTAMP(begin_time) as begin_time,UNIX_TIMESTAMP(end_time) as end_time,oper_user,authority"

	tblChannelLiveRecord = "tblChannelLiveRecord" // 直播的数据记录，供运营人员查询 直播后的数据

	tblMonthAnchorScore = "tblMonthAnchorScore_%d%02d"
	tblAnchorOperLog    = "anchor_oper_log" // 运营人员对主播的操作记录

	/*
		tblChannelLivePkRecord = "tblChannelLivePkRecord" // PK开始的记录(废弃)
		tblChannelLiveOperLog  = "tbl_channel_live_oper_log" // 直播过程的主播操作记录(废弃)
	*/
)

var createChannelLiveTable = `CREATE TABLE IF NOT EXISTS tbl_channel_live (
  uid int(10) unsigned NOT NULL COMMENT '主播UID',
  channel_id int(10) unsigned NOT NULL COMMENT '主播房间ID',
  create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  begin_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '权限开始时间',
  end_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '权限结束时间',
  oper_user varchar(25) NOT NULL DEFAULT '' COMMENT '开通主播操作后台人员',
  authority int(10) unsigned NOT NULL DEFAULT 0 COMMENT '主播权限',
  PRIMARY KEY (uid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8`

var createChannelLiveLogTable = `CREATE TABLE IF NOT EXISTS tbl_channel_live_log (
  id int(10) unsigned NOT NULL auto_increment,
  uid int(10) unsigned NOT NULL COMMENT '主播UID',
  channel_id int(10) unsigned NOT NULL COMMENT '主播房间ID',
  create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  begin_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '权限开始时间',
  end_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '权限结束时间',
  oper_user varchar(25) NOT NULL DEFAULT '' COMMENT '开通主播操作后台人员',
  authority int(10) unsigned NOT NULL DEFAULT 0 COMMENT '主播权限',
  index uid_index(uid),
  primary key (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8`

var createAnchorTotalDataTable = `create table IF NOT EXISTS tbl_anchor_channel_live_total_data (
	uid int(10) unsigned not null, 
	channel_id int(10) unsigned not null, 
	first_live_time timestamp not null comment '首次开播时间',
	total_score int(10) unsigned not null comment '历史直播总流水',
	PRIMARY KEY (uid)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8`

var createAnchorScoreTable = `create table IF NOT EXISTS tblAnchorScore(
	uid int(10) unsigned not null,
	score int(10) unsigned not null default 0,
	primary key(uid)
) engine=InnoDB default charset=utf8`

var createAnchorScoreOrderTable = `create table IF NOT EXISTS %s(
	id int(10) unsigned NOT NULL auto_increment,
	uid int(10) unsigned not null,
	order_id varchar(128) not null,
	add_score int(10) not null,
	final_score int(10) unsigned not null,
	source_type int unsigned not null default 0,
	create_time timestamp not null default current_timestamp,
	outside_time int(10) unsigned NOT NULL DEFAULT 0,
	mission_time_cnt int unsigned not null default 0 COMMENT '任务中直播时长',
	mission_income int unsigned not null default 0 COMMENT '任务中直播收入',
	mission_award_percent float not null default 0 COMMENT '任务中奖励流水百分比',
	mission_award_base_score int unsigned not null default 0 COMMENT '任务中基础奖励积分',
	mission_real_award_score int unsigned not null default 0 COMMENT '任务中实际奖励积分',
	primary key(id),
	index idx_uid(uid),
	unique key(order_id)
) engine=InnoDB default charset=utf8`

var createMonthAnchorScoreTable = `create table IF NOT EXISTS %s(
	uid int(10) unsigned not null,
	score int(10) unsigned not null default 0,
    update_time timestamp not null default current_timestamp on update current_timestamp,
	primary key(uid)
) engine=InnoDB default charset=utf8`

// 直播数据表
var createLiveRecoredTable = `create table IF NOT EXISTS tblChannelLiveRecord (
	uid int(10) unsigned not null,
	channel_id int(10) unsigned not null COMMENT '房间ID',
	channel_live_id int(10) unsigned not null COMMENT '直播ID',
	score int(10) unsigned not null default 0 COMMENT '积分',
	anchor_gift_value int(10) unsigned not null default 0 COMMENT '本场主播收礼值',
	live_time int(10) unsigned not null default 0 COMMENT '直播时长',
	add_fans int(10) unsigned not null default  0 COMMENT '新增粉丝数',
	add_group_fans int(10) unsigned not null default 0 COMMENT '新增骑士团粉丝数',
	audiences_cnt int(10) unsigned not null default 0,
	send_gift_user_cnt int(10) unsigned not null default 0 COMMENT '送礼用户数',
	begin_time timestamp not null default CURRENT_TIMESTAMP COMMENT '直播开始时间',
	end_time timestamp not null default CURRENT_TIMESTAMP COMMENT '直播结束时间',
	knight_value int(10) unsigned NOT NULL DEFAULT '0' COMMENT '骑士流水',
  	game_fee int(10) unsigned NOT NULL DEFAULT '0' COMMENT '互动游戏流水',
  	game_ts int(10) unsigned NOT NULL DEFAULT '0' COMMENT '互动游戏时长',
  	anchor_type int(10) unsigned NOT NULL DEFAULT '0' COMMENT '主播类型',
  	PRIMARY KEY (uid, channel_live_id),
    KEY channel_live_id_index (channel_live_id),
    KEY idx_begin_time (begin_time),
    KEY idx_end_time (end_time)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8`

/*

// PK开始的记录
var createLivePkRecoredTable = `create table IF NOT EXISTS tblChannelLivePkRecord(
    uid int(10) unsigned not null,
    account varchar(32) not null ,
    channel_id int(10) unsigned not null COMMENT 'PK发起方房间ID',
    channel_live_id bigint not null COMMENT 'PK发起方的直播ID',
	target_uid int(10) unsigned not null,
	target_account varchar (32) not null ,
    target_channel_id int(10) unsigned not null COMMENT 'PK接收方房间ID',
	target_channel_live_id bigint not null COMMENT 'PK接收方的直播ID',
	create_time timestamp not null default current_timestamp,
	index uid_index(uid),
	index target_uid_index(target_uid)
    ) engine=InnoDB default charset=utf8`


// 直播过程的主播操作记录
var createOperTable = `create table IF NOT EXISTS tbl_channel_live_oper_log(
	uid int(10) unsigned not null,
    channel_id int(10) unsigned not null,
    channel_live_id bigint unsigned not null,
	oper int(10) unsigned not null,
    create_time timestamp not null default current_timestamp
) engine=InnoDB default charset=utf8`

// PK匹配记录
var createMatchLogTable = `create table IF NOT EXISTS tbl_match_log(
	idx bigint not null auto_increment,
	uid int(10) unsigned not null,
	oper int(10) unsigned not null comment '1 开始匹配，0，取消匹配，2匹配成功',
	match_type int(10) unsigned not null comment '1 随机匹配， 2 排位匹配',
    match_score int(10) unsigned not null comment '匹配分值',
    target_uid int(10) unsigned not null comment '成功匹配对象UID',
    target_score int(10) unsigned not null comment '成功匹配对象PK分值',
    create_time timestamp not null default current_timestamp,
    primary key(idx),
    KEY index_create_time (create_time)
) engine=InnoDB default charset=utf8`

*/

type ChannelLiveInfo struct {
	Uid        uint32 `db:"uid"`
	ChannelID  uint32 `db:"channel_id"`
	CreateTime uint32 `db:"create_time"`
	BeginTime  uint32 `db:"begin_time"`
	EndTime    uint32 `db:"end_time"`
	OperUser   string `db:"oper_user"`
	Authority  uint32 `db:"authority"`
}

type ChannelLiveRecored struct {
	Uid             uint32 `db:"uid"`
	ChannelId       uint32 `db:"channel_id"`
	ChannelLiveId   uint32 `db:"channel_live_id"`
	LiveTime        uint32 `db:"live_time"` // 直播持续时间 s 单位
	BeginTime       uint32 `db:"begin_time"`
	EndTime         uint32 `db:"end_time"`
	Score           uint32 `db:"score"` // 直播收礼值
	AddFans         uint32 `db:"add_fans"`
	AddGroupFans    uint32 `db:"add_group_fans"`
	Audiences       uint32 `db:"audiences_cnt"`      // 观众总数
	SendGiftUserCnt uint32 `db:"send_gift_user_cnt"` // 送礼人数
	AnchorGiftValue uint32 `db:"anchor_gift_value"`  // 主播收礼值
	KnightValue     uint32 `db:"knight_value"`       // 骑士团流水
	GameFee         uint32 `db:"game_fee"`           // 互动有效流水
	GameTs          uint32 `db:"game_ts"`            //互动游戏时长 秒
	AnchorType      uint32 `db:"anchor_type"`        // 主播身份
}

type TotalRecored struct {
	AllScore uint32 `db:"score"`
}

type AnchorScore struct {
	Uid   uint32 `db:"uid"`
	Score uint32 `db:"score"`
}

type AnchorMonthScore struct {
	Uid        uint32    `db:"uid"`
	Score      uint32    `db:"score"`
	UpdateTime time.Time `db:"update_time"`
}

type AnchorOperLog struct {
	Id         uint32    `db:"id"`
	Uid        uint32    `db:"uid"`
	Tid        string    `db:"tid"`
	NickName   string    `db:"nickname"`
	GuildId    uint32    `db:"guild_id"`
	GuildName  string    `db:"guild_name"`
	TagId      uint32    `db:"tag_id"`
	OperType   uint32    `db:"oper_type"`
	OperUser   string    `db:"oper_user"`
	UpdateTime time.Time `db:"update_time"`
	CreateTime time.Time `db:"create_time"`
}

type VirtualAnchorPer struct {
	Id       uint32    `db:"id"`
	Cid      uint32    `db:"cid"`
	Uid      uint32    `db:"uid"`
	BeginTm  time.Time `db:"begin_tm"`
	EndTm    time.Time `db:"end_tm"`
	UpdateTm time.Time `db:"update_tm"`
	CreateTm time.Time `db:"create_tm"`
}

func NewMysql(db, readonlyDb *sqlx.DB) *Store {
	return &Store{
		db:         db,
		readonlyDb: readonlyDb,
	}
}

type Store struct {
	db         *sqlx.DB
	readonlyDb *sqlx.DB
}

func (c *Store) CreateTables() error {

	// 直播权限
	_, err := c.db.Exec(createChannelLiveTable)
	if err != nil {
		log.Errorf("CreateTables fail sql:%v err:%v", createChannelLiveTable, err)
	}

	_, err = c.db.Exec(createChannelLiveLogTable)
	if err != nil {
		log.Errorf("CreateTables fail sql:%v err:%v", createChannelLiveLogTable, err)
	}

	// 直播数据
	_, err = c.db.Exec(createLiveRecoredTable)
	if err != nil {
		log.Errorf("CreateTables fail sql:%v err:%v", createLiveRecoredTable, err)
	}

	// 主播积分
	_, err = c.db.Exec(createAnchorScoreTable)
	if err != nil {
		log.Errorf("CreateTables fail sql:%v err:%v", createAnchorScoreTable, err)
	}

	_, err = c.db.Exec(fmt.Sprintf(createAnchorScoreOrderTable, c.genAnchorScoreOrderTblName(time.Now())))
	if err != nil {
		log.Errorf("CreateTables fail sql:%v err:%v", fmt.Sprintf(createAnchorScoreOrderTable, c.genAnchorScoreOrderTblName(time.Now())), err)
	}

	_, err = c.db.Exec(createAnchorTotalDataTable)
	if err != nil {
		log.Errorf("CreateTables fail sql:%v err:%v", createAnchorTotalDataTable, err)
	}

	/*

		_, err = c.db.Exec(createLivePkRecoredTable)
		if err != nil {
			log.Errorf("CreateTables fail sql:%v err:%v", createLivePkRecoredTable, err)
		}

		_, err = c.db.Exec(createOperTable)
		if err != nil {
			log.Errorf("CreateTables fail sql:%v err:%v", createOperTable, err)
		}

		_, err = c.db.Exec(createMatchLogTable)
		if err != nil {
			log.Errorf("CreateTables fail sql:%v err:%v", createMatchLogTable, err)

		}
	*/

	return err
}

func (s *Store) Transaction(ctx context.Context, fn func(tx *sql.Tx) error) error {
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return err
	}

	err = fn(tx)
	if err != nil {
		_ = tx.Rollback()
		return err
	}

	_ = tx.Commit()
	return nil
}

/*
// PK匹配记录（废弃）
func (c *Store) AddMatchLog(ctx context.Context, uid, oper, matchType, matchScore, targetUid, targetScore int64) error {
	sql := fmt.Sprintf("insert into tbl_match_log (uid,oper,match_type,match_score,target_uid,target_score) values(%v,%v,%v,%v,%v,%v)", uid, oper, matchType, matchScore, targetUid, targetScore)
	_, err := c.db.Exec(sql)
	if nil != err {
		log.Errorf("AddMatchLog err:%v", err)
	}
	return err
}

// 直播过程的主播操作记录（废弃）
func (c *Store) AddOperLog(ctx context.Context, uid, channelId, oper uint32, channelLiveId uint64) error {
	sql := fmt.Sprintf("insert into %v (uid, channel_id, channel_live_id, oper) values(?,?,?,?)", tblChannelLiveOperLog)
	_, err := c.db.Exec(sql, uid, channelId, channelLiveId, oper)
	if err != nil {
		log.Errorf("addOperLog err:%v uid:%v", err, uid)
	}
	return nil
}

*/

func (c *Store) GetChannelLiveInfo(ctx context.Context, channelLiveinfo *pb.ChannelLiveInfo, uid uint32) error {
	sql := fmt.Sprintf("select %s from %s where uid=?", channelLiveInfoFiles, tblChannelLive)

	chLive := &ChannelLiveInfo{}

	err := c.readonlyDb.GetContext(ctx, chLive, sql, uid)
	if err != nil {
		log.Errorf("GetChannelLiveInfo select fail err:%v", err)
		return err
	}

	channelLiveinfo.ChannelId = chLive.ChannelID
	channelLiveinfo.Uid = chLive.Uid
	channelLiveinfo.CreateTime = chLive.CreateTime
	channelLiveinfo.BeginTime = chLive.BeginTime
	channelLiveinfo.EndTime = chLive.EndTime
	channelLiveinfo.OperName = chLive.OperUser
	channelLiveinfo.Authority = chLive.Authority

	return nil
}

func (c *Store) GetChannelLiveInfoByCid(ctx context.Context, channelLiveinfo *pb.ChannelLiveInfo, cid uint32) error {
	sql := fmt.Sprintf("select %s from %s where  channel_id =?", channelLiveInfoFiles, tblChannelLive)

	chLive := &ChannelLiveInfo{}

	err := c.readonlyDb.GetContext(ctx, chLive, sql, cid)
	if err != nil {
		log.Errorf("GetChannelLiveInfoByCid select fail err:%v", err)
		return err
	}

	channelLiveinfo.ChannelId = chLive.ChannelID
	channelLiveinfo.Uid = chLive.Uid
	channelLiveinfo.CreateTime = chLive.CreateTime
	channelLiveinfo.BeginTime = chLive.BeginTime
	channelLiveinfo.EndTime = chLive.EndTime
	channelLiveinfo.OperName = chLive.OperUser
	channelLiveinfo.Authority = chLive.Authority

	return nil
}

func (c *Store) GetAllChannelLiveInfo(ctx context.Context, channelLiveinfoArr *[]*pb.ChannelLiveInfo, off, count int) error {
	sql := fmt.Sprintf("select %s from %s limit ?,?", channelLiveInfoFiles, tblChannelLive)

	chLiveList := make([]*ChannelLiveInfo, 0)

	err := c.readonlyDb.SelectContext(ctx, &chLiveList, sql, off, count)
	if err != nil {
		log.Errorf("GetAllChannelLiveInfo select fail err:%v", err)
		return err
	}

	for _, v := range chLiveList {

		tmp := &pb.ChannelLiveInfo{
			Uid:        v.Uid,
			ChannelId:  v.ChannelID,
			CreateTime: v.CreateTime,
			BeginTime:  v.BeginTime,
			EndTime:    v.EndTime,
			OperName:   v.OperUser,
			Authority:  v.Authority,
		}

		*channelLiveinfoArr = append(*channelLiveinfoArr, tmp)
	}

	return nil
}

func (c *Store) GetChannelLiveInfoByUidList(ctx context.Context, uidList []uint32) ([]*ChannelLiveInfo, error) {
	chLiveList := make([]*ChannelLiveInfo, 0)

	if len(uidList) == 0 {
		return chLiveList, nil
	}

	strUidList := make([]string, 0)
	for _, uid := range uidList {
		strUidList = append(strUidList, fmt.Sprintf("%d", uid))
	}
	strUid := strings.Join(strUidList, ",")

	sql := fmt.Sprintf("select %s from %s where uid in(%s)", channelLiveInfoFiles, tblChannelLive, strUid)

	err := c.db.SelectContext(ctx, &chLiveList, sql)
	if err != nil {
		return chLiveList, err
	}

	return chLiveList, nil
}

func (c *Store) GetValidAnchorList(ctx context.Context, channelLiveinfoArr *[]*pb.ChannelLiveInfo, off, count int, uidList []uint32, beginTs, endTs uint32) error {
	sql := fmt.Sprintf("select %s from %s where end_time >= now() limit ?,?", channelLiveInfoFiles, tblChannelLive)

	if len(uidList) != 0 {
		strUidList := make([]string, 0)
		for _, uid := range uidList {
			strUidList = append(strUidList, fmt.Sprintf("%d", uid))
		}
		strUid := strings.Join(strUidList, ",")

		sql = fmt.Sprintf("select %s from %s where uid in(%s) and end_time >= now() limit ?,?", channelLiveInfoFiles, tblChannelLive, strUid)
	} else if beginTs != 0 && endTs != 0 {
		sql = fmt.Sprintf("select %s from %s where end_time >= now() and create_time >= FROM_UNIXTIME(%d) and create_time < FROM_UNIXTIME(%d) limit ?,?",
			channelLiveInfoFiles, tblChannelLive, beginTs, endTs)
	}

	chLiveList := make([]*ChannelLiveInfo, 0)

	err := c.readonlyDb.SelectContext(ctx, &chLiveList, sql, off, count)
	if err != nil {
		log.Errorf("GetAllChannelLiveInfo select fail err:%v", err)
		return err
	}

	for _, v := range chLiveList {

		tmp := &pb.ChannelLiveInfo{
			Uid:        v.Uid,
			ChannelId:  v.ChannelID,
			CreateTime: v.CreateTime,
			BeginTime:  v.BeginTime,
			EndTime:    v.EndTime,
			OperName:   v.OperUser,
			Authority:  v.Authority,
		}

		*channelLiveinfoArr = append(*channelLiveinfoArr, tmp)
	}

	return nil
}

// 获取主播总数
func (c *Store) GetValidAnchorTotalCnt(ctx context.Context, uidList []uint32, beginTs, endTs uint32) (cnt uint32, err error) {
	sql := fmt.Sprintf("select count(1) from %s where end_time >= now()", tblChannelLive)

	if len(uidList) != 0 {
		strUidList := make([]string, 0)
		for _, uid := range uidList {
			strUidList = append(strUidList, fmt.Sprintf("%d", uid))
		}
		strUid := strings.Join(strUidList, ",")

		sql = fmt.Sprintf("select count(1) from %s where uid in(%s) and end_time >= now()", tblChannelLive, strUid)
	} else if beginTs != 0 && endTs != 0 {
		sql = fmt.Sprintf("select count(1) from %s where end_time >= now() and create_time >= FROM_UNIXTIME(%d) and create_time < FROM_UNIXTIME(%d)",
			tblChannelLive, beginTs, endTs)
	}

	err = c.readonlyDb.GetContext(ctx, &cnt, sql)
	if err != nil && !strings.Contains(err.Error(), "Error 1146") {
		return 0, err
	}
	return cnt, nil
}

func (c *Store) SetChannelLiveInfo(ctx context.Context, channelLiveinfo *pb.ChannelLiveInfo) error {

	tmpSql := "insert into %s (uid, channel_id, begin_time, end_time, oper_user, authority) values(?,?,FROM_UNIXTIME(?),FROM_UNIXTIME(?),?,?) ON DUPLICATE KEY UPDATE " +
		"channel_id=?, begin_time=FROM_UNIXTIME(?), end_time=FROM_UNIXTIME(?), oper_user=?, authority=?"

	sql := fmt.Sprintf(tmpSql, tblChannelLive)
	_, err := c.db.ExecContext(ctx, sql, channelLiveinfo.Uid,
		channelLiveinfo.ChannelId,
		channelLiveinfo.BeginTime, channelLiveinfo.EndTime, channelLiveinfo.OperName, channelLiveinfo.Authority,
		channelLiveinfo.ChannelId,
		channelLiveinfo.BeginTime, channelLiveinfo.EndTime, channelLiveinfo.OperName, channelLiveinfo.Authority)

	if err != nil {
		log.Errorf("SetChannelLiveInfo insert fail err:%v", err)
		return err
	}

	sqlLog := fmt.Sprintf(tmpSql, tblChannelLiveLog)
	_, err = c.db.ExecContext(ctx, sqlLog, channelLiveinfo.Uid,
		channelLiveinfo.ChannelId,
		channelLiveinfo.BeginTime, channelLiveinfo.EndTime, channelLiveinfo.OperName, channelLiveinfo.Authority,
		channelLiveinfo.ChannelId,
		channelLiveinfo.BeginTime, channelLiveinfo.EndTime, channelLiveinfo.OperName, channelLiveinfo.Authority)

	if err != nil {
		log.Errorf("SetChannelLiveInfoLog insert fail err:%v", err)
		//return err
	}

	return nil
}

// 回收主播直播权限
func (c *Store) DelChannelLiveInfo(ctx context.Context, uid uint32) error {
	sql := fmt.Sprintf("update %s set end_time = current_time(), authority=0 where uid = ?", tblChannelLive)
	_, err := c.db.Exec(sql, uid)

	if err != nil {
		log.Errorf("DelChannelLiveInfo delete fail err:%v", err)
		return err
	}

	// 记录操作关闭时间
	var id int64 = 0
	sql = fmt.Sprintf("select max(id) from %s where uid = %v", tblChannelLiveLog, uid)
	c.db.GetContext(ctx, &id, sql)

	if id > 0 {
		updateSql := fmt.Sprintf("update %s set end_time = '%v' where uid = %v and id = %v",
			tblChannelLiveLog, time.Now().Format("2006-01-02 15:04:05"), uid, id)
		c.db.Exec(updateSql)

		fmt.Printf("%v %v\n", updateSql, id)
	}

	return nil
}

// 直播记录
func (c *Store) AddChannelLiveRecord(ctx context.Context, uid, channelId, anchorType uint32, channel_live_id uint64) error {
	sql := fmt.Sprintf("insert into %s (uid, channel_id, channel_live_id, anchor_type) values(?,?,?,?)", tblChannelLiveRecord)
	_, err := c.db.ExecContext(ctx, sql, uid, channelId, channel_live_id, anchorType)

	if err != nil {
		log.Errorf("AddChannelLiveRecord insert fail err:%v", err)
		return err
	}
	return nil
}

func (c *Store) GetMaxChannelLiveID(ctx context.Context) (uint64, error) {
	sql := fmt.Sprintf("select max(channel_live_id) from %s", tblChannelLiveRecord)
	var channelLiveID uint64 = 0
	err := c.db.GetContext(ctx, &channelLiveID, sql)
	if err != nil {
		log.Errorf("GetMaxChannelLiveID err:%v", err)
		return 0, err
	}
	return channelLiveID, nil
}

func (c *Store) UpdateAnchorTotalData(uid, channelID, score uint32, ts string) {
	sql := fmt.Sprintf("insert into %v (uid, channel_id, first_live_time, total_score) values(?,?,?,?) ON DUPLICATE KEY update total_score=total_score+?", tblAnchorTotal)
	_, err := c.db.Exec(sql, uid, channelID, ts, score, score)
	if err != nil {
		log.Errorf("UpdateAnchorTotalData uid:%v score:%v ts:%v err:%v", uid, score, ts, err)
	}
}

func (c *Store) AddChannelLiveRecordWhenFinish(ctx context.Context, channel_live_id uint64,
	giftValue, addFans, addGroupFans, audiences, sendGiftUserCnt, anchorGiftValue, knightValue, gameFee, gameTs uint32) error {

	log.Debugf("AddChannelLiveRecordWhenFinish %d %d %d %d %d %d %d %d", channel_live_id, giftValue,
		addFans, addGroupFans, audiences, sendGiftUserCnt, anchorGiftValue, knightValue)

	sql := fmt.Sprintf("update %s set end_time=from_unixtime(?), score=if(score>=?,score,?),add_fans=if(add_fans>=?,add_fans,?),"+
		"add_group_fans=if(add_group_fans>=?,add_group_fans,?),audiences_cnt=if(audiences_cnt>=?,audiences_cnt,?),"+
		"send_gift_user_cnt=if(send_gift_user_cnt>=?,send_gift_user_cnt,?), anchor_gift_value=if(anchor_gift_value>=?,anchor_gift_value,?), "+
		"knight_value=if(knight_value>=?,knight_value,?), game_fee=if(game_fee>=?,game_fee,?), game_ts=if(game_ts>=?,game_ts,?) where channel_live_id=?",
		tblChannelLiveRecord)

	_, err := c.db.ExecContext(ctx, sql, time.Now().Unix(), giftValue, giftValue, addFans, addFans, addGroupFans, addGroupFans,
		audiences, audiences, sendGiftUserCnt, sendGiftUserCnt, anchorGiftValue, anchorGiftValue, knightValue, knightValue, gameFee,
		gameFee, gameTs, gameTs, channel_live_id)

	if err != nil {
		log.Errorf("AddChannelLiveRecordWhenFinish err:%v", err)
		return err
	}
	return nil
}

// 根据主播UID和时间段获取直播记录
func (c *Store) GetChannelLiveRecordsByTime(ctx context.Context, uids []uint32, beginTs, endTs uint32) (map[uint32][]*ChannelLiveRecored, error) {
	mapRecord := make(map[uint32][]*ChannelLiveRecored)

	if len(uids) == 0 {
		return mapRecord, nil
	}

	uidStrArr := make([]string, 0)
	for _, uid := range uids {
		uidStrArr = append(uidStrArr, fmt.Sprintf("%v", uid))
	}

	uidStr := strings.Join(uidStrArr, ",")

	sql := fmt.Sprintf("SELECT uid, channel_id, channel_live_id, UNIX_TIMESTAMP(begin_time) AS begin_time, "+
		" UNIX_TIMESTAMP(end_time) AS end_time, score, add_fans, add_group_fans, audiences_cnt, send_gift_user_cnt, "+
		" live_time,anchor_gift_value "+
		" FROM %s "+
		" WHERE uid in (%v) and begin_time >= FROM_UNIXTIME(?) and begin_time <= FROM_UNIXTIME(?) ORDER BY begin_time",
		tblChannelLiveRecord, uidStr)

	records := make([]*ChannelLiveRecored, 0)
	err := c.readonlyDb.SelectContext(ctx, &records, sql, beginTs, endTs)
	if err != nil {
		log.Errorf("GetChannelLiveRecords select fail err:%v", err)
		return mapRecord, err
	}

	for _, re := range records {
		if _, ok := mapRecord[re.Uid]; !ok {
			mapRecord[re.Uid] = make([]*ChannelLiveRecored, 0)
		}
		mapRecord[re.Uid] = append(mapRecord[re.Uid], re)
	}

	return mapRecord, nil
}

type AnchorTotalInfo struct {
	Uid         uint32 `db:"uid"`
	ChannelId   uint32 `db:"channel_id"`
	TotalScore  uint32 `db:"total_score"`
	FirstLiveTs uint32 `db:"first_live_time"`
}

func (c *Store) BatchGetAnchorTotalData(ctx context.Context, uids []uint32) (map[uint32]*AnchorTotalInfo, error) {

	uidStrArr := make([]string, 0)
	for _, uid := range uids {
		uidStrArr = append(uidStrArr, fmt.Sprintf("%v", uid))
	}

	uidStr := strings.Join(uidStrArr, ",")

	resArr := make([]*AnchorTotalInfo, 0)

	sql := fmt.Sprintf("select uid,channel_id,total_score, unix_timestamp(first_live_time) as first_live_time "+
		"from %v where uid in (%v);", tblAnchorTotal, uidStr)

	err := c.readonlyDb.SelectContext(ctx, &resArr, sql)

	mapAnchor := make(map[uint32]*AnchorTotalInfo)
	for _, res := range resArr {
		mapAnchor[res.Uid] = res
	}
	return mapAnchor, err
}

// 根据主播UID和偏移限制获取最近的主播场次的直播记录
func (c *Store) GetChannelLiveRecords(ctx context.Context, uid, offset, limit uint32) ([]*ChannelLiveRecored, error) {
	sql := fmt.Sprintf("select uid, channel_id, channel_live_id, UNIX_TIMESTAMP(begin_time) AS begin_time, UNIX_TIMESTAMP(end_time) AS end_time, "+
		"score, add_fans, add_group_fans, audiences_cnt, send_gift_user_cnt, live_time,anchor_gift_value,knight_value, game_fee, game_ts, anchor_type "+
		"from %s where uid = ? ORDER BY begin_time DESC LIMIT ? OFFSET ?", tblChannelLiveRecord)

	records := make([]*ChannelLiveRecored, 0)
	err := c.readonlyDb.SelectContext(ctx, &records, sql, uid, limit, offset)
	if err != nil {
		log.Errorf("GetChannelLiveRecords select fail err:%v", err)
		return records, err
	}

	return records, nil
}

/*
// 直播 PK 的 记录
func (c *Store) AddChannelLivePkRecored(ctx context.Context, uid, channelId,targetUid, targetChannelId uint32,
	channelLiveId, targetChannelLiveId uint64, account, targetAccount string) error {

	sql := fmt.Sprintf("INSERT INTO %s "+
		" (uid, account, channel_id,channel_live_id,target_uid, target_account,target_channel_id, target_channel_live_id) "+
		" VALUES(?,?,?,?,?,?,?,?)",
		tblChannelLivePkRecord)
	_, err := c.db.ExecContext(ctx, sql,
		uid, account, channelId, channelLiveId, targetUid, targetAccount, targetChannelId, targetChannelLiveId)
	if err != nil {
		log.Errorf("AddChannelLivePkRecored insert fail err:%v", err)
		return err
	}
	return nil
}

func (c *Store) GetChannelLivePkRecoreds(ctx context.Context, uid uint32) ([]*metrics.ChannelLivePkRecored, error) {
	sql := fmt.Sprintf("SELECT uid,channel_id,channel_live_id,target_uid,target_channel_id,target_channel_live_id"+
		" FROM %s where uid = ? or target_uid=?",
		tblChannelLivePkRecord)

	pkRecored := make([]*metrics.ChannelLivePkRecored, 0)
	err := c.readonlyDb.SelectContext(ctx, &pkRecored, sql, uid, uid)

	if err != nil {
		log.Errorf("GetChannelLivePkRecoreds select fail err:%v", err)
		return pkRecored, err
	}
	return pkRecored, nil
}
*/

// 增加主播额外积分
func (c *Store) AddAnchorScore(ctx context.Context, uid uint32, addScore int32, orderId string, sourceType uint32, outsideTime uint32, missionDetail *pb.MissionAwardDetail) (finalScore uint32, err error) {
	now := time.Now()
	thisMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	lastMonth := thisMonth.AddDate(0, -1, 0)

	checkOrderSql := fmt.Sprintf("select count(*) from %s where order_id = '%s'", c.genAnchorScoreOrderTblName(now), orderId)
	checkLastMonthOrderSql := fmt.Sprintf("select count(*) from %s where order_id = '%s'", c.genAnchorScoreOrderTblName(lastMonth), orderId)

	tx := c.db.MustBegin()

	var cnt int64
	// 查本月
	err = tx.GetContext(ctx, &cnt, checkOrderSql)
	if err != nil && !strings.Contains(err.Error(), "Error 1146") {
		tx.Rollback()
		log.Errorf("AddAnchorScore select fail %s %v", orderId, err)
		return 0, err
	}
	if cnt > 0 {
		tx.Rollback()
		log.Errorf("AddAnchorScore mup order_id %s", orderId)
		return 0, protocol.NewExactServerError(nil, status.ErrUserScoreOrderExist)
	}
	// 查上月
	err = tx.GetContext(ctx, &cnt, checkLastMonthOrderSql)
	if err != nil && !strings.Contains(err.Error(), "Error 1146") {
		tx.Rollback()
		log.Errorf("AddAnchorScore select fail %s %v", orderId, err)
		return 0, err
	}
	if cnt > 0 {
		tx.Rollback()
		log.Errorf("AddAnchorScore mup order_id %s", orderId)
		return 0, protocol.NewExactServerError(nil, status.ErrUserScoreOrderExist)
	}

	var insertScore int32 = 0
	if addScore > 0 {
		insertScore = addScore
	}

	updateSql := fmt.Sprintf("insert into %s (uid, score) value(?,?) ON DUPLICATE KEY UPDATE score = if( (?)>0 or (score >= abs(?)), score+(?), score );", tblAnchorScore)
	result, err := tx.Exec(updateSql, uid, insertScore, addScore, addScore, addScore)
	if err != nil {
		tx.Rollback()
		log.Errorf("AddAnchorScore insert fail %s %v", orderId, err)
		return 0, err
	}
	if addScore < 0 {
		if rows, _ := result.RowsAffected(); rows == 0 {
			tx.Rollback()
			log.Errorf("AddAnchorScore insert fail %s %v", orderId, err)
			return 0, protocol.NewExactServerError(nil, status.ErrUserScoreNotEnough)
		}
	}

	selectSql := fmt.Sprintf("select uid, score from %s where uid = ?", tblAnchorScore)
	finalResult, txErr2 := tx.Query(selectSql, uid)
	if txErr2 != nil {
		tx.Rollback()
		log.Errorf("AddAnchorScore select fail %s %v", orderId, txErr2)
		return 0, txErr2
	}

	defer finalResult.Close()

	if finalResult.Err() != nil {
		log.Errorf("AddAnchorScore finalResult.Err() %s err:%v", orderId, finalResult.Err())
	}

	for finalResult.Next() {
		var uid, score uint32

		err := finalResult.Scan(&uid, &score)
		if err != nil {
			tx.Rollback()
			log.Errorf("AddAnchorScore Scan fail %s %v", orderId, err)
			return 0, err
		}
		finalScore = score
	}

	addLogSql := fmt.Sprintf("insert into %s (uid, order_id, add_score, final_score, source_type,"+
		"mission_time_cnt,mission_income,mission_award_percent,mission_award_base_score,mission_real_award_score,outside_time) values(?,?,?,?,?,?,?,?,?,?,?)",
		c.genAnchorScoreOrderTblName(now))
	_, err = tx.Exec(addLogSql, uid, orderId, addScore, finalScore, sourceType,
		missionDetail.GetLiveTimeCnt(), missionDetail.GetLiveIncome(), missionDetail.GetAwardPercent(), missionDetail.GetAwardBaseScore(), missionDetail.GetRealAwardScore(), outsideTime)

	if err != nil {
		// 表不存在
		if strings.Contains(err.Error(), "Error 1146") {
			// 建表
			_, err = tx.Exec(fmt.Sprintf(createAnchorScoreOrderTable, c.genAnchorScoreOrderTblName(now)))
			if err != nil {
				tx.Rollback()
				log.Errorf("AddAnchorScore create table fail %s %v", orderId, err)
				return 0, err
			}
			// retry add log
			_, err = tx.Exec(addLogSql, uid, orderId, addScore, finalScore, sourceType,
				missionDetail.GetLiveTimeCnt(), missionDetail.GetLiveIncome(), missionDetail.GetAwardPercent(), missionDetail.GetAwardBaseScore(), missionDetail.GetRealAwardScore(), outsideTime)
			if err != nil {
				tx.Rollback()
				log.Errorf("AddAnchorScore insert fail %s %v", orderId, err)
				return 0, err
			}

		} else {
			tx.Rollback()
			log.Errorf("AddAnchorScore insert fail %s %v", orderId, err)
			return 0, err
		}
	}

	tx.Commit()

	return finalScore, nil
}

func (c *Store) GetAnchorScore(ctx context.Context, uid uint32) (uint32, error) {
	query := fmt.Sprintf("select uid,score from %s where uid = ?", tblAnchorScore)

	anchorScore := &AnchorScore{}

	err := c.db.GetContext(ctx, anchorScore, query, uid)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, nil
		}
		return 0, err
	}
	return anchorScore.Score, nil
}

type AnchorScoreLog struct {
	Uid        uint32 `db:"uid"`
	OrderId    string `db:"order_id"`
	AddScore   int32  `db:"add_score"`
	FinalScore uint32 `db:"final_score"`
	SourceType uint32 `db:"source_type"`
	CreateTime uint32 `db:"create_time"`
}

func (c *Store) GetAnchorScoreMonthLog(ctx context.Context, uid, begin, limit uint32, sourceList []uint32, t time.Time) ([]*AnchorScoreLog, error) {
	logs := make([]*AnchorScoreLog, 0, limit)
	sql := fmt.Sprintf("select uid,order_id,add_score,final_score,source_type,UNIX_TIMESTAMP(create_time) as create_time from %s where uid=? ",
		c.genAnchorScoreOrderTblName(t))

	strList := make([]string, 0)
	if len(sourceList) > 0 {
		for _, source := range sourceList {
			strList = append(strList, fmt.Sprint(source))
		}

		strSourceList := strings.Join(strList, ",")
		sql += fmt.Sprintf("and source_type in (%s) ", strSourceList)
	}

	sql += "order by create_time desc limit ?,?"

	err := c.readonlyDb.SelectContext(ctx, &logs, sql, uid, begin, limit)
	if err != nil && !strings.Contains(err.Error(), "Error 1146") {
		log.Errorf("GetAnchorScoreMonthLog fail. uid:%d err:%v", uid, err)
		return logs, err
	}

	log.Debugf("GetAnchorScoreMonthLog uid:%d size(%d)", uid, len(logs))
	return logs, nil
}

func (c *Store) GetAnchorScoreMonthLogCnt(ctx context.Context, uid uint32, t time.Time) (cnt uint32, err error) {
	sql := fmt.Sprintf("select count(1) from %s where uid=? ", c.genAnchorScoreOrderTblName(t))
	err = c.readonlyDb.GetContext(ctx, &cnt, sql, uid)
	if err != nil && !strings.Contains(err.Error(), "Error 1146") {
		log.Errorf("GetAnchorScoreMonthLogCnt fail. uid:%d err:%v", uid, err)
		return 0, err
	}

	log.Debugf("GetAnchorScoreMonthLogCnt uid:%d cnt:%d", uid, cnt)
	return cnt, nil
}

func (s *Store) GetAnchorScoreOrder(ctx context.Context, uid uint32, beginTm, endTm time.Time) ([]*AnchorScoreLog, error) {
	orderList := make([]*AnchorScoreLog, 0)
	sql := fmt.Sprintf("select uid,order_id,add_score,final_score,source_type,UNIX_TIMESTAMP(create_time) as create_time from %s where uid=? and"+
		" create_time >= ? and create_time <= ?",
		s.genAnchorScoreOrderTblName(beginTm))

	err := s.readonlyDb.SelectContext(ctx, &orderList, sql, uid, beginTm, endTm)
	if err != nil && !strings.Contains(err.Error(), "Error 1146") {
		return orderList, err
	}

	return orderList, nil
}

func (s *Store) GetAnchorScoreOrderList(ctx context.Context, offset, limit uint32, beginTm, endTm time.Time) ([]*AnchorScoreLog, error) {
	orderList := make([]*AnchorScoreLog, 0)
	sql := fmt.Sprintf("select uid,order_id,add_score,final_score,source_type,UNIX_TIMESTAMP(create_time) as create_time from %s where "+
		"create_time >= ? and create_time <= ? limit ?, ?",
		s.genAnchorScoreOrderTblName(beginTm))

	err := s.readonlyDb.SelectContext(ctx, &orderList, sql, beginTm, endTm, offset, limit)
	if err != nil && !strings.Contains(err.Error(), "Error 1146") {
		return orderList, err
	}

	return orderList, nil
}

// 生成月度剩余表
func (c *Store) GenMonthAnchorScore(month time.Time) (bool, error) {
	tableName := fmt.Sprintf("tblMonthAnchorScore_%d%02d", month.Year(), month.Month())
	createSql := fmt.Sprintf(createMonthAnchorScoreTable, tableName)
	now := time.Now()

	tx, err := c.db.Beginx()
	if err != nil {
		log.Errorf("GenMonthAnchorScore fail. err:%v", err)
		return false, err
	}

	_, err = tx.Exec(createSql)
	if err != nil {
		log.Errorf("GenMonthAnchorScore fail. sql:%v, err:%v", createSql, err)
		tx.Rollback()

		// 表已存在
		if strings.Contains(err.Error(), "Error 1050") {
			return true, nil
		}

		return false, err
	}

	num := 0
	begin := 0
	limit := 500
	querySql := fmt.Sprintf("select uid,score from %s where score>0 limit ?,?", tblAnchorScore)

	for {
		scoreList := make([]*AnchorScore, 0, limit)
		// 从从库读取
		err = c.readonlyDb.Select(&scoreList, querySql, begin, limit)
		if err != nil {
			log.Errorf("GenMonthAnchorScore fail. sql:%v, err:%v", querySql, err)
			tx.Rollback()
			return false, err
		}

		strList := make([]string, 0, len(scoreList))
		for _, info := range scoreList {
			num++
			str := fmt.Sprintf("(%d,%d)", info.Uid, info.Score)
			strList = append(strList, str)
		}

		if len(strList) == 0 {
			break
		}

		strVal := strings.Join(strList, ",")

		insertSql := fmt.Sprintf("insert into %s(uid,score) values %s", tableName, strVal)
		_, err = tx.Exec(insertSql)
		if err != nil {
			log.Errorf("GenMonthAnchorScore fail. sql:%v, err:%v", insertSql, err)
			tx.Rollback()
			return false, err
		}

		begin += len(strList)
	}

	tx.Commit()
	log.Infof("GenMonthAnchorScore done. num:%v, cost:%v", num, time.Since(now))

	return false, nil
}

// 修正月度剩余表数据
func (c *Store) AdjustMonthAnchorScore(month time.Time, now time.Time) error {
	monthScoreTbl := fmt.Sprintf("tblMonthAnchorScore_%d%02d", month.Year(), month.Month())
	monthScoreLogTbl := c.genAnchorScoreOrderTblName(now)
	logs := make([]*AnchorScoreLog, 0)

	sql := fmt.Sprintf("select uid,order_id,add_score,final_score,source_type,UNIX_TIMESTAMP(create_time) as create_time from %s "+
		"where id in (select min(id) from %s where create_time <= ? group by uid)", monthScoreLogTbl, monthScoreLogTbl)

	err := c.db.Select(&logs, sql, now)
	if err != nil {
		log.Errorf("AdjustMonthAnchorScore fail. err:%v", err)
		return err
	}

	num := 0
	begin := 0
	limit := 500
	for {
		end := begin + limit
		if end > len(logs) {
			end = len(logs)
		}
		if begin >= len(logs) {
			break
		}

		tmpLogs := logs[begin:end]
		strList := make([]string, 0, limit)

		for _, info := range tmpLogs {
			score := int32(info.FinalScore) - info.AddScore
			if score < 0 {
				continue
			}

			strList = append(strList, fmt.Sprintf("(%d,%d)", info.Uid, score))
			num++
		}

		if len(strList) == 0 {
			break
		}

		strValues := strings.Join(strList, ",")
		updateSql := fmt.Sprintf("replace into %s(uid,score) values %s", monthScoreTbl, strValues)
		_, err = c.db.Exec(updateSql)
		if err != nil {
			log.Errorf("AdjustMonthAnchorScore fail. err:%v", err)
			return err
		}

		begin += len(strList)
	}

	log.Infof("AdjustMonthAnchorScore done. len(logs):%v, num:%v, cost:%v", len(logs), num, time.Since(now))
	return nil
}

func (s *Store) GetAnchorMonthScore(ctx context.Context, uidList []uint32, tm time.Time) ([]*AnchorMonthScore, error) {
	infoList := make([]*AnchorMonthScore, 0)

	idStrArr := make([]string, 0)
	for _, id := range uidList {
		idStrArr = append(idStrArr, fmt.Sprintf("%d", id))
	}
	idStr := strings.Join(idStrArr, ",")

	sql := fmt.Sprintf("select uid, score, update_time from %s where uid in (%s)", fmt.Sprintf(tblMonthAnchorScore, tm.Year(), tm.Month()), idStr)
	err := s.readonlyDb.SelectContext(ctx, &infoList, sql)
	if err != nil {
		return infoList, err
	}

	return infoList, nil
}

func (s *Store) GetAnchorMonthScoreList(ctx context.Context, offset, limit uint32, tm time.Time) ([]*AnchorMonthScore, error) {
	sql := fmt.Sprintf("select uid, score, update_time from %s limit ?,?", fmt.Sprintf(tblMonthAnchorScore, tm.Year(), tm.Month()))

	list := make([]*AnchorMonthScore, 0)

	err := s.readonlyDb.SelectContext(ctx, &list, sql, offset, limit)
	if err != nil {
		return list, err
	}

	return list, nil
}

func (c *Store) genAnchorScoreOrderTblName(now time.Time) string {
	if now.Before(time.Date(2021, 2, 1, 0, 0, 0, 0, time.Local)) {
		return tblAnchorScoreOrder
	} else {
		return fmt.Sprintf(tblAnchorScoreMonthOrder, now.Format("200601"))
	}
}

// 新增指定pk信息
func (s *Store) AddAppointPkInfo(ctx context.Context, tx *sql.Tx, uid, beginTs, endTs, updateTs uint32, operator string) (uint32, error) {
	sql := fmt.Sprintf("insert into %s (uid, begin_ts, end_ts, update_ts, operator) values(?,?,?,?,?)", tblAppointPkInfo)
	res, err := tx.ExecContext(ctx, sql, uid, beginTs, endTs, updateTs, operator)
	if err != nil {
		return 0, err
	}

	lastId, _ := res.LastInsertId()

	return uint32(lastId), nil
}

// 删除指定pk信息
func (s *Store) DelAppointPkInfo(ctx context.Context, tx *sql.Tx, appointId uint32) error {
	sql := fmt.Sprintf("delete from %s where appoint_id = ?", tblAppointPkInfo)
	_, err := tx.ExecContext(ctx, sql, appointId)
	if err != nil {
		return err
	}
	return nil
}

// 更新指定pk信息
func (s *Store) UpdateAppointPkInfo(ctx context.Context, tx *sql.Tx, appointId, uid, beginTs, endTs, updateTs uint32, operator string) error {
	sql := fmt.Sprintf("update %s set uid = ?, begin_ts = ?, end_ts = ?, update_ts = ?, operator = ? where appoint_id = ?", tblAppointPkInfo)
	_, err := tx.ExecContext(ctx, sql, uid, beginTs, endTs, updateTs, operator, appointId)
	if err != nil {
		return err
	}
	return nil
}

func (s *Store) GetAppointPkInfoList(ctx context.Context, uid, beginTs, endTs, offset, limit uint32, queryType pb.GetAppointPkInfoListReq_QueryType) ([]*metrics.AppointPkInfo, error) {
	infoList := make([]*metrics.AppointPkInfo, 0)

	switch queryType {
	case pb.GetAppointPkInfoListReq_Query_All:
		sql := fmt.Sprintf("select appoint_id, uid, begin_ts, end_ts, update_ts, operator from %s order by update_ts desc limit ?,?", tblAppointPkInfo)
		err := s.db.SelectContext(ctx, &infoList, sql, offset, limit)
		if err != nil {
			return infoList, err
		}
	case pb.GetAppointPkInfoListReq_Query_By_Ts:
		sql := fmt.Sprintf("select appoint_id, uid, begin_ts, end_ts, update_ts, operator from %s where begin_ts >= ? and end_ts <= ? "+
			"order by update_ts desc limit ?,?", tblAppointPkInfo)
		err := s.db.SelectContext(ctx, &infoList, sql, beginTs, endTs, offset, limit)
		if err != nil {
			return infoList, err
		}
	case pb.GetAppointPkInfoListReq_Query_By_Uid:
		sql := fmt.Sprintf("select appoint_id, uid, begin_ts, end_ts, update_ts, operator from %s where uid = ? "+
			"order by update_ts desc limit ?,?", tblAppointPkInfo)
		err := s.db.SelectContext(ctx, &infoList, sql, uid, offset, limit)
		if err != nil {
			return infoList, err
		}
	}

	return infoList, nil
}

// 获取主播还没结束的指定PK列表
func (s *Store) GetAnchorValidAppointPkList(ctx context.Context, uid uint32) ([]*metrics.AppointPkInfo, error) {
	infoList := make([]*metrics.AppointPkInfo, 0)

	nowTs := uint32(time.Now().Unix())

	sql := fmt.Sprintf("select appoint_id, uid, begin_ts, end_ts, update_ts, operator from %s where uid = ? and end_ts >= ?", tblAppointPkInfo)
	err := s.db.SelectContext(ctx, &infoList, sql, uid, nowTs)
	if err != nil {
		return infoList, err
	}

	return infoList, nil
}

func (s *Store) GetAppointPkInfoTotalCnt(ctx context.Context, uid, beginTs, endTs uint32, queryType pb.GetAppointPkInfoListReq_QueryType) (uint32, error) {
	var cnt uint32 = 0

	switch queryType {
	case pb.GetAppointPkInfoListReq_Query_All:
		sql := fmt.Sprintf("select count(1) from %s", tblAppointPkInfo)
		err := s.db.GetContext(ctx, &cnt, sql)
		if err != nil {
			return 0, err
		}
	case pb.GetAppointPkInfoListReq_Query_By_Ts:
		sql := fmt.Sprintf("select count(1) from %s where begin_ts >= ? and end_ts <= ? ", tblAppointPkInfo)
		err := s.db.GetContext(ctx, &cnt, sql, beginTs, endTs)
		if err != nil {
			return 0, err
		}
	case pb.GetAppointPkInfoListReq_Query_By_Uid:
		sql := fmt.Sprintf("select count(1) from %s where uid = ? ", tblAppointPkInfo)
		err := s.db.GetContext(ctx, &cnt, sql, uid)
		if err != nil {
			return 0, err
		}
	}

	return cnt, nil
}

// 获取需要处理的指定pk信息列表
func (s *Store) GetNeedProcAppointPkList(ctx context.Context, nowTs, ts uint32) ([]*metrics.AppointPkInfo, error) {
	infoList := make([]*metrics.AppointPkInfo, 0)

	sql := fmt.Sprintf("select appoint_id, uid, begin_ts, end_ts, update_ts, operator from %s where begin_ts <= ? and end_ts >= ?", tblAppointPkInfo)
	err := s.db.SelectContext(ctx, &infoList, sql, ts, nowTs)
	if err != nil {
		return infoList, err
	}

	return infoList, nil
}

func (s Store) GetAppointPkInfoById(ctx context.Context, appointId uint32) (*metrics.AppointPkInfo, error) {
	info := &metrics.AppointPkInfo{}

	sql := fmt.Sprintf("select appoint_id, uid, begin_ts, end_ts, update_ts, operator from %s where appoint_id = ?", tblAppointPkInfo)
	err := s.db.GetContext(ctx, info, sql, appointId)
	if err != nil {
		return info, err
	}

	return info, nil
}

// 新增指定pk对手消息
func (s *Store) AddAppointPkRivalInfo(ctx context.Context, tx *sql.Tx, appointId, uid, beginTs, updateTs uint32) error {
	sql := fmt.Sprintf("insert into %s (appoint_id, uid, begin_ts, update_ts) values(?,?,?,?)", tblAppointPkRivalInfo)
	_, err := tx.ExecContext(ctx, sql, appointId, uid, beginTs, updateTs)
	if err != nil {
		return err
	}

	return nil
}

// 删除指定pk对手消息
func (s *Store) DelAppointPkRivalInfo(ctx context.Context, tx *sql.Tx, appointId uint32) error {
	sql := fmt.Sprintf("delete from %s where appoint_id = ?", tblAppointPkRivalInfo)
	_, err := tx.ExecContext(ctx, sql, appointId)
	if err != nil {
		return err
	}

	return nil
}

type PkRivalDetailInfo struct {
	AppointId uint32 `db:"appoint_id"`
	Uid       uint32 `db:"uid"`
	BeginTs   uint32 `db:"begin_ts"`
	EndTs     uint32 `db:"end_ts"`
}

func (s *Store) GetPkRivalDetailInfoListByUids(ctx context.Context, uidList []uint32) ([]*PkRivalDetailInfo, error) {
	infoList := make([]*PkRivalDetailInfo, 0)

	if len(uidList) == 0 {
		return infoList, nil
	}

	uidStrArr := make([]string, 0)
	for _, uid := range uidList {
		uidStrArr = append(uidStrArr, fmt.Sprintf("%v", uid))
	}
	uidStr := strings.Join(uidStrArr, ",")

	nowTs := uint32(time.Now().Unix())

	sql := fmt.Sprintf("select a.appoint_id, b.uid, a.begin_ts, a.end_ts from %s a inner join %s b on a.appoint_id = b.appoint_id "+
		"where b.uid in(%v) and a.end_ts >= ? ", tblAppointPkInfo, tblAppointPkRivalInfo, uidStr)
	err := s.db.SelectContext(ctx, &infoList, sql, nowTs)
	if err != nil {
		return infoList, err
	}

	return infoList, nil
}

// 获取主播即将开始的指定PK信息（用于检查10分钟内限制）
func (s *Store) GetAnchorUpcomingAppointPkList(ctx context.Context, uidList []uint32, startTs, endTs uint32) ([]*metrics.AppointPkInfo, error) {
	infoList := make([]*metrics.AppointPkInfo, 0)

	idStrArr := make([]string, 0)
	for _, id := range uidList {
		idStrArr = append(idStrArr, fmt.Sprintf("%v", id))
	}
	idStr := strings.Join(idStrArr, ",")
	sql := fmt.Sprintf(
		"select appoint_id, uid, begin_ts from %s where uid in (%s) and begin_ts >= ? and begin_ts <= ?",
		tblAppointPkInfo, idStr)
	err := s.db.SelectContext(ctx, &infoList, sql, startTs, endTs)
	if err != nil {
		return infoList, err
	}

	return infoList, nil
}

func (s *Store) GetAnchorUpcomingPKRivalAppointPkList(ctx context.Context, uidList []uint32, startTs, endTs uint32) ([]*metrics.AppointPkRivalInfo, error) {
	infoList := make([]*metrics.AppointPkRivalInfo, 0)

	idStrArr := make([]string, 0)
	for _, id := range uidList {
		idStrArr = append(idStrArr, fmt.Sprintf("%v", id))
	}
	idStr := strings.Join(idStrArr, ",")
	sql := fmt.Sprintf(
		"select appoint_id, uid, begin_ts from %s where uid in (%v) and begin_ts >= ? and begin_ts <= ? ",
		tblAppointPkRivalInfo, idStr)
	err := s.db.SelectContext(ctx, &infoList, sql, startTs, endTs)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorUpcomingPKRivalAppointPkList failed in:%v err:%v", uidList, err)
		return infoList, err
	}
	return infoList, nil
}

func (s *Store) GetAppointPkRivalListByIds(ctx context.Context, uidList []uint32) ([]*metrics.AppointPkRivalInfo, error) {
	infoList := make([]*metrics.AppointPkRivalInfo, 0)
	if len(uidList) == 0 {
		return infoList, nil
	}

	idStrArr := make([]string, 0)
	for _, id := range uidList {
		idStrArr = append(idStrArr, fmt.Sprintf("%v", id))
	}
	idStr := strings.Join(idStrArr, ",")

	sql := fmt.Sprintf("select appoint_id, uid, begin_ts from %s where appoint_id in(%v)", tblAppointPkRivalInfo, idStr)
	err := s.db.SelectContext(ctx, &infoList, sql)
	if err != nil {
		return infoList, err
	}

	return infoList, nil
}

// 运营人员对主播的操作记录
func (c *Store) AddAnchorOperLog(ctx context.Context, operLog AnchorOperLog) error {

	sql := fmt.Sprintf("insert into %s "+
		"(tid,nickname,uid,guild_id,guild_name,tag_id,oper_type,oper_user) values(?,?,?,?,?,?,?,?)",
		tblAnchorOperLog)

	_, err := c.db.ExecContext(ctx, sql, operLog.Tid, operLog.NickName, operLog.Uid, operLog.GuildId,
		operLog.GuildName, operLog.TagId, operLog.OperType, operLog.OperUser)
	return err
}

func (c *Store) GetAnchorOperLogList(ctx context.Context, guildId, operType uint32, uidList []uint32, offset, limit uint32) ([]*AnchorOperLog, error) {
	sql := fmt.Sprintf("select * from %s where 1 ", tblAnchorOperLog)

	if guildId != 0 {
		sql += fmt.Sprintf("and guild_id = %d ", guildId)
	}
	if operType != uint32(pb.OperateType_InValidOper) {
		sql += fmt.Sprintf("and oper_type = %d ", operType)
	}
	if len(uidList) != 0 {
		strUidList := make([]string, 0)
		for _, uid := range uidList {
			strUidList = append(strUidList, fmt.Sprintf("%d", uid))
		}
		strUid := strings.Join(strUidList, ",")

		sql += fmt.Sprintf("and uid in(%s) ", strUid)
	}

	sql += "order by id desc limit ?, ?"

	logList := make([]*AnchorOperLog, 0)
	err := c.readonlyDb.SelectContext(ctx, &logList, sql, offset, limit)
	if err != nil {
		return logList, err
	}

	return logList, nil
}

func (c *Store) GetAnchorOperLogTotalCnt(ctx context.Context, guildId, operType uint32, uidList []uint32) (uint32, error) {
	sql := fmt.Sprintf("select count(1) from %s where 1 ", tblAnchorOperLog)

	if guildId != 0 {
		sql += fmt.Sprintf("and guild_id = %d ", guildId)
	}
	if operType != uint32(pb.OperateType_InValidOper) {
		sql += fmt.Sprintf("and oper_type = %d ", operType)
	}
	if len(uidList) != 0 {
		strUidList := make([]string, 0)
		for _, uid := range uidList {
			strUidList = append(strUidList, fmt.Sprintf("%d", uid))
		}
		strUid := strings.Join(strUidList, ",")

		sql += fmt.Sprintf("and uid in(%s) ", strUid)
	}

	var cnt uint32
	err := c.readonlyDb.GetContext(ctx, &cnt, sql)
	if err != nil {
		return cnt, err
	}

	return cnt, nil
}

func (s *Store) AddVirtualAnchorPer(ctx context.Context, anchorPer VirtualAnchorPer) error {
	sql := "insert into virtual_anchor_per (cid,uid,begin_tm,end_tm) values(?,?,?,?)"
	_, err := s.db.ExecContext(ctx, sql, anchorPer.Cid, anchorPer.Uid, anchorPer.BeginTm, anchorPer.EndTm)
	return err
}

func (s *Store) UpdateVirtualAnchorPer(ctx context.Context, anchorPer VirtualAnchorPer) error {
	sql := "update virtual_anchor_per set cid=?, uid=?, begin_tm=?, end_tm = ? where id = ?"
	_, err := s.db.ExecContext(ctx, sql, anchorPer.Cid, anchorPer.Uid, anchorPer.BeginTm, anchorPer.EndTm, anchorPer.Id)
	if err != nil {
		return err
	}
	return nil
}

func (s *Store) DelVirtualAnchorPer(ctx context.Context, id uint32) error {
	sql := "delete from virtual_anchor_per where id = ?"
	_, err := s.db.ExecContext(ctx, sql, id)
	if err != nil {
		return err
	}
	return nil
}

func (c *Store) GetVirtualAnchorPerList(ctx context.Context, id, cid, uid, offset, limit uint32, isNowValid, isValid bool) ([]*VirtualAnchorPer, error) {
	sql := "select * from virtual_anchor_per where 1 "

	if cid != 0 {
		sql += fmt.Sprintf("and cid = %d ", cid)
	}
	if uid != 0 {
		sql += fmt.Sprintf("and uid = %d ", uid)
	}
	if isNowValid {
		sql += "and begin_tm <= now() and end_tm >= now() "
	}
	if id != 0 {
		sql += fmt.Sprintf("and id = %d ", id)
	}
	if isValid {
		sql += "and end_tm >= now() "
	}

	sql += "order by id desc limit ?, ?"

	list := make([]*VirtualAnchorPer, 0)
	err := c.readonlyDb.SelectContext(ctx, &list, sql, offset, limit)
	if err != nil {
		return list, err
	}

	return list, nil
}

func (c *Store) GetVirtualAnchorPerTotalCnt(ctx context.Context, cid, uid uint32) (uint32, error) {
	sql := "select count(1) from virtual_anchor_per where 1 "

	if cid != 0 {
		sql += fmt.Sprintf("and cid = %d ", cid)
	}
	if uid != 0 {
		sql += fmt.Sprintf("and uid = %d ", uid)
	}

	var cnt uint32
	err := c.readonlyDb.GetContext(ctx, &cnt, sql)
	if err != nil {
		return cnt, err
	}

	return cnt, nil
}
