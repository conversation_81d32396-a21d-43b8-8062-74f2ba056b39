package manager

import (
	"bou.ke/monkey"
	"context"
	numeric_logic "golang.52tt.com/protocol/app/numeric-logic"
	fellow_accompany "golang.52tt.com/protocol/services/fellow-accompany"
	pb "golang.52tt.com/protocol/services/numeric-rank"
	"golang.52tt.com/services/rank/numeric-rank/cache"
	"golang.52tt.com/services/rank/numeric-rank/conf"
	"google.golang.org/grpc"
	"reflect"
	"testing"
)

func TestManager_GetUserGloryRank(t *testing.T) {
	uid := uint32(123456)

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.ActivityLocalCache{}), "GetActivityKeyList",
		func(c *cache.ActivityLocalCache) cache.ActivityKeyList {
			return []string{"activity_a"}
		})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.ActivityLocalCache{}), "GetUserActivityRankingByKey",
		func(c *cache.ActivityLocalCache, activityKey string, uid uint32) (cache.UserRankInfo, bool) {
			return cache.UserRankInfo{
				Uid:          uid,
				Ranking:      1,
				ActivitySign: "activity_a",
				ActivityName: "activity_a",
				RankSign:     "",
				RankName:     "",
				OfflineTime:  0,
			}, true
		})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.ActivityLocalCache{}), "GetActivityKeyList",
		func(c *cache.ActivityLocalCache) cache.ActivityKeyList {
			return []string{"activity_a"}
		})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericRankCache{}), "GetUserNumericRanking",
		func(c *cache.NumericRankCache, rankType pb.RANK_TYPE, uid uint32) *pb.UserRank {
			return &pb.UserRank{}
		})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericRankCache{}), "GetUserCelebrityRanking",
		func(c *cache.NumericRankCache, uid uint32) *pb.UserActivityRank {
			return nil
		})
	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericRankCache{}), "GetUserGameRanking",
		func(c *cache.NumericRankCache, uid uint32) *pb.UserActivityRank {
			return nil
		})

	monkey.PatchInstanceMethod(reflect.TypeOf(&fellow_accompany.Client{}), "GetUserTop10Info",
		func(c *fellow_accompany.Client, ctx context.Context, req *fellow_accompany.GetUserTop10InfoReq, opts ...grpc.CallOption) (*fellow_accompany.GetUserTop10InfoResp, error) {
			return &fellow_accompany.GetUserTop10InfoResp{
				RankMap: map[uint32]uint32{
					1: 1,
					2: 1,
					3: 1,
				},
			}, nil

		})

	type fields struct {
		cacheStore         cache.INumericRankCache
		sc                 conf.IServiceConfigT
		fellowAccompanyCli fellow_accompany.FellowAccompanyServiceClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetUserGloryInfoReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetUserGloryInfoResp
		wantErr bool
	}{
		{
			name: "GetUserGloryRank",
			fields: fields{
				cacheStore:         &cache.NumericRankCache{},
				sc:                 &conf.ServiceConfigT{},
				fellowAccompanyCli: &fellow_accompany.Client{},
			},
			args: args{
				req: &pb.GetUserGloryInfoReq{
					Uid: uid,
				},
			},
			want: &pb.GetUserGloryInfoResp{
				UserNumericRank: &pb.UserNumericRank{
					Uid:               uid,
					RichDayRanking:    0,
					RichWeekRanking:   0,
					RichMonthRanking:  0,
					CharmDayRanking:   0,
					CharmWeekRanking:  0,
					CharmMonthRanking: 0,
				},
				UserActivityRankList: []*pb.UserActivityRank{
					{
						RankType:     uint32(numeric_logic.GloryRankType_ActivityRank),
						Uid:          uid,
						ActivitySign: "activity_a",
						ActivityName: "activity_a",
						RankSign:     "",
						RankName:     "",
						ActivityRank: 1,
					},
					{
						ActivitySign: fellowAccompanySign,
						ActivityName: fellowAccompanyName,
						RankSign:     DayRankSign,
						RankName:     DayRankName,
						ActivityRank: 1,
						RankType:     uint32(numeric_logic.GloryRankType_FellowAccompanyRank),
					},
					{
						ActivitySign: fellowAccompanySign,
						ActivityName: fellowAccompanyName,
						RankSign:     WeekRankSign,
						RankName:     WeekRankName,
						ActivityRank: 1,
						RankType:     uint32(numeric_logic.GloryRankType_FellowAccompanyRank),
					},
					{
						ActivitySign: fellowAccompanySign,
						ActivityName: fellowAccompanyName,
						RankSign:     monthRankSign,
						RankName:     monthRankName,
						ActivityRank: 1,
						RankType:     uint32(numeric_logic.GloryRankType_FellowAccompanyRank),
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheStore:         tt.fields.cacheStore,
				sc:                 tt.fields.sc,
				fellowAccompanyCli: tt.fields.fellowAccompanyCli,
			}
			got, err := m.GetUserGloryRank(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserGloryRank() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserGloryRank() got = %v, want %v", got, tt.want)
			}
		})
	}
}
