package client

import (
	glorycelebrity "golang.52tt.com/clients/glory-celebrity"
	revenueextgame "golang.52tt.com/clients/revenue-ext-game"
	_ "golang.52tt.com/services/recommend-dialog/tools/grpc_proxy/enable"
)

var (
	GloryCelebrityCli glorycelebrity.IClient
	RevenueExtGameCli revenueextgame.IClient
)

func init() {
	GloryCelebrityCli, _ = glorycelebrity.NewClient()
	RevenueExtGameCli, _ = revenueextgame.NewClient()
}
