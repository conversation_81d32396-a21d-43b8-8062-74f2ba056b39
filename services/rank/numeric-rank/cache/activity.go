package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/numeric-rank"
	"strconv"
	"time"
)

// ActivityKeyList 活动名列表
type ActivityKeyList []string

// ActivityRank 活动榜单结构
type ActivityRank struct {
	ActivitySign string
	ActivityName string
	RankSign     string
	RankName     string
	OfflineTime  int64
	OnlineTime   int64
	RankingMap   map[uint32]uint32 // uid => ranking
}

// UserRankInfo 用户排名结构
type UserRankInfo struct {
	Uid          uint32
	Ranking      uint32
	ActivitySign string
	ActivityName string
	RankSign     string
	RankName     string
	OfflineTime  int64
	OnlineTime   int64
}

// RecordInterval 记录活动榜单最小时间间隔
const RecordInterval = 3 * time.Second

// ReportRankExpire 上报榜单过期时间
const ReportRankExpire = time.Hour * 24 * 30

func GetActivityRankListKey() string {
	return "activity_info_list"
}

func GetActivityRankKey(activitySign, RankSign string) string {
	return fmt.Sprintf("activity_rank:%s:%s", activitySign, RankSign)
}

func GetActivityReportLockKey(sign string) string {
	return fmt.Sprintf("activity_lock:%s", sign)
}

func (c *NumericRankCache) SaveActivityRank(ctx context.Context, req *pb.ReportGloryRankReq) error {
	activitySign, rankSign := req.GetActivitySign(), req.GetRankSign()

	// 活动不存在则先记录活动信息
	c.CheckActivity(ctx, req)

	// 更新本地缓存
	rank := ActivityRank{
		ActivitySign: activitySign,
		ActivityName: req.GetActivityName(),
		RankSign:     rankSign,
		RankName:     req.GetRankName(),
		OfflineTime:  int64(req.GetOfflineTime()),
		OnlineTime:   int64(req.GetOnlineTime()),
		RankingMap:   make(map[uint32]uint32),
	}
	for _, u := range req.GetUserList() {
		rank.RankingMap[u.GetUid()] = u.GetRank()
	}

	rankJs, _ := json.Marshal(rank)
	expire := ReportRankExpire
	if rank.OfflineTime > 0 {
		durSec := rank.OfflineTime - time.Now().Unix() + 60
		if durSec > 0 {
			expire = time.Duration(durSec) * time.Second
		}
	}
	setRet := c.redisClient.Set(GetActivityRankKey(activitySign, rankSign), rankJs, expire)
	if err := setRet.Err(); err != nil {
		log.ErrorWithCtx(ctx, "SaveActivityRank save activity rank err:%s", err)
		return err
	}

	c.activityLocalCache.SetActivityRank(activitySign, rankSign, rank)

	return nil
}

// GetReportLock 活动榜单上报锁
func (c *NumericRankCache) GetReportLock(ctx context.Context, label string) (bool, error) {
	return c.GetLock(ctx, GetActivityReportLockKey(label), RecordInterval)
}
func (c *NumericRankCache) ReleaseReportLock(ctx context.Context, label string) error {
	return c.ReleaseLock(ctx, GetActivityReportLockKey(label))
}

func (c *NumericRankCache) GetLock(ctx context.Context, key string, expire time.Duration) (bool, error) {
	lock, err := c.redisClient.WithContext(ctx).SetNX(key, 1, expire).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLock SetNX err:%v", err)
		return false, err
	}
	return lock, nil
}

func (c *NumericRankCache) ReleaseLock(ctx context.Context, key string) error {
	if err := c.redisClient.WithContext(ctx).Del(key).Err(); err != nil {
		log.ErrorWithCtx(ctx, "ReleaseLock del err:%v", err)
	}
	return nil
}

func (c *NumericRankCache) CheckActivity(ctx context.Context, req *pb.ReportGloryRankReq) {
	activitySign, rankSign := req.GetActivitySign(), req.GetRankSign()
	arKey := GetActivityRankKey(activitySign, rankSign)

	activityList, _ := c.GetActivityKeyList(ctx)
	for _, ar := range activityList {
		if ar == arKey {
			// 活动榜单存在
			return
		}
	}

	// 添加到活动榜单列表
	activityList = append(activityList, arKey)
	js, _ := json.Marshal(activityList)
	ret := c.redisClient.Set(GetActivityRankListKey(), js, 0)
	if ret.Err() != nil {
		log.ErrorWithCtx(ctx, "CheckActivity Set err:%v, sign:%s", ret.Err(), arKey)
	}
}

func (c *NumericRankCache) RemoveActivity(ctx context.Context, activitySign, rankSign string) {
	arKey := GetActivityRankKey(activitySign, rankSign)
	c.RemoveActivityByKey(ctx, arKey)
}

func (c *NumericRankCache) RemoveActivityByKey(ctx context.Context, arKey string) {
	activityList, _ := c.GetActivityKeyList(ctx)
	newActivityList := make(ActivityKeyList, 0)
	for _, ar := range activityList {
		// 排除待删除的榜单
		if ar != arKey {
			newActivityList = append(newActivityList, ar)
		}
	}

	js, _ := json.Marshal(newActivityList)
	ret := c.redisClient.Set(GetActivityRankListKey(), js, 0)
	if ret.Err() != nil {
		log.ErrorWithCtx(ctx, "RemoveActivityByKey Set err:%v, sign:%s", ret.Err(), arKey)
	}
}

func (c *NumericRankCache) GetActivityKeyList(ctx context.Context) (ActivityKeyList, error) {
	var infos ActivityKeyList
	ret := c.redisClient.WithContext(ctx).Get(GetActivityRankListKey())
	if ret.Err() != nil {
		if ret.Err() == redis.Nil {
			return infos, nil
		}
		log.Errorf("GetActivityKeyList Get err:%v", ret.Err())
		return nil, ret.Err()
	}
	js, err := ret.Result()
	if err != nil {
		log.Errorf("GetActivityKeyList Result err:%v", err)
		return nil, err
	}
	if err = json.Unmarshal([]byte(js), &infos); err != nil {
		log.Errorf("GetActivityKeyList Unmarshal err:%v", err)
		return nil, err
	}
	return infos, nil
}

func (c *NumericRankCache) GetActivityRankList(ctx context.Context) ([]ActivityRank, error) {
	keys, err := c.GetActivityKeyList(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetActivityRankList GetActivityKeyList err:%v", err)
		return nil, err
	}
	ranks := make([]ActivityRank, 0, len(keys))
	for _, key := range keys {
		ret := c.redisClient.Get(key)
		var js string
		js, err = ret.Result()
		if err != nil {
			if err == redis.Nil {
				// 榜单不存在，从列表删除Key
				c.RemoveActivityByKey(ctx, key)
				log.InfoWithCtx(ctx, "GetActivityRankList redis.Nil, remove activity, %s", key)
				continue
			}
			log.ErrorWithCtx(ctx, "GetActivityRankList GetActivityRank err:%v, key:%s", err, key)
			continue
		}
		var rank ActivityRank
		if err = json.Unmarshal([]byte(js), &rank); err != nil {
			log.ErrorWithCtx(ctx, "GetActivityRankList Unmarshal err:%v, key:%s", err, key)
			continue
		}
		ranks = append(ranks, rank)
	}
	return ranks, nil
}

func (c *NumericRankCache) GetUserActivityRanking(ctx context.Context, activitySign, RankSign string, uid uint32) (uint32, error) {
	rankKey := GetActivityRankKey(activitySign, RankSign)
	ret := c.redisClient.WithContext(ctx).ZRank(rankKey, strconv.Itoa(int(uid)))
	if ret.Err() != nil {
		log.Errorf("GetUserActivityRanking del err:%v", ret.Err())
		return 0, ret.Err()
	}
	rank, err := ret.Result()
	if err != nil {
		log.Errorf("GetUserActivityRanking del err:%v", err)
		return 0, err
	}
	return uint32(rank), nil
}
