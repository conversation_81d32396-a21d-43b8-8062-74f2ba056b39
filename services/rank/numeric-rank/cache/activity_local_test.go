package cache

import (
	localCache "github.com/patrickmn/go-cache"
	"reflect"
	"testing"
	"time"
)

func TestNewActivityLocalCache(t *testing.T) {
	cache := NewActivityLocalCache()
	if cache == nil || cache.lc == nil {
		t.<PERSON><PERSON>("Expected a valid cache instance, got nil")
	}
}

func TestSetAndGetActivityRank(t *testing.T) {
	cache := NewActivityLocalCache()
	activityRank := ActivityRank{
		ActivitySign: "testActivity",
		RankSign:     "testRank",
		RankingMap:   map[uint32]uint32{1: 1},
	}

	cache.SetActivityRank("testActivity", "testRank", activityRank)
	retrievedRank := cache.GetActivityRank("testActivity", "testRank")

	if retrievedRank.ActivitySign != "testActivity" {
		t.<PERSON><PERSON>rf("Expected testActivity, got %s", retrievedRank.ActivitySign)
	}
}

func TestLocalCache_GetUserActivityRankingByKey(t *testing.T) {
	lc := localCache.New(1*time.Minute, 1*time.Minute)
	uid := uint32(123456)
	key := GetActivityRankKey("activitySign", "rankSign")
	lc.Set(key, ActivityRank{
		RankingMap: map[uint32]uint32{
			uid: 1,
		},
	}, 0)
	type fields struct {
		lc *localCache.Cache
	}
	type args struct {
		key string
		uid uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   UserRankInfo
		want1  bool
	}{
		{
			name: "GetUserActivityRankingByKey",
			fields: fields{
				lc: lc,
			},
			args: args{
				key: key,
				uid: uid,
			},
			want: UserRankInfo{
				Uid:     uid,
				Ranking: 1,
			},
			want1: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &ActivityLocalCache{
				lc: tt.fields.lc,
			}
			got, got1 := c.GetUserActivityRankingByKey(tt.args.key, tt.args.uid)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserActivityRankingByKey() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("GetUserActivityRankingByKey() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestGetUserActivityRanking(t *testing.T) {
	cache := NewActivityLocalCache()
	activityRank := ActivityRank{
		ActivitySign: "testActivity",
		RankSign:     "testRank",
		RankingMap:   map[uint32]uint32{1: 1},
	}

	cache.SetActivityRank("testActivity", "testRank", activityRank)
	rank, exists := cache.GetUserActivityRanking("testActivity", "testRank", 1)

	if !exists || rank != 1 {
		t.Errorf("Expected rank 1 for user 1, got rank %d", rank)
	}
}

func TestGetActivityRank(t *testing.T) {
	cache := NewActivityLocalCache()
	activityRank := ActivityRank{
		ActivitySign: "testActivity2",
		RankSign:     "testRank2",
		RankingMap:   map[uint32]uint32{2: 2},
	}

	cache.SetActivityRank("testActivity2", "testRank2", activityRank)
	retrievedRank := cache.GetActivityRank("testActivity2", "testRank2")

	if retrievedRank.ActivitySign != "testActivity2" {
		t.Errorf("Expected testActivity2, got %s", retrievedRank.ActivitySign)
	}
}

func TestSetActivityRank(t *testing.T) {
	cache := NewActivityLocalCache()
	activityRank := ActivityRank{
		ActivitySign: "testActivity3",
		RankSign:     "testRank3",
		RankingMap:   map[uint32]uint32{3: 3},
	}

	cache.SetActivityRank("testActivity3", "testRank3", activityRank)
	retrievedRank := cache.GetActivityRank("testActivity3", "testRank3")

	if retrievedRank.RankSign != "testRank3" {
		t.Errorf("Expected testRank3, got %s", retrievedRank.RankSign)
	}
}

func TestGetActivityKeyList(t *testing.T) {
	cache := NewActivityLocalCache()
	activityList := ActivityKeyList{"key1", "key2"}
	cache.lc.Set(GetActivityRankListKey(), activityList, localCache.DefaultExpiration)
	retrievedList := cache.GetActivityKeyList()

	if len(retrievedList) != 2 || retrievedList[0] != "key1" {
		t.Errorf("Expected key1 in the retrieved list, got %v", retrievedList)
	}
}
