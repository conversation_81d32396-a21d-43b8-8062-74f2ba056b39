package cache

import (
	context "context"
	time "time"

	redis "github.com/go-redis/redis"
	pb "golang.52tt.com/protocol/services/numeric-rank"
)

type INumericRankCache interface {
	CheckActivity(ctx context.Context, req *pb.ReportGloryRankReq)
	GetActivityKeyList(ctx context.Context) (ActivityKeyList, error)
	GetActivityRankList(ctx context.Context) ([]ActivityRank, error)
	GetCurrentRankingValue(ctx context.Context, uid uint32) (*pb.GetUserCurrentRankValueResp, error)
	GetLock(ctx context.Context, key string, expire time.Duration) (bool, error)
	GetPersonalRanking(ctx context.Context, uid uint32) *pb.UserNumericRank
	GetRankList(ctx context.Context, rankType pb.RANK_TYPE, index, count uint32, isCurrent bool) (rankList []*pb.UserRank, err error)
	GetMyRank(ctx context.Context, rankType pb.RANK_TYPE, uid uint32) (lastRank, currentRank uint32, gapVal uint64, err error)
	GetReportLock(ctx context.Context, label string) (bool, error)
	GetUserActivityRanking(ctx context.Context, activitySign, RankSign string, uid uint32) (uint32, error)
	GetUserActivityRankingByKey(activityKey string, uid uint32) (UserRankInfo, bool)
	GetUserCelebrityRanking(uid uint32) *pb.UserActivityRank
	GetUserGameRanking(uid uint32) *pb.UserActivityRank
	GetUserNumericRanking(rankType pb.RANK_TYPE, uid uint32) *pb.UserRank
	ReleaseLock(ctx context.Context, key string) error
	ReleaseReportLock(ctx context.Context, label string) error
	RemoveActivity(ctx context.Context, activitySign, rankSign string)
	RemoveActivityByKey(ctx context.Context, arKey string)
	SaveActivityRank(ctx context.Context, req *pb.ReportGloryRankReq) error
	ZRangeWithScoresLimit(ctx context.Context, key string, start, limit int64) *redis.ZSliceCmd
	ZRevRangeWithScoresLimit(ctx context.Context, key string, start, limit int64) *redis.ZSliceCmd
}

type IActivityLocalCache interface {
	GetActivityKeyList() ActivityKeyList
	GetActivityRank(activitySign, rankSign string) ActivityRank
	GetUserActivityRanking(activitySign, rankSign string, uid uint32) (uint32, bool)
	GetUserActivityRankingByKey(key string, uid uint32) (UserRankInfo, bool)
	SetActivityRank(activitySign, rankSign string, rank ActivityRank)
}

type ICelebrityRankLocalCache interface {
	GetRankingByUid(uid uint32) uint32
	GetUserRank(uid uint32) *pb.UserActivityRank
	ReloadLocalCache()
}

type IGameRankLocalCache interface {
	GetRankingByUid(uid uint32) uint32
	GetUserRank(uid uint32) *pb.UserActivityRank
	ReloadLocalCache()
}

type INumericRankLocalCache interface {
	ResetLocalCache(rankType pb.RANK_TYPE, newMap map[uint32]pb.UserRank)
}
