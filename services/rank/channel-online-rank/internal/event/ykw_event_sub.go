package event

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"golang.52tt.com/pkg/config"
	rankV2 "golang.52tt.com/services/rank/channel-online-rank/internal/rank-v2/mgr"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	ypb "golang.52tt.com/protocol/services/youknowwho"
)

type YKWEventLinkSub struct {
	ykwEvent subscriber.Subscriber
	handle   func(ctx context.Context, e *ypb.UKWChangeStatusMsg) error
}

func NewYKWEventLinkSub(ctx context.Context, kfkConf *config.KafkaConfig, handle func(ctx context.Context, e *ypb.UKWChangeStatusMsg) error) (*YKWEventLinkSub, error) {
	e := YKWEventLinkSub{}
	cfg := kafka.DefaultConfig()
	cfg.ClientID = kfkConf.ClientID
	cfg.Consumer.Offsets.Initial = kafka.OffsetNewest
	cfg.Consumer.Return.Errors = true
	kafkaSub, err := kafka.NewSubscriber(kfkConf.BrokerList(), cfg, subscriber.WithMaxRetryTimes(5))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewYKWEventLinkSub NewSubscriber err", err)
		panic(err)
	}
	err = kafkaSub.SubscribeContext(kfkConf.GroupID, kfkConf.TopicList(), subscriber.ProcessorContextFunc(e.handlerYkwEvent))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewYKWEventLinkSub SubscribeContext err", err)
		panic(err)
	}
	e.ykwEvent = kafkaSub
	e.handle = handle
	return &e, nil
}

func (s *YKWEventLinkSub) Close() {
	_ = s.ykwEvent.Stop()
}

func (s *YKWEventLinkSub) handlerYkwEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	ykwEv := &ypb.UKWChangeStatusMsg{}
	err := proto.Unmarshal(msg.Value, ykwEv)
	if err != nil {
		log.ErrorWithCtx(ctx, "handlerYkwEvent Failed to proto.Unmarshal %+v", err)
		return err, false
	}

	if ykwEv.GetMsgType() != ypb.UKWMsgType_ENUM_USER_EXPOSURE_UKW_TYPE {
		return nil, false
	}

	if ykwEv.GetOldFakeUid() == 0 && ykwEv.GetNewFakeUid() == 0 {
		return nil, false
	}

	log.DebugWithCtx(ctx, "handlerYkwEvent event:%+v", ykwEv)

	rankV2.Inst().HandleUkwEvent(ykwEv)

	return nil, false
}
