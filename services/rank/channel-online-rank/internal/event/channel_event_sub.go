package event

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/protocol/services/channelol-go/event"
	"golang.52tt.com/services/rank/channel-online-rank/internal/conf"
	rankV2 "golang.52tt.com/services/rank/channel-online-rank/internal/rank-v2/mgr"
	"golang.52tt.com/services/rank/channel-online-rank/internal/rank-v2/task"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/metrics"
	"golang.52tt.com/pkg/protocol"
	ga "golang.52tt.com/protocol/app/channel"
)

type ChannelEventLinkSub struct {
	channelEvent subscriber.Subscriber
	handle       func(ctx context.Context, e *event.ChannelOLEvent) error
	pool         *task.Task
}

func NewChannelEventLinkSub(ctx context.Context, kfkConf *config.KafkaConfig, handle func(ctx context.Context, e *event.ChannelOLEvent) error) (*ChannelEventLinkSub, error) {
	poolSize := int(conf.GetNewChannelEventPoolSize())
	kafkaWorkerSize := conf.GetNewChannelEventWorkSize()
	if kafkaWorkerSize <= 0 {
		kafkaWorkerSize = 1
	}
	pool, err := task.NewTask(ctx, "ChannelEventPool", poolSize)
	if err != nil {
		log.Errorf("NewChannelEventLinkSub NewTask fail %v", err)
		return nil, err
	}
	e := ChannelEventLinkSub{pool: pool}
	cfg := kafka.DefaultConfig()
	cfg.ClientID = kfkConf.ClientID
	cfg.Consumer.Offsets.Initial = kafka.OffsetNewest
	cfg.Consumer.Return.Errors = true
	kafkaSub, err := kafka.NewSubscriber(kfkConf.BrokerList(), cfg, subscriber.WithMaxRetryTimes(3), subscriber.WithProcessWorkerNum(kafkaWorkerSize))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewChannelEventLinkSub NewSubscriber err", err)
		panic(err)
	}
	err = kafkaSub.SubscribeContext(kfkConf.GroupID, kfkConf.TopicList(), subscriber.ProcessorContextFunc(e.handlerChannelEvent))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewChannelEventLinkSub SubscribeContext err", err)
		panic(err)
	}
	e.channelEvent = kafkaSub
	e.handle = handle
	return &e, nil
}

func (s *ChannelEventLinkSub) Close() {
	_ = s.channelEvent.Stop()
	s.pool.Close()
}

func (s *ChannelEventLinkSub) handlerChannelEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	channelEv := &event.ChannelOLEvent{}
	err := proto.Unmarshal(msg.Value, channelEv)
	if err != nil {
		log.ErrorWithCtx(ctx, "handlerChannelEvent Failed to proto.Unmarshal %+v", err)
		return err, false
	}

	log.DebugWithCtx(ctx, "handlerChannelEvent event:%+v", *channelEv)

	if channelEv.GetUid() == 0 || channelEv.GetCid() == 0 {
		return nil, false
	}

	//CPL大房不处理
	if ga.ChannelType(channelEv.GetChannelType()) == ga.ChannelType_CPL_SUPER_CHANNEL_TYPE {
		return nil, false
	}

	m := metrics.DISCOVERY_FUNC_TRACK(protocol.NewExactServerError(nil, 0, "handlerChannelEvent.task.SubmitJob"))

	err = s.pool.Submit(func() {
		rankV2.Inst().HandleChannelEventProxy(channelEv)
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "handlerChannelEvent Submit fail, will sync retry, err:%v", err)
		// 降级为同步处理
		rankV2.Inst().HandleChannelEventProxy(channelEv)
		return nil, false
	}

	m.End()

	return nil, false
}
