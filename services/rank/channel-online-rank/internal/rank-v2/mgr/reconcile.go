package mgr

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/services/rank/channel-online-rank/internal/rank-v2/cache"
	"golang.52tt.com/services/rank/channel-online-rank/internal/rank-v2/model"
	"golang.52tt.com/services/rank/channel-online-rank/internal/rpc"
	"time"

	"github.com/go-sql-driver/mysql"
	"golang.52tt.com/pkg/log"
	ga "golang.52tt.com/protocol/app/channel"
	pb "golang.52tt.com/protocol/services/channelonlinerank"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
)

type Params struct {
	ChannelType uint32 `json:"channel_type"`
	SourceType  uint32 `json:"source_type"`
}

func (m *RankV2Manager) GetOrderCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	out := &reconcile_v2.CountResp{}
	params := &Params{}
	err := json.Unmarshal([]byte(req.Params), params)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderCount Unmarshal fail %v, req=%+v", err, req)
		return out, err
	}
	if params.SourceType == 0 {
		log.ErrorWithCtx(ctx, "GetOrderCount invalid params, req=%+v", req)
		return out, nil
	}

	orderCnt, totalPrice, err := m.model.GetOrderCount(ctx,
		uint32(req.BeginTime),
		uint32(req.EndTime),
		params.ChannelType,
		params.SourceType,
	)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderCount GetOrderCount fail %v, req=%+v", err, req)
		return out, err
	}

	out.Count = orderCnt
	out.Value = totalPrice
	log.DebugWithCtx(ctx, "GetOrderCount req=%+v out=%+v", req, out)
	return out, nil
}

func (m *RankV2Manager) GetOrderList(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	out := &reconcile_v2.OrderIdsResp{}
	params := &Params{}
	err := json.Unmarshal([]byte(req.Params), params)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderList Unmarshal fail %v, req=%+v", err, req)
		return out, err
	}
	if params.SourceType == 0 {
		log.ErrorWithCtx(ctx, "GetOrderList invalid params, req=%+v", req)
		return out, nil
	}

	list, err := m.model.GetOrderList(ctx,
		uint32(req.BeginTime),
		uint32(req.EndTime),
		0,
		0,
		params.ChannelType,
		params.SourceType,
		-1,
	)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderList fail %v, req=%+v", err, req)
		return out, err
	}
	for _, info := range list {
		out.OrderIds = append(out.OrderIds, info.OrderId)
		out.Values = append(out.Values, info.TotalPrice)
	}
	log.DebugWithCtx(ctx, "GetOrderList req=%+v out=%+v", req, out)
	return out, nil
}

func (m *RankV2Manager) ReplaceOrder(ctx context.Context, req *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
	out := &reconcile_v2.EmptyResp{}
	log.InfoWithCtx(ctx, "ReplaceOrder begin %+v", req)
	m.report.SendInfo(fmt.Sprintf("ReplaceOrder %s", req.OrderId))

	orderId := req.OrderId
	params := &Params{}
	err := json.Unmarshal([]byte(req.Params), params)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReplaceOrder Unmarshal fail %v, req=%+v", err, req)
		return out, err
	}

	resp, err := rpc.ReconcilePresentCli.GetOrderInfoById(ctx, orderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReplaceOrder GetOrderInfoById fail %v, in=%+v", err, req)
		return out, err
	}
	if resp.GetOrderInfo().GetOrderId() == "" {
		log.ErrorWithCtx(ctx, "ReplaceOrder no find order %s", orderId)
		return out, nil
	}

	orderInfo := resp.GetOrderInfo()
	log.InfoWithCtx(ctx, "ReplaceOrder GetOrderInfo=%+v", orderInfo)

	err = m.model.RecordChannelRankPresentLog(&model.ChannelRankOrderLog{
		OrderId:     orderInfo.OrderId,
		Uid:         orderInfo.FromUid,
		RankUid:     orderInfo.FromUid,
		ChannelId:   orderInfo.ChanelId,
		ChannelType: uint8(orderInfo.ChannelType),
		SourceType:  uint8(pb.ChannelOnlineRankValSourceType_SEND_GIFT),
		Status:      uint8(pb.ConsumeStatusType_ENUM_COMMIT),
		TotalPrice:  uint32(orderInfo.TotalPrice),
		OutsideTime: time.Unix(int64(orderInfo.CreateTime), 0),
	})
	if err != nil {
		log.Errorf("ReplaceOrder RecordChannelRankPresentLog fail %v, orderInfo=%+v", err, orderInfo)
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1062 {
			return out, nil
		}
		return out, err
	}

	cid := orderInfo.ChanelId
	channelType := orderInfo.ChannelType
	sendUid := orderInfo.FromUid
	totalPrice := uint32(orderInfo.TotalPrice)

	olCid, serr := rpc.ChannelOlCli.GetUserChannelId(ctx, 0, sendUid)
	if serr != nil {
		log.Errorf("ReplaceOrder GetUserChannelId fail %v, cid=%d uid=%d", serr, cid, sendUid)
		return out, serr
	}

	ts := orderInfo.CreateTime
	if channelType == uint32(ga.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		ts = 0
	}

	log.Infof("ReplaceOrder no in channel, cid=%d uid=%d olcid=%d", cid, sendUid, olCid)
	exist, err := m.cache.IncrMemberScore(
		cid,
		sendUid,
		uint32(ts),
		&cache.MemberInfo{
			TodayConsume: int32(totalPrice),
			TotalConsume: int32(totalPrice),
		})
	if err != nil {
		log.Errorf("ReplaceOrder IncrMemberScore fail %v, orderInfo=%+v", err, orderInfo)
		return out, err
	}
	if !exist {
		m.fixInRank(sendUid, sendUid, cid, uint32(ts), uint32(totalPrice))
		log.InfoWithCtx(ctx, "ReplaceOrder fixInRank order %s sendUid=%d", orderId, sendUid)
	}

	err = m.cache.IncrRankDayRecord(cid, sendUid, uint32(ts), totalPrice)
	if err != nil {
		log.Errorf("ReplaceOrder IncrRankDayRecord fail %v, ev=%+v", err, orderInfo)
	}

	// 周榜
	_ = m.cache.AddWeekConsume(cid, sendUid, uint32(orderInfo.CreateTime), uint32(totalPrice))

	return out, nil
}
