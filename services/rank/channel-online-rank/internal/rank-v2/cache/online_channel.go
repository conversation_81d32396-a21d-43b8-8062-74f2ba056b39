package cache

import (
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/log"
)

func genCheckChannelKey(ts uint32) string {
	tm := time.Unix(int64(ts), 0)
	return fmt.Sprintf("channelolrank:active_channel_%s", tm.Format("20060102"))
}

func (c *RedisCache) AddActiveChannel(cid uint32) error {
	now := time.Now()
	key := genCheckChannelKey(uint32(now.Unix()))
	log.Debugf("AddActiveChannel %d", cid)

	pipe := c.rc.Pipeline()
	pipe.ZAdd(key, redis.Z{
		Score:  float64(now.Unix()),
		Member: fmt.Sprintf("%d", cid),
	})
	pipe.Expire(key, time.Hour*24)
	_, err := pipe.Exec()
	return err
}

func (c *RedisCache) GetActiveChannels(preTime, cnt uint32) ([]uint32, error) {
	now := time.Now()
	key := genCheckChannelKey(uint32(now.Unix()))
	list, err := c.rc.ZRevRangeByScore(key, redis.ZRangeBy{
		Min:    fmt.Sprintf("%d", time.Now().Unix()-int64(preTime)),
		Max:    "+inf",
		Offset: 0,
		Count:  int64(cnt),
	}).Result()
	if err != nil {
		return nil, err
	}

	cids := make([]uint32, 0, len(list))
	for _, str := range list {
		cid, _ := strconv.Atoi(str)
		cids = append(cids, uint32(cid))
	}
	return cids, nil
}

func (c *RedisCache) GetActiveChannelTotal() (uint32, error) {
	now := time.Now()
	key := genCheckChannelKey(uint32(now.Unix()))
	val, err := c.rc.ZCard(key).Result()
	if err != nil && err != redis.Nil {
		return 0, err
	}
	return uint32(val), nil
}
