package main

import (
	"flag"
	"fmt"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
)

var (
	tbl = flag.String("tbl", "", "tbl")
)

func main() {

	flag.Parse()
	fmt.Printf("tbl=%s\n", *tbl)
	if *tbl == "" {
		return
	}

	mysqlConfig := &config.MysqlConfig{
		Host:     "**************",
		Port:     3306,
		Database: "appsvr",
		UserName: "godman",
		Password: "thegodofman",
		Charset:  "utf8mb4",
	}

	log.Infof("mysql_config connect %s", mysqlConfig.ConnectionString())
	db, err := sqlx.Connect("mysql", mysqlConfig.ConnectionString())
	if err != nil {
		fmt.Println(err)
		return
	}
	db.SetMaxIdleConns(50)
	db.SetMaxOpenConns(50)

	for {
		execSql := fmt.Sprintf("delete from %s limit 1000", *tbl)
		res, err := db.Exec(execSql)
		if err != nil {
			log.Errorf("Exec fail %v", err)
			return
		}
		row, _ := res.RowsAffected()
		log.Infof("RowsAffected=%d", row)
		if row < 1000 {
			break
		}
		time.Sleep(time.Millisecond * 50)
	}
}
