package financial_security

import (
	"context"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/account"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
)

// CheckInternalFinancialSecurity 检查用户是否有资金安全风险
func CheckInternalFinancialSecurity(ctx context.Context, uid, targetUid uint32) protocol.ServerError {
	// 内部账号给外部账号消费
	if account.IsInternalUid(ctx, uid) && !account.IsInternalUid(ctx, targetUid) {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "消费异常，请检查您的消费对象账号")
	}

	// 外部账号给内部账号消费
	if !account.IsInternalUid(ctx, uid) && account.IsInternalUid(ctx, targetUid) {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "消费失败，当前消费对象账号异常")
	}

	return nil
}
