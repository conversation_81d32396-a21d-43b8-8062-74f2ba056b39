package main

import (
    "context"
    "fmt"
    "go.mongodb.org/mongo-driver/bson"
    "go.mongodb.org/mongo-driver/mongo"
    "go.mongodb.org/mongo-driver/mongo/readpref"
    "golang.52tt.com/pkg/config"
    "golang.52tt.com/services/tt-rev/esport/esport-skill/internal/store"
)


func main()  {
    ctx := context.Background()
    var err error
    cfg := &config.MongoConfig{
        Addrs:       "10.208.0.84:27017",
        Database:    "esport_skill",
        MaxPoolSize: 10,
        UserName:    "esport_skill_rw",
        Password:    "w9*Y8FTDAmzki30",
    }

    client, err := mongo.Connect(ctx, cfg.OptionsForReplicaSet())
    if err != nil {
        fmt.Println("NewClient",err)
        return
    }

    // 检查连接
    err = client.Ping(context.Background(), readpref.Primary())
    if err != nil {
        fmt.Println("Error connecting to MongoDB:", err)
        return
    }


    res, err := FindDuplicatesByAuditTokenAndType(ctx, client)
    var records []string
    for _, v := range res {
        if v.AuditType == 1 {
            records = append(records, v.ID)
        }
    }

    fmt.Println(records)
    DelRecord(ctx, client, records)
}

// FindDuplicatesByAuditTokenAndType 函数用于查找audit_token相同、记录数大于1且audit_type为1的所有记录
func FindDuplicatesByAuditTokenAndType(ctx context.Context, client *mongo.Client ) ([]*store.UserSkillAuditHistory, error) {
    
    coll := client.Database(store.DB).Collection(store.EsportSkillAuditHistoryColl) //
    // 构造聚合管道
    pipeline := mongo.Pipeline{
        // 第一阶段：筛选audit_type为1的记录
        //bson.D{{Key: "$match", Value: bson.D{{Key: "audit_type", Value: 1}}}},
        // 第二阶段：按audit_token分组并计算每组的记录数
        {
            {"$group", bson.D{
                {"_id", "$audit_token"},
                {"doc_count", bson.D{{"$sum", 1}}},
                {"docs", bson.D{{"$push", "$$ROOT"}}},
            }},
            // 按audit_token分组并统计数量，同时收集所有匹配的文档
        },
        // 第三阶段：筛选出记录数大于1的分组
        {
            {"$match", bson.D{{"doc_count", bson.D{{"$gt", 1}}}}}, // 筛选出数量大于1的分组
        },
        {
            {"$unwind", "$docs"}, // 展开docs数组
        },
        {
            {"$replaceRoot", bson.D{{"newRoot", "$docs"}}}, // 将docs中的文档提升为根文档
        },
    }

    // 执行聚合操作
    cursor, err := coll.Aggregate(ctx, pipeline)
    if err != nil {
        fmt.Println("Aggregate",err)
        return nil, err
    }
    defer cursor.Close(ctx)

    // 解析结果
    var results []*store.UserSkillAuditHistory
    if err = cursor.All(ctx, &results); err != nil {
        fmt.Println("Aggregate All",err)
        return nil, err
    }

    return results, nil
}


func DelRecord(ctx context.Context, client *mongo.Client, delRecords []string ) error {
    coll := client.Database(store.DB).Collection(store.EsportSkillAuditHistoryColl) //

    delRes, err := coll.DeleteMany(ctx,  bson.M{
        "_id": bson.M{
            "$in": delRecords,
        },
    })

    fmt.Println("DelRecord", delRes.DeletedCount,delRecords)
    return err
}

