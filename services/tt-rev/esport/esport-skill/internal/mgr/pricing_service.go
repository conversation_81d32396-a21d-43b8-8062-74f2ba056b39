package mgr

import (
    "context"
    "errors"
    "fmt"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    pb "golang.52tt.com/protocol/services/esport-skill"
    "golang.52tt.com/services/tt-rev/esport/esport-skill/internal/store"
    "google.golang.org/grpc/codes"
    "time"
)

type PricingService interface {
    // GetBasePriceSetting 获取特定教练和游戏的当前基础定价配置。
    GetBasePriceSetting(ctx context.Context, coachID uint32, gameID uint32) (*store.CoachBasePriceSetting, error)
    // BatchGetBasePriceSetting 批量获取特定教练和游戏的当前基础定价配置。map[大神id][技能id]价格配置
    BatchGetBasePriceSetting(ctx context.Context, coachIDList []uint32, gameIDList []uint32) (map[uint32]map[uint32]*store.CoachBasePriceSetting, error)
    // SetBasePriceSetting 设置特定教练和游戏的基础定价配置。
    SetBasePriceSetting(ctx context.Context, coachID uint32, gameID uint32, level uint32) error
    // CalculatePrice 计算指定教练的指定游戏的价格
    CalculatePrice(ctx context.Context, coachID uint32, level uint32, gameID uint32, basePriceConfig *pb.BasePrice,
        labelList []*store.Label,
        addPriceLabelIds []uint32,
        renownedPlayerInfo *store.RenownedPlayer,
    ) (uint32, error)
    HandleGodLevelChange(ctx context.Context, coachID, oldLevel, newLevel uint32) error
    // BatGetCoachPrice 从缓存中获取计算后的价格
    BatGetCoachPrice(ctx context.Context, gameId uint32, coachUid []uint32) (map[uint32]*pb.Price, []uint32, error)
    // BatSetCoachPrice 批量设置计算后的价格到缓存
    BatSetCoachPrice(ctx context.Context, gameId uint32, priceMap map[uint32]*pb.Price) error
}

// CalculatePrice 价格计算
// 价格计算规则：
// 1. 如果是知名选手，则直接使用知名选手的价格
// 2. 查询教练是否有配置指定技能的基础定价配置，如果有，则使用配置的基础定价配置中的等级。否则，使用最低等级 1
// 3. 根据等级获取 basePriceConfig 中的价格配置，作为基础价格
// 4. 如果教练有标识，则需要把标识的价格累加到基础价格上。如果是大神类型的标识，直接累加。如果是是技能类型的标识，还需要判断是否是当前的技能，如果是当前技能，则累加价格。
// 5. 最后返回价格。
func (m *Manager) CalculatePrice(ctx context.Context, coachID uint32, level uint32, gameID uint32, basePriceConfig *pb.BasePrice,
    labelList []*store.Label,
    addPriceLabelIds []uint32,
    renownedPlayerInfo *store.RenownedPlayer,
) (uint32, error) {

    addPriceLabelMap := make(map[uint32]struct{})
    for _, labelId := range addPriceLabelIds {
        addPriceLabelMap[labelId] = struct{}{}
    }

    // 判断白名单
    if m.bc.GetConfig().PricingService.IsOpenWhiteList && !m.bc.GetConfig().PricingService.IsInWhiteList(coachID) {
        return 0, nil
    }

    // If the coach is a renowned player, return the price of the renowned player
    if renownedPlayerInfo != nil {
        return renownedPlayerInfo.Price, nil
    }

    // Get the base price from the basePriceConfig according to the level
    basePrice := basePriceConfig.GetRankPriceMap()[level]

    // If the coach has labels, add the prices of the labels to the base price
    for _, label := range labelList {
        switch label.LabelType {
        case store.LabelType_LABEL_TYPE_COACH:
            if _, ok := addPriceLabelMap[label.ID]; ok { // 大神、技能类型需要判断用户的加价开关
                basePrice += label.Price
            }
        case store.LabelType_LABEL_TYPE_SKILL:
            _, ok := addPriceLabelMap[label.ID]
            if label.GameID == gameID && ok { // 大神、技能类型需要判断用户的加价开关
                basePrice += label.Price
            }
        case store.LabelType_LABEL_TYPE_SPECIAL:
            if label.GameID == gameID {
                basePrice += label.Price
            }

        default:
            // do nothing
        }
    }

    // Return the final price
    return basePrice, nil
}

// BatchGetBasePriceSetting 批量获取特定教练和游戏的当前基础定价配置。
func (m *Manager) BatchGetBasePriceSetting(ctx context.Context, coachIDList []uint32, gameIDList []uint32) (map[uint32]map[uint32]*store.CoachBasePriceSetting, error) {
    basePriceSetting, err := m.store.BatchGetBasePriceSetting(ctx, coachIDList, gameIDList)
    if err != nil {
        // 使用 fmt.Errorf 包装原始错误信息，并添加更多的上下文信息
        return nil, fmt.Errorf("批量获取教练和游戏的基础定价配置失败: %w", err)
    }
    // 遍历查询到的列表，建立索引
    // map[coachId] => map[gameId] => price
    basePriceSettingMap := make(map[uint32]map[uint32]*store.CoachBasePriceSetting)
    for _, setting := range basePriceSetting {
        if _, ok := basePriceSettingMap[setting.CoachID]; !ok {
            basePriceSettingMap[setting.CoachID] = make(map[uint32]*store.CoachBasePriceSetting)
        }
        basePriceSettingMap[setting.CoachID][setting.GameID] = setting
    }
    // 遍历教练 id 列表，如果没有查询到的配置，则创建一个默认的配置
    for _, coachID := range coachIDList {
        if _, ok := basePriceSettingMap[coachID]; !ok {
            basePriceSettingMap[coachID] = make(map[uint32]*store.CoachBasePriceSetting)
        }
        for _, gameId := range gameIDList {
            if _, ok := basePriceSettingMap[coachID][gameId]; !ok {
                basePriceSettingMap[coachID][gameId] = &store.CoachBasePriceSetting{
                    CoachLevel: 1,
                    GameID:     gameId,
                    CoachID:    coachID,
                }
            }
        }
    }
    return basePriceSettingMap, err
}

// GetBasePriceSetting 获取特定教练和游戏的当前基础定价配置。
// - 接受指定教练和游戏的 ID 作为参数。
// - 调用 `StoreApi` 来检索与教练和游戏相关的基础价格设置。
// - 处理可能出现的错误并返回结果。
func (m *Manager) GetBasePriceSetting(ctx context.Context, coachID uint32, gameID uint32) (*store.CoachBasePriceSetting, error) {
    setting, err := m.store.GetCoachBasePriceSetting(ctx, coachID, gameID)
    if err != nil {
        if errors.Is(err, store.ErrNotFound) {
            return &store.CoachBasePriceSetting{
                CoachLevel: 1,
                GameID:     gameID,
                CoachID:    coachID,
            }, nil
        }
        // 使用 fmt.Errorf 包装原始错误信息，并添加更多的上下文信息
        return nil, fmt.Errorf("获取教练和游戏的基础定价配置失败: %w", err)
    }
    return setting, nil
}

// SetBasePriceSetting 设置特定教练和游戏的基础定价配置。
// - 接受基础价格设置信息作为参数。
// - 调用 `StoreApi` 来创建或更新基础价格设置。
// - 处理可能出现的错误并确认操作被正确执行。
func (m *Manager) SetBasePriceSetting(ctx context.Context, coachID uint32, gameID uint32, level uint32) error {
    // Create a new CoachBasePriceSetting instance
    setting := &store.CoachBasePriceSetting{
        CoachID:    coachID,
        GameID:     gameID,
        CoachLevel: level,
        UpdateTime: time.Now(),
    }

    // Call the UpsertCoachBasePriceSetting method from the store package
    err := m.store.UpsertCoachBasePriceSetting(ctx, setting)
    if err != nil {
        // If an error occurs, wrap it using fmt.Errorf to provide more context
        return fmt.Errorf("failed to set base price setting: %w", err)
    }

    err = m.cache.BatDelCoachPrice(ctx, gameID, []uint32{coachID})
    if err != nil {
        log.WarnWithCtx(ctx, "SetBasePriceSetting: BatDelCoachPrice failed, coachID:%d, gameID:%d, err:%v", coachID, gameID, err)
    }
    // If no error occurs, return nil to indicate success
    return nil
}

// HandleGodLevelChange 处理大神等级变更
func (m *Manager) HandleGodLevelChange(ctx context.Context, coachID, oldLevel, newLevel uint32) error {
    // 等级不变or升级，不改变基础定价
    log.DebugWithCtx(ctx, "HandleGodLevelChange: coachID:%d, oldLevel:%d, newLevel:%d", coachID, oldLevel, newLevel)
    if oldLevel <= newLevel {
        log.DebugWithCtx(ctx, "HandleGodLevelChange: no need to handle base price change, coachID:%d, oldLevel:%d, newLevel:%d", coachID, oldLevel, newLevel)
        return nil
    } else {
        return m.handleRankDown(ctx, coachID, oldLevel, newLevel)
    }
}

func (m *Manager) handleRankDown(ctx context.Context, coachID uint32, oldLevel, newLevel uint32) error {
    skills, err := m.store.GetUserSkills(ctx, coachID)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleGodLevelChange: GetUserSkills failed, coachID:%d, err:%v", coachID, err)
        return err
    }
    // gameIdList
    gameIdList := make([]uint32, 0, len(skills))
    for _, skill := range skills {
        gameIdList = append(gameIdList, skill.GameId)
    }
    // 批量获取教练的基础定价配置
    basePriceSettingMap, err := m.BatchGetBasePriceSetting(ctx, []uint32{coachID}, gameIdList)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleGodLevelChange: BatchGetBasePriceSetting failed, coachID:%d, err:%v", coachID, err)
        return err
    }

    // 获取基础定价配置
    if _, ok := basePriceSettingMap[coachID]; !ok {
        log.ErrorWithCtx(ctx, "HandleGodLevelChange: basePriceSetting not found, coachID:%d", coachID)
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    // needToUpdateList
    needToUpdateList := make([]*store.CoachBasePriceSetting, 0, len(skills))
    for _, skill := range skills {
        basePriceSetting, ok := basePriceSettingMap[coachID][skill.GameId]
        if !ok {
            log.ErrorWithCtx(ctx, "HandleGodLevelChange: basePriceSetting not found, coachID:%d, gameId:%d", coachID, skill.GameId)
            continue
        }
        if basePriceSetting.CoachLevel > newLevel {
            // 更新基础定价配置
            basePriceSetting.CoachLevel = newLevel
            needToUpdateList = append(needToUpdateList, basePriceSetting)
            log.InfoWithCtx(ctx, "HandleGodLevelChange need to handle, basePriceSetting:%+v", basePriceSetting)
        } else {
            log.DebugWithCtx(ctx, "HandleGodLevelChange no need to handle, basePriceSetting:%+v", basePriceSetting)
        }
    }
    // 批量设置价格
    err = m.store.BatchSetBasePriceSetting(ctx, needToUpdateList)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleGodLevelChange: BatchSetBasePriceSetting failed, coachID:%d, err:%v", coachID, err)
        return err
    }
    log.InfoWithCtx(ctx, "HandleGodLevelChange: coachID:%d, oldLevel:%d, newLevel:%d, needToUpdateList:%v", coachID, oldLevel, newLevel, needToUpdateList)
    return nil
}

func (m *Manager) BatGetCoachPrice(ctx context.Context, gameId uint32, coachUid []uint32) (map[uint32]*pb.Price, []uint32, error) {
    return m.cache.BatGetCoachPrice(ctx, gameId, coachUid)
}

func (m *Manager) BatSetCoachPrice(ctx context.Context, gameId uint32, priceMap map[uint32]*pb.Price) error {
    return m.cache.BatSetCoachPrice(ctx, gameId, priceMap)
}
