package mgr

import (
    "context"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/services/esport_hall"
    "golang.52tt.com/services/tt-rev/esport/esport-skill/internal/store"
    "time"
)

func (m *Manager) SetGameGuaranteeWin(ctx context.Context, uid, gameId uint32, isGuaranteeWin, isSystemSource bool) error {
    err := m.store.SetGameGuaranteeWin(ctx, uid, gameId, isGuaranteeWin)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetGameGuaranteeWin uid:%d, gameId:%d, err:%v", uid, gameId, err)
        return err
    }
    _, err = m.rpc.EsportHallCli.SetGuaranteeWinSwitch(ctx, &esport_hall.SetGuaranteeWinSwitchRequest{
        Uid:     uid,
        SkillId: gameId,
        Switch:  isGuaranteeWin,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetGameGuaranteeWin rpc err:%v", err)
        return err
    }
    go func() {
        goCtx, cancel := grpc.NewContextWithInfoTimeout(ctx, time.Second*5)
        defer cancel()
        log.DebugWithCtx(ctx, "SetGameGuaranteeWin uid:%d, gameId:%d, isGuaranteeWin:%v", uid, gameId, isGuaranteeWin)
        ReportEsportGuaranteeSwitchLog(goCtx, uid, gameId, isGuaranteeWin, isSystemSource)
    }()

    log.InfoWithCtx(ctx, "SetGameGuaranteeWin uid:%d, gameId:%d, isGuaranteeWin:%v", uid, gameId, isGuaranteeWin)
    return nil
}

func (m *Manager) RunTaskCheckGuaranteeWinPermission() {
    ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
    defer cancel()

    // 捞取所有已开启包赢的大神技能
    skills, err := m.store.GetUserSkillsWithGuaranteeWin(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserSkillsWithGuaranteeWin err:%v", err)
        return
    }
    // 轮询检查包赢权限
    for _, skill := range skills {
        m.checkGuaranteeWinPermission(skill)
    }
}

func (m *Manager) checkGuaranteeWinPermission(skill *store.UserSkillInfo) {
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()

    // 查询是否有对应的技能认证
    hasGameLabel, err := m.hasGameLabel(ctx, skill.Uid, skill.GameId)
    if err != nil {
        log.ErrorWithCtx(ctx, "checkGuaranteeWinPermission hasGameLabel err: %v, skill: %+v", err, *skill)
        return
    }
    if hasGameLabel {
        return
    }

    // 关闭包赢
    err = m.SetGameGuaranteeWin(ctx, skill.Uid, skill.GameId, false, true)
    if err != nil {
        log.ErrorWithCtx(ctx, "checkGuaranteeWinPermission SetGameGuaranteeWin err: %v, skill: %+v", err, *skill)
        return
    }

    log.InfoWithCtx(ctx, "checkGuaranteeWinPermission closeGuaranteeWin, skill: %+v", *skill)
}

func (m *Manager) hasGameLabel(ctx context.Context, uid, gameId uint32) (bool, error) {
    labels, err := m.GetCoachLabels(ctx, uid, gameId)
    if err != nil {
        return false, err
    }
    for _, label := range labels {
        if label.LabelType == store.LabelType_LABEL_TYPE_SKILL && label.GameID == gameId {
            return true, nil
        }
    }
    return false, nil
}
