package mgr

import (
	"context"
	"google.golang.org/grpc/codes"
	"sort"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/esport-skill"
	pbRole "golang.52tt.com/protocol/services/esport_role"
	"golang.52tt.com/services/tt-rev/esport/esport-skill/internal/store"
)

var (
	InAuditType = []uint32{
		uint32(pbRole.ApplyESportAuditType_ESPORT_AUDIT_TYPE_INIT),
		uint32(pbRole.ApplyESportAuditType_ESPORT_AUDIT_TYPE_WAIT_FOR_GUILD),
		uint32(pbRole.ApplyESportAuditType_ESPORT_AUDIT_TYPE_WAIT_FOR_PLATFORM),
	}

	guaranteeSection = "包赢承诺"

	SkillStatusPassed  = 1
	SkillStatusInAudit = 2
)

type SkillService interface {
	BatchCheckCoachHasGame(ctx context.Context, uidList []uint32, gameId uint32) (map[uint32]bool, error)
	SetGameGuaranteeWin(ctx context.Context, uid, gameId uint32, isGuaranteeWin, isSystemSource bool) error
}

func (m *Manager) BatchCheckCoachHasGame(ctx context.Context, uidList []uint32, gameId uint32) (map[uint32]bool, error) {
	out, err := m.store.BatchCheckCoachHasGame(ctx, uidList, gameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchCheckCoachHasGame uidList:%v, gameId:%d, err:%v", uidList, gameId, err)
		return out, err
	}
	return out, nil
}
func (m *Manager) DelUserSkill(ctx context.Context, req *pb.DelUserSkillRequest) error {
	return m.store.DelUserSkill(ctx, req.GetUid(), req.GetGameId())
}

func (m *Manager) BatchGetUserCurrentSkill(ctx context.Context, uidList []uint32) (*pb.BatchGetUserCurrentSkillResponse, error) {
	out := &pb.BatchGetUserCurrentSkillResponse{}
	skills, err := m.store.BatchGetUserSkills(ctx, uidList)
	if err != nil {
		return out, err
	}

	for _, skill := range skills {
		currentSkill := &pb.UserCurrentSkill{
			Uid: skill.Uid,
		}

		pbUserSkill := m.transStoreUserSkillInfoToPb(skill, true)
		currentSkill.Skill = append(currentSkill.Skill, pbUserSkill)
		out.List = append(out.List, currentSkill)
	}

	return out, nil
}

func (m *Manager) GetUserCurrentSkill(ctx context.Context, uid uint32, withPrefix bool) (*pb.GetUserCurrentSkillResponse, error) {
	out := &pb.GetUserCurrentSkillResponse{
		Uid: uid,
	}

	skills, err := m.store.GetUserSkills(ctx, uid)
	if err != nil {
		return out, err
	}

	sort.SliceStable(skills, func(right, left int) bool {
		return skills[right].GameRank < skills[left].GameRank
	})

	freezeMap, err := m.store.GetUserSkillFreezeStatus(ctx, uid)
	if err != nil {
		return out, err
	}

	for _, skill := range skills {
		pbUserSkill := m.transStoreUserSkillInfoToPb(skill, withPrefix)
		pbUserSkill.FreezeType = pb.FreezeType(freezeMap[skill.GameId].GetFreezeType())
		pbUserSkill.FreezeStopTs = freezeMap[skill.GameId].GetFreezeStopTs()
		out.Skill = append(out.Skill, pbUserSkill)
	}
	return out, nil
}

func (m *Manager) transStoreUserSkillInfoToPb(st *store.UserSkillInfo, withPrefix bool) *pb.UserSkillInfo {
	sectionList := make([]*pb.SectionInfo, 0)
	for _, v := range st.SectionList {
		sectionList = append(sectionList, &pb.SectionInfo{
			SectionName: v.SectionName,
			ItemList:    v.ItemList,
			SectionId:   v.SectionId,
		})
	}

	skill := &pb.UserSkillInfo{
		GameId:         st.GameId,
		GameName:       st.GameName,
		SkillEvidence:  st.SkillEvidence,
		Audio:          st.Audio,
		SectionList:    sectionList,
		TextDesc:       st.TextDesc,
		AudioDuration:  st.AudioDuration,
		IsGuaranteeWin: st.IsGuaranteeWin,
	}

	if withPrefix {
		if len(skill.SkillEvidence) > 0 {
			skill.SkillEvidence = m.bc.GetFileUrlPrefix() + skill.SkillEvidence
		}
		if len(skill.Audio) > 0 && st.AudioDuration > 0 {
			skill.Audio = m.bc.GetFileUrlPrefix() + skill.Audio
		}
	}

	return skill
}

func (m *Manager) TestAddUserSkill(ctx context.Context, request *pb.TestAddUserSkillRequest) error {
	storeSkill := &store.UserSkillInfo{
		Uid:           request.GetUid(),
		GameId:        request.GetSkill().GetGameId(),
		GameName:      request.GetSkill().GetGameName(),
		SkillEvidence: request.GetSkill().GetSkillEvidence(),
		TextDesc:      request.GetSkill().GetTextDesc(),
		Audio:         request.GetSkill().GetAudio(),
		CreateTime:    time.Now(),
		UpdateTime:    time.Now(),
	}

	for _, section := range request.GetSkill().GetSectionList() {
		storeSkill.SectionList = append(storeSkill.SectionList, &store.SectionInfo{
			SectionName: section.SectionName,
			ItemList:    section.ItemList,
			SectionId:   section.SectionId,
		})
	}

	return m.store.AddUserSkill(ctx, storeSkill)
}

func (m *Manager) AddUserSkill(ctx context.Context, uid uint32, skill *store.UserSkillInfo) error {
	skill.Uid = uid
	skill.CreateTime = time.Now().Local()
	skill.UpdateTime = time.Now().Local()
	return m.store.AddUserSkill(ctx, skill)
}

func (m *Manager) getGuaranteeList(sectionList []*pb.GameInformation) *pb.GameInformation {
	for _, section := range sectionList {
		if section.GetSectionName() == guaranteeSection {
			return section
		}
	}
	return &pb.GameInformation{}
}

func (m *Manager) GetUserSkillByGameId(ctx context.Context, uid, gameId uint32) (*pb.GetUserSkillByGameIdResponse, error) {
	out := &pb.GetUserSkillByGameIdResponse{
		Uid:    uid,
		GameId: gameId,
	}

	skill, err := m.store.GetUserSkillByGameId(ctx, uid, gameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserSkillByGameId %v", skill)
		return out, err
	}

	if skill == nil {
		log.ErrorWithCtx(ctx, "GetUserSkillByGameId not found uid:%d, gameId:%d", uid, gameId)
		return out, protocol.NewExactServerError(codes.OK, status.ErrEsportsSkillNotFound)
	}
	out.CurrentSkill = m.transStoreUserSkillInfoToPb(skill, false)

	auditSkills, err := m.store.BatchGetAuditSkill(ctx, &pb.BatchGetAuditSkillRequest{
		Uids:        []uint32{uid},
		AuditType:   InAuditType,
		OffSet:      0,
		Limit:       10,
		NeedTotal:   0,
		SearchType:  uint32(pb.AuditSearchType_AUDIT_SEARCH_TYPE_ALL),
		AuditSource: []uint32{uint32(pb.AuditSource_AUDIT_SOURCE_NEW_SKILL), uint32(pb.AuditSource_AUDIT_SOURCE_MOD_SKILL)},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAuditSkill %v", skill)
		return out, err
	}

	for _, v := range auditSkills {
		for _, sk := range v.SkillList {
			if sk.GameId == gameId {
				skillPb := m.transStoreUserSkillInfoToPb(sk, false)
				// 语音 修改可删除 展示审核中
				if v.ModifyType == store.ModifyTypeSkillTextAndAudio {
					if len(skillPb.Audio) == 0 {
						skillPb.Audio = " "
					}
				}
				out.AuditSkill = append(out.AuditSkill, skillPb)
			}
		}
	}

	return out, err
}

func (m *Manager) handleGameUpdateToUserSkill(ctx context.Context, game *pb.EsportGameConfig, skill *store.UserSkillInfo) error {
	var skillGuaranteeSection *store.SectionInfo
	for _, section := range skill.SectionList {
		if section.SectionName == guaranteeSection && len(section.ItemList) > 0 {
			skillGuaranteeSection = section
			break
		}
	}

	var needUpdateSkill bool

	guaranteeSectionInfo := m.getGuaranteeList(game.GetGameInformationList())

	// 包赢配置为空
	if len(guaranteeSectionInfo.ItemList) <= 0 {
		// 包赢配置被删除
		if skillGuaranteeSection != nil {
			newSectionList := make([]*store.SectionInfo, 0)
			for _, section := range skill.SectionList {
				if section.SectionName != guaranteeSection {
					newSectionList = append(newSectionList, section)
				}
			}
			skill.SectionList = newSectionList
			needUpdateSkill = true
			log.InfoWithCtx(ctx, "handleGameUpdateToUserSkill gameId:%d, uid:%d, guaranteeSectionInfo 包赢配置被删除", game.GetGameId(), skill.Uid)
		} else {
			log.DebugWithCtx(ctx, "handleGameUpdateToUserSkill gameId:%d, guaranteeSectionInfo 配置为空，技能没有包赢，无需处理", game.GetGameId())
		}

	} else {
		// 判断是否更新
		if skillGuaranteeSection != nil {
			modify := true
			for _, guarantee := range guaranteeSectionInfo.GetItemList() {
				if guarantee == skillGuaranteeSection.ItemList[0] {
					modify = false
					break
				}
			}
			// 默认选中第一个
			if modify {
				skillGuaranteeSection.ItemList = []string{guaranteeSectionInfo.GetItemList()[0]}
				needUpdateSkill = true
				log.InfoWithCtx(ctx, "handleGameUpdateToUserSkill gameId:%d, uid:%d, guaranteeSectionInfo 被修改", game.GetGameId(), skill.Uid)
			} else {
				log.DebugWithCtx(ctx, "handleGameUpdateToUserSkill gameId:%d, uid:%d, guaranteeSectionInfo 未被修改", game.GetGameId(), skill.Uid)
			}

			// 包赢配置为新增
		} else {
			// 默认选中第一个
			skill.SectionList = append(skill.SectionList, &store.SectionInfo{
				SectionName: guaranteeSection,
				ItemList:    []string{guaranteeSectionInfo.GetItemList()[0]},
				SectionId:   guaranteeSectionInfo.GetSectionId(),
			})
			needUpdateSkill = true
			log.InfoWithCtx(ctx, "handleGameUpdateToUserSkill gameId:%d, uid:%d, guaranteeSectionInfo 被新增", game.GetGameId(), skill.Uid)
		}
	}

	if needUpdateSkill {
		return m.updateSkillSection(ctx, skill.Uid, skill)
	}
	return nil
}

// 因游戏配置改动，更新技能信息
func (m *Manager) updateSkillSection(ctx context.Context, uid uint32, skill *store.UserSkillInfo) error {
	err := m.store.SetUserSkillSection(ctx, uid, skill.GameId, skill.SectionList)
	if err != nil {
		log.ErrorWithCtx(ctx, "ModifyUserSkill SetUserSkillSection uid:%d, err:%+v", uid, err)
		return err
	}
	err = m.notifyEsportHallUpdate(ctx, uid, skill)
	if err != nil {
		log.ErrorWithCtx(ctx, "ModifyUserSkill notifyEsportHallUpdate uid:%d, err:%+v", uid, err)
		return err
	}
	log.InfoWithCtx(ctx, "ModifyUserSkill uid:%d, gameId:%d, success", uid, skill.GameId)
	return nil
}

func (m *Manager) GetUserSkillStatus(ctx context.Context, uid uint32) (*pb.GetUserSkillStatusResponse, error) {
	out := &pb.GetUserSkillStatusResponse{
		Uid: uid,
	}

	skills, err := m.store.GetUserSkills(ctx, uid)
	if err != nil {
		return out, err
	}

	out.StatusMap = make(map[uint32]uint32)
	for _, skill := range skills {
		out.StatusMap[skill.GameId] = uint32(pbRole.ApplyESportAuditType_ESPORT_AUDIT_TYPE_PASS)
	}

	list, err := m.store.BatchGetAuditSkill(ctx, &pb.BatchGetAuditSkillRequest{
		Uids:       []uint32{uid},
		AuditType:  InAuditType,
		OffSet:     0,
		Limit:      20,
		NeedTotal:  0,
		SearchType: uint32(pb.AuditSearchType_AUDIT_SEARCH_TYPE_ALL),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAuditSkill uid:%d, err:%v", uid)
		return out, err
	}

	for _, record := range list {
		if len(record.SkillList) == 0 {
			continue
		}
		gameId := record.SkillList[0].GameId
		out.StatusMap[gameId] = record.AuditType
	}

	log.DebugWithCtx(ctx, "GetUserSkillStatus uid:%d, out:%v", out)
	return out, nil
}

func (m *Manager) handleGameUpdate(ctx context.Context, gameCfg *pb.EsportGameConfig) error {
	ts := time.Now()
	pageSize := int64(200)
	page := int64(0)
	for {
		userInfoList, err := m.store.GetGameUserSkillsByPage(ctx, gameCfg.GetGameId(), page, pageSize)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGameUserSkillsByPage gameId:%d, page:%d, err:%v", gameCfg.GetGameId(), page, err)
			return err
		}
		for _, user := range userInfoList {
			if err = m.handleGameUpdateToUserSkill(ctx, gameCfg, user); err != nil {
				log.ErrorWithCtx(ctx, "HandleGameUpdate gameId:%d, uid:%d, err:%v", gameCfg.GetGameId(), user.Uid, err)
				return err
			}
			log.DebugWithCtx(ctx, "HandleGameUpdate gameId:%d, uid:%d, success", gameCfg.GetGameId(), user.Uid)
		}
		log.InfoWithCtx(ctx, "HandleGameUpdate gameId:%d, page:%d, len:%d, success", gameCfg.GetGameId(), page, len(userInfoList))
		if len(userInfoList) < int(pageSize) {
			log.InfoWithCtx(ctx, "HandleGameUpdate gameId:%d, page:%d, 耗时:%s, end", gameCfg.GetGameId(), page, time.Since(ts))
			break
		}
		page++
		time.Sleep(time.Millisecond * 50)
	}
	return nil
}

func (m *Manager) HandleGameUpdate(ctx context.Context, gameId uint32) error {
	ctx = context.Background()
	game, err := m.store.GetGameDetailById(ctx, gameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameDetailById gameId:%d, err:%v", gameId, err)
		return err
	}
	err = m.handleGameUpdate(ctx, game)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleGameUpdate gameId:%d, err:%v", gameId, err)
		return err
	}
	log.InfoWithCtx(ctx, "HandleGameUpdate gameId:%d, success", gameId)
	return nil
}
