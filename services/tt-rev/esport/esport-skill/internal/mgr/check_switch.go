package mgr

import (
    "context"
    "golang.52tt.com/pkg/log"
)

func (m *Manager) CheckCoachCouldOpenSwitch(ctx context.Context, uid uint32) (string, bool) {
    groupId := m.bc.GetCoachSwitchGroupId()
    if groupId == "" {
        return "", true
    }
    retList, err := m.rpc.UserGroupCli.GetUserHasGroup(ctx, uid, []string{groupId})
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckCoachCouldOpenSwitch: get user group : %s", err)
        return "", true // 失败了就不限制
    }
    if len(retList) == 0 {
        return "", true
    }
    log.ErrorWithCtx(ctx, "CheckCoachCouldOpenSwitch: user %d has group %s", uid, groupId)
    return "当前无法开启该功能，请联系公会管理", false

}
