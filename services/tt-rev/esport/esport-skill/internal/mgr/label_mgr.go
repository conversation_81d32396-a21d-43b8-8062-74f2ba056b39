package mgr

import (
    "context"
    "errors"
    fmt "fmt"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    pb "golang.52tt.com/protocol/services/esport-skill"
    "golang.52tt.com/services/tt-rev/esport/common/collection/transform"
    "golang.52tt.com/services/tt-rev/esport/esport-skill/internal/store"
    context0 "golang.org/x/net/context"
    "google.golang.org/grpc/codes"
    "sort"
    "sync"
    "time"
)

// LabelManager 提供定价策略聚合的操作接口。
type LabelManager interface {
    // CreateLabel 创建一个新的标签，并关联定价。
    CreateLabel(ctx context.Context, label *store.Label) error
    // EditLabel 编辑一个现有标签的详情和定价。
    EditLabel(ctx context.Context, label *store.Label) error
    // DeleteLabel 删除一个现有的标签，并从所有教练中撤销。
    DeleteLabel(ctx context.Context, labelID uint32) error
    // BatchGetLabel 批量获取标签
    BatchGetLabel(ctx context.Context, ids []uint32) ([]*store.Label, error)
    // BatchGetCoachLabels 批量获取多个教练的标识列表，如果 gameId有传，则进一步根据 gameId 过滤
    BatchGetCoachLabels(ctx context.Context, coachIds []uint32, gameId []uint32) (map[uint32][]*store.Label, map[uint32][]uint32, error)
    // GetCoachLabels 获取教练当前拥有的标签，如果有 gameIds，则进一步根据 gameIds 过滤，最终只返回满足 gameIds 的技能类型的标识和大神类型的标识
    GetCoachLabels(ctx context.Context, coachID uint32, gameIds ...uint32) ([]*store.Label, error)
    // GetLabelsByGamesWithCoachType 获取指定游戏的技能类型的标签和大神类型的标签
    GetLabelsByGamesWithCoachType(ctx context.Context, gameIds ...uint32) ([]*store.Label, error)
    // IssueLabel 发放标签给教练，使其生效。
    IssueLabel(ctx context.Context, labelId uint32, effectiveStartTime, effectiveEndTime time.Time, coachUid uint32) (uint32, error)
    // BatchIssueLabel 发放标签给多个教练，使其生效。
    BatchIssueLabel(ctx context.Context, labelId uint32, effectiveStartTime, effectiveEndTime time.Time, coachUidList []uint32) ([]uint32, error)
    // SetLabelPriceAdditionalSwitch 设置指定教练指定标签的加价开关
    SetLabelPriceAdditionalSwitch(ctx context.Context, coachId, labelId uint32, switchState bool) error
    // RevokeLabel 从教练那里撤销标签，使其失效。
    RevokeLabel(ctx context.Context, issuanceRecordId uint32) error
    // BatchAddRenownedPlayer 将指定教练的指定游戏设置成知名玩家，并设置特殊定价。
    BatchAddRenownedPlayer(ctx context.Context, itemList []*PlayerItem) error
    // RemoveRenownedPlayer 删除指定的知名玩家记录
    RemoveRenownedPlayer(ctx context.Context, id uint32) error
    // BatchRemoveRenownedPlayers 批量删除多个知名玩家记录
    BatchRemoveRenownedPlayers(ctx context.Context, ids []uint32) error
    // ListLabels 提供系统中所有可用标签的分页列表。
    ListLabels(ctx context.Context, pageNumber int32, pageSize int32, labelId uint32, labelType store.LabelType, gameId uint32) ([]*store.Label, uint32, error)
    // ListRenownedPlayers 提供所有知名玩家及其定价的分页列表。
    ListRenownedPlayers(ctx context.Context, pageNumber int32, pageSize int32, coachUid uint32, gameId []uint32) ([]*store.RenownedPlayer, uint32, error)
    // BatchGetUserRenownedInfoByCoachIdsAndGameId 批量获取指定的游戏关联的多个教练的知名玩家信息
    BatchGetUserRenownedInfoByCoachIdsAndGameId(ctx context.Context, coachIds []uint32, gameId uint32) ([]*store.RenownedPlayer, error)
    // BatchGetUserRenownedInfoByCoachIdAndGameIds 批量获取指定的教练关联的多个游戏的知名玩家信息
    BatchGetUserRenownedInfoByCoachIdAndGameIds(ctx context.Context, coachIds uint32, gameIds []uint32) ([]*store.RenownedPlayer, error)
    // UpdateRenownedPlayer 更新知名选手
    UpdateRenownedPlayer(ctx context.Context, id uint32, price int64) error
    // ListIssuanceRecords 提供所有标签发放记录的分页列表。
    ListIssuanceRecords(ctx context.Context,
        pageNumber int32, pageSize int32,
        coachIds []uint32, labelId uint32, effectiveStartTime, effectiveEndTime int64, status store.IssuanceRecordStatus,
        labelType store.LabelType,
    ) ([]*store.IssuanceRecord, uint32, error)
    CheckLabelOrder(c context0.Context, labelType pb.LabelType, displayOrder, labelId uint32, checkType pb.CheckLabelOrderRequest_CheckType) error
    UpdateUserSpecialLabel(c context0.Context, request *pb.UpdateUserSpecialLabelRequest) error
    BatchGetUserSpecialLabel(c context0.Context, userIds []uint32) (*pb.BatchGetUserSpecialLabelResponse, error)
    BatchGetUserGameSpecialLabel(c context0.Context, request *pb.BatchGetUserGameSpecialLabelRequest) (*pb.BatchGetUserGameSpecialLabelResponse, error)
    GetSpecialLabelIssuanceRecord(c context.Context, pageNum, pageSize int64) ([]*store.IssuanceRecord, error)
    GetEffectiveIssuanceRecord(c context.Context, pageNum, pageSize int64) ([]*store.IssuanceRecord, error)
}

type PlayerItem struct {
    UID      uint32
    GameID   uint32
    Price    uint32
    GameName string
    TTid     string
}

// CreateLabel 创建一个新的标签，并关联定价。
// - 接受标签信息作为参数。
// - 调用 `StoreApi` 以在数据库中创建新标签。
// - 处理可能出现的错误并返回创建结果。
func (m *Manager) CreateLabel(ctx context.Context, label *store.Label) error {

    label.CreateTime = time.Now()
    label.UpdateTime = time.Now()

    // Step 2: Call the StoreApi to create a new label in the database
    err := m.store.CreateLabel(ctx, label)
    if err != nil {
        // Step 3: Handle any errors that might occur during the database operation
        return fmt.Errorf("failed to create label: %w", err)
    }

    // Step 4: Return the result of the operation
    return nil
}

// EditLabel 编辑一个现有标签的详情和定价。
// - 接受标签 ID 和需要更新的标签信息作为参数。
// - 验证标签 ID 是否有效并检查更新信息。
// - 调用 `StoreApi` 来更新指定的标签信息。
// - 处理可能出现的错误并返回更新结果。
func (m *Manager) EditLabel(ctx context.Context, label *store.Label) error {
    // Step 1: Validate the input
    if label == nil || label.ID == 0 {
        return errors.New("invalid label: label is nil or ID is zero")
    }

    label.UpdateTime = time.Now()

    // Step 2: Call the StoreApi to update the label
    err := m.store.UpdateLabel(ctx, label)
    if err != nil {
        // Step 3: Handle any errors that might occur during the database operation
        return fmt.Errorf("failed to update label: %w", err)
    }

    // Step 4: Return the result of the operation
    return nil
}

// DeleteLabel 删除一个现有的标签，并从所有教练中撤销。
// - 接受标签 ID 作为参数。
// - 验证标签 ID 是否有效。
// - 调用 `StoreApi` 来删除指定的标签。
// - 调用 `StoreApi` 来删除所有教练的与该标签相关的发放记录。
// - 处理可能出现的错误并确认标签已被删除。
func (m *Manager) DeleteLabel(ctx context.Context, labelID uint32) error {
    // Step 1: Validate the input
    if labelID == 0 {
        return errors.New("invalid label ID: cannot be zero")
    }

    // Step 2: Call the StoreApi to delete the specified label
    err := m.store.DeleteLabelByID(ctx, labelID)
    if err != nil {
        // Step 3: Handle any errors that might occur during the database operation
        return fmt.Errorf("failed to delete label: %w", err)
    }

    // Step 4: Call the StoreApi to delete all issuance records related to this label from all coaches
    err = m.store.DeleteIssuanceRecordByLabelID(ctx, labelID)
    if err != nil {
        // Handle any errors that might occur during the database operation
        return fmt.Errorf("failed to delete issuance records for label: %w", err)
    }

    // Step 5: Return the result of the operation
    return nil
}

// ListLabels 提供系统中所有可用标签的分页列表。
// - 调用 `StoreApi` 来检索所有标签（根据过滤条件，如果有的话）。
// - 处理可能出现的错误并返回标签列表和总数。
func (m *Manager) ListLabels(ctx context.Context, pageNumber int32, pageSize int32, labelId uint32, labelType store.LabelType, gameId uint32) ([]*store.Label, uint32, error) {
    // Step 1: Call the StoreApi to retrieve all labels (based on the filter conditions, if any)
    labels, err := m.store.GetLabels(ctx, pageNumber, pageSize, labelId, labelType, gameId)
    if err != nil {
        // Step 2: Handle any errors that might occur during the database operation
        return nil, 0, fmt.Errorf("failed to get labels: %w", err)
    }

    // Step 3: Call the StoreApi to count all labels (based on the filter conditions, if any)
    count, err := m.store.CountLabels(ctx, labelId, labelType, gameId)
    if err != nil {
        // Step 4: Handle any errors that might occur during the database operation
        return nil, 0, fmt.Errorf("failed to count labels: %w", err)
    }

    // Step 5: Return the list of labels and the total count
    return labels, count, nil
}

// IssueLabel 发放标签给教练，使其生效。
// - 验证教练 ID 和标签 ID 是否有效。
// - 验证有效时间是否有效。
// - 创建一个发行记录，并调用 `StoreApi` 来更新标签的发行状态。
// - 处理可能出现的错误并确认标签已发放。
func (m *Manager) IssueLabel(ctx context.Context, labelId uint32, effectiveStartTime, effectiveEndTime time.Time, coachUid uint32) (uint32, error) {
    // Step 1: Validate the input
    if labelId == 0 || coachUid == 0 || effectiveStartTime.After(effectiveEndTime) {
        return 0, fmt.Errorf("invalid input: labelId, coachUid must be greater than zero and effectiveStartTime must be before effectiveEndTime")
    }

    // Step 2: Create a new IssuanceRecord
    record := &store.IssuanceRecord{
        CoachID:           coachUid,
        LabelID:           labelId,
        EffectiveDuration: &store.Duration{StartTime: effectiveStartTime, EndTime: effectiveEndTime},
        CreateTime:        time.Now(),
        UpdateTime:        time.Now(),
    }

    // Step 3: Call the StoreApi to create a new issuance record
    err := m.store.CreateIssuanceRecord(ctx, record)
    if err != nil {
        // Step 4: Handle any errors that might occur during the database operation
        return 0, fmt.Errorf("failed to issue label: %w", err)
    }

    // Step 5: Return the result of the operation
    return record.ID, nil
}

// RevokeLabel 从教练那里撤销标签，使其失效。
// - 调用 `StoreApi` 来更新或删除相关的发行记录，撤销标签。
// - 处理可能出现的错误并确认标签已被撤销。
func (m *Manager) RevokeLabel(ctx context.Context, issuanceRecordId uint32) error {
    // Step 1: Validate the input
    if issuanceRecordId == 0 {
        return errors.New("invalid issuance record ID: cannot be zero")
    }

    // Step 2: Call the StoreApi to delete the issuance record
    err := m.store.DeleteIssuanceRecordByID(ctx, issuanceRecordId)
    if err != nil {
        // Step 3: Handle any errors that might occur during the database operation
        return fmt.Errorf("failed to revoke label: %w", err)
    }

    // Step 4: Return the result of the operation
    return nil
}

// ListIssuanceRecords 提供所有标签发放记录的分页列表。
// - 可以接受过滤条件作为参数。
// - 调用 `StoreApi` 来检索所有知名教练（根据过滤条件，如果有的话）。
// - 处理可能出现的错误并返回发放记录列表。
func (m *Manager) ListIssuanceRecords(ctx context.Context, pageNumber int32, pageSize int32,
    coachIds []uint32, labelId uint32, effectiveStartTime, effectiveEndTime int64,
    status store.IssuanceRecordStatus, labelType store.LabelType) ([]*store.IssuanceRecord, uint32, error) {

    log.DebugWithCtx(ctx, "ListIssuanceRecords 空日志")
    log.DebugWithCtx(ctx, "ListIssuanceRecords status: %d, labelType: %d", uint32(status), uint32(labelType))

    // Call the GetIssuanceRecords method of the store
    records, err := m.store.GetIssuanceRecords(ctx, pageNumber, pageSize, coachIds, labelId, effectiveStartTime, effectiveEndTime, status, labelType)
    if err != nil {
        // If an error occurred, wrap it with fmt.Errorf and return it
        return nil, 0, fmt.Errorf("failed to list issuance records: %w", err)
    }

    // Call the CountIssuanceRecords method of the store
    count, err := m.store.CountIssuanceRecords(ctx, coachIds, labelId, effectiveStartTime, effectiveEndTime, status, labelType)
    if err != nil {
        // If an error occurred, wrap it with fmt.Errorf and return it
        return nil, 0, fmt.Errorf("failed to count issuance records: %w", err)
    }

    // Return the list of issuance records and the total count
    return records, count, nil
}

// RemoveRenownedPlayer 从教练那里移除知名玩家状态。
// - 接受教练 ID 作为参数。
// - 验证教练 ID 是否有效。
// - 调用 `StoreApi` 来删除知名教练记录。
// - 处理可能出现的错误并确认教练已从知名教练列表中移除。
func (m *Manager) RemoveRenownedPlayer(ctx context.Context, playerID uint32) error {
    // Step 1: Validate the input
    if playerID == 0 {
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "id不能为0")
    }

    // Step 2: Call the StoreApi to delete the renowned player
    err := m.store.DeleteRenownedPlayerByID(ctx, playerID)
    if err != nil {
        // Step 3: Handle any errors that might occur during the database operation
        return fmt.Errorf("failed to remove renowned player: %w", err)
    }

    // If no error occurred, return nil
    return nil
}

// BatchRemoveRenownedPlayers 批量处理多个教练的知名玩家状态和定价的移除。
// - 接受多个教练 ID 作为参数。
// - 验证所有传入的教练 ID 是否有效。
// - 调用 `StoreApi` 来批量删除知名教练记录。
// - 处理可能出现的错误并确认所有指定的教练已被移除。
func (m *Manager) BatchRemoveRenownedPlayers(ctx context.Context, playerIDs []uint32) error {
    return m.store.BatchDeleteRenownedPlayers(ctx, playerIDs)
}

// ListRenownedPlayers 提供所有知名玩家及其定价的分页列表。
// - 可以接受过滤条件作为参数。
// - 调用 `StoreApi` 来检索所有知名教练（根据过滤条件，如果有的话）。
// - 返回知名玩家列表和总数。
func (m *Manager) ListRenownedPlayers(ctx context.Context, pageNumber int32, pageSize int32, coachUid uint32, gameIdList []uint32) ([]*store.RenownedPlayer, uint32, error) {
    // Call the GetRenownedPlayers method of the store
    players, err := m.store.GetRenownedPlayers(ctx, pageNumber, pageSize, coachUid, gameIdList)
    if err != nil {
        // If an error occurred, wrap it with fmt.Errorf and return it
        return nil, 0, fmt.Errorf("failed to list renowned players: %w", err)
    }

    // Call the CountRenownedPlayers method of the store
    count, err := m.store.CountRenownedPlayers(ctx, coachUid, gameIdList)
    if err != nil {
        // If an error occurred, wrap it with fmt.Errorf and return it
        return nil, 0, fmt.Errorf("failed to count renowned players: %w", err)
    }

    // Return the list of renowned players and the total count
    return players, count, nil
}

// UpdateRenownedPlayer 更新知名选手
func (m *Manager) UpdateRenownedPlayer(ctx context.Context, id uint32, price int64) error {
    if id == 0 || price == 0 {
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    err := m.store.UpdateRenownedPlayer(ctx, &store.RenownedPlayer{
        ID:         id,
        Price:      uint32(price),
        UpdateTime: time.Now(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateRenownedPlayer fail, id:%d, price:%d, err:%v", id, price, err)
        return err
    }
    log.InfoWithCtx(ctx, "UpdateRenownedPlayer success, id:%d, price:%d", id, price)
    return nil
}

// BatchGetUserRenownedInfoByCoachIdsAndGameId 批量获取指定的游戏关联的多个教练的知名玩家信息
func (m *Manager) BatchGetUserRenownedInfoByCoachIdsAndGameId(ctx context.Context, coachIds []uint32, gameId uint32) ([]*store.RenownedPlayer, error) {
    return m.store.BatchGetUserRenownedInfoByCoachIdsAndGameId(ctx, coachIds, gameId)
}

// BatchGetUserRenownedInfoByCoachIdAndGameIds 批量获取指定的游戏关联的多个教练的知名玩家信息
func (m *Manager) BatchGetUserRenownedInfoByCoachIdAndGameIds(ctx context.Context, coachIds uint32, gameIds []uint32) ([]*store.RenownedPlayer, error) {
    return m.store.BatchGetUserRenownedInfoByCoachIdAndGameIds(ctx, coachIds, gameIds)
}

// BatchIssueLabel 发放标签给多个教练，使其生效。
func (m *Manager) BatchIssueLabel(ctx context.Context, labelId uint32, effectiveStartTime, effectiveEndTime time.Time, coachUidList []uint32) ([]uint32, error) {
    // Step 1: Validate the input
    if labelId == 0 || len(coachUidList) == 0 || effectiveStartTime.After(effectiveEndTime) {
        return []uint32{}, fmt.Errorf("invalid input: labelId, coachUidList must be greater than zero and effectiveStartTime must be before effectiveEndTime")
    }

    // Step 2: Iterate over the coachUidList
    var records []*store.IssuanceRecord
    for _, coachUid := range coachUidList {
        // Step 3: Create a new IssuanceRecord
        record := &store.IssuanceRecord{
            CoachID:               coachUid,
            LabelID:               labelId,
            EffectiveDuration:     &store.Duration{StartTime: effectiveStartTime, EndTime: effectiveEndTime},
            CreateTime:            time.Now(),
            UpdateTime:            time.Now(),
            PriceAdditionalSwitch: false, // 标签价格加成，发放标签时默认设置为false
        }
        records = append(records, record)
    }

    // Step 4: Call the StoreApi to create new issuance records
    err := m.store.BatchCreateIssuanceRecord(ctx, records)
    if err != nil {
        // Step 5: Handle any errors that might occur during the database operation
        return []uint32{}, fmt.Errorf("failed to issue label: %w", err)
    }

    // Step 6: If no error occurred, return nil
    issueIds := make([]uint32, len(records))
    for i, record := range records {
        issueIds[i] = record.ID
    }

    return issueIds, nil
}

// SetLabelPriceAdditionalSwitch 设置标签价格加成开关
func (m *Manager) SetLabelPriceAdditionalSwitch(ctx context.Context, coachId, labelId uint32, switchState bool) error {
    // Step 1: Validate the input
    if coachId == 0 || labelId == 0 {
        return fmt.Errorf("invalid input: coachId:%d labelId:%d is invalid", coachId, labelId)
    }

    // Step 2: Call the StoreApi to update the label's price additional switch
    err := m.store.UpdateLabelPriceAdditionalSwitch(ctx, coachId, labelId, switchState)
    if err != nil {
        // Step 3: Handle any errors that might occur during the database operation
        return fmt.Errorf("failed to set label price additional switch: %w", err)
    }

    return nil
}

// BatchGetLabel 批量获取标签
func (m *Manager) BatchGetLabel(ctx context.Context, ids []uint32) ([]*store.Label, error) {
    // Step 1: Check if the ids slice is empty
    if len(ids) == 0 {
        return []*store.Label{}, nil
    }

    // Step 2: Call the BatchGetLabel method of the store
    labels, err := m.store.BatchGetLabel(ctx, ids)
    if err != nil {
        // Step 3: Handle any errors that might occur during the store operation
        return nil, fmt.Errorf("failed to get labels: %w", err)
    }

    // Step 4: If no error occurred, return the retrieved labels and nil error
    return labels, nil
}

// GetCoachLabels 获取教练当前拥有的标签，如果有 gameIds，则进一步根据 gameIds 过滤，最终只返回满足 gameIds 的技能类型的标识和大神类型的标识
// 1. 查询指定教练的标识发放记录，
// 2. 抽取标识 id。
// 3. 批量查询标识信息。
// 4. 返回标识信息
func (m *Manager) GetCoachLabels(ctx context.Context, coachID uint32, gameIds ...uint32) ([]*store.Label, error) {
    // Step 1: Get the issuance records for the given coach ID
    issuanceRecords, err := m.store.GetCoachAvailableIssuanceRecord(ctx, coachID)
    if err != nil {
        return nil, fmt.Errorf("failed to get issuance records: %w", err)
    }

    // If there are no issuance records, return an empty list of labels
    if len(issuanceRecords) == 0 {
        return []*store.Label{}, nil
    }

    // Step 2: Extract the label IDs from the issuance records
    labelIDs := make([]uint32, len(issuanceRecords))
    for i, record := range issuanceRecords {
        labelIDs[i] = record.LabelID
    }

    // Step 3: Get the labels for the extracted label IDs
    labels, err := m.store.BatchGetLabel(ctx, labelIDs)
    if err != nil {
        return nil, fmt.Errorf("failed to get labels: %w", err)
    }
    // 如果 gameIds 不为空，则使用 gameIds 进一步过滤标识，只保留满足 gameIds 的技能类型的标识和大神类型的标识
    if len(gameIds) > 0 {
        filteredLabels := make([]*store.Label, 0, len(labels))
        for _, label := range labels {
            if label.LabelType == store.LabelType_LABEL_TYPE_COACH {
                filteredLabels = append(filteredLabels, label)
            } else {
                for _, gameId := range gameIds {
                    if label.GameID == gameId {
                        filteredLabels = append(filteredLabels, label)
                    }
                }
            }
        }
        return filteredLabels, nil
    }
    // Step 4: Return the list of labels
    return labels, nil
}

// GetLabelsByGamesWithCoachType  获取指定游戏的技能类型的标签和大神类型的标签
func (m *Manager) GetLabelsByGamesWithCoachType(ctx context.Context, gameIds ...uint32) ([]*store.Label, error) {
    labels, err := m.store.GetLabelsByCoachTypeAndGames(ctx, gameIds...)
    if err != nil {
        return nil, fmt.Errorf("GetLabelsByCoachTypeAndGames failed to get labels: %w", err)
    }

    // 过滤出技能类型和大神类型的标签
    labels = transform.FlatMap(labels, func(label *store.Label) []*store.Label {
        if label.LabelType == store.LabelType_LABEL_TYPE_COACH || label.LabelType == store.LabelType_LABEL_TYPE_SKILL {
            return []*store.Label{label}
        }
        return []*store.Label{}
    })

    return labels, nil
}

// BatchAddRenownedPlayer 将指定教练的指定游戏设置成知名玩家，并设置特殊定价。
func (m *Manager) BatchAddRenownedPlayer(ctx context.Context, itemList []*PlayerItem) error {
    if len(itemList) == 0 {
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "列表为空")
    }
    ts := time.Now()
    players := make([]*store.RenownedPlayer, 0, len(itemList))
    for _, item := range itemList {
        players = append(players, &store.RenownedPlayer{
            UID:        item.UID,
            GameID:     item.GameID,
            Price:      item.Price,
            GameName:   item.GameName,
            TTid:       item.TTid,
            UpdateTime: ts,
            CreateTime: ts,
        })
    }
    return m.store.BatchAddRenownedPlayer(ctx, players)
}

// BatchGetCoachLabels 批量获取多个教练的标识列表，如果 gameId有传，则进一步根据 gameId 过滤
// 1. 根据 gameId 查询关联的标识列表，然后抽取 id 成标识 id 列表 labelIds和建立索引 labelMap，标识 id 为 key
// 2. 根据 CoachIds 和 labelIds 批量查询发放记录
// 3. 组装数据，使用教练 id 作为 key，教练对应的标签数组作为 value。
// 4. 并发地按照标签的排序号进行排序
// 第二个返回参数map[uint32][]uint32 为 "coachId" to "开启价格加成的labelId列表"
func (m *Manager) BatchGetCoachLabels(ctx context.Context, coachIds []uint32, gameId []uint32) (map[uint32][]*store.Label, map[uint32][]uint32, error) {
    // 1. 根据 gameId 查询关联的标识列表，然后抽取 id 成标识 id 列表 labelIds和建立索引 labelMap，标识 id 为 key
    labelList, err := m.store.GetLabelsByCoachTypeAndGames(ctx, gameId...)
    if err != nil {
        return nil, nil, fmt.Errorf("BatchGetCoachLabelsForGame failed to get label list: %w", err)
    }
    labelMap := make(map[uint32]*store.Label, len(labelList))
    labelIds := make([]uint32, 0, len(labelList))
    for _, label := range labelList {
        labelMap[label.ID] = label
        labelIds = append(labelIds, label.ID)
    }

    // 2. 根据 CoachIds 和 labelIds 批量查询发放记录
    issuanceRecords, err := m.store.BatchGetIssuanceRecordByCoachIdsAndLabelIds(ctx, coachIds, labelIds)
    if err != nil {
        return nil, nil, fmt.Errorf("BatchGetCoachLabelsForGame failed to get issuance records: %w", err)
    }

    specialLabelIssuanceRecords, err := m.store.BatchGetSpecialLabelIssuanceRecords(ctx, coachIds, labelIds)
    if err != nil {
        return nil, nil, fmt.Errorf("BatchGetCoachLabelsForGame failed to get special label: %w", err)
    }
    // 合并到issuanceRecords中
    issuanceRecords = append(issuanceRecords, specialLabelIssuanceRecords...)

    // 3. 组装数据，使用教练 id 作为 key，教练对应的标签数组作为 value。
    coachLabelMap := make(map[uint32][]*store.Label, len(coachIds))
    coach2LabelIdsMap := make(map[uint32][]uint32, len(coachIds))
    for _, record := range issuanceRecords {
        label, ok := labelMap[record.LabelID]
        if !ok {
            continue
        }
        if record.PriceAdditionalSwitch {
            coach2LabelIdsMap[record.CoachID] = append(coach2LabelIdsMap[record.CoachID], record.LabelID)
        }
        coachLabelMap[record.CoachID] = append(coachLabelMap[record.CoachID], label)
    }

    // 4. 并发地按照标签的排序号进行排序
    wg := sync.WaitGroup{}
    for _, labels := range coachLabelMap {
        wg.Add(1)
        go func(labelList []*store.Label) {
            defer wg.Done()
            sort.SliceStable(labelList, func(i, j int) bool {
                return labelList[i].DisplayOrder < labelList[j].DisplayOrder
            })
        }(labels)
    }
    wg.Wait()

    return coachLabelMap, coach2LabelIdsMap, nil
}

func (m *Manager) CheckLabelOrder(c context0.Context, labelType pb.LabelType, displayOrder uint32, labelId uint32, checkType pb.CheckLabelOrderRequest_CheckType) error {
    switch checkType {
    case pb.CheckLabelOrderRequest_CHECK_TYPE_ADD:
        return m.store.CheckAddLabelOrder(c, labelType, displayOrder)
    case pb.CheckLabelOrderRequest_CHECK_TYPE_EDIT:
        return m.store.CheckUpdateLabelOrder(c, labelType, displayOrder, labelId)
    default:
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "checkType不合法")
    }
}

func (m *Manager) UpdateUserSpecialLabel(c context0.Context, request *pb.UpdateUserSpecialLabelRequest) error {
    return m.store.UpdateUserSpecialLabel(c, request)
}

func (m *Manager) BatchGetUserSpecialLabel(c context0.Context, userIds []uint32) (*pb.BatchGetUserSpecialLabelResponse, error) {
    out := &pb.BatchGetUserSpecialLabelResponse{}
    if len(userIds) == 0 {
        log.WarnWithCtx(c, "BatchGetUserSpecialLabel userIds is empty")
        return out, nil
    }
    specialLabelMap, err := m.store.BatchGetUserSpecialLabel(c, userIds)
    if err != nil {
        log.ErrorWithCtx(c, "BatchGetUserSpecialLabel failed, err:%v", err)
        return out, err
    }

    labelMap := make(map[uint32]bool)
    for _, labels := range specialLabelMap {
        for _, labelIds := range labels {
            for _, labelId := range labelIds {
                if labelId != 0 {
                    labelMap[labelId] = true
                }
            }
        }
    }
    labelIdList := make([]uint32, 0, len(labelMap))
    for labelId := range labelMap {
        labelIdList = append(labelIdList, labelId)
    }
    labelInfoMap, err := m.store.BatchGetLabelByIds(c, labelIdList)
    if err != nil {
        log.ErrorWithCtx(c, "BatchGetLabel failed, err:%v", err)
        return out, err
    }

    out.UserSpecialLabelMap = make(map[uint32]*pb.BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList, len(specialLabelMap))
    for uid, labels := range specialLabelMap {
        gameList := make([]*pb.UserGameSpecialLabelItem, 0, len(labels))
        for gameId, label := range labels {
            labelList := make([]*pb.UserSpecialLabelItem, 0, len(labels))
            for _, labelId := range label {
                var specialLabelType pb.SpecialLabelType
                var labelName string
                if labelInfoMap != nil {
                    labelInfo, ok := labelInfoMap[labelId]
                    if ok {
                        switch labelInfo.LabelType {
                        case store.LabelType_LABEL_TYPE_SPECIAL:
                            specialLabelType = pb.SpecialLabelType_SPECIAL_LABEL_TYPE_SPECIAL
                        case store.LabelType_LABEL_TYPE_VOICE:
                            specialLabelType = pb.SpecialLabelType_SPECIAL_LABEL_TYPE_VOICE
                        }
                        labelName = labelInfo.Name
                    }
                }

                labelList = append(labelList, &pb.UserSpecialLabelItem{
                    LabelId:   labelId,
                    LabelType: specialLabelType,
                    LabelName: labelName,
                })
            }
            gameList = append(gameList, &pb.UserGameSpecialLabelItem{
                GameId:    gameId,
                LabelList: labelList,
            })
        }
        out.UserSpecialLabelMap[uid] = &pb.BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList{
            GameList: gameList,
        }
    }
    return out, nil
}

func (m *Manager) BatchGetUserGameSpecialLabel(c context0.Context, request *pb.BatchGetUserGameSpecialLabelRequest) (*pb.BatchGetUserGameSpecialLabelResponse, error) {
    out := &pb.BatchGetUserGameSpecialLabelResponse{}
    labelListMap, err := m.store.BatchGetUserGameSpecialLabel(c, request.GetGameId(), request.GetUidList())
    if err != nil {
        log.ErrorWithCtx(c, "BatchGetUserGameSpecialLabel failed, err:%v", err)
        return out, err
    }

    // labelIdMap
    labelIdMap := make(map[uint32]bool)
    for _, labelList := range labelListMap {
        for _, label := range labelList {
            labelIdMap[label] = true
        }
    }
    // labelIdList
    labelIdList := make([]uint32, 0, len(labelIdMap))
    for labelId := range labelIdMap {
        labelIdList = append(labelIdList, labelId)
    }

    // labelInfoMap
    labelInfoMap, err := m.store.BatchGetLabelByIds(c, labelIdList)
    if err != nil {
        log.ErrorWithCtx(c, "BatchGetLabelByIds failed, err:%v", err)
        return out, err
    }

    // out
    out.UserSpecialLabelMap = make(map[uint32]*pb.BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList)
    for uid, labelList := range labelListMap {
        labelInfoList := make([]*pb.BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo, 0)
        var voiceLabelName string
        if labelInfoMap != nil {
            for _, labelId := range labelList {
                labelInfo, ok := labelInfoMap[labelId]
                if ok {
                    switch labelInfo.LabelType {
                    case store.LabelType_LABEL_TYPE_SPECIAL:
                        labelInfoList = append(labelInfoList, &pb.BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo{
                            LabelName:    labelInfo.Name,
                            LabelImage:   labelInfo.ImageUrl,
                            DisplayOrder: labelInfo.DisplayOrder,
                        })
                    case store.LabelType_LABEL_TYPE_VOICE:
                        voiceLabelName = labelInfo.Name
                    }
                }
            }
        }

        // 排序 labelInfoList
        sort.Slice(labelInfoList, func(i, j int) bool {
            return labelInfoList[i].DisplayOrder < labelInfoList[j].DisplayOrder
        })

        out.UserSpecialLabelMap[uid] = &pb.BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList{
            LabelList:  labelInfoList,
            VoiceLabel: voiceLabelName,
        }
    }

    return out, nil
}

func (m *Manager) GetSpecialLabelIssuanceRecord(c context.Context, pageNum, pageSize int64) ([]*store.IssuanceRecord, error) {
    return m.store.GetSpecialLabelIssuanceRecord(c, pageNum, pageSize)
}

func (m *Manager) GetEffectiveIssuanceRecord(c context.Context, pageNum, pageSize int64) ([]*store.IssuanceRecord, error) {
    return m.store.GetEffectiveIssuanceRecord(c, pageNum, pageSize)
}
