package mgr

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/bylink"
	"golang.52tt.com/pkg/log"
)

const (
	EsportGuaranteeSwitchLog = "esports_promise_win_log"
)

type EsportGuaranteeSwitchLogData struct {
	Platform string `json:"$platform"`
	AppType  string `json:"app_type"`
	Uid      uint32 `json:"uid"`
	GameId   string `json:"game_id"`
	Op       string `json:"op"`
	Source   string `json:"source"`
}

func ReportEsportGuaranteeSwitchLog(ctx context.Context, uid, gameId uint32, guaranteeSwitch, isSystemSource bool) {
	appType, platform := bylink.ConvertAppNameAndPlatformByCtx(ctx)
	op := "close"
	if guaranteeSwitch {
		op = "open"
	}
	source := "0"
	if isSystemSource {
		source = "1"
	}
	data := &EsportGuaranteeSwitchLogData{
		Platform: platform,
		AppType:  appType,
		Uid:      uid,
		Op:       op,
		Source:   source,
		GameId:   fmt.Sprintf("%d", gameId),
	}
	err := bylink.Track(ctx, uint64(uid), EsportGuaranteeSwitchLog, data, true)

	if err != nil {
		log.ErrorWithCtx(ctx, "ReportEsportGuaranteeSwitchLog track, uid:%d, gameId:%d, guaranteeSwitch:%v, err:%v", uid, gameId, guaranteeSwitch, err)
		return
	}
	log.InfoWithCtx(ctx, "ReportEsportGuaranteeSwitchLog track, uid:%d, gameId:%d,guaranteeSwitch:%v, data:%+v", uid, gameId, guaranteeSwitch, data)

}
