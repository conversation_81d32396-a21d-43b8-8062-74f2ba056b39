// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/esport/esport-skill/internal/mgr (interfaces: LabelManager,PricingService,GameConfigService,ImService,SkillService)

// Package mgr is a generated GoMock package.
package mgr

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	esport_skill "golang.52tt.com/protocol/services/esport-skill"
	store "golang.52tt.com/services/tt-rev/esport/esport-skill/internal/store"
)

// MockLabelManager is a mock of LabelManager interface.
type MockLabelManager struct {
	ctrl     *gomock.Controller
	recorder *MockLabelManagerMockRecorder
}

// MockLabelManagerMockRecorder is the mock recorder for MockLabelManager.
type MockLabelManagerMockRecorder struct {
	mock *MockLabelManager
}

// NewMockLabelManager creates a new mock instance.
func NewMockLabelManager(ctrl *gomock.Controller) *MockLabelManager {
	mock := &MockLabelManager{ctrl: ctrl}
	mock.recorder = &MockLabelManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLabelManager) EXPECT() *MockLabelManagerMockRecorder {
	return m.recorder
}

// BatchAddRenownedPlayer mocks base method.
func (m *MockLabelManager) BatchAddRenownedPlayer(arg0 context.Context, arg1 []*PlayerItem) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddRenownedPlayer", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchAddRenownedPlayer indicates an expected call of BatchAddRenownedPlayer.
func (mr *MockLabelManagerMockRecorder) BatchAddRenownedPlayer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddRenownedPlayer", reflect.TypeOf((*MockLabelManager)(nil).BatchAddRenownedPlayer), arg0, arg1)
}

// BatchGetCoachLabels mocks base method.
func (m *MockLabelManager) BatchGetCoachLabels(arg0 context.Context, arg1, arg2 []uint32) (map[uint32][]*store.Label, map[uint32][]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetCoachLabels", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32][]*store.Label)
	ret1, _ := ret[1].(map[uint32][]uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// BatchGetCoachLabels indicates an expected call of BatchGetCoachLabels.
func (mr *MockLabelManagerMockRecorder) BatchGetCoachLabels(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetCoachLabels", reflect.TypeOf((*MockLabelManager)(nil).BatchGetCoachLabels), arg0, arg1, arg2)
}

// BatchGetLabel mocks base method.
func (m *MockLabelManager) BatchGetLabel(arg0 context.Context, arg1 []uint32) ([]*store.Label, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetLabel", arg0, arg1)
	ret0, _ := ret[0].([]*store.Label)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetLabel indicates an expected call of BatchGetLabel.
func (mr *MockLabelManagerMockRecorder) BatchGetLabel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetLabel", reflect.TypeOf((*MockLabelManager)(nil).BatchGetLabel), arg0, arg1)
}

// BatchGetUserGameSpecialLabel mocks base method.
func (m *MockLabelManager) BatchGetUserGameSpecialLabel(arg0 context.Context, arg1 *esport_skill.BatchGetUserGameSpecialLabelRequest) (*esport_skill.BatchGetUserGameSpecialLabelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserGameSpecialLabel", arg0, arg1)
	ret0, _ := ret[0].(*esport_skill.BatchGetUserGameSpecialLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserGameSpecialLabel indicates an expected call of BatchGetUserGameSpecialLabel.
func (mr *MockLabelManagerMockRecorder) BatchGetUserGameSpecialLabel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserGameSpecialLabel", reflect.TypeOf((*MockLabelManager)(nil).BatchGetUserGameSpecialLabel), arg0, arg1)
}

// BatchGetUserRenownedInfoByCoachIdAndGameIds mocks base method.
func (m *MockLabelManager) BatchGetUserRenownedInfoByCoachIdAndGameIds(arg0 context.Context, arg1 uint32, arg2 []uint32) ([]*store.RenownedPlayer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserRenownedInfoByCoachIdAndGameIds", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.RenownedPlayer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserRenownedInfoByCoachIdAndGameIds indicates an expected call of BatchGetUserRenownedInfoByCoachIdAndGameIds.
func (mr *MockLabelManagerMockRecorder) BatchGetUserRenownedInfoByCoachIdAndGameIds(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserRenownedInfoByCoachIdAndGameIds", reflect.TypeOf((*MockLabelManager)(nil).BatchGetUserRenownedInfoByCoachIdAndGameIds), arg0, arg1, arg2)
}

// BatchGetUserRenownedInfoByCoachIdsAndGameId mocks base method.
func (m *MockLabelManager) BatchGetUserRenownedInfoByCoachIdsAndGameId(arg0 context.Context, arg1 []uint32, arg2 uint32) ([]*store.RenownedPlayer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserRenownedInfoByCoachIdsAndGameId", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.RenownedPlayer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserRenownedInfoByCoachIdsAndGameId indicates an expected call of BatchGetUserRenownedInfoByCoachIdsAndGameId.
func (mr *MockLabelManagerMockRecorder) BatchGetUserRenownedInfoByCoachIdsAndGameId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserRenownedInfoByCoachIdsAndGameId", reflect.TypeOf((*MockLabelManager)(nil).BatchGetUserRenownedInfoByCoachIdsAndGameId), arg0, arg1, arg2)
}

// BatchGetUserSpecialLabel mocks base method.
func (m *MockLabelManager) BatchGetUserSpecialLabel(arg0 context.Context, arg1 []uint32) (*esport_skill.BatchGetUserSpecialLabelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserSpecialLabel", arg0, arg1)
	ret0, _ := ret[0].(*esport_skill.BatchGetUserSpecialLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserSpecialLabel indicates an expected call of BatchGetUserSpecialLabel.
func (mr *MockLabelManagerMockRecorder) BatchGetUserSpecialLabel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserSpecialLabel", reflect.TypeOf((*MockLabelManager)(nil).BatchGetUserSpecialLabel), arg0, arg1)
}

// BatchIssueLabel mocks base method.
func (m *MockLabelManager) BatchIssueLabel(arg0 context.Context, arg1 uint32, arg2, arg3 time.Time, arg4 []uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchIssueLabel", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchIssueLabel indicates an expected call of BatchIssueLabel.
func (mr *MockLabelManagerMockRecorder) BatchIssueLabel(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchIssueLabel", reflect.TypeOf((*MockLabelManager)(nil).BatchIssueLabel), arg0, arg1, arg2, arg3, arg4)
}

// BatchRemoveRenownedPlayers mocks base method.
func (m *MockLabelManager) BatchRemoveRenownedPlayers(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchRemoveRenownedPlayers", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchRemoveRenownedPlayers indicates an expected call of BatchRemoveRenownedPlayers.
func (mr *MockLabelManagerMockRecorder) BatchRemoveRenownedPlayers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchRemoveRenownedPlayers", reflect.TypeOf((*MockLabelManager)(nil).BatchRemoveRenownedPlayers), arg0, arg1)
}

// CheckLabelOrder mocks base method.
func (m *MockLabelManager) CheckLabelOrder(arg0 context.Context, arg1 esport_skill.LabelType, arg2, arg3 uint32, arg4 esport_skill.CheckLabelOrderRequest_CheckType) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckLabelOrder", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckLabelOrder indicates an expected call of CheckLabelOrder.
func (mr *MockLabelManagerMockRecorder) CheckLabelOrder(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckLabelOrder", reflect.TypeOf((*MockLabelManager)(nil).CheckLabelOrder), arg0, arg1, arg2, arg3, arg4)
}

// CreateLabel mocks base method.
func (m *MockLabelManager) CreateLabel(arg0 context.Context, arg1 *store.Label) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLabel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateLabel indicates an expected call of CreateLabel.
func (mr *MockLabelManagerMockRecorder) CreateLabel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLabel", reflect.TypeOf((*MockLabelManager)(nil).CreateLabel), arg0, arg1)
}

// DeleteLabel mocks base method.
func (m *MockLabelManager) DeleteLabel(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteLabel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteLabel indicates an expected call of DeleteLabel.
func (mr *MockLabelManagerMockRecorder) DeleteLabel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLabel", reflect.TypeOf((*MockLabelManager)(nil).DeleteLabel), arg0, arg1)
}

// EditLabel mocks base method.
func (m *MockLabelManager) EditLabel(arg0 context.Context, arg1 *store.Label) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EditLabel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// EditLabel indicates an expected call of EditLabel.
func (mr *MockLabelManagerMockRecorder) EditLabel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EditLabel", reflect.TypeOf((*MockLabelManager)(nil).EditLabel), arg0, arg1)
}

// GetCoachLabels mocks base method.
func (m *MockLabelManager) GetCoachLabels(arg0 context.Context, arg1 uint32, arg2 ...uint32) ([]*store.Label, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCoachLabels", varargs...)
	ret0, _ := ret[0].([]*store.Label)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachLabels indicates an expected call of GetCoachLabels.
func (mr *MockLabelManagerMockRecorder) GetCoachLabels(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachLabels", reflect.TypeOf((*MockLabelManager)(nil).GetCoachLabels), varargs...)
}

// GetEffectiveIssuanceRecord mocks base method.
func (m *MockLabelManager) GetEffectiveIssuanceRecord(arg0 context.Context, arg1, arg2 int64) ([]*store.IssuanceRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEffectiveIssuanceRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.IssuanceRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEffectiveIssuanceRecord indicates an expected call of GetEffectiveIssuanceRecord.
func (mr *MockLabelManagerMockRecorder) GetEffectiveIssuanceRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEffectiveIssuanceRecord", reflect.TypeOf((*MockLabelManager)(nil).GetEffectiveIssuanceRecord), arg0, arg1, arg2)
}

// GetLabelsByGamesWithCoachType mocks base method.
func (m *MockLabelManager) GetLabelsByGamesWithCoachType(arg0 context.Context, arg1 ...uint32) ([]*store.Label, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLabelsByGamesWithCoachType", varargs...)
	ret0, _ := ret[0].([]*store.Label)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLabelsByGamesWithCoachType indicates an expected call of GetLabelsByGamesWithCoachType.
func (mr *MockLabelManagerMockRecorder) GetLabelsByGamesWithCoachType(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLabelsByGamesWithCoachType", reflect.TypeOf((*MockLabelManager)(nil).GetLabelsByGamesWithCoachType), varargs...)
}

// GetSpecialLabelIssuanceRecord mocks base method.
func (m *MockLabelManager) GetSpecialLabelIssuanceRecord(arg0 context.Context, arg1, arg2 int64) ([]*store.IssuanceRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSpecialLabelIssuanceRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.IssuanceRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSpecialLabelIssuanceRecord indicates an expected call of GetSpecialLabelIssuanceRecord.
func (mr *MockLabelManagerMockRecorder) GetSpecialLabelIssuanceRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSpecialLabelIssuanceRecord", reflect.TypeOf((*MockLabelManager)(nil).GetSpecialLabelIssuanceRecord), arg0, arg1, arg2)
}

// IssueLabel mocks base method.
func (m *MockLabelManager) IssueLabel(arg0 context.Context, arg1 uint32, arg2, arg3 time.Time, arg4 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IssueLabel", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IssueLabel indicates an expected call of IssueLabel.
func (mr *MockLabelManagerMockRecorder) IssueLabel(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IssueLabel", reflect.TypeOf((*MockLabelManager)(nil).IssueLabel), arg0, arg1, arg2, arg3, arg4)
}

// ListIssuanceRecords mocks base method.
func (m *MockLabelManager) ListIssuanceRecords(arg0 context.Context, arg1, arg2 int32, arg3 []uint32, arg4 uint32, arg5, arg6 int64, arg7 store.IssuanceRecordStatus, arg8 store.LabelType) ([]*store.IssuanceRecord, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListIssuanceRecords", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
	ret0, _ := ret[0].([]*store.IssuanceRecord)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListIssuanceRecords indicates an expected call of ListIssuanceRecords.
func (mr *MockLabelManagerMockRecorder) ListIssuanceRecords(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListIssuanceRecords", reflect.TypeOf((*MockLabelManager)(nil).ListIssuanceRecords), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
}

// ListLabels mocks base method.
func (m *MockLabelManager) ListLabels(arg0 context.Context, arg1, arg2 int32, arg3 uint32, arg4 store.LabelType, arg5 uint32) ([]*store.Label, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListLabels", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]*store.Label)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListLabels indicates an expected call of ListLabels.
func (mr *MockLabelManagerMockRecorder) ListLabels(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListLabels", reflect.TypeOf((*MockLabelManager)(nil).ListLabels), arg0, arg1, arg2, arg3, arg4, arg5)
}

// ListRenownedPlayers mocks base method.
func (m *MockLabelManager) ListRenownedPlayers(arg0 context.Context, arg1, arg2 int32, arg3 uint32, arg4 []uint32) ([]*store.RenownedPlayer, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRenownedPlayers", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*store.RenownedPlayer)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListRenownedPlayers indicates an expected call of ListRenownedPlayers.
func (mr *MockLabelManagerMockRecorder) ListRenownedPlayers(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRenownedPlayers", reflect.TypeOf((*MockLabelManager)(nil).ListRenownedPlayers), arg0, arg1, arg2, arg3, arg4)
}

// RemoveRenownedPlayer mocks base method.
func (m *MockLabelManager) RemoveRenownedPlayer(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveRenownedPlayer", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveRenownedPlayer indicates an expected call of RemoveRenownedPlayer.
func (mr *MockLabelManagerMockRecorder) RemoveRenownedPlayer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveRenownedPlayer", reflect.TypeOf((*MockLabelManager)(nil).RemoveRenownedPlayer), arg0, arg1)
}

// RevokeLabel mocks base method.
func (m *MockLabelManager) RevokeLabel(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RevokeLabel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RevokeLabel indicates an expected call of RevokeLabel.
func (mr *MockLabelManagerMockRecorder) RevokeLabel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RevokeLabel", reflect.TypeOf((*MockLabelManager)(nil).RevokeLabel), arg0, arg1)
}

// SetLabelPriceAdditionalSwitch mocks base method.
func (m *MockLabelManager) SetLabelPriceAdditionalSwitch(arg0 context.Context, arg1, arg2 uint32, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetLabelPriceAdditionalSwitch", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetLabelPriceAdditionalSwitch indicates an expected call of SetLabelPriceAdditionalSwitch.
func (mr *MockLabelManagerMockRecorder) SetLabelPriceAdditionalSwitch(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLabelPriceAdditionalSwitch", reflect.TypeOf((*MockLabelManager)(nil).SetLabelPriceAdditionalSwitch), arg0, arg1, arg2, arg3)
}

// UpdateRenownedPlayer mocks base method.
func (m *MockLabelManager) UpdateRenownedPlayer(arg0 context.Context, arg1 uint32, arg2 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRenownedPlayer", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRenownedPlayer indicates an expected call of UpdateRenownedPlayer.
func (mr *MockLabelManagerMockRecorder) UpdateRenownedPlayer(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRenownedPlayer", reflect.TypeOf((*MockLabelManager)(nil).UpdateRenownedPlayer), arg0, arg1, arg2)
}

// UpdateUserSpecialLabel mocks base method.
func (m *MockLabelManager) UpdateUserSpecialLabel(arg0 context.Context, arg1 *esport_skill.UpdateUserSpecialLabelRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserSpecialLabel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserSpecialLabel indicates an expected call of UpdateUserSpecialLabel.
func (mr *MockLabelManagerMockRecorder) UpdateUserSpecialLabel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserSpecialLabel", reflect.TypeOf((*MockLabelManager)(nil).UpdateUserSpecialLabel), arg0, arg1)
}

// MockPricingService is a mock of PricingService interface.
type MockPricingService struct {
	ctrl     *gomock.Controller
	recorder *MockPricingServiceMockRecorder
}

// MockPricingServiceMockRecorder is the mock recorder for MockPricingService.
type MockPricingServiceMockRecorder struct {
	mock *MockPricingService
}

// NewMockPricingService creates a new mock instance.
func NewMockPricingService(ctrl *gomock.Controller) *MockPricingService {
	mock := &MockPricingService{ctrl: ctrl}
	mock.recorder = &MockPricingServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPricingService) EXPECT() *MockPricingServiceMockRecorder {
	return m.recorder
}

// BatGetCoachPrice mocks base method.
func (m *MockPricingService) BatGetCoachPrice(arg0 context.Context, arg1 uint32, arg2 []uint32) (map[uint32]*esport_skill.Price, []uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetCoachPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]*esport_skill.Price)
	ret1, _ := ret[1].([]uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// BatGetCoachPrice indicates an expected call of BatGetCoachPrice.
func (mr *MockPricingServiceMockRecorder) BatGetCoachPrice(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetCoachPrice", reflect.TypeOf((*MockPricingService)(nil).BatGetCoachPrice), arg0, arg1, arg2)
}

// BatSetCoachPrice mocks base method.
func (m *MockPricingService) BatSetCoachPrice(arg0 context.Context, arg1 uint32, arg2 map[uint32]*esport_skill.Price) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatSetCoachPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatSetCoachPrice indicates an expected call of BatSetCoachPrice.
func (mr *MockPricingServiceMockRecorder) BatSetCoachPrice(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatSetCoachPrice", reflect.TypeOf((*MockPricingService)(nil).BatSetCoachPrice), arg0, arg1, arg2)
}

// BatchGetBasePriceSetting mocks base method.
func (m *MockPricingService) BatchGetBasePriceSetting(arg0 context.Context, arg1, arg2 []uint32) (map[uint32]map[uint32]*store.CoachBasePriceSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetBasePriceSetting", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]map[uint32]*store.CoachBasePriceSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBasePriceSetting indicates an expected call of BatchGetBasePriceSetting.
func (mr *MockPricingServiceMockRecorder) BatchGetBasePriceSetting(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBasePriceSetting", reflect.TypeOf((*MockPricingService)(nil).BatchGetBasePriceSetting), arg0, arg1, arg2)
}

// CalculatePrice mocks base method.
func (m *MockPricingService) CalculatePrice(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 *esport_skill.BasePrice, arg5 []*store.Label, arg6 []uint32, arg7 *store.RenownedPlayer) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculatePrice", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculatePrice indicates an expected call of CalculatePrice.
func (mr *MockPricingServiceMockRecorder) CalculatePrice(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculatePrice", reflect.TypeOf((*MockPricingService)(nil).CalculatePrice), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
}

// GetBasePriceSetting mocks base method.
func (m *MockPricingService) GetBasePriceSetting(arg0 context.Context, arg1, arg2 uint32) (*store.CoachBasePriceSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBasePriceSetting", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.CoachBasePriceSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBasePriceSetting indicates an expected call of GetBasePriceSetting.
func (mr *MockPricingServiceMockRecorder) GetBasePriceSetting(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBasePriceSetting", reflect.TypeOf((*MockPricingService)(nil).GetBasePriceSetting), arg0, arg1, arg2)
}

// HandleGodLevelChange mocks base method.
func (m *MockPricingService) HandleGodLevelChange(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleGodLevelChange", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleGodLevelChange indicates an expected call of HandleGodLevelChange.
func (mr *MockPricingServiceMockRecorder) HandleGodLevelChange(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleGodLevelChange", reflect.TypeOf((*MockPricingService)(nil).HandleGodLevelChange), arg0, arg1, arg2, arg3)
}

// SetBasePriceSetting mocks base method.
func (m *MockPricingService) SetBasePriceSetting(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBasePriceSetting", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetBasePriceSetting indicates an expected call of SetBasePriceSetting.
func (mr *MockPricingServiceMockRecorder) SetBasePriceSetting(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBasePriceSetting", reflect.TypeOf((*MockPricingService)(nil).SetBasePriceSetting), arg0, arg1, arg2, arg3)
}

// MockGameConfigService is a mock of GameConfigService interface.
type MockGameConfigService struct {
	ctrl     *gomock.Controller
	recorder *MockGameConfigServiceMockRecorder
}

// MockGameConfigServiceMockRecorder is the mock recorder for MockGameConfigService.
type MockGameConfigServiceMockRecorder struct {
	mock *MockGameConfigService
}

// NewMockGameConfigService creates a new mock instance.
func NewMockGameConfigService(ctrl *gomock.Controller) *MockGameConfigService {
	mock := &MockGameConfigService{ctrl: ctrl}
	mock.recorder = &MockGameConfigServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGameConfigService) EXPECT() *MockGameConfigServiceMockRecorder {
	return m.recorder
}

// AddEsportGameConfig mocks base method.
func (m *MockGameConfigService) AddEsportGameConfig(arg0 context.Context, arg1 *esport_skill.AddEsportGameConfigRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddEsportGameConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddEsportGameConfig indicates an expected call of AddEsportGameConfig.
func (mr *MockGameConfigServiceMockRecorder) AddEsportGameConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddEsportGameConfig", reflect.TypeOf((*MockGameConfigService)(nil).AddEsportGameConfig), arg0, arg1)
}

// GetEsportGameConfigListByPage mocks base method.
func (m *MockGameConfigService) GetEsportGameConfigListByPage(arg0 context.Context, arg1 *esport_skill.GetEsportGameConfigListByPageRequest) (*esport_skill.GetEsportGameConfigListByPageResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEsportGameConfigListByPage", arg0, arg1)
	ret0, _ := ret[0].(*esport_skill.GetEsportGameConfigListByPageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEsportGameConfigListByPage indicates an expected call of GetEsportGameConfigListByPage.
func (mr *MockGameConfigServiceMockRecorder) GetEsportGameConfigListByPage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEsportGameConfigListByPage", reflect.TypeOf((*MockGameConfigService)(nil).GetEsportGameConfigListByPage), arg0, arg1)
}

// GetGameDetailById mocks base method.
func (m *MockGameConfigService) GetGameDetailById(arg0 context.Context, arg1 uint32) (*esport_skill.GetGameDetailByIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameDetailById", arg0, arg1)
	ret0, _ := ret[0].(*esport_skill.GetGameDetailByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameDetailById indicates an expected call of GetGameDetailById.
func (mr *MockGameConfigServiceMockRecorder) GetGameDetailById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameDetailById", reflect.TypeOf((*MockGameConfigService)(nil).GetGameDetailById), arg0, arg1)
}

// GetGameDetailByIds mocks base method.
func (m *MockGameConfigService) GetGameDetailByIds(arg0 context.Context, arg1 []uint32) (*esport_skill.GetGameDetailByIdsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameDetailByIds", arg0, arg1)
	ret0, _ := ret[0].(*esport_skill.GetGameDetailByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameDetailByIds indicates an expected call of GetGameDetailByIds.
func (mr *MockGameConfigServiceMockRecorder) GetGameDetailByIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameDetailByIds", reflect.TypeOf((*MockGameConfigService)(nil).GetGameDetailByIds), arg0, arg1)
}

// GetGameListByGameNameFuzzy mocks base method.
func (m *MockGameConfigService) GetGameListByGameNameFuzzy(arg0 context.Context, arg1 string) ([]*esport_skill.SimpleGameInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameListByGameNameFuzzy", arg0, arg1)
	ret0, _ := ret[0].([]*esport_skill.SimpleGameInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameListByGameNameFuzzy indicates an expected call of GetGameListByGameNameFuzzy.
func (mr *MockGameConfigServiceMockRecorder) GetGameListByGameNameFuzzy(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameListByGameNameFuzzy", reflect.TypeOf((*MockGameConfigService)(nil).GetGameListByGameNameFuzzy), arg0, arg1)
}

// MockImService is a mock of ImService interface.
type MockImService struct {
	ctrl     *gomock.Controller
	recorder *MockImServiceMockRecorder
}

// MockImServiceMockRecorder is the mock recorder for MockImService.
type MockImServiceMockRecorder struct {
	mock *MockImService
}

// NewMockImService creates a new mock instance.
func NewMockImService(ctrl *gomock.Controller) *MockImService {
	mock := &MockImService{ctrl: ctrl}
	mock.recorder = &MockImServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockImService) EXPECT() *MockImServiceMockRecorder {
	return m.recorder
}

// AsyncSendLabelIssueImMsg mocks base method.
func (m *MockImService) AsyncSendLabelIssueImMsg(arg0 context.Context, arg1 []uint32) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AsyncSendLabelIssueImMsg", arg0, arg1)
}

// AsyncSendLabelIssueImMsg indicates an expected call of AsyncSendLabelIssueImMsg.
func (mr *MockImServiceMockRecorder) AsyncSendLabelIssueImMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncSendLabelIssueImMsg", reflect.TypeOf((*MockImService)(nil).AsyncSendLabelIssueImMsg), arg0, arg1)
}

// MockSkillService is a mock of SkillService interface.
type MockSkillService struct {
	ctrl     *gomock.Controller
	recorder *MockSkillServiceMockRecorder
}

// MockSkillServiceMockRecorder is the mock recorder for MockSkillService.
type MockSkillServiceMockRecorder struct {
	mock *MockSkillService
}

// NewMockSkillService creates a new mock instance.
func NewMockSkillService(ctrl *gomock.Controller) *MockSkillService {
	mock := &MockSkillService{ctrl: ctrl}
	mock.recorder = &MockSkillServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSkillService) EXPECT() *MockSkillServiceMockRecorder {
	return m.recorder
}

// BatchCheckCoachHasGame mocks base method.
func (m *MockSkillService) BatchCheckCoachHasGame(arg0 context.Context, arg1 []uint32, arg2 uint32) (map[uint32]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCheckCoachHasGame", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckCoachHasGame indicates an expected call of BatchCheckCoachHasGame.
func (mr *MockSkillServiceMockRecorder) BatchCheckCoachHasGame(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckCoachHasGame", reflect.TypeOf((*MockSkillService)(nil).BatchCheckCoachHasGame), arg0, arg1, arg2)
}

// SetGameGuaranteeWin mocks base method.
func (m *MockSkillService) SetGameGuaranteeWin(arg0 context.Context, arg1, arg2 uint32, arg3, arg4 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGameGuaranteeWin", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetGameGuaranteeWin indicates an expected call of SetGameGuaranteeWin.
func (mr *MockSkillServiceMockRecorder) SetGameGuaranteeWin(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGameGuaranteeWin", reflect.TypeOf((*MockSkillService)(nil).SetGameGuaranteeWin), arg0, arg1, arg2, arg3, arg4)
}
