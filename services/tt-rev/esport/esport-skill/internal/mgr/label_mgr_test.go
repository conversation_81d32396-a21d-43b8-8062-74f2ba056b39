package mgr

import (
    "context"
    "errors"
    "fmt"
    "github.com/golang/mock/gomock"
    pb "golang.52tt.com/protocol/services/esport-skill"
    "golang.52tt.com/services/tt-rev/esport/esport-skill/internal/store"
    "reflect"
    "sort"

    "testing"
    "time"
)

func TestManager_CreateLabel(t *testing.T) {
    type args struct {
        ctx   context.Context
        label *store.Label
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        initFunc func(s *mgrHelperForTest)
    }{
        {
            name: "正常",
            args: args{
                ctx: context.Background(),
                label: &store.Label{
                    LabelType:       store.LabelType_LABEL_TYPE_COACH,
                    ImageUrl:        "fasfasfd",
                    IsPriced:        true,
                    Price:           123,
                    ApplicableLevel: 1,
                    DisplayOrder:    32,
                    Description:     "dsfaf",
                    Requirements:    "asdfasf",
                    ApplyEntry:      "fasfsdaf",
                    Name:            "fasfa",
                },
            },
            wantErr: false,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().CreateLabel(gomock.Any(), gomock.Any()).Return(nil)
            },
        },
        {
            name: "保存出错",
            args: args{
                ctx: context.Background(),
                label: &store.Label{
                    LabelType:       store.LabelType_LABEL_TYPE_COACH,
                    ImageUrl:        "fasfasfd",
                    IsPriced:        true,
                    Price:           123,
                    ApplicableLevel: 1,
                    DisplayOrder:    32,
                    Description:     "dsfaf",
                    Requirements:    "asdfasf",
                    ApplyEntry:      "fasfsdaf",
                    Name:            "fasfa",
                },
            },
            wantErr: true,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().CreateLabel(gomock.Any(), gomock.Any()).Return(errors.New("error"))
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            if tt.initFunc != nil {
                tt.initFunc(m)
            }
            if err := m.CreateLabel(tt.args.ctx, tt.args.label); (err != nil) != tt.wantErr {
                t.Errorf("SetBasePriceSetting() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestManager_EditLabel(t *testing.T) {
    type args struct {
        ctx   context.Context
        label *store.Label
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        initFunc func(s *mgrHelperForTest)
    }{
        {
            name: "正常",
            args: args{
                ctx: context.Background(),
                label: &store.Label{
                    ID:              1,
                    LabelType:       store.LabelType_LABEL_TYPE_COACH,
                    ImageUrl:        "newImageUrl",
                    IsPriced:        false,
                    Price:           100,
                    ApplicableLevel: 2,
                    DisplayOrder:    20,
                    Description:     "newDescription",
                    Requirements:    "newRequirements",
                    ApplyEntry:      "newApplyEntry",
                    Name:            "newName",
                },
            },
            wantErr: false,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().UpdateLabel(gomock.Any(), gomock.Any()).Return(nil)
            },
        },
        {
            name: "保存出错",
            args: args{
                ctx: context.Background(),
                label: &store.Label{
                    ID:              1,
                    LabelType:       store.LabelType_LABEL_TYPE_COACH,
                    ImageUrl:        "newImageUrl",
                    IsPriced:        false,
                    Price:           100,
                    ApplicableLevel: 2,
                    DisplayOrder:    20,
                    Description:     "newDescription",
                    Requirements:    "newRequirements",
                    ApplyEntry:      "newApplyEntry",
                    Name:            "newName",
                },
            },
            wantErr: true,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().UpdateLabel(gomock.Any(), gomock.Any()).Return(errors.New("error"))
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            if tt.initFunc != nil {
                tt.initFunc(m)
            }
            if err := m.EditLabel(tt.args.ctx, tt.args.label); (err != nil) != tt.wantErr {
                t.Errorf("EditLabel() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestManager_DeleteLabel(t *testing.T) {
    type args struct {
        ctx     context.Context
        labelID uint32
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        initFunc func(s *mgrHelperForTest)
    }{
        {
            name: "成功删除标签和相关发放记录",
            args: args{
                ctx:     context.Background(),
                labelID: 123,
            },
            wantErr: false,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().DeleteLabelByID(gomock.Any(), gomock.Eq(uint32(123))).Return(nil)
                s.getStore().EXPECT().DeleteIssuanceRecordByLabelID(gomock.Any(), gomock.Eq(uint32(123))).Return(nil)
            },
        },
        {
            name: "无效的标签ID",
            args: args{
                ctx:     context.Background(),
                labelID: 0,
            },
            wantErr: true,
            initFunc: func(s *mgrHelperForTest) {
                // 对于无效的标签ID，不应调用store的任何方法
            },
        },
        {
            name: "删除标签时发生错误",
            args: args{
                ctx:     context.Background(),
                labelID: 123,
            },
            wantErr: true,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().DeleteLabelByID(gomock.Any(), gomock.Eq(uint32(123))).Return(errors.New("delete label error"))
            },
        },
        {
            name: "删除相关发放记录时发生错误",
            args: args{
                ctx:     context.Background(),
                labelID: 123,
            },
            wantErr: true,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().DeleteLabelByID(gomock.Any(), gomock.Eq(uint32(123))).Return(nil)
                s.getStore().EXPECT().DeleteIssuanceRecordByLabelID(gomock.Any(), gomock.Eq(uint32(123))).Return(errors.New("delete issuance record error"))
            },
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            if tt.initFunc != nil {
                tt.initFunc(m)
            }
            err := m.DeleteLabel(tt.args.ctx, tt.args.labelID)
            if (err != nil) != tt.wantErr {
                t.Errorf("Manager.DeleteLabel() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestManager_IssueLabel(t *testing.T) {
    type args struct {
        ctx                context.Context
        labelId            uint32
        effectiveStartTime time.Time
        effectiveEndTime   time.Time
        coachUid           uint32
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        initFunc func(s *mgrHelperForTest)
    }{
        {
            name: "正常发放",
            args: args{
                ctx:                context.Background(),
                labelId:            1,
                effectiveStartTime: time.Now(),
                effectiveEndTime:   time.Now().Add(24 * time.Hour),
                coachUid:           12345,
            },
            wantErr: false,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().CreateIssuanceRecord(gomock.Any(), gomock.Any()).Return(nil)
            },
        },
        {
            name: "labelId为0",
            args: args{
                ctx:                context.Background(),
                labelId:            0,
                effectiveStartTime: time.Now(),
                effectiveEndTime:   time.Now().Add(24 * time.Hour),
                coachUid:           12345,
            },
            wantErr: true,
        },
        {
            name: "coachUid为0",
            args: args{
                ctx:                context.Background(),
                labelId:            1,
                effectiveStartTime: time.Now(),
                effectiveEndTime:   time.Now().Add(24 * time.Hour),
                coachUid:           0,
            },
            wantErr: true,
        },
        {
            name: "开始时间晚于结束时间",
            args: args{
                ctx:                context.Background(),
                labelId:            1,
                effectiveStartTime: time.Now().Add(24 * time.Hour),
                effectiveEndTime:   time.Now(),
                coachUid:           12345,
            },
            wantErr: true,
        },
        {
            name: "数据库操作失败",
            args: args{
                ctx:                context.Background(),
                labelId:            1,
                effectiveStartTime: time.Now(),
                effectiveEndTime:   time.Now().Add(24 * time.Hour),
                coachUid:           12345,
            },
            wantErr: true,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().CreateIssuanceRecord(gomock.Any(), gomock.Any()).Return(errors.New("database error"))
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            if tt.initFunc != nil {
                tt.initFunc(m)
            }
            _, err := m.IssueLabel(tt.args.ctx, tt.args.labelId, tt.args.effectiveStartTime, tt.args.effectiveEndTime, tt.args.coachUid)
            if (err != nil) != tt.wantErr {
                t.Errorf("Manager.IssueLabel() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestManager_RevokeLabel(t *testing.T) {
    type args struct {
        ctx              context.Context
        issuanceRecordId uint32
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        initFunc func(m *mgrHelperForTest)
    }{
        {
            name: "正常撤销",
            args: args{
                ctx:              context.Background(),
                issuanceRecordId: 123,
            },
            wantErr: false,
            initFunc: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().DeleteIssuanceRecordByID(gomock.Any(), gomock.Eq(uint32(123))).Return(nil)
            },
        },
        {
            name: "撤销失败 - 无效的发放记录ID",
            args: args{
                ctx:              context.Background(),
                issuanceRecordId: 0,
            },
            wantErr:  true,
            initFunc: nil, // 不需要mock，因为期望直接返回错误
        },
        {
            name: "撤销失败 - 数据库操作错误",
            args: args{
                ctx:              context.Background(),
                issuanceRecordId: 123,
            },
            wantErr: true,
            initFunc: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().DeleteIssuanceRecordByID(gomock.Any(), gomock.Eq(uint32(123))).Return(errors.New("database error"))
            },
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            if tt.initFunc != nil {
                tt.initFunc(m)
            }
            if err := m.RevokeLabel(tt.args.ctx, tt.args.issuanceRecordId); (err != nil) != tt.wantErr {
                t.Errorf("RevokeLabel() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestManager_ListIssuanceRecords(t *testing.T) {
    type args struct {
        ctx                context.Context
        pageNumber         int32
        pageSize           int32
        coachIds           []uint32
        labelId            uint32
        effectiveStartTime int64
        effectiveEndTime   int64
    }
    tests := []struct {
        name      string
        args      args
        want      []*store.IssuanceRecord
        wantCount uint32
        wantErr   bool
        initFunc  func(m *mgrHelperForTest)
    }{
        {
            name: "正常",
            args: args{
                ctx:        context.Background(),
                pageNumber: 1,
                pageSize:   10,
            },
            want: []*store.IssuanceRecord{
                {ID: 1, CoachID: 123, LabelID: 10},
                {ID: 2, CoachID: 124, LabelID: 20},
            },
            wantCount: 2,
            wantErr:   false,
            initFunc: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().GetIssuanceRecords(gomock.Any(), int32(1), int32(10), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*store.IssuanceRecord{
                    {ID: 1, CoachID: 123, LabelID: 10},
                    {ID: 2, CoachID: 124, LabelID: 20},
                }, nil)
                m.getStore().EXPECT().CountIssuanceRecords(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(2), nil)
            },
        },
        {
            name: "查询错误",
            args: args{
                ctx:        context.Background(),
                pageNumber: 1,
                pageSize:   10,
            },
            want:      nil,
            wantCount: 0,
            wantErr:   true,
            initFunc: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().GetIssuanceRecords(gomock.Any(), int32(1), int32(10), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("query error"))
            },
        },
        {
            name: "计数错误",
            args: args{
                ctx:        context.Background(),
                pageNumber: 1,
                pageSize:   10,
            },
            want:      []*store.IssuanceRecord{},
            wantCount: 0,
            wantErr:   true,
            initFunc: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().GetIssuanceRecords(gomock.Any(), int32(1), int32(10), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*store.IssuanceRecord{
                    {ID: 1, CoachID: 123, LabelID: 10},
                    {ID: 2, CoachID: 124, LabelID: 20},
                }, nil)
                m.getStore().EXPECT().CountIssuanceRecords(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), errors.New("count error"))
            },
        },
        {
            name: "无数据",
            args: args{
                ctx:        context.Background(),
                pageNumber: 1,
                pageSize:   10,
            },
            want:      []*store.IssuanceRecord{},
            wantCount: 0,
            wantErr:   false,
            initFunc: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().GetIssuanceRecords(gomock.Any(), int32(1), int32(10), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*store.IssuanceRecord{}, nil)
                m.getStore().EXPECT().CountIssuanceRecords(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), nil)
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            if tt.initFunc != nil {
                tt.initFunc(m)
            }
            got, gotCount, err := m.ListIssuanceRecords(tt.args.ctx, tt.args.pageNumber, tt.args.pageSize, tt.args.coachIds, tt.args.labelId, tt.args.effectiveStartTime, tt.args.effectiveEndTime, 0, 0)
            if (err != nil) != tt.wantErr {
                t.Errorf("Manager.ListIssuanceRecords() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !compareIssuanceRecords(got, tt.want) || gotCount != tt.wantCount {
                t.Errorf("Manager.ListIssuanceRecords() = %v, %v, want %v, %v", got, gotCount, tt.want, tt.wantCount)
            }
        })
    }
}

// Helper function to compare slices of IssuanceRecord pointers
func compareIssuanceRecords(a, b []*store.IssuanceRecord) bool {
    if len(a) != len(b) {
        return false
    }
    for i := range a {
        if a[i].ID != b[i].ID || a[i].CoachID != b[i].CoachID || a[i].LabelID != b[i].LabelID {
            return false
        }
    }
    return true
}

func TestManager_ListLabels(t *testing.T) {
    type args struct {
        ctx        context.Context
        pageNumber int32
        pageSize   int32
        labelId    uint32
        labelType  store.LabelType
        gameId     uint32
    }
    tests := []struct {
        name       string
        args       args
        wantLabels []*store.Label
        wantCount  uint32
        wantErr    bool
        initMock   func(m *mgrHelperForTest)
    }{
        {
            name: "成功获取标签列表",
            args: args{
                ctx:        context.Background(),
                pageNumber: 1,
                pageSize:   10,
                labelId:    0,
                labelType:  store.LabelType_LABEL_TYPE_UNSPECIFIED,
                gameId:     0,
            },
            wantLabels: []*store.Label{
                {
                    ID:        1,
                    LabelType: store.LabelType_LABEL_TYPE_COACH,
                    Name:      "Test Label",
                },
            },
            wantCount: 1,
            wantErr:   false,
            initMock: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().GetLabels(gomock.Any(), int32(1), int32(10), uint32(0), store.LabelType_LABEL_TYPE_UNSPECIFIED, uint32(0)).Return([]*store.Label{
                    {
                        ID:        1,
                        LabelType: store.LabelType_LABEL_TYPE_COACH,
                        Name:      "Test Label",
                    },
                }, nil)
                m.getStore().EXPECT().CountLabels(gomock.Any(), uint32(0), store.LabelType_LABEL_TYPE_UNSPECIFIED, uint32(0)).Return(uint32(1), nil)
            },
        },
        {
            name: "获取标签列表失败",
            args: args{
                ctx:        context.Background(),
                pageNumber: 1,
                pageSize:   10,
                labelId:    0,
                labelType:  store.LabelType_LABEL_TYPE_UNSPECIFIED,
                gameId:     0,
            },
            wantLabels: nil,
            wantCount:  0,
            wantErr:    true,
            initMock: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().GetLabels(gomock.Any(), int32(1), int32(10), uint32(0), store.LabelType_LABEL_TYPE_UNSPECIFIED, uint32(0)).Return(nil, errors.New("error"))
            },
        },
        {
            name: "统计标签数量失败",
            args: args{
                ctx:        context.Background(),
                pageNumber: 1,
                pageSize:   10,
                labelId:    0,
                labelType:  store.LabelType_LABEL_TYPE_UNSPECIFIED,
                gameId:     0,
            },
            wantLabels: nil,
            wantCount:  0,
            wantErr:    true,
            initMock: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().GetLabels(gomock.Any(), int32(1), int32(10), uint32(0), store.LabelType_LABEL_TYPE_UNSPECIFIED, uint32(0)).Return([]*store.Label{
                    {
                        ID:        1,
                        LabelType: store.LabelType_LABEL_TYPE_COACH,
                        Name:      "Test Label",
                    },
                }, nil)
                m.getStore().EXPECT().CountLabels(gomock.Any(), uint32(0), store.LabelType_LABEL_TYPE_UNSPECIFIED, uint32(0)).Return(uint32(0), errors.New("error"))
            },
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            tt.initMock(m)

            gotLabels, gotCount, err := m.ListLabels(tt.args.ctx, tt.args.pageNumber, tt.args.pageSize, tt.args.labelId, tt.args.labelType, tt.args.gameId)
            if (err != nil) != tt.wantErr {
                t.Errorf("Manager.ListLabels() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(gotLabels, tt.wantLabels) {
                t.Errorf("Manager.ListLabels() gotLabels = %v, want %v", gotLabels, tt.wantLabels)
            }
            if gotCount != tt.wantCount {
                t.Errorf("Manager.ListLabels() gotCount = %v, want %v", gotCount, tt.wantCount)
            }
        })
    }
}

func TestManager_UpdateRenownedPlayer(t *testing.T) {
    type args struct {
        ctx    context.Context
        player *store.RenownedPlayer
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        initFunc func(s *mgrHelperForTest)
    }{
        {
            name: "更新成功",
            args: args{
                ctx: context.Background(),
                player: &store.RenownedPlayer{
                    ID:     1,
                    UID:    123,
                    GameID: 456,
                    Price:  100,
                },
            },
            wantErr: false,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().UpdateRenownedPlayer(gomock.Any(), gomock.Any()).Return(nil)
            },
        },
        {
            name: "更新失败 - 数据库错误",
            args: args{
                ctx: context.Background(),
                player: &store.RenownedPlayer{
                    ID:     2,
                    UID:    123,
                    GameID: 456,
                    Price:  200,
                },
            },
            wantErr: true,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().UpdateRenownedPlayer(gomock.Any(), gomock.Any()).Return(errors.New("database error"))
            },
        },
        {
            name: "更新失败 - 参数为 nil",
            args: args{
                ctx:    context.Background(),
                player: &store.RenownedPlayer{},
            },
            wantErr: true,
            initFunc: func(s *mgrHelperForTest) {
                // 参数为 nil，不会调用 store.UpdateRenownedPlayer 方法
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            if tt.initFunc != nil {
                tt.initFunc(m)
            }
            err := m.UpdateRenownedPlayer(tt.args.ctx, tt.args.player.ID, int64(tt.args.player.Price))
            if (err != nil) != tt.wantErr {
                t.Errorf("Manager.UpdateRenownedPlayer() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestManager_RemoveRenownedPlayer(t *testing.T) {
    type args struct {
        ctx      context.Context
        playerID uint32
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        initFunc func(s *mgrHelperForTest)
    }{
        {
            name: "成功删除知名玩家记录",
            args: args{
                ctx:      context.Background(),
                playerID: 1,
            },
            wantErr: false,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().DeleteRenownedPlayerByID(gomock.Any(), uint32(1)).Return(nil)
            },
        },
        {
            name: "删除操作失败",
            args: args{
                ctx:      context.Background(),
                playerID: 2,
            },
            wantErr: true,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().DeleteRenownedPlayerByID(gomock.Any(), uint32(2)).Return(errors.New("delete error"))
            },
        },
        {
            name: "无效的玩家ID",
            args: args{
                ctx:      context.Background(),
                playerID: 0,
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            if tt.initFunc != nil {
                tt.initFunc(m)
            }
            err := m.RemoveRenownedPlayer(tt.args.ctx, tt.args.playerID)
            if (err != nil) != tt.wantErr {
                t.Errorf("RemoveRenownedPlayer() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestManager_BatchIssueLabel(t *testing.T) {
    type args struct {
        ctx                context.Context
        labelId            uint32
        effectiveStartTime time.Time
        effectiveEndTime   time.Time
        coachUidList       []uint32
    }
    now := time.Now()
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        initFunc func(s *mgrHelperForTest)
    }{
        {
            name: "成功发放标签",
            args: args{
                ctx:                context.Background(),
                labelId:            1,
                effectiveStartTime: now,
                effectiveEndTime:   now.Add(24 * time.Hour),
                coachUidList:       []uint32{101, 102},
            },
            wantErr: false,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().BatchCreateIssuanceRecord(gomock.Any(), gomock.Any()).Return(nil)
            },
        },
        {
            name: "标签 ID 为零",
            args: args{
                ctx:                context.Background(),
                labelId:            0,
                effectiveStartTime: now,
                effectiveEndTime:   now.Add(24 * time.Hour),
                coachUidList:       []uint32{101, 102},
            },
            wantErr: true,
        },
        {
            name: "教练列表为空",
            args: args{
                ctx:                context.Background(),
                labelId:            1,
                effectiveStartTime: now,
                effectiveEndTime:   now.Add(24 * time.Hour),
                coachUidList:       []uint32{},
            },
            wantErr: true,
        },
        {
            name: "开始时间在结束时间之后",
            args: args{
                ctx:                context.Background(),
                labelId:            1,
                effectiveStartTime: now.Add(24 * time.Hour),
                effectiveEndTime:   now,
                coachUidList:       []uint32{101, 102},
            },
            wantErr: true,
        },
        {
            name: "数据库操作失败",
            args: args{
                ctx:                context.Background(),
                labelId:            1,
                effectiveStartTime: now,
                effectiveEndTime:   now.Add(24 * time.Hour),
                coachUidList:       []uint32{101, 102},
            },
            wantErr: true,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().BatchCreateIssuanceRecord(gomock.Any(), gomock.Any()).Return(errors.New("database error"))
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            if tt.initFunc != nil {
                tt.initFunc(m)
            }
            if _, err := m.BatchIssueLabel(tt.args.ctx, tt.args.labelId, tt.args.effectiveStartTime, tt.args.effectiveEndTime, tt.args.coachUidList); (err != nil) != tt.wantErr {
                t.Errorf("Manager.BatchIssueLabel() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestManager_BatchGetUserRenownedInfoByCoachIdsAndGameId(t *testing.T) {
    type args struct {
        ctx      context.Context
        coachIds []uint32
        gameId   uint32
    }
    tests := []struct {
        name      string
        args      args
        want      []*store.RenownedPlayer
        wantErr   bool
        mockSetup func(m *mgrHelperForTest)
    }{
        {
            name: "获取知名玩家信息成功",
            args: args{
                ctx:      context.Background(),
                coachIds: []uint32{1, 2},
                gameId:   100,
            },
            want: []*store.RenownedPlayer{
                {UID: 1, GameID: 100, Price: 150},
                {UID: 2, GameID: 100, Price: 200},
            },
            wantErr: false,
            mockSetup: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().BatchGetUserRenownedInfoByCoachIdsAndGameId(gomock.Any(), []uint32{1, 2}, uint32(100)).Return([]*store.RenownedPlayer{
                    {UID: 1, GameID: 100, Price: 150},
                    {UID: 2, GameID: 100, Price: 200},
                }, nil)
            },
        },
        {
            name: "没有找到知名玩家信息",
            args: args{
                ctx:      context.Background(),
                coachIds: []uint32{3},
                gameId:   101,
            },
            want:    []*store.RenownedPlayer{},
            wantErr: false,
            mockSetup: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().BatchGetUserRenownedInfoByCoachIdsAndGameId(gomock.Any(), []uint32{3}, uint32(101)).Return([]*store.RenownedPlayer{}, nil)
            },
        },
        {
            name: "数据库查询错误",
            args: args{
                ctx:      context.Background(),
                coachIds: []uint32{1, 2},
                gameId:   100,
            },
            want:    nil,
            wantErr: true,
            mockSetup: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().BatchGetUserRenownedInfoByCoachIdsAndGameId(gomock.Any(), []uint32{1, 2}, uint32(100)).Return(nil, errors.New("database error"))
            },
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            tt.mockSetup(m) // Set up mock expectations
            got, err := m.BatchGetUserRenownedInfoByCoachIdsAndGameId(tt.args.ctx, tt.args.coachIds, tt.args.gameId)
            if (err != nil) != tt.wantErr {
                t.Errorf("BatchGetUserRenownedInfoByCoachIdsAndGameId() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("BatchGetUserRenownedInfoByCoachIdsAndGameId() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestManager_BatchRemoveRenownedPlayers(t *testing.T) {
    tests := []struct {
        name          string
        playerIDs     []uint32
        mockSetupFunc func(m *mgrHelperForTest)
        wantErr       bool
    }{
        {
            name:      "Successful removal of multiple renowned players",
            playerIDs: []uint32{1, 2, 3},
            mockSetupFunc: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().
                    BatchDeleteRenownedPlayers(gomock.Any(), []uint32{1, 2, 3}).
                    Return(nil).
                    Times(1)
            },
            wantErr: false,
        },
        {
            name:      "Error occurred while removing multiple renowned players",
            playerIDs: []uint32{1, 2, 3},
            mockSetupFunc: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().
                    BatchDeleteRenownedPlayers(gomock.Any(), []uint32{1, 2, 3}).
                    Return(errors.New("delete error")).
                    Times(1)
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            manager := newMgrHelperForTest(t)

            if tt.mockSetupFunc != nil {
                tt.mockSetupFunc(manager)
            }

            err := manager.BatchRemoveRenownedPlayers(context.Background(), tt.playerIDs)
            if (err != nil) != tt.wantErr {
                t.Errorf("Manager.BatchRemoveRenownedPlayers() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestManager_BatchGetCoachLabels(t *testing.T) {
    type args struct {
        ctx      context.Context
        coachIds []uint32
        gameId   []uint32
    }
    tests := []struct {
        name       string
        args       args
        wantResult map[uint32][]*store.Label
        wantErr    bool
        initMock   func(m *mgrHelperForTest)
    }{
        {
            name: "成功获取教练的标识列表",
            args: args{
                ctx:      context.Background(),
                coachIds: []uint32{1, 2},
                gameId:   []uint32{100},
            },
            wantResult: map[uint32][]*store.Label{
                1: {
                    {ID: 2, Name: "Label2", DisplayOrder: 9},
                    {ID: 1, Name: "Label1", DisplayOrder: 10},
                },
                2: {
                    {ID: 3, Name: "Label3", DisplayOrder: 1},
                },
            },
            wantErr: false,
            initMock: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().
                    GetLabelsByCoachTypeAndGames(gomock.Any(), gomock.Eq(uint32(100))).
                    Return([]*store.Label{
                        {ID: 1, Name: "Label1", DisplayOrder: 10},
                        {ID: 2, Name: "Label2", DisplayOrder: 9},
                        {ID: 3, Name: "Label3", DisplayOrder: 1},
                    }, nil)

                m.getStore().EXPECT().
                    BatchGetIssuanceRecordByCoachIdsAndLabelIds(gomock.Any(), gomock.Eq([]uint32{1, 2}), gomock.Any()).
                    Return([]*store.IssuanceRecord{
                        {CoachID: 1, LabelID: 1},
                        {CoachID: 1, LabelID: 2},
                        {CoachID: 2, LabelID: 3},
                    }, nil)
                m.getStore().EXPECT().BatchGetSpecialLabelIssuanceRecords(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*store.IssuanceRecord{}, nil)
            },
        },
        {
            name: "获取标签失败",
            args: args{
                ctx:      context.Background(),
                coachIds: []uint32{1, 2},
                gameId:   []uint32{100},
            },
            wantResult: nil,
            wantErr:    true,
            initMock: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().
                    GetLabelsByCoachTypeAndGames(gomock.Any(), gomock.Eq(uint32(100))).
                    Return(nil, errors.New("internal error"))
            },
        },
        {
            name: "获取发放记录失败",
            args: args{
                ctx:      context.Background(),
                coachIds: []uint32{1, 2},
                gameId:   []uint32{100},
            },
            wantResult: nil,
            wantErr:    true,
            initMock: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().
                    GetLabelsByCoachTypeAndGames(gomock.Any(), gomock.Eq(uint32(100))).
                    Return([]*store.Label{
                        {ID: 1, Name: "Label1"},
                        {ID: 2, Name: "Label2"},
                        {ID: 3, Name: "Label3"},
                    }, nil)

                m.getStore().EXPECT().
                    BatchGetIssuanceRecordByCoachIdsAndLabelIds(gomock.Any(), gomock.Eq([]uint32{1, 2}), gomock.Any()).
                    Return(nil, errors.New("internal error"))

            },
        },
        // 其他测试用例...
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            tt.initMock(m)

            gotResult, _, err := m.BatchGetCoachLabels(tt.args.ctx, tt.args.coachIds, tt.args.gameId)
            if (err != nil) != tt.wantErr {
                t.Errorf("Manager.BatchGetCoachLabels() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(gotResult, tt.wantResult) {
                t.Errorf("Manager.BatchGetCoachLabels() = %v, want %v", gotResult, tt.wantResult)
            }
        })
    }
}

func TestManager_BatchAddRenownedPlayer(t *testing.T) {
    type args struct {
        ctx      context.Context
        itemList []*PlayerItem
    }
    tests := []struct {
        name     string
        args     args
        mockInit func(m *mgrHelperForTest)
        wantErr  bool
    }{
        {
            name: "正常添加",
            args: args{
                ctx: context.Background(),
                itemList: []*PlayerItem{
                    {
                        UID:    1,
                        GameID: 101,
                        Price:  1000,
                    },
                    {
                        UID:    2,
                        GameID: 101,
                        Price:  1500,
                    },
                },
            },
            mockInit: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().BatchAddRenownedPlayer(gomock.Any(), gomock.Any()).Return(nil)
            },
            wantErr: false,
        },
        {
            name: "重复添加",
            args: args{
                ctx: context.Background(),
                itemList: []*PlayerItem{
                    {
                        UID:    1,
                        GameID: 101,
                        Price:  1000,
                    },
                },
            },
            mockInit: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().BatchAddRenownedPlayer(gomock.Any(), gomock.Any()).Return(errors.New("serror"))
            },
            wantErr: true,
        },
        {
            name: "数据库错误",
            args: args{
                ctx: context.Background(),
                itemList: []*PlayerItem{
                    {
                        UID:    3,
                        GameID: 102,
                        Price:  1200,
                    },
                },
            },
            mockInit: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().BatchAddRenownedPlayer(gomock.Any(), gomock.Any()).Return(errors.New("db error"))
            },
            wantErr: true,
        },
        {
            name: "空列表",
            args: args{
                ctx:      context.Background(),
                itemList: []*PlayerItem{},
            },
            mockInit: func(m *mgrHelperForTest) {
                // 空列表的情况下，不应调用 BatchAddRenownedPlayer 方法
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            if tt.mockInit != nil {
                tt.mockInit(m)
            }
            err := m.BatchAddRenownedPlayer(tt.args.ctx, tt.args.itemList)
            if (err != nil) != tt.wantErr {
                t.Errorf("Manager.BatchAddRenownedPlayer() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestManager_GetLabelsByGamesWithCoachType(t *testing.T) {
    type args struct {
        ctx     context.Context
        gameIds []uint32
    }
    tests := []struct {
        name             string
        args             args
        mockExpectations func(s *mgrHelperForTest)
        wantLabels       []*store.Label
        wantErr          bool
    }{
        {
            name: "无游戏 ID，只获取教练类型的标签",
            args: args{
                ctx:     context.Background(),
                gameIds: nil,
            },
            mockExpectations: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().GetLabelsByCoachTypeAndGames(gomock.Any(), []uint32{}).Return([]*store.Label{
                    {ID: 1, LabelType: store.LabelType_LABEL_TYPE_COACH},
                }, nil)
            },
            wantLabels: []*store.Label{
                {ID: 1, LabelType: store.LabelType_LABEL_TYPE_COACH},
            },
            wantErr: false,
        },
        {
            name: "有游戏 ID，获取教练类型和游戏类型的标签",
            args: args{
                ctx:     context.Background(),
                gameIds: []uint32{100},
            },
            mockExpectations: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().GetLabelsByCoachTypeAndGames(gomock.Any(), gomock.Eq(uint32(100))).Return([]*store.Label{
                    {ID: 1, LabelType: store.LabelType_LABEL_TYPE_COACH},
                    {ID: 2, LabelType: store.LabelType_LABEL_TYPE_SKILL, GameID: 100},
                }, nil)
            },
            wantLabels: []*store.Label{
                {ID: 1, LabelType: store.LabelType_LABEL_TYPE_COACH},
                {ID: 2, LabelType: store.LabelType_LABEL_TYPE_SKILL, GameID: 100},
            },
            wantErr: false,
        },
        {
            name: "数据库操作错误",
            args: args{
                ctx:     context.Background(),
                gameIds: []uint32{100},
            },
            mockExpectations: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().GetLabelsByCoachTypeAndGames(gomock.Any(), gomock.Eq(uint32(100))).Return(nil, errors.New("database error"))
            },
            wantLabels: nil,
            wantErr:    true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            if tt.mockExpectations != nil {
                tt.mockExpectations(m)
            }
            gotLabels, err := m.GetLabelsByGamesWithCoachType(tt.args.ctx, tt.args.gameIds...)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetLabelsByGamesWithCoachType() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(gotLabels, tt.wantLabels) {
                t.Errorf("GetLabelsByGamesWithCoachType() gotLabels = %v, want %v", gotLabels, tt.wantLabels)
            }
        })
    }
}

func TestManager_ListRenownedPlayers(t *testing.T) {
    tests := []struct {
        name          string
        pageNumber    int32
        pageSize      int32
        coachUid      uint32
        gameId        []uint32
        mockStoreFunc func(m *mgrHelperForTest)
        wantPlayers   []*store.RenownedPlayer
        wantCount     uint32
        wantErr       bool
    }{
        {
            name:       "正常情况",
            pageNumber: 1,
            pageSize:   10,
            coachUid:   123,
            gameId:     []uint32{456},
            mockStoreFunc: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().GetRenownedPlayers(gomock.Any(), int32(1), int32(10), uint32(123), []uint32{456}).Return([]*store.RenownedPlayer{
                    {
                        ID:     1,
                        UID:    123,
                        GameID: 456,
                        Price:  100,
                    },
                }, nil)
                m.getStore().EXPECT().CountRenownedPlayers(gomock.Any(), uint32(123), []uint32{456}).Return(uint32(1), nil)
            },
            wantPlayers: []*store.RenownedPlayer{
                {
                    ID:     1,
                    UID:    123,
                    GameID: 456,
                    Price:  100,
                },
            },
            wantCount: 1,
            wantErr:   false,
        },
        {
            name:       "数据库查询错误",
            pageNumber: 1,
            pageSize:   10,
            coachUid:   123,
            gameId:     []uint32{456},
            mockStoreFunc: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().GetRenownedPlayers(gomock.Any(), int32(1), int32(10), uint32(123), []uint32{456}).Return(nil, errors.New("db error"))
            },
            wantPlayers: nil,
            wantCount:   0,
            wantErr:     true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            if tt.mockStoreFunc != nil {
                tt.mockStoreFunc(m)
            }
            gotPlayers, gotCount, err := m.ListRenownedPlayers(context.Background(), tt.pageNumber, tt.pageSize, tt.coachUid, tt.gameId)
            if (err != nil) != tt.wantErr {
                t.Errorf("Manager.ListRenownedPlayers() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !compareRenownedPlayers(gotPlayers, tt.wantPlayers) || gotCount != tt.wantCount {
                t.Errorf("Manager.ListRenownedPlayers() = %v, %v, want %v, %v", gotPlayers, gotCount, tt.wantPlayers, tt.wantCount)
            }
        })
    }
}

// compareRenownedPlayers is a helper function to compare slices of RenownedPlayer.
func compareRenownedPlayers(a, b []*store.RenownedPlayer) bool {
    if len(a) != len(b) {
        return false
    }
    for i := range a {
        if a[i].ID != b[i].ID || a[i].UID != b[i].UID || a[i].GameID != b[i].GameID || a[i].Price != b[i].Price {
            return false
        }
    }
    return true
}

func TestManager_BatchGetLabel(t *testing.T) {
    // 定义测试用例类型
    type args struct {
        ctx context.Context
        ids []uint32
    }
    tests := []struct {
        name       string
        args       args
        wantLabels []*store.Label
        wantErr    bool
        setupMock  func(m *mgrHelperForTest)
    }{
        {
            name: "成功获取多个标签",
            args: args{
                ctx: context.Background(),
                ids: []uint32{1, 2, 3},
            },
            wantLabels: []*store.Label{
                {ID: 1, Name: "Label 1"},
                {ID: 2, Name: "Label 2"},
                {ID: 3, Name: "Label 3"},
            },
            wantErr: false,
            setupMock: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().BatchGetLabel(gomock.Any(), []uint32{1, 2, 3}).Return([]*store.Label{
                    {ID: 1, Name: "Label 1"},
                    {ID: 2, Name: "Label 2"},
                    {ID: 3, Name: "Label 3"},
                }, nil)
            },
        },
        {
            name: "未找到标签",
            args: args{
                ctx: context.Background(),
                ids: []uint32{4},
            },
            wantLabels: []*store.Label{},
            wantErr:    false,
            setupMock: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().BatchGetLabel(gomock.Any(), []uint32{4}).Return([]*store.Label{}, nil)
            },
        },
        {
            name: "数据库操作错误",
            args: args{
                ctx: context.Background(),
                ids: []uint32{5},
            },
            wantLabels: nil,
            wantErr:    true,
            setupMock: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().BatchGetLabel(gomock.Any(), []uint32{5}).Return(nil, errors.New("database error"))
            },
        },
        {
            name: "传入空的ID列表",
            args: args{
                ctx: context.Background(),
                ids: []uint32{},
            },
            wantLabels: []*store.Label{},
            wantErr:    false,
            setupMock: func(m *mgrHelperForTest) {
            },
        },
    }

    // 遍历每个测试用例
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 创建Manager实例，注入mock对象
            m := newMgrHelperForTest(t)
            tt.setupMock(m)
            // 调用BatchGetLabel方法
            gotLabels, err := m.BatchGetLabel(tt.args.ctx, tt.args.ids)
            if (err != nil) != tt.wantErr {
                t.Errorf("Manager.BatchGetLabel() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(gotLabels, tt.wantLabels) {
                t.Errorf("Manager.BatchGetLabel() = %v, want %v", gotLabels, tt.wantLabels)
            }
        })
    }
}

func TestManager_GetCoachLabels(t *testing.T) {
    type args struct {
        ctx     context.Context
        coachID uint32
        gameIds []uint32
    }
    tests := []struct {
        name       string
        args       args
        wantLabels []*store.Label
        wantErr    bool
        initFunc   func(s *mgrHelperForTest)
    }{
        {
            name: "no issuance records",
            args: args{
                ctx:     context.Background(),
                coachID: 1234,
                gameIds: nil,
            },
            wantLabels: []*store.Label{},
            wantErr:    false,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().GetCoachAvailableIssuanceRecord(gomock.Any(), gomock.Eq(uint32(1234))).Return(nil, nil)
            },
        },
        {
            name: "with issuance records but no gameIds",
            args: args{
                ctx:     context.Background(),
                coachID: 1234,
                gameIds: nil,
            },
            wantLabels: []*store.Label{
                {ID: 1, LabelType: store.LabelType_LABEL_TYPE_COACH},
            },
            wantErr: false,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().GetCoachAvailableIssuanceRecord(gomock.Any(), gomock.Eq(uint32(1234))).Return([]*store.IssuanceRecord{{LabelID: 1}}, nil)
                s.getStore().EXPECT().BatchGetLabel(gomock.Any(), gomock.Eq([]uint32{1})).Return([]*store.Label{{ID: 1, LabelType: store.LabelType_LABEL_TYPE_COACH}}, nil)
            },
        },
        {
            name: "with issuance records and gameIds",
            args: args{
                ctx:     context.Background(),
                coachID: 1234,
                gameIds: []uint32{100},
            },
            wantLabels: []*store.Label{
                {ID: 1, LabelType: store.LabelType_LABEL_TYPE_COACH},
                {ID: 2, GameID: 100, LabelType: store.LabelType_LABEL_TYPE_SKILL},
            },
            wantErr: false,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().GetCoachAvailableIssuanceRecord(gomock.Any(), gomock.Eq(uint32(1234))).Return([]*store.IssuanceRecord{{LabelID: 1}, {LabelID: 2}}, nil)
                s.getStore().EXPECT().BatchGetLabel(gomock.Any(), gomock.Eq([]uint32{1, 2})).Return(
                    []*store.Label{
                        {ID: 1, LabelType: store.LabelType_LABEL_TYPE_COACH},
                        {ID: 2, GameID: 100, LabelType: store.LabelType_LABEL_TYPE_SKILL},
                    }, nil)
            },
        },
        {
            name: "error getting issuance records",
            args: args{
                ctx:     context.Background(),
                coachID: 1234,
                gameIds: nil,
            },
            wantLabels: nil,
            wantErr:    true,
            initFunc: func(s *mgrHelperForTest) {
                s.getStore().EXPECT().GetCoachAvailableIssuanceRecord(gomock.Any(), gomock.Eq(uint32(1234))).Return(nil, errors.New("error"))
            },
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            if tt.initFunc != nil {
                tt.initFunc(m)
            }
            gotLabels, err := m.GetCoachLabels(tt.args.ctx, tt.args.coachID, tt.args.gameIds...)
            if (err != nil) != tt.wantErr {
                t.Errorf("Manager.GetCoachLabels() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(gotLabels, tt.wantLabels) {
                t.Errorf("Manager.GetCoachLabels() gotLabels = %v, want %v", gotLabels, tt.wantLabels)
            }
        })
    }
}

func Test_SortSlice(t *testing.T) {
    // 排序 labelInfoList
    labelInfoList := []*store.Label{
        {ID: 1, Name: "Label1", DisplayOrder: 10},
        {ID: 2, Name: "Label2", DisplayOrder: 9},
        {ID: 3, Name: "Label3", DisplayOrder: 1},
    }
    sort.Slice(labelInfoList, func(i, j int) bool {
        return labelInfoList[i].DisplayOrder < labelInfoList[j].DisplayOrder
    })

    for _, item := range labelInfoList {
        fmt.Println(item)
    }
}

func TestManager_BatchGetUserSpecialLabel(t *testing.T) {
    type args struct {
        ctx  context.Context
        uids []uint32
    }
    tests := []struct {
        name       string
        args       args
        wantResult *pb.BatchGetUserSpecialLabelResponse
        wantErr    bool
        mockSetup  func(m *mgrHelperForTest)
    }{
        {
            name: "成功获取用户特殊标签",
            args: args{
                ctx:  context.Background(),
                uids: []uint32{1, 2},
            },
            wantResult: &pb.BatchGetUserSpecialLabelResponse{
                UserSpecialLabelMap: map[uint32]*pb.BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList{
                    1: {
                        GameList: []*pb.UserGameSpecialLabelItem{
                            {
                                GameId: 119,
                                LabelList: []*pb.UserSpecialLabelItem{
                                    {
                                        LabelId:   1,
                                        LabelName: "Label1",
                                        LabelType: pb.SpecialLabelType_SPECIAL_LABEL_TYPE_VOICE,
                                    },
                                    {
                                        LabelId:   2,
                                        LabelName: "Label2",
                                        LabelType: pb.SpecialLabelType_SPECIAL_LABEL_TYPE_SPECIAL,
                                    },
                                },
                            },
                        },
                    },
                    2: {
                        GameList: []*pb.UserGameSpecialLabelItem{
                            {
                                GameId: 119,
                                LabelList: []*pb.UserSpecialLabelItem{
                                    {
                                        LabelId:   1,
                                        LabelName: "Label1",
                                        LabelType: pb.SpecialLabelType_SPECIAL_LABEL_TYPE_VOICE,
                                    },
                                },
                            },
                        },
                    },
                },
            },
            wantErr: false,
            mockSetup: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().BatchGetUserSpecialLabel(gomock.Any(), gomock.Eq([]uint32{1, 2})).Return(map[uint32]map[uint32][]uint32{
                    1: map[uint32][]uint32{119: []uint32{1, 2}},
                    2: map[uint32][]uint32{119: []uint32{1}}}, nil)
                m.getStore().EXPECT().BatchGetLabelByIds(gomock.Any(), gomock.Eq([]uint32{1, 2})).Return(map[uint32]*store.Label{
                    1: {ID: 1, Name: "Label1", LabelType: store.LabelType_LABEL_TYPE_VOICE},
                    2: {ID: 2, Name: "Label2", LabelType: store.LabelType_LABEL_TYPE_SPECIAL},
                }, nil)
            },
        },
        {
            name: "数据库查询错误",
            args: args{
                ctx:  context.Background(),
                uids: []uint32{1, 2},
            },
            wantResult: &pb.BatchGetUserSpecialLabelResponse{},
            wantErr:    true,
            mockSetup: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().BatchGetUserSpecialLabel(gomock.Any(), gomock.Eq([]uint32{1, 2})).Return(nil, errors.New("database error"))
            },
        },
        {
            name: "空的用户ID列表",
            args: args{
                ctx:  context.Background(),
                uids: []uint32{},
            },
            wantErr:    false,
            wantResult: &pb.BatchGetUserSpecialLabelResponse{},
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            if tt.mockSetup != nil {
                tt.mockSetup(m)
            }
            gotResult, err := m.BatchGetUserSpecialLabel(tt.args.ctx, tt.args.uids)
            if (err != nil) != tt.wantErr {
                t.Errorf("BatchGetUserSpecialLabel() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(gotResult, tt.wantResult) {
                t.Errorf("BatchGetUserSpecialLabel() gotResult = %v, want %v", gotResult, tt.wantResult)
            }
        })
    }
}

func TestManager_BatchGetUserGameSpecialLabel(t *testing.T) {
    type args struct {
        ctx context.Context
        req *pb.BatchGetUserGameSpecialLabelRequest
    }
    tests := []struct {
        name       string
        args       args
        wantResult *pb.BatchGetUserGameSpecialLabelResponse
        wantErr    bool
        mockSetup  func(m *mgrHelperForTest)
    }{
        {
            name: "成功获取用户特殊标签",
            args: args{
                ctx: context.Background(),
                req: &pb.BatchGetUserGameSpecialLabelRequest{
                    UidList: []uint32{1, 2},
                    GameId:  100,
                },
            },
            wantResult: &pb.BatchGetUserGameSpecialLabelResponse{
                UserSpecialLabelMap: map[uint32]*pb.BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList{
                    1: {
                        LabelList: []*pb.BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo{
                            {
                                LabelName:    "Label1",
                                LabelImage:   "http1",
                                DisplayOrder: 10,
                            },
                            {
                                LabelName:    "Label2",
                                LabelImage:   "http2",
                                DisplayOrder: 100,
                            },
                        },
                        VoiceLabel: "声音",
                    },
                    2: {
                        LabelList: []*pb.BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo{
                            {
                                LabelName:    "Label1",
                                LabelImage:   "http1",
                                DisplayOrder: 10,
                            },
                        },
                        VoiceLabel: "声音",
                    },
                },
            },
            wantErr: false,
            mockSetup: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().BatchGetUserGameSpecialLabel(gomock.Any(), gomock.Eq(uint32(100)), gomock.Eq([]uint32{1, 2})).Return(map[uint32][]uint32{
                    1: {1, 2, 3},
                    2: {1, 3},
                }, nil)
                m.getStore().EXPECT().BatchGetLabelByIds(gomock.Any(), gomock.Any()).Return(map[uint32]*store.Label{
                    1: {ID: 1, Name: "Label1", LabelType: store.LabelType_LABEL_TYPE_SPECIAL, ImageUrl: "http1", DisplayOrder: 10},
                    2: {ID: 2, Name: "Label2", LabelType: store.LabelType_LABEL_TYPE_SPECIAL, ImageUrl: "http2", DisplayOrder: 100},
                    3: {ID: 3, Name: "声音", LabelType: store.LabelType_LABEL_TYPE_VOICE},
                }, nil)
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            if tt.mockSetup != nil {
                tt.mockSetup(m)
            }
            gotResult, err := m.BatchGetUserGameSpecialLabel(tt.args.ctx, tt.args.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("BatchGetUserGameSpecialLabel() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(gotResult, tt.wantResult) {
                t.Errorf("BatchGetUserGameSpecialLabel() gotResult = %v, want %v", gotResult, tt.wantResult)
            }
        })
    }
}

func TestManager_SetLabelPriceAdditionalSwitch(t *testing.T) {

    type args struct {
        ctx         context.Context
        coachId     uint32
        labelId     uint32
        switchState bool
    }
    tests := []struct {
        name     string
        initFunc func(m *mgrHelperForTest)
        args     args
        wantErr  bool
    }{
        {
            name: "成功设置标签加价开关",
            initFunc: func(m *mgrHelperForTest) {
                m.getStore().EXPECT().UpdateLabelPriceAdditionalSwitch(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
            },
            args: args{
                ctx:     context.Background(),
                coachId: 1,
                labelId: 2,
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newMgrHelperForTest(t)
            if tt.initFunc != nil {
                tt.initFunc(m)
            }
            err := m.SetLabelPriceAdditionalSwitch(tt.args.ctx, tt.args.coachId, tt.args.labelId, tt.args.switchState)
            if (err != nil) != tt.wantErr {
                t.Errorf("SetLabelPriceAdditionalSwitch() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}
