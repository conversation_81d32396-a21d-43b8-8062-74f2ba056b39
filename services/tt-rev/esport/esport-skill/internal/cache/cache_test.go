package cache

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/log"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"os"
)

func getRedisCfg() *redisConnect.RedisConfig {
	ctx := context.Background()
	configFile := "../../service-config.json"
	data, err := os.ReadFile(configFile)
	if err != nil {
		log.ErrorWithCtx(ctx, "HuntMonsterMissionActivityConf ReadFile err:%v", err)
		panic(err)
	}
	type config struct {
		RedisCfg *redisConnect.RedisConfig `json:"redis"`
	}
	cfg := &config{}
	err = json.Unmarshal(data, cfg)
	if err != nil {
		panic(err)
	}
	return cfg.RedisCfg
}

func getRedisCli(cfg *redisConnect.RedisConfig) ICache {
	cli, err := NewCache(context.Background(), cfg)
	if err != nil {
		panic(err)
	}
	return cli
}
