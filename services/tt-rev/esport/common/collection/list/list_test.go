package list

import (
    "fmt"
    "reflect"
    "strconv"
    "testing"
)

func TestDiff_EmptyLists(t *testing.T) {
    result := Diff([]int{}, []int{}, func(i int) int { return i })
    if len(result) != 0 {
        t.<PERSON><PERSON><PERSON>("Expected empty result, got %v", result)
    }
}

func TestDiff_NoCommonElements(t *testing.T) {
    a := []int{1, 2, 3}
    b := []int{4, 5, 6}
    result := Diff(a, b, func(i int) int { return i })
    if len(result) != len(a) {
        t.<PERSON><PERSON>rf("Expected result length %d, got %d", len(a), len(result))
    }
}

func TestDiff_AllElementsIgnored(t *testing.T) {
    a := []int{1, 2, 3}
    b := []int{1, 2, 3}
    result := Diff(a, b, func(i int) int { return i })
    if len(result) != 0 {
        t.<PERSON><PERSON><PERSON>("Expected empty result, got %v", result)
    }
}

func TestDiff_SomeElementsIgnored(t *testing.T) {
    a := []int{1, 2, 3, 4}
    b := []int{2, 4}
    expected := []int{1, 3}
    result := Diff(a, b, func(i int) int { return i })
    if len(result) != len(expected) {
        t.Errorf("Expected result length %d, got %d", len(expected), len(result))
    }
    for i, v := range result {
        if v != expected[i] {
            t.Errorf("Expected %d at index %d, got %d", expected[i], i, v)
        }
    }
}

func TestDiff_DifferentTypes(t *testing.T) {
    a := []struct{ id int }{{1}, {2}, {3}}
    b := []int{2}
    expected := []struct{ id int }{{1}, {3}}
    result := Diff(a, b, func(s struct{ id int }) int { return s.id })
    if len(result) != len(expected) {
        t.Errorf("Expected result length %d, got %d", len(expected), len(result))
    }
    for i, v := range result {
        if v != expected[i] {
            t.Errorf("Expected %v at index %d, got %v", expected[i], i, v)
        }
    }
}

func TestIntersect(t *testing.T) {
    tests := []struct {
        name     string
        a        []int
        b        []int
        expected []int
    }{
        {
            name:     "No common elements",
            a:        []int{1, 2, 3},
            b:        []int{4, 5, 6},
            expected: []int{},
        },
        {
            name:     "Some common elements",
            a:        []int{1, 2, 3, 4},
            b:        []int{3, 4, 5, 6},
            expected: []int{3, 4},
        },
        {
            name:     "All elements common",
            a:        []int{1, 2, 3},
            b:        []int{1, 2, 3},
            expected: []int{1, 2, 3},
        },
        {
            name:     "Empty first list",
            a:        []int{},
            b:        []int{1, 2, 3},
            expected: []int{},
        },
        {
            name:     "Empty second list",
            a:        []int{1, 2, 3},
            b:        []int{},
            expected: []int{},
        },
        {
            name:     "Both lists empty",
            a:        []int{},
            b:        []int{},
            expected: []int{},
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result := Intersect(tt.a, tt.b, func(i int) int { return i })
            if !reflect.DeepEqual(result, tt.expected) {
                t.Errorf("Expected %v, got %v", tt.expected, result)
            }
        })
    }
}

func TestDiffs_EmptyLists(t *testing.T) {
    result := Diffs([]int{}, []int{})
    if len(result) != 0 {
        t.Errorf("Expected empty result, got %v", result)
    }
}

func TestDiffs_NoCommonElements(t *testing.T) {
    a := []int{1, 2, 3}
    b := []int{4, 5, 6}
    result := Diffs(a, b)
    if len(result) != len(a) {
        t.Errorf("Expected result length %d, got %d", len(a), len(result))
    }
}

func TestDiffs_AllElementsIgnored(t *testing.T) {
    a := []int{1, 2, 3}
    b := []int{1, 2, 3}
    result := Diffs(a, b)
    if len(result) != 0 {
        t.Errorf("Expected empty result, got %v", result)
    }
}

func TestDiffs_SomeElementsIgnored(t *testing.T) {
    a := []int{1, 2, 3, 4}
    b := []int{2, 4}
    expected := []int{1, 3}
    result := Diffs(a, b)
    if len(result) != len(expected) {
        t.Errorf("Expected result length %d, got %d", len(expected), len(result))
    }
    for i, v := range result {
        if v != expected[i] {
            t.Errorf("Expected %d at index %d, got %d", expected[i], i, v)
        }
    }
}

func TestDiffs_DifferentTypes(t *testing.T) {
    a := []string{"a", "b", "c"}
    b := []string{"b"}
    expected := []string{"a", "c"}
    result := Diffs(a, b)
    if len(result) != len(expected) {
        t.Errorf("Expected result length %d, got %d", len(expected), len(result))
    }
    for i, v := range result {
        if v != expected[i] {
            t.Errorf("Expected %v at index %d, got %v", expected[i], i, v)
        }
    }
}

func TestMerge(t *testing.T) {
    listA := []uint32{1, 2, 3}
    listB := []uint32{3, 4, 5}
    result := Merge(listA, listB)
    fmt.Println(result)

    listC := []string{"2", "3", "4"}
    result = Merge(listA, listC, func(s string) uint32 {
        res, err := strconv.ParseUint(s, 10, 32)
        if err != nil {
            return 0
        }
        return uint32(res)
    })
    fmt.Println(result)

    listD := []int{2, 3, 4, 5}
    result = Merge(listA, listD, func(i int) uint32 {
        return uint32(i)
    })
    fmt.Println(result)
}
