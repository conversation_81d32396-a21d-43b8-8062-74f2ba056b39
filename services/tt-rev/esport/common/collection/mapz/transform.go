package mapz

// ToMap 将 items 列表转换为 map，通过 getKey 函数获取 key
func ToMap[E any, K comparable](items []E, getKey func(E) K) map[K]E {
    result := make(map[K]E, len(items))
    for _, item := range items {
        result[getKey(item)] = item
    }
    return result
}

// ToMapG 将 items 列表转换为 map，通过 getKey 函数获取 key
func ToMapG[E, V any, K comparable](items []E, getPair func(int, E) (K, V)) map[K]V {
    result := make(map[K]V, len(items))
    for i, item := range items {
        key, value := getPair(i, item)
        result[key] = value
    }
    return result
}

// Keys 获取 map 的所有 key
func Keys[T any, K comparable](m map[K]T) []K {
    keys := make([]K, 0, len(m))
    for k := range m {
        keys = append(keys, k)
    }
    return keys
}

// Values 获取 map 的所有 value
func Values[T any, K comparable](m map[K]T) []T {
    values := make([]T, 0, len(m))
    for _, v := range m {
        values = append(values, v)
    }
    return values
}
