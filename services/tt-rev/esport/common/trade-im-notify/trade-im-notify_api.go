package trade_im_notify

import(
	context "context"
	impb "golang.52tt.com/protocol/services/im-api"
)

type ITradeImNotify interface {
	PushOrderChangeNotify(ctx context.Context, orderId string, uidList []uint32) error
	SendImMsg(ctx context.Context, cmd uint32, from, to *UserMsg) (fromMsgId, toMsgId uint64,err error)
	SendOfficialAccountMsg(ctx context.Context, namespace string, toUid uint32, text *impb.Text) error
	SendOneSideImMsg(ctx context.Context, cmd uint32, fromUid, toUid, whichSide uint32, rawMsg *ImMsg) (msgId uint64,err error)
	SendOrderImMsg(ctx context.Context, from, to *UserOrderImMsg) (fromMsgId, toMsgId uint64,err error)
	SendOrderImSysMsg(ctx context.Context, from, to *UserOrderImSysMsg) (fromMsgId, toMsgId uint64,err error)
}

