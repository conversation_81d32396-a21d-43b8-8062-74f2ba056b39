package event

import (
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/services/tt-rev/esport/esport-role/internal/conf"
	"golang.52tt.com/services/tt-rev/esport/esport-role/internal/mgr"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
)

type KafkaEvent struct {
	mgr mgr.IMgr
	bc  conf.IBusinessConfManager

	ContractEventSub        subscriber.Subscriber
	GuildCoopChangeEventSub subscriber.Subscriber
}

// NewKafkaEvent .
func NewKafkaEvent(sc *conf.StartConfig, m mgr.IMgr, bc conf.IBusinessConfManager) (*KafkaEvent, error) {
	sub := &KafkaEvent{
		mgr: m,
		bc:  bc,
	}
	// cfg作用于kafka模式下的配置
	cfg := kafka.DefaultConfig()

	var err error
	//sub.ContractEventSub, err = comKafka.NewKFKSub(sc.ContractKFK, sub.handlerContractEvent)
	//if err != nil {
	//	log.Errorf("NewKafkaEvent new ContractEventSub fail: %v", err)
	//	return sub, err
	//}

	// NewSubscriber创建消费者，通过环境变量TT_EVENT_SUBSCRIBE_MODE决定开启那种模式（kafka、eventlink、mix，不配置默认为kafka）
	sub.ContractEventSub, err = kafka.NewSubscriber(sc.ContractKFK.BrokerList(), cfg, subscriber.WithMaxRetryTimes(5))
	if err != nil {
		log.Errorf("NewKafkaEvent new ContractEventSub fail: %v", err)
		return sub, err
	}
	err = sub.ContractEventSub.SubscribeContext(sc.CoachChangeKFK.GroupID, []string{sc.ContractKFK.Topics}, subscriber.ProcessorContextFunc(sub.handlerContractEvent))
	if err != nil {
		panic(err)
	}

	// NewSubscriber创建消费者，通过环境变量TT_EVENT_SUBSCRIBE_MODE决定开启那种模式（kafka、eventlink、mix，不配置默认为kafka）
	sub.GuildCoopChangeEventSub, err = kafka.NewSubscriber(sc.GuildCoopChangeKFK.BrokerList(), cfg, subscriber.WithMaxRetryTimes(5))
	if err != nil {
		log.Errorf("NewKafkaEvent new ContractEventSub fail: %v", err)
		return sub, err
	}

	err = sub.GuildCoopChangeEventSub.SubscribeContext(sc.GuildCoopChangeKFK.GroupID, []string{sc.GuildCoopChangeKFK.Topics}, subscriber.ProcessorContextFunc(sub.handlerGuildCoopChangeEvent))
	if err != nil {
		log.Errorf("NewKafkaEvent new GuildCoopChangeEventSub fail: %v", err)
		return sub, err
	}

	log.Infof("NewKFKSub success,config:%+v,", sc)
	return sub, nil
}

func (e *KafkaEvent) Shutdown() {
	e.ContractEventSub.Stop()
	e.GuildCoopChangeEventSub.Stop()
}
