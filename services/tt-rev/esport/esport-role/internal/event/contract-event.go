package event

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"github.com/google/uuid"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkaanchorcontract"
	"time"
)

const topicContractEvent = "anchor_contract_event_kafka"

func (e *KafkaEvent) handlerContractEvent(c context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	if msg.Topic != topicContractEvent {
		return nil, false
	}

	eventMsg := &kafkaanchorcontract.AnchorContractEvent{}
	err := proto.Unmarshal(msg.Value, eventMsg)
	if err != nil {
		log.Errorf(" handlerContractEvent Failed to proto.Unmarshal err(%v)", err)
		return err, true
	}

	log.Debugf("handlerContractEvent %+v", eventMsg)

	ctx, cancel := context.WithTimeout(protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{RequestID: uuid.New().String()}), 3*time.Second)
	defer cancel()

	if eventMsg.EventType == uint32(kafkaanchorcontract.EVENT_TYPE_EVENT_CANCEL_CONTRACT) {
		err = e.mgr.CancelContractMsgHandle(ctx, eventMsg)
		if err != nil {
			log.Errorf("handlerContractEvent fail to PresentEventHandle, msg:%v, err:%v", eventMsg, err)
			return err, true
		}

	} else if eventMsg.EventType == uint32(kafkaanchorcontract.EVENT_TYPE_EVENT_CONTRACT_DEL_ESPORTS) {
		err = e.mgr.ContractDelESportMsgHandle(ctx, eventMsg)
		if err != nil {
			log.Errorf("handlerContractEvent fail to PresentEventHandle, msg:%v, err:%v", eventMsg, err)
			return err, true
		}

	} else if eventMsg.EventType == uint32(kafkaanchorcontract.EVENT_TYPE_EVENT_EXTENSION_CONTRACT) {
		err = e.mgr.ContractRenewMsgHandle(ctx, eventMsg)
		if err != nil {
			log.Errorf("handlerContractEvent fail to PresentEventHandle, msg:%v, err:%v", eventMsg, err)
			return err, true
		}

	} else {
		return nil, false
	}

	return nil, false
}
