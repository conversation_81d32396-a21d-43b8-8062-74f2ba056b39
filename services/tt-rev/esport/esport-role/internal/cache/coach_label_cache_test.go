package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/log"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"golang.52tt.com/protocol/services/esport_role"
	"os"
	"testing"
)

func getRedisCfg() *redisConnect.RedisConfig {
	ctx := context.Background()
	configFile := "../../esport-role.json"
	data, err := os.ReadFile(configFile)
	if err != nil {
		log.ErrorWithCtx(ctx, "esport-role redis ReadFile err:%v", err)
		panic(err)
	}
	type config struct {
		RedisCfg *redisConnect.RedisConfig `json:"redis"`
	}
	cfg := &config{}
	err = json.Unmarshal(data, cfg)
	if err != nil {
		panic(err)
	}
	return cfg.RedisCfg
}

func getRedisCli(cfg *redisConnect.RedisConfig) ICache {
	cli, err := NewCache(context.Background(), cfg)
	if err != nil {
		panic(err)
	}
	return cli
}

func TestCache_BatchInsertCoachLabel(t *testing.T) {
	t.Skip()
	cli := getRedisCli(getRedisCfg())
	err := cli.BatchInsertCoachLabel(context.Background(), map[uint32]*esport_role.CoachLabel{
		2465920: &esport_role.CoachLabel{
			Type:      0,
			SourceUrl: "http://xxxx.png",
		},
		2400464: &esport_role.CoachLabel{
			Type:      1,
			SourceUrl: "https://xxxx.webp",
		},
	})
	fmt.Println(err)
}

func TestCache_BatchGetCoachLabel(t *testing.T) {
	t.Skip()
	cli := getRedisCli(getRedisCfg())
	out, err := cli.BatchGetCoachLabel(context.Background(), []uint32{2465920, 2400464, 123})
	fmt.Println(out, err)
}

func TestCache_BatchDelCoachLabelCache(t *testing.T) {
	t.Skip()
	cli := getRedisCli(getRedisCfg())
	err := cli.BatchDelCoachLabelCache(context.Background(), []uint32{2465920, 2400464, 123})
	fmt.Println(err)
}
