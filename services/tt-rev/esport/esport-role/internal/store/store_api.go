package store

import(
	context "context"
	mysql1 "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	time "time"
	esport_role "golang.52tt.com/protocol/services/esport_role"
)

type IStore interface {
	AddUserBlacklistOpLog(ctx context.Context, info *BlacklistOpLog) error
	AddUserToApplyBlacklist(ctx context.Context, info *ApplyBlacklist) error
	BatGetApplyRecordByGuildId(ctx context.Context, guildId uint32, applyType, status []uint32, limit, offset uint32, sortByApplyTime bool) ([]*ApplyRecord,error)
	BatGetApplyRecordByUidList(ctx context.Context, uidList []uint32, applyType, status []uint32, limit, offset uint32, sortByApplyTime bool) ([]*ApplyRecord,error)
	BatGetApplyRecordCntByGuildId(ctx context.Context, guildId uint32, applyType, status []uint32) (uint32,error)
	BatGetApplyRecordCntByUidList(ctx context.Context, uidList []uint32, applyType, status []uint32) (uint32,error)
	BatchAddUserToApplyBlacklist(ctx context.Context, infos []*ApplyBlacklist) error
	BatchGetCoachLabel(c context.Context, uidList []uint32) (map[uint32]*esport_role.CoachLabel,error)
	BatchGetUserLabelDuration(c context.Context, uidList []uint32) (map[uint32][]*TimeDuration,error)
	BatchGrantCoachLabel(c context.Context, uidList []uint32, labelStr string, labelType uint32, beginTs, endTs int64) error
	BatchInsertReclaimInfo(ctx context.Context, l []*ReclaimRoleLog) error
	ChangeReclaimRoleLogStatus(ctx context.Context, id, newStatus, oldStatus uint32) (int64,error)
	Close() error
	GetAllESportRole(ctx context.Context, offset, limit uint32) ([]*ESportCoachInfo,error)
	GetApplyBlacklistByPage(ctx context.Context, offset, limit uint32, t time.Time) ([]*ApplyBlacklist,error)
	GetApplyBlacklistByUid(ctx context.Context, uid uint32, t time.Time) (*ApplyBlacklist,error)
	GetApplyBlacklistByUidList(ctx context.Context, uidList []uint32, t time.Time, limit, offset uint32) ([]*ApplyBlacklist,error)
	GetApplyBlacklistCount(ctx context.Context, t time.Time) (uint32,error)
	GetApplyBlacklistCountByUidList(ctx context.Context, uidList []uint32, t time.Time) (uint32,error)
	GetApplyRecordApplyTypeCount(ctx context.Context, applyType []uint32, status ...uint32) (uint32,error)
	GetApplyRecordByApplyType(ctx context.Context, limit, offset uint32, sortByApplyTime bool, applyType []uint32, status ...uint32) ([]*ApplyRecord,error)
	GetApplyRecordByAuditToken(ctx context.Context, auditToken string) (*ApplyRecord,error)
	GetApplyRecordByIdList(ctx context.Context, idList ...uint32) ([]*ApplyRecord,error)
	GetApplyRecordByStatus(ctx context.Context, limit uint32, t time.Time, status ...uint32) ([]*ApplyRecord,error)
	GetApplyRecordWithIdNum(ctx context.Context, idNum string, statusList ...uint32) ([]*ApplyRecord,error)
	GetApplyRecordWithUid(ctx context.Context, uid uint32, statusList ...uint32) ([]*ApplyRecord,error)
	GetBlackListHandleRecord(ctx context.Context, uidList []uint32, offset, limit uint32) ([]*BlacklistOpLog,error)
	GetBlackListHandleRecordCnt(ctx context.Context, uidList []uint32) (uint32,error)
	GetCoachLabelByRecordId(c context.Context, recordId uint32) (uid uint32,label *esport_role.CoachLabel,err error)
	GetCoachLabelRecordByPage(c context.Context, uid uint32, labelStatus esport_role.CoachLabelStatus, pageNum, pageSize uint32) (*esport_role.GetCoachLabelRecordByPageResponse,error)
	GetCoachLabelRecordCnt(c context.Context, uid uint32, labelStatus esport_role.CoachLabelStatus) (uint32,error)
	GetESportRoleInfoByIdentifyNum(ctx context.Context, identityNum string, t time.Time) (*ESportCoachInfo,error)
	GetESportRoleInfoByUid(ctx context.Context, uid uint32, t time.Time) (*ESportCoachInfo,error)
	GetESportRoleInfosByGuildId(ctx context.Context, guildId uint32, roleType []uint32, t time.Time, limit, offset uint32) ([]*ESportCoachInfo,error)
	GetESportRoleInfosByGuildIdAndUid(ctx context.Context, guildId uint32, roleType []uint32, t time.Time, limit, offset uint32, uid uint32) ([]*ESportCoachInfo,error)
	GetESportRoleInfosByGuildIdCount(ctx context.Context, guildId uint32, roleType []uint32, t time.Time) (uint32,error)
	GetESportRoleInfosByPage(ctx context.Context, roleType []uint32, t time.Time, offset, limit uint32) ([]*ESportCoachInfo,error)
	GetESportRoleInfosByUids(ctx context.Context, uids []uint32, roleType []uint32, t time.Time, limit, offset uint32) ([]*ESportCoachInfo,error)
	GetESportRoleInfosCountByType(ctx context.Context, roleType []uint32, t time.Time) (uint32,error)
	GetESportRoleInfosCountByUids(ctx context.Context, uids []uint32, roleType []uint32, t time.Time) (uint32,error)
	GetExistESportUid2IdMap(ctx context.Context, uidList []uint32, t time.Time) (map[uint32]uint32,error)
	GetNotifyGuildLogByStatus(ctx context.Context, status []uint32, limit uint32) ([]*NotifyGuildLog,error)
	GetReclaimPendingList(ctx context.Context, limit uint32) ([]*ReclaimRoleLog,error)
	GetReclaimRoleLogCount(ctx context.Context) (uint32,error)
	GetReclaimRoleLogCountByGuildId(ctx context.Context, guildId uint32) (uint32,error)
	GetReclaimRoleLogCountByUids(ctx context.Context, uids []uint32) (uint32,error)
	GetReclaimRoleLogListByGuildId(ctx context.Context, guildId, limit, offset uint32) ([]*ReclaimRoleLog,error)
	GetReclaimRoleLogListByPage(ctx context.Context, offset, limit uint32) ([]*ReclaimRoleLog,error)
	GetReclaimRoleLogListByUids(ctx context.Context, uids []uint32, limit, offset uint32) ([]*ReclaimRoleLog,error)
	GetReclaimingUidList(ctx context.Context, status ...uint32) ([]uint32,error)
	InsertApplyLog(ctx context.Context, r *ApplyRecord) error
	InsertChangeRoleLog(ctx context.Context, tx mysql1.Txx, l *ChangeRoleLog) error
	InsertNotifyGuildLog(ctx context.Context, tx mysql1.Txx, info *NotifyGuildLog) error
	InsertOrUpdateESportRoleInfo(ctx context.Context, tx mysql1.Txx, info *ESportCoachInfo) error
	RecallCoachLabel(c context.Context, recordId uint32) error
	ReclaimESportRoleInfo(ctx context.Context, tx mysql1.Txx, uid uint32, t time.Time) (int64,error)
	Transaction(ctx context.Context, f func(tx mysql1.Txx) error) error
	UpdateApplyInfoInTx(ctx context.Context, tx mysql1.Txx, r *ApplyRecord, oldStatus []uint32) (int64,error)
	UpdateApplyStatus(ctx context.Context, applyId, newStatus, oldStatus uint32, remarks string) (int64,error)
	UpdateCoachLabel(c context.Context, recordId uint32, labelStr string, labelType uint32) error
	UpdateNotifyGuildLogStatus(ctx context.Context, id uint32, status, oldStatus uint32) error
	UpdateReclaimRoleLog(ctx context.Context, tx mysql1.Txx, l *ReclaimRoleLog, oldStatus uint32) (int64,error)
	UpdateUserApplyBlacklist(ctx context.Context, uid uint32, endTime time.Time, handler, remarks string) error
}

