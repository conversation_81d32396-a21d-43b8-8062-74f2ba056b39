package store

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/log"
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	"golang.52tt.com/protocol/services/esport_role"
	"os"
	"testing"
)

func getMysqlCfg() *mysqlConnect.MysqlConfig {
	ctx := context.Background()
	configFile := "../../esport-role.json"
	data, err := os.ReadFile(configFile)
	if err != nil {
		log.ErrorWithCtx(ctx, "getMysqlCfg ReadFile err:%v", err)
		panic(err)
	}
	type config struct {
		MysqlCfg *mysqlConnect.MysqlConfig `json:"mysql"`
	}
	cfg := &config{}
	err = json.Unmarshal(data, cfg)
	if err != nil {
		panic(err)
	}
	return cfg.MysqlCfg
}

func getMysqlCli() IStore {
	store, err := NewStore(context.Background(), getMysqlCfg(), getMysqlCfg())
	if err != nil {
		panic(err)
	}
	return store
}

func TestStore_BatchGrantCoachLabel(t *testing.T) {
	cli := getMysqlCli()
	err := cli.BatchGrantCoachLabel(context.Background(), []uint32{2400464, 2465920},
		"https://ga-album-cdnqn.52tt.com/testing-yunying/206f-18d2f21702b.png", 1, 1702548000, 1766851202)
	fmt.Println(err)
}

func TestStore_RecallCoachLabel(t *testing.T) {
	t.Skip()
	cli := getMysqlCli()
	err := cli.RecallCoachLabel(context.Background(), 3)
	fmt.Println(err)
}

func TestStore_UpdateCoachLabel(t *testing.T) {
	t.Skip()
	cli := getMysqlCli()
	err := cli.UpdateCoachLabel(context.Background(), 1, "http://xxx", 2)
	fmt.Println(err)
}

func TestStore_BatchGetCoachLabel(t *testing.T) {
	t.Skip()
	cli := getMysqlCli()
	out, err := cli.BatchGetCoachLabel(ctx, []uint32{2400464, 2465920})
	fmt.Println(out, err)
}

func TestStore_GetCoachLabelRecordByPage(t *testing.T) {
	t.Skip()
	cli := getMysqlCli()
	out, err := cli.GetCoachLabelRecordByPage(context.Background(), 0, esport_role.CoachLabelStatus_COACH_LABEL_STATUS_ALL, 1, 1)
	fmt.Println(out, err)
}

func TestStore_BatchGetUserLabelDuration(t *testing.T) {
	t.Skip()
	cli := getMysqlCli()
	out, err := cli.BatchGetUserLabelDuration(context.Background(), []uint32{2465920, 2628834})
	fmt.Println(out, err)
}

func TestStore_GetCoachLabelByRecordId(t *testing.T) {
	t.Skip()
	cli := getMysqlCli()
	uid, label, err := cli.GetCoachLabelByRecordId(context.Background(), 7)
	fmt.Println(uid, label, err)
}
