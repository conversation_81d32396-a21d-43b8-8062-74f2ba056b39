package mgr

import (
    "context"
    "golang.52tt.com/services/tt-rev/esport/esport-role/internal/store"
    "reflect"
    "testing"
    pb "golang.52tt.com/protocol/services/esport_role"
    "github.com/golang/mock/gomock"
    "golang.52tt.com/clients/account"
    "time"
)

func TestMgr_GetApplyBlacklist(t *testing.T) {
    initRpcCli(t)

    beginTime := time.Now()
    endTime := time.Now().Add(10 * time.Hour)
    type args struct {
        ctx context.Context
        in  *pb.GetApplyBlackListReq
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *pb.GetApplyBlackListResp
        wantErr  bool
    }{
        {
            name: "common by uid list",
            initFunc: func() {
                mockStore.EXPECT().GetApplyBlacklistByUidList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*store.ApplyBlacklist{
                    {
                        Id:         1,
                        AuditToken: "11111",
                        Uid:        uid,
                        BeginTime:  beginTime,
                        EndTime:    endTime,
                        RejectTime: beginTime,
                        UpdateTime: beginTime,
                        OpSource:   1,
                    },
                }, nil)
                AccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(map[uint32]*account.User{}, nil)
                mockStore.EXPECT().GetApplyBlacklistCountByUidList(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil)
            },
            args: args{
                ctx: ctx,
                in: &pb.GetApplyBlackListReq{
                    QueryType: 0,
                    UidList:   []uint32{uid},
                    Page:      1,
                    PageSize:  10,
                },
            },
            want: &pb.GetApplyBlackListResp{
                RecordList: []*pb.ApplyBlackList{
                    {
                        Uid:        uid,
                        BeginTime:  uint32(beginTime.Unix()),
                        EndTime:    uint32(endTime.Unix()),
                        UpdateTime: uint32(beginTime.Unix()),
                    },
                },
                TotalCnt: 1,
            },
            wantErr: false,
        },
        {
            name: "common all",
            initFunc: func() {
                mockStore.EXPECT().GetApplyBlacklistByPage(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*store.ApplyBlacklist{
                    {
                        Id:         1,
                        AuditToken: "11111",
                        Uid:        uid,
                        BeginTime:  beginTime,
                        EndTime:    endTime,
                        RejectTime: beginTime,
                        UpdateTime: beginTime,
                        OpSource:   1,
                    },
                }, nil)
                mockStore.EXPECT().GetApplyBlacklistCount(gomock.Any(), gomock.Any()).Return(uint32(1), nil)
                AccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(map[uint32]*account.User{}, nil)
            },
            args: args{
                ctx: ctx,
                in: &pb.GetApplyBlackListReq{
                    QueryType: 2,
                    Page:      1,
                    PageSize:  10,
                },
            },
            want: &pb.GetApplyBlackListResp{
                RecordList: []*pb.ApplyBlackList{
                    {
                        Uid:        uid,
                        BeginTime:  uint32(beginTime.Unix()),
                        EndTime:    uint32(endTime.Unix()),
                        UpdateTime: uint32(beginTime.Unix()),
                    },
                },
                TotalCnt: 1,
            },
            wantErr: false,
        },
        {
            name: "common wrong search type",
            initFunc: func() {

            },
            args: args{
                ctx: ctx,
                in: &pb.GetApplyBlackListReq{
                    QueryType: 1,
                },
            },
            want:    &pb.GetApplyBlackListResp{},
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := mgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := m.GetApplyBlacklist(tt.args.ctx, tt.args.in)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetApplyBlacklist() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GetApplyBlacklist() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_UpdateApplyBlacklistFreezeTime(t *testing.T) {
    initRpcCli(t)

    beginTime := time.Now()
    endTime := time.Now().Add(10 * time.Hour)
    type args struct {
        ctx context.Context
        in  *pb.UpdateApplyBlacklistFreezeTimeReq
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "common set forever",
            initFunc: func() {
                mockStore.EXPECT().GetApplyBlacklistByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.ApplyBlacklist{
                    Id:         1,
                    AuditToken: "111",
                    Uid:        uid,
                    BeginTime:  beginTime,
                    EndTime:    endTime,
                }, nil)
                mockStore.EXPECT().UpdateUserApplyBlacklist(gomock.Any(), gomock.Any(), time.Unix(AddBlackPermanentTime, 0), gomock.Any(), gomock.Any()).Return(nil)
                mockStore.EXPECT().AddUserBlacklistOpLog(gomock.Any(), gomock.Any()).Return(nil)
            },
            args: args{
                ctx: ctx,
                in: &pb.UpdateApplyBlacklistFreezeTimeReq{
                    Uid:    uid,
                    OpType: 1,
                },
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := mgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            if err := m.UpdateApplyBlacklistFreezeTime(tt.args.ctx, tt.args.in); (err != nil) != tt.wantErr {
                t.Errorf("UpdateApplyBlacklistFreezeTime() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}
