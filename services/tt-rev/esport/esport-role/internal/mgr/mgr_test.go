package mgr

import (
	"context"
	"fmt"
	"github.com/golang/mock/gomock"
	mockaccount "golang.52tt.com/clients/mocks/account"
	mockAnchorcontract "golang.52tt.com/clients/mocks/anchorcontract-go"
	mockApiCenter "golang.52tt.com/clients/mocks/apicenter/apiserver"
	mockGuild "golang.52tt.com/clients/mocks/guild"
	mockImApi "golang.52tt.com/clients/mocks/im-api"
	mockTtcProxy "golang.52tt.com/clients/mocks/ttc-proxy"
	mockUserOl "golang.52tt.com/clients/mocks/user-online"
	anchorcontractpb "golang.52tt.com/protocol/services/anchorcontract-go"
	pb "golang.52tt.com/protocol/services/esport_role"
	clientmocks "golang.52tt.com/protocol/services/mocks"
	"golang.52tt.com/services/account-appeal-http-logic/models"
	"golang.52tt.com/services/tt-rev/esport/esport-role/internal/conf"
	"golang.52tt.com/services/tt-rev/esport/esport-role/internal/mocks"
	"golang.52tt.com/services/tt-rev/esport/esport-role/internal/rpc"
	"golang.52tt.com/services/tt-rev/esport/esport-role/internal/store"
	"net"
	"net/http"
	"testing"
	"time"
)

var rpcCli *rpc.Client
var mockBc *mocks.MockIBusinessConfManager
var mockKfkCoachChange *mocks.MockICoachChangeEventProducer
var mockStore *mocks.MockIStore
var mockCache *mocks.MockICache
var mgr *Mgr

var (
	uid = uint32(2266893)
	ctx = context.Background()

	auditToken  = "test"
	applyType   = uint32(2)
	applyStatus = uint32(8)
	idNum       = "111111200010012222"

	now     = time.Now()
	guildId = uint32(1)

	guildSignTime   = now
	guildSignExpire = now.Add(time.Hour * 24 * 7)

	applyId1 = uint32(1)
	applyId2 = uint32(2)
)

var AccountCli *mockaccount.MockIClient
var ApiClient *mockApiCenter.MockIClient
var ImApiCli *mockImApi.MockIClient
var UserOlCli *mockUserOl.MockIClient
var AnchorContractCli *mockAnchorcontract.MockIClient
var GuildCli *mockGuild.MockIClient
var TtcProxyCli *mockTtcProxy.MockIClient
var ESportStatisticCli *clientmocks.MockEsportStatisticsClient
var ESportHallCli *clientmocks.MockEsportHallClient
var ESportSkillCli *clientmocks.MockEsportSkillClient
var ESportWechatCli *clientmocks.MockEsportWechatClient

func initRpcCli(t *testing.T) {
	ctl := gomock.NewController(t)

	AccountCli = mockaccount.NewMockIClient(ctl)
	ApiClient = mockApiCenter.NewMockIClient(ctl)
	ImApiCli = mockImApi.NewMockIClient(ctl)
	UserOlCli = mockUserOl.NewMockIClient(ctl)
	AnchorContractCli = mockAnchorcontract.NewMockIClient(ctl)
	GuildCli = mockGuild.NewMockIClient(ctl)
	TtcProxyCli = mockTtcProxy.NewMockIClient(ctl)
	ESportStatisticCli = clientmocks.NewMockEsportStatisticsClient(ctl)
	ESportHallCli = clientmocks.NewMockEsportHallClient(ctl)
	ESportSkillCli = clientmocks.NewMockEsportSkillClient(ctl)
	ESportWechatCli = clientmocks.NewMockEsportWechatClient(ctl)

	mockKfkCoachChange = mocks.NewMockICoachChangeEventProducer(ctl)

	httpCli := &http.Client{
		Transport: &http.Transport{
			DialContext: func(ctx context.Context, network, addr string) (conn net.Conn, e error) {
				host, _, err := net.SplitHostPort(addr)
				if err != nil {
					return nil, err
				}
				addrs, err := net.LookupHost(host)
				if err != nil {
					return nil, err
				}
				fmt.Printf("resolved %s to %v\n", host, addrs)
				return net.Dial(network, addr)
			},
			MaxIdleConns:          500,              // 最大空闲连接
			MaxConnsPerHost:       500,              // 每个pod最多多少链接
			IdleConnTimeout:       60 * time.Second, // 空闲连接的超时时间
			ExpectContinueTimeout: 10 * time.Second, // 等待服务第一个响应的超时时间
			MaxIdleConnsPerHost:   100,              // 每个host保持的空闲连接数
		},
	}

	rpcCli = &rpc.Client{
		AccountCli:         AccountCli,
		ApiClient:          ApiClient,
		ImApiCli:           ImApiCli,
		UserOlCli:          UserOlCli,
		AnchorContractCli:  AnchorContractCli,
		GuildCli:           GuildCli,
		TtcProxyCli:        TtcProxyCli,
		ESportStatisticCli: ESportStatisticCli,
		ESportHallCli:      ESportHallCli,
		ESportSkillCli:     ESportSkillCli,
		EsportWechatClint:  ESportWechatCli,
		TBeanCli:           models.InitTBean("http://tbean-service.quicksilver.svc/portal/", "123456", "tt_server"),
	}

	mockBc = mocks.NewMockIBusinessConfManager(ctl)
	mockStore = mocks.NewMockIStore(ctl)
	mockCache = mocks.NewMockICache(ctl)

	mgr = &Mgr{
		shutDown: make(chan interface{}),
		bc:       mockBc,
		rpcCli:   rpcCli,
		httpCli:  httpCli,

		cache: mockCache,
		store: mockStore,

		coachChangeEventKfk: mockKfkCoachChange,
	}
}

/*
mockgen.exe -destination=mocks/mock_cache.go -package=mocks golang.52tt.com/services/tt-rev/esport/esport-role/internal/cache ICache
mockgen.exe -destination=mocks/mock_store.go -package=mocks golang.52tt.com/services/tt-rev/esport/esport-role/internal/store IStore
mockgen.exe -destination=mocks/mock_conf.go -package=mocks golang.52tt.com/services/tt-rev/esport/esport-role/internal/conf IBusinessConfManager
mockgen.exe -destination=mocks/skill_service.go -package=mocks golang.52tt.com/protocol/services/esport-skill EsportSkillClient
mockgen.exe -destination=mocks/statist_service.go -package=mocks golang.52tt.com/protocol/services/esport-statistics EsportStatisticsClient
mockgen.exe -destination=mocks/hall_service.go -package=mocks golang.52tt.com/protocol/services/esport_hall EsportHallClient
*/

func TestCountdown(t *testing.T) {
	t.Logf("Countdown:%v", Countdown(time.Now().Add(24*time.Hour)))
	t.Logf("%v", time.Unix(0, 0))
}

func TestDateFormat(t *testing.T) {
	timeNow := time.Now()
	content := fmt.Sprintf("您已成功和公会【%s】(%d)签约,签约期限至%s。签约期间请严格遵守签约协议,一旦违规签约账号将受到处罚,详情请查看签约须知。",
		"name", 111, timeNow.Format("2006年1月2日"))
	t.Logf("content:%s", content)
	t.Logf(timeNow.Format("2006年1月2日"))
}

func TestMgr_ApplyRoleMgr(t *testing.T) {
	initRpcCli(t)

	nilApplyLogs := make([]*store.ApplyRecord, 0)

	type args struct {
		ctx context.Context

		in *pb.ApplyESportRequset
	}
	tests := []struct {
		name     string
		initFunc func()
		args     args
		wantErr  bool
	}{
		{
			name: "TestMgr_ApplyRoleMgr common",
			initFunc: func() {
				mockStore.EXPECT().GetApplyBlacklistByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.ApplyBlacklist{}, nil)
				mockStore.EXPECT().GetESportRoleInfoByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.ESportCoachInfo{}, nil)
				mockBc.EXPECT().IsInIdNumLimitWhiteList(gomock.Any()).Return(false)
				mockStore.EXPECT().GetESportRoleInfoByIdentifyNum(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.ESportCoachInfo{}, nil)
				mockStore.EXPECT().GetApplyRecordWithUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(nilApplyLogs, nil)
				mockStore.EXPECT().GetApplyRecordWithIdNum(gomock.Any(), gomock.Any(), gomock.Any()).Return(nilApplyLogs, nil)
				mockBc.EXPECT().GetUserTagIopCfg().Return(&conf.UserTagIopCfg{
					ServerHost:   "http://dap-yuntest.ttyuyin.com:8040",
					ClientSecret: "123456",
					ClientCaller: "tt-aq",
					MaxCount:     100,
				}, nil)
				TtcProxyCli.EXPECT().GetTheSameRealNameUserList(gomock.Any(), gomock.Any())
				mockStore.EXPECT().InsertApplyLog(gomock.Any(), gomock.Any()).Return(nil)
				ImApiCli.EXPECT().SendPublicAccountText(gomock.Any(), gomock.Any()).AnyTimes().Return(nil, nil)

			},
			args: args{
				ctx: ctx,
				in: &pb.ApplyESportRequset{
					AuditToken:   auditToken,
					Uid:          uid,
					CurRole:      0,
					ApplyType:    applyType,
					IdentifyId:   idNum,
					ApplyTime:    uint32(now.Unix()),
					GuildId:      guildId,
					SignDuration: 36,
				},
			},
			wantErr: false,
		},
		{
			name: "TestMgr_ApplyRoleMgr in blacklist,forever",
			initFunc: func() {
				mockStore.EXPECT().GetApplyBlacklistByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.ApplyBlacklist{
					Id:      1,
					Uid:     uid,
					EndTime: time.Unix(int64(**********), 0),
				}, nil)
			},
			args: args{
				ctx: ctx,
				in: &pb.ApplyESportRequset{
					AuditToken:   auditToken,
					Uid:          uid,
					CurRole:      0,
					ApplyType:    applyType,
					IdentifyId:   idNum,
					ApplyTime:    uint32(now.Unix()),
					GuildId:      guildId,
					SignDuration: 36,
				},
			},
			wantErr: true,
		},
		{
			name: "TestMgr_ApplyRoleMgr is already target role",
			initFunc: func() {
				mockStore.EXPECT().GetApplyBlacklistByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.ApplyBlacklist{}, nil)
				mockStore.EXPECT().GetESportRoleInfoByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.ESportCoachInfo{
					Id:         1,
					Uid:        uid,
					ESportType: 2,
				}, nil)
			},
			args: args{
				ctx: ctx,
				in: &pb.ApplyESportRequset{
					AuditToken:   auditToken,
					Uid:          uid,
					CurRole:      0,
					ApplyType:    applyType,
					IdentifyId:   idNum,
					ApplyTime:    uint32(now.Unix()),
					GuildId:      guildId,
					SignDuration: 36,
				},
			},
			wantErr: true,
		},
		{
			name: "TestMgr_ApplyRoleMgr real name limit",
			initFunc: func() {
				mockStore.EXPECT().GetApplyBlacklistByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.ApplyBlacklist{}, nil)
				mockStore.EXPECT().GetESportRoleInfoByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.ESportCoachInfo{}, nil)
				mockBc.EXPECT().IsInIdNumLimitWhiteList(gomock.Any()).Return(false)
				mockStore.EXPECT().GetESportRoleInfoByIdentifyNum(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.ESportCoachInfo{
					Id:  1,
					Uid: uid + 1,
				}, nil)
			},
			args: args{
				ctx: ctx,
				in: &pb.ApplyESportRequset{
					AuditToken:   auditToken,
					Uid:          uid,
					CurRole:      0,
					ApplyType:    applyType,
					IdentifyId:   idNum,
					ApplyTime:    uint32(now.Unix()),
					GuildId:      guildId,
					SignDuration: 36,
				},
			},
			wantErr: true,
		},
		{
			name: "TestMgr_ApplyRoleMgr same uid already apply",
			initFunc: func() {
				mockStore.EXPECT().GetApplyBlacklistByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.ApplyBlacklist{}, nil)
				mockStore.EXPECT().GetESportRoleInfoByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.ESportCoachInfo{}, nil)
				mockBc.EXPECT().IsInIdNumLimitWhiteList(gomock.Any()).Return(false)
				mockStore.EXPECT().GetESportRoleInfoByIdentifyNum(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.ESportCoachInfo{}, nil)
				mockStore.EXPECT().GetApplyRecordWithUid(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*store.ApplyRecord{&store.ApplyRecord{
					Id:  1,
					Uid: uid,
				}}, nil)
			},
			args: args{
				ctx: ctx,
				in: &pb.ApplyESportRequset{
					AuditToken:   auditToken,
					Uid:          uid,
					CurRole:      0,
					ApplyType:    applyType,
					IdentifyId:   idNum,
					ApplyTime:    uint32(now.Unix()),
					GuildId:      guildId,
					SignDuration: 36,
				},
			},
			wantErr: true,
		},
		{
			name: "TestMgr_ApplyRoleMgr same real name apply",
			initFunc: func() {
				mockStore.EXPECT().GetApplyBlacklistByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.ApplyBlacklist{}, nil)
				mockStore.EXPECT().GetESportRoleInfoByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.ESportCoachInfo{}, nil)
				mockBc.EXPECT().IsInIdNumLimitWhiteList(gomock.Any()).Return(false)
				mockStore.EXPECT().GetESportRoleInfoByIdentifyNum(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.ESportCoachInfo{}, nil)
				mockStore.EXPECT().GetApplyRecordWithUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(nilApplyLogs, nil)
				mockStore.EXPECT().GetApplyRecordWithIdNum(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*store.ApplyRecord{&store.ApplyRecord{
					Id:  1,
					Uid: uid,
				}}, nil)
			},
			args: args{ctx: ctx,
				in: &pb.ApplyESportRequset{
					AuditToken:   auditToken,
					Uid:          uid,
					CurRole:      0,
					ApplyType:    applyType,
					IdentifyId:   idNum,
					ApplyTime:    uint32(now.Unix()),
					GuildId:      guildId,
					SignDuration: 36,
				}},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := mgr
			if tt.initFunc != nil {
				tt.initFunc()
			}
			if err := m.ApplyRoleMgr(tt.args.ctx, tt.args.in); (err != nil) != tt.wantErr {
				t.Errorf("ApplyRoleMgr() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

//func TestMgr_GuildSignResultHandle(t *testing.T) {
//	type fields struct {
//		shutDown chan interface{}
//		wg       sync.WaitGroup
//		bc       conf.IBusinessConfManager
//		rpcCli   *rpc.Client
//		cache    cache.ICache
//		store    store.IStore
//		timerD   *timer.Timer
//	}
//	type args struct {
//		ctx context.Context
//		in  *pb.ESportGuildAuditResultReq
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			m := &Mgr{
//				shutDown: tt.fields.shutDown,
//				wg:       tt.fields.wg,
//				bc:       tt.fields.bc,
//				rpcCli:   tt.fields.rpcCli,
//				cache:    tt.fields.cache,
//				store:    tt.fields.store,
//				timerD:   tt.fields.timerD,
//			}
//			if err := m.GuildSignResultHandle(tt.args.ctx, tt.args.in); (err != nil) != tt.wantErr {
//				t.Errorf("GuildSignResultHandle() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}

func TestMgr_PlatformSignResultHandle(t *testing.T) {

	type args struct {
		ctx context.Context
		in  *pb.OfficialHandleApplyESportReq
	}
	tests := []struct {
		name     string
		initFunc func()
		args     args
		wantErr  bool
	}{
		{
			name: "TestMgr_PlatformSignResultHandle common pass",
			initFunc: func() {
				mockStore.EXPECT().GetApplyRecordByIdList(gomock.Any(), gomock.Any()).Return([]*store.ApplyRecord{
					{
						Id:          applyId1,
						AuditToken:  auditToken,
						Uid:         uid,
						GuildId:     guildId,
						ApplyType:   3,
						ApplyStatus: AuditStatusWaitForPlatform,
					},
				}, nil)
				AnchorContractCli.EXPECT().GetUserContractCacheInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&anchorcontractpb.ContractCacheInfo{}, nil)
				mockStore.EXPECT().GetESportRoleInfoByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.ESportCoachInfo{
					Id:         1,
					Uid:        uid,
					ESportType: 1,
				}, nil)
				mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()).Return(nil)
				//mockStore.EXPECT().UpdateApplyInfoInTx(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				//mockStore.EXPECT().InsertOrUpdateESportRoleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				//mockStore.EXPECT().InsertChangeRoleLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				ImApiCli.EXPECT().SendPublicAccountText(gomock.Any(), gomock.Any()).AnyTimes().Return(nil, nil)
				mockCache.EXPECT().DelUserRoleInfo(gomock.Any(), gomock.Any()).Return(nil)

				mockKfkCoachChange.EXPECT().SendMsg(gomock.Any(), gomock.Any()).Return()

				ESportSkillCli.EXPECT().SetUserSkillAuditType(gomock.Any(), gomock.Any()).Return(nil, nil)
				ESportWechatCli.EXPECT().RoleStatusChange(gomock.Any(), gomock.Any()).AnyTimes().Return(nil, nil)

				time.Sleep(1 * time.Second)
			},
			args: args{
				ctx: ctx,
				in: &pb.OfficialHandleApplyESportReq{
					ApplyId:   []uint32{applyId1},
					AuditType: 8,
					Operator:  "sys",
				},
			},
			wantErr: false,
		},
		{
			name: "TestMgr_PlatformSignResultHandle guild was remove",
			initFunc: func() {
				mockStore.EXPECT().GetApplyRecordByIdList(gomock.Any(), gomock.Any()).Return([]*store.ApplyRecord{
					{
						Id:          applyId1,
						Remarks:     "因工会被移出合作库而终止",
						ApplyStatus: AuditStatusPlatformReject,
					},
				}, nil)
			},
			args: args{
				ctx: ctx,
				in: &pb.OfficialHandleApplyESportReq{
					ApplyId: []uint32{applyId1},
				},
			},
			wantErr: false,
		},
		{
			name: "TestMgr_PlatformSignResultHandle cannot find record",
			initFunc: func() {
				mockStore.EXPECT().GetApplyRecordByIdList(gomock.Any(), gomock.Any()).Return([]*store.ApplyRecord{
					{
						Id: applyId1,
					},
				}, nil)
			},
			args: args{
				ctx: ctx,
				in: &pb.OfficialHandleApplyESportReq{
					ApplyId: []uint32{applyId1},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		initRpcCli(t)
		t.Run(tt.name, func(t *testing.T) {
			m := mgr
			if tt.initFunc != nil {
				tt.initFunc()
			}
			if err := m.PlatformSignResultHandle(tt.args.ctx, tt.args.in); (err != nil) != tt.wantErr {
				t.Errorf("PlatformSignResultHandle() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

//
//func TestMgr_RiskResultHandle(t *testing.T) {
//	type fields struct {
//		shutDown chan interface{}
//		wg       sync.WaitGroup
//		bc       conf.IBusinessConfManager
//		rpcCli   *rpc.Client
//		cache    cache.ICache
//		store    store.IStore
//		timerD   *timer.Timer
//	}
//	type args struct {
//		ctx context.Context
//		in  *pb.ESportRiskAuditResultReq
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			m := &Mgr{
//				shutDown: tt.fields.shutDown,
//				wg:       tt.fields.wg,
//				bc:       tt.fields.bc,
//				rpcCli:   tt.fields.rpcCli,
//				cache:    tt.fields.cache,
//				store:    tt.fields.store,
//				timerD:   tt.fields.timerD,
//			}
//			if err := m.RiskResultHandle(tt.args.ctx, tt.args.in); (err != nil) != tt.wantErr {
//				t.Errorf("RiskResultHandle() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}
//
//func TestMgr_adjustApplyStatus(t *testing.T) {
//	type fields struct {
//		shutDown chan interface{}
//		wg       sync.WaitGroup
//		bc       conf.IBusinessConfManager
//		rpcCli   *rpc.Client
//		cache    cache.ICache
//		store    store.IStore
//		timerD   *timer.Timer
//	}
//	tests := []struct {
//		name   string
//		fields fields
//	}{
//		// TODO: Add test cases.
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			m := &Mgr{
//				shutDown: tt.fields.shutDown,
//				wg:       tt.fields.wg,
//				bc:       tt.fields.bc,
//				rpcCli:   tt.fields.rpcCli,
//				cache:    tt.fields.cache,
//				store:    tt.fields.store,
//				timerD:   tt.fields.timerD,
//			}
//			m.adjustApplyStatus()
//		})
//	}
//}

//func TestMgr_applyBlackListCheck(t *testing.T) {
//	type fields struct {
//		shutDown chan interface{}
//		wg       sync.WaitGroup
//		bc       conf.IBusinessConfManager
//		rpcCli   *rpc.Client
//		cache    cache.ICache
//		store    store.IStore
//		timerD   *timer.Timer
//	}
//	type args struct {
//		ctx context.Context
//		uid uint32
//		now time.Time
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			m := &Mgr{
//				shutDown: tt.fields.shutDown,
//				wg:       tt.fields.wg,
//				bc:       tt.fields.bc,
//				rpcCli:   tt.fields.rpcCli,
//				cache:    tt.fields.cache,
//				store:    tt.fields.store,
//				timerD:   tt.fields.timerD,
//			}
//			if err := m.applyBlackListCheck(tt.args.ctx, tt.args.uid, tt.args.now); (err != nil) != tt.wantErr {
//				t.Errorf("applyBlackListCheck() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}
//
//func TestMgr_applyCurRoleCheck(t *testing.T) {
//	type fields struct {
//		shutDown chan interface{}
//		wg       sync.WaitGroup
//		bc       conf.IBusinessConfManager
//		rpcCli   *rpc.Client
//		cache    cache.ICache
//		store    store.IStore
//		timerD   *timer.Timer
//	}
//	type args struct {
//		ctx       context.Context
//		uid       uint32
//		applyType uint32
//	}
//	tests := []struct {
//		name        string
//		fields      fields
//		args        args
//		wantCurRole uint32
//		wantErr     bool
//	}{
//		// TODO: Add test cases.
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			m := &Mgr{
//				shutDown: tt.fields.shutDown,
//				wg:       tt.fields.wg,
//				bc:       tt.fields.bc,
//				rpcCli:   tt.fields.rpcCli,
//				cache:    tt.fields.cache,
//				store:    tt.fields.store,
//				timerD:   tt.fields.timerD,
//			}
//			gotCurRole, err := m.applyCurRoleCheck(tt.args.ctx, tt.args.uid, tt.args.applyType)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("applyCurRoleCheck() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if gotCurRole != tt.wantCurRole {
//				t.Errorf("applyCurRoleCheck() gotCurRole = %v, want %v", gotCurRole, tt.wantCurRole)
//			}
//		})
//	}
//}
//
//func TestMgr_platformPassHandle(t *testing.T) {
//	type fields struct {
//		shutDown chan interface{}
//		wg       sync.WaitGroup
//		bc       conf.IBusinessConfManager
//		rpcCli   *rpc.Client
//		cache    cache.ICache
//		store    store.IStore
//		timerD   *timer.Timer
//	}
//	type args struct {
//		ctx         context.Context
//		applyRecord *store.ApplyRecord
//		in          *pb.OfficialHandleApplyESportReq
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *store.ApplyRecord
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			m := &Mgr{
//				shutDown: tt.fields.shutDown,
//				wg:       tt.fields.wg,
//				bc:       tt.fields.bc,
//				rpcCli:   tt.fields.rpcCli,
//				cache:    tt.fields.cache,
//				store:    tt.fields.store,
//				timerD:   tt.fields.timerD,
//			}
//			got, err := m.platformPassHandle(tt.args.ctx, tt.args.applyRecord, tt.args.in)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("platformPassHandle() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("platformPassHandle() got = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
//func TestMgr_platformRejectHandle(t *testing.T) {
//	type fields struct {
//		shutDown chan interface{}
//		wg       sync.WaitGroup
//		bc       conf.IBusinessConfManager
//		rpcCli   *rpc.Client
//		cache    cache.ICache
//		store    store.IStore
//		timerD   *timer.Timer
//	}
//	type args struct {
//		ctx         context.Context
//		applyRecord *store.ApplyRecord
//		in          *pb.OfficialHandleApplyESportReq
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *store.ApplyRecord
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			m := &Mgr{
//				shutDown: tt.fields.shutDown,
//				wg:       tt.fields.wg,
//				bc:       tt.fields.bc,
//				rpcCli:   tt.fields.rpcCli,
//				cache:    tt.fields.cache,
//				store:    tt.fields.store,
//				timerD:   tt.fields.timerD,
//			}
//			got, err := m.platformRejectHandle(tt.args.ctx, tt.args.applyRecord, tt.args.in)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("platformRejectHandle() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("platformRejectHandle() got = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
//func Test_platformHandleTypeToGuildType(t *testing.T) {
//	type args struct {
//		handleType uint32
//	}
//	tests := []struct {
//		name string
//		args args
//		want uint32
//	}{
//		// TODO: Add test cases.
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			if got := platformHandleTypeToGuildType(tt.args.handleType); got != tt.want {
//				t.Errorf("platformHandleTypeToGuildType() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
