package recall_source_rule

import (
    "context"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/entity"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/local_cache"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/store"
)

type CoachPriceRule struct {
    localCache *local_cache.LocalCache
}

func NewCoachPriceRule(lc *local_cache.LocalCache) *CoachPriceRule {
    return &CoachPriceRule{
        localCache: lc,
    }
}

func (r *CoachPriceRule) Recall(ctx context.Context, ruleParam *store.RecallSourceRuleSettingCondition) ([]*entity.SimpleSkillProduct, error) {
    rs := make([]*entity.SimpleSkillProduct, 0)
    for _, sp := range r.localCache.GetEnableSkillProduct() {
        if RuleParam(*ruleParam).isValidNumber(float64(r.localCache.GetCoachGamePrice(sp.SkillId, sp.Uid))) {
            rs = append(rs, sp)
        }
    }
    return rs, nil
}
