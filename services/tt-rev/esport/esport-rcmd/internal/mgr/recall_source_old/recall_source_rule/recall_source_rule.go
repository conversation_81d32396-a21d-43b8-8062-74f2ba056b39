package recall_source_rule

import (
    "context"
    pb "golang.52tt.com/protocol/services/esport_rcmd"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/entity"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/store"
    "strconv"
)

// RecallFunc 函数类型规则
type RecallFunc func(ctx context.Context, ruleParam *store.RecallSourceRuleSettingCondition) ([]*entity.SimpleSkillProduct, error)

func (r RecallFunc) Recall(ctx context.Context, ruleParam *store.RecallSourceRuleSettingCondition) ([]*entity.SimpleSkillProduct, error) {
    return r(ctx, ruleParam)
}

// RecallSourceRule 对象类型规则
type RecallSourceRule interface {
    Recall(ctx context.Context, ruleParam *store.RecallSourceRuleSettingCondition) ([]*entity.SimpleSkillProduct, error)
}

// RuleParam 增强型参数
type RuleParam store.RecallSourceRuleSettingCondition

const (
    RecallSourceRuleBoolOperatorValTrue  = "是"
    RecallSourceRuleBoolOperatorValFalse = "否"
)

func (r RuleParam) isTrue() bool {
    return r.Value == RecallSourceRuleBoolOperatorValTrue
}

func (r RuleParam) strCompare(val string) bool {
    if r.OperatorType == uint32(pb.RecallSourceRuleOperatorType_RECALL_SOURCE_RULE_OPERATOR_TYPE_EQ) {
        return r.Value == val
    }
    return r.Value != val
}

func (r RuleParam) isValidNumber(num float64) bool {
    targetVal, err := strconv.ParseFloat(r.Value, 64)
    if err != nil {
        return false
    }
    switch r.OperatorType {
    case uint32(pb.RecallSourceRuleOperatorType_RECALL_SOURCE_RULE_OPERATOR_TYPE_EQ):
        return num == targetVal
    case uint32(pb.RecallSourceRuleOperatorType_RECALL_SOURCE_RULE_OPERATOR_TYPE_GT):
        return num > targetVal
    case uint32(pb.RecallSourceRuleOperatorType_RECALL_SOURCE_RULE_OPERATOR_TYPE_GTE):
        return num >= targetVal
    case uint32(pb.RecallSourceRuleOperatorType_RECALL_SOURCE_RULE_OPERATOR_TYPE_LT):
        return num < targetVal
    case uint32(pb.RecallSourceRuleOperatorType_RECALL_SOURCE_RULE_OPERATOR_TYPE_LTE):
        return num <= targetVal
    default:
        return false
    }
}


