package recall_source

import (
    "context"
    "fmt"
    "golang.52tt.com/pkg/log"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    pbApp "golang.52tt.com/protocol/app"
    pb "golang.52tt.com/protocol/services/esport_rcmd"
    "golang.52tt.com/services/tt-rev/esport/common/collection/mapz"
    "golang.52tt.com/services/tt-rev/esport/common/collection/transform"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/conf"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/entity"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/store"
    "sort"
    "sync"
)

type SortableSkillProduct struct {
    *entity.SimpleSkillProduct
    Score                float64
    IsQuickReceive       int // 是否秒接单
    IsFirstRound         int // 是否首单
    IsDiffSex            int // 是否异性
    IsOnline             int // 是否在线
    IsExposeGradingLimit int // 是否曝光分级限制
    IsNewCustomerPrice   int // 是否新客价
}

type SortOption func(a, b *SortableSkillProduct) (bool, bool) // 返回参数, 1.规则是否生效, 2. 比较结果

var msp = map[uint32]SortOption{
    uint32(pb.MultilevelSortType_MULTILEVEL_SORT_TYPE_SOCRE): ScorePerFer,
    uint32(pb.MultilevelSortType_MULTILEVEL_SORT_TYPE_QUICK_RECEIVE): QuickReceivePerFer,
    uint32(pb.MultilevelSortType_MULTILEVEL_SORT_TYPE_FIRST_ROUND): FirstRoundPerFer,
    uint32(pb.MultilevelSortType_MULTILEVEL_SORT_TYPE_DIFF_SEX): DiffSexPerFer,
    uint32(pb.MultilevelSortType_MULTILEVEL_SORT_TYPE_ONLINE_STATUS): OnlinePerFer,
    uint32(pb.MultilevelSortType_MULTILEVEL_SORT_TYPE_CONVERSION_RATE): ConvRateReachPerFer,
    uint32(pb.MultilevelSortType_MULTILEVEL_SORT_TYPE_IS_NEW_CUSTOMER_PRICE): NewCustomerPricePerFer,
}

func QuickReceivePerFer(a, b *SortableSkillProduct) (bool, bool) {
    if a.IsQuickReceive != b.IsQuickReceive {
        return true, a.IsQuickReceive > b.IsQuickReceive
    }
    return false, false
}

func FirstRoundPerFer(a, b *SortableSkillProduct) (bool, bool) {
    if a.IsFirstRound != b.IsFirstRound {
        return true, a.IsFirstRound > b.IsFirstRound
    }
    return false, false
}

func DiffSexPerFer(a, b *SortableSkillProduct) (bool, bool) {
    if a.IsDiffSex != b.IsDiffSex {
        return true, a.IsDiffSex > b.IsDiffSex
    }
    return false, false
}

func OnlinePerFer(a, b *SortableSkillProduct) (bool, bool) {
    if a.IsOnline != b.IsOnline {
        return true, a.IsOnline > b.IsOnline
    }
    return false, false
}

func ConvRateReachPerFer(a, b *SortableSkillProduct) (bool, bool) {
    if a.IsExposeGradingLimit != b.IsExposeGradingLimit {
        return true, a.IsExposeGradingLimit < b.IsExposeGradingLimit
    }
    return false, false
}

func NewCustomerPricePerFer(a, b *SortableSkillProduct) (bool, bool) {
    if a.IsNewCustomerPrice != b.IsNewCustomerPrice {
        return true, a.IsNewCustomerPrice > b.IsNewCustomerPrice
    }
    return false, false
}

func ScorePerFer(a, b *SortableSkillProduct) (bool, bool) {
    return true, a.Score > b.Score
}

func (r *RecallSource) Sort(ctx context.Context, recallSourceId uint32, data []*entity.SimpleSkillProduct, queueRuleTypeList []uint32,
    scoreParamList []*store.SortParameterTableSortParameter) ([]*entity.SimpleSkillProduct, error) {
    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        return data, fmt.Errorf("get opUid from context failed")
    }
    sortableSkillProduct := make([]*SortableSkillProduct, 0, len(data))
    for _, item := range data {
        sortableSkillProduct = append(sortableSkillProduct, &SortableSkillProduct{
            SimpleSkillProduct: item,
        })
    }

    // 异步打分
    cntScoreWg := sync.WaitGroup{}
    cntScoreWg.Add(1)
    go func() {
      defer cntScoreWg.Done()
      if err := r.countRcmdItemScore(ctx, sortableSkillProduct, len(scoreParamList) == 0, scoreParamList); err != nil {
          log.ErrorWithCtx(ctx, "countRcmdItemScore error: %v", err)
      }
    }()

    queueRuleTypeList = append(queueRuleTypeList, uint32(pb.MultilevelSortType_MULTILEVEL_SORT_TYPE_SOCRE)) // 固有的分数排序, 硬编码补上
    multilevelSortTypeMap := make(map[uint32]bool, len(queueRuleTypeList))
    for _, sortType := range queueRuleTypeList {
        multilevelSortTypeMap[sortType] = true
    }

    opUserInfo, err := r.rpcCli.UserProfileCli.GetUserProfileV2(ctx, serviceInfo.UserID, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserProfileV2, uid: %d, error: %v", opUserInfo.GetUid(), err)
        opUserInfo = &pbApp.UserProfile{
            Sex: 1, // 默认男性
        }
    }

    for _, item := range sortableSkillProduct {
        // 标记秒接单
        if multilevelSortTypeMap[uint32(pb.MultilevelSortType_MULTILEVEL_SORT_TYPE_QUICK_RECEIVE)] && r.localCache.IsQuickReceiveUser(item.Uid) {
            item.IsQuickReceive = 1
        }
        // 标记首单
        if multilevelSortTypeMap[uint32(pb.MultilevelSortType_MULTILEVEL_SORT_TYPE_FIRST_ROUND)] && r.localCache.IsFirstRoundCoach(item.SkillId, item.Uid) {
            item.IsFirstRound = 1
        }
        // 标记异性
        if multilevelSortTypeMap[uint32(pb.MultilevelSortType_MULTILEVEL_SORT_TYPE_DIFF_SEX)] {
           if opUserInfo.GetSex() != r.localCache.GetUserSex(item.Uid) {
                item.IsDiffSex = 1
           }
        }
        // 标记在线
        if multilevelSortTypeMap[uint32(pb.MultilevelSortType_MULTILEVEL_SORT_TYPE_ONLINE_STATUS)] && r.localCache.IsOnlineUser(item.Uid) {
            item.IsOnline = 1
        }
        // 标记新客价
        if multilevelSortTypeMap[uint32(pb.MultilevelSortType_MULTILEVEL_SORT_TYPE_IS_NEW_CUSTOMER_PRICE)] && r.localCache.IsNewCustomer(item.SkillId, item.Uid) {
            item.IsNewCustomerPrice = 1
        }
    }

    // 标记转化率达标
    if multilevelSortTypeMap[uint32(pb.MultilevelSortType_MULTILEVEL_SORT_TYPE_CONVERSION_RATE)] {
        if err := r.markConvRateReach(ctx, sortableSkillProduct); err != nil {
            log.ErrorWithCtx(ctx, "markConvRateReach error: %v", err)
            return data, err
        }
    }

    // 等待打分完成
    cntScoreWg.Wait()

    sort.Slice(sortableSkillProduct, func(i, j int) bool {
        for _, sortType := range queueRuleTypeList {
            if fn, ok := msp[sortType]; ok {
                if ok, res := fn(sortableSkillProduct[i], sortableSkillProduct[j]); ok {
                    return res
                }
            }
        }
        return false
    })

    // 调试log
    //svrInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
    //for _, item := range sortableSkillProduct {
    //    log.InfoWithCtx(ctx, "GetRcmdSkillProduct, opUid: %d, recallSourceId: %d, item: %+v, Score: %f, IsQuickReceive: %d, IsFirstRound: %d, IsDiffSex: %d, IsOnline: %d, IsExposeGradingLimit: %d",
    //        svrInfo.UserID, recallSourceId, item.SimpleSkillProduct, item.Score, item.IsQuickReceive, item.IsFirstRound, item.IsDiffSex, item.IsOnline, item.IsExposeGradingLimit)
    //}

    tmpData := make([]*entity.SimpleSkillProduct, 0, len(sortableSkillProduct))
    for _, item := range sortableSkillProduct {
        tmpData = append(tmpData, item.SimpleSkillProduct)
    }
    data = tmpData


    return data, nil
}

func (r *RecallSource) markConvRateReach(ctx context.Context, data []*SortableSkillProduct) error {
    coachUidList := transform.Map(data, func(item *SortableSkillProduct) uint32 {
        return item.Uid
    })

    hourExposeCntMap, err := r.cache.BatchGetCoachExposeGradingCount(ctx, coachUidList, 3600)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetCoachExposeGradingCount error: %v", err)
        return err
    }

    dayExposeCntMap, err := r.cache.BatchGetCoachExposeGradingCount(ctx, coachUidList, 86400)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetCoachExposeGradingCount error: %v", err)
        return err
    }


    // 不配置曝光分级策略, 直接返回. 注意!!! 配置要带所有字段, 后续逻辑不做空值判断
    if r.bc.GetConfig().ExposeGradingConf == nil {
        log.WarnWithCtx(ctx, "ExposeGradingConf is nil")
        return nil
    }

    godLvLimitMap := mapz.ToMap(r.bc.GetConfig().ExposeGradingConf.GodLevelConf, func(item *conf.ExposeGradingGodLevelConf) uint32 {
        return item.LV
    })


    for _, item := range data {
        // 计算当前大神对应大神等级/知名选手的曝光配额总和
        hourLimit := uint32(0)
        dayLimit := uint32(0)
        godLv := r.localCache.GetGodLevel(item.Uid)
        isFamous := r.localCache.IsFamousPlayer(item.SkillId, item.Uid)
        if godLvLimitMap[godLv] != nil && r.localCache.GetCoachConversionRateRanking(item.Uid) <= godLvLimitMap[godLv].ConversionRateRequire {
            hourLimit += godLvLimitMap[godLv].HourExposeLimit
            dayLimit += godLvLimitMap[godLv].DayExposeLimit
        }
        if isFamous {
            hourLimit += r.bc.GetConfig().ExposeGradingConf.FamousPlayerConf.HourExposeLimit
            dayLimit += r.bc.GetConfig().ExposeGradingConf.FamousPlayerConf.DayExposeLimit
        }

        log.DebugWithCtx(ctx, "GetGameCoachList, uid: %d, godLv: %d, isFamous: %v, hourLimit: %d, dayLimit: %d", item.Uid, godLv, isFamous, hourLimit, dayLimit)
        if hourExposeCntMap[item.Uid] >= hourLimit ||
            dayExposeCntMap[item.Uid] >= dayLimit {
            item.IsExposeGradingLimit = 1
        }
    }
    return nil
}

