package rule

import (
    "context"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/entity"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/local_cache"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/store"
)

type CoachTypeRule struct {
    localCache *local_cache.LocalCache
    coachType  uint32
}

func NewCoachTypeRule(localCache *local_cache.LocalCache, ct uint32) *CoachTypeRule {
    return &CoachTypeRule{
        localCache: localCache,
        coachType:  ct,
    }
}

func (r *CoachTypeRule) Recall(ctx context.Context, ruleParam *store.RecallSourceRuleSettingCondition) ([]*entity.SimpleSkillProduct, error) {
    rs := make([]*entity.SimpleSkillProduct, 0)

    for _, item := range r.localCache.GetEnableSkillProduct() {
        if RuleParam(*ruleParam).isTrue() && r.localCache.GetCoachRole(item.Uid) == r.coachType {
            rs = append(rs, item)
            continue
        }
        if !RuleParam(*ruleParam).isTrue() && r.localCache.GetCoachRole(item.Uid) != r.coachType {
            rs = append(rs, item)
        }
    }

    return rs, nil
}
