package rule

import (
    "context"
    "fmt"
    "golang.52tt.com/pkg/log"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/entity"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/local_cache"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/store"
)

type FirstRoundUsableRule struct {
    store store.IStore
    localCache *local_cache.LocalCache
}

func NewFirstRoundUsableRule(store store.IStore, localCache *local_cache.LocalCache) *FirstRoundUsableRule {
    return &FirstRoundUsableRule{
        store: store,
        localCache: localCache,
    }
}

func (r *FirstRoundUsableRule) Recall(ctx context.Context, ruleParam *store.RecallSourceRuleSettingCondition) ([]*entity.SimpleSkillProduct, error) {
    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "get service info from context failed")
        return nil, fmt.Errorf("get service info from context failed")
    }
    firstOrderRecord, err := r.store.GetFirstRoundOrderRecordByUid(ctx, svrInfo.UserID)
    if err != nil {
        log.ErrorWithCtx(ctx, "get first round order record by uid failed, err: %v", err)
        return nil, err
    }

    recordMap := make(map[uint32]map[uint32]bool)
    for _, record := range firstOrderRecord {
        if _, ok := recordMap[record.GameId]; !ok {
            recordMap[record.GameId] = make(map[uint32]bool)
        }
        recordMap[record.GameId][record.CoachUid] = true
    }

    rs := make([]*entity.SimpleSkillProduct, 0)
    for _, sp := range r.localCache.GetEnableSkillProduct() {
        if RuleParam(*ruleParam).isTrue() && r.localCache.IsFirstRoundCoach(sp.SkillId, sp.Uid) && !recordMap[sp.SkillId][sp.Uid] {
            rs = append(rs, sp)
        }
        if !RuleParam(*ruleParam).isTrue() && !r.localCache.IsFirstRoundCoach(sp.SkillId, sp.Uid) {
            rs = append(rs, sp)
        }
    }

    return rs, nil
}

