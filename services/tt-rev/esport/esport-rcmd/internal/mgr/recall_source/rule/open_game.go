package rule

import (
    "context"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/entity"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/local_cache"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/store"
)

type OpenSkillRule struct {
    localCache *local_cache.LocalCache
}

func NewOpenSkillRule(lc *local_cache.LocalCache) *OpenSkillRule {
    return &OpenSkillRule{
        localCache: lc,
    }
}

func (r *OpenSkillRule) Recall(ctx context.Context, ruleParam *store.RecallSourceRuleSettingCondition) ([]*entity.SimpleSkillProduct, error) {
   gameNameMap := map[uint32]string{}
    for _, item := range r.localCache.GetGameInfo() {
        gameNameMap[item.GameId] = item.GameName
    }

    rs := make([]*entity.SimpleSkillProduct, 0)
    for _, item := range r.localCache.GetEnableSkillProduct() {
        if RuleParam(*ruleParam).strCompare(gameNameMap[item.SkillId]) {
            rs = append(rs, item)
        }
    }

    return rs, nil
}
