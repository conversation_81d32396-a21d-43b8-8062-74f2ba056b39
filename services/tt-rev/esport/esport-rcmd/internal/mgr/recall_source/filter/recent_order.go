package filter

import (
    "context"
    "fmt"
    "golang.52tt.com/pkg/log"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    esport_statistics "golang.52tt.com/protocol/services/esport-statistics"
    "golang.52tt.com/services/tt-rev/esport/common/collection/mapz"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/entity"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/rpc"
    "time"
)

type RecentOrderFilter struct {
    rpcCli *rpc.Client
}

func NewRecentOrderFilter(rpcCli *rpc.Client) *RecentOrderFilter {
    return &RecentOrderFilter{
        rpcCli: rpcCli,
    }
}


// 过滤近期下单过
func (f *RecentOrderFilter) Handle(ctx context.Context, data []*entity.SimpleSkillProduct, filterParam *filterParam) ([]*entity.SimpleSkillProduct, error) {
    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "get service info failed")
        return data, fmt.Errorf("get service info failed")
    }

    endTime := time.Now().Unix()
    startTime := endTime - 3*24*60*60

    orderCoachResp, err := f.rpcCli.ESportStatCli.GetUserOrderCoachList(ctx, &esport_statistics.GetUserOrderCoachListRequest{
        Uid:       svrInfo.UserID,
        StartTime: startTime,
        EndTime:   endTime,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "recentOrderFilter.GetUserOrderCoachList, uid: %d, err: %v", svrInfo.UserID, err)
        return data, err
    }

    orderCoachUid := mapz.Keys(orderCoachResp.GetCoachOrderTimeMap())

    return filterData(data, orderCoachUid, filterParam.filterNum), nil
}

