package mgr

import (
    "context"
    "golang.52tt.com/pkg/log"
    esport_skill "golang.52tt.com/protocol/services/esport-skill"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/store"
    "sync"
    "time"
)

const (
    pageSize   = 1000
    macPageNum = 10000 // 每个进程最大处理1w页
)

// SkillProductMarking 技能商品标签生成任务
func (m *Mgr) SkillProductMarking(ctx context.Context) {
    st := time.Now()
    defer func() {
        log.Infof("SkillProductMarking cost: %v", time.Since(st))
    }()

    for i := 0; i < macPageNum; i++ {
        spList, err := m.store.GetEnableSkillProduct(ctx, int64(i), pageSize)
        if err != nil {
            log.Errorf("SkillProductMarking fail to GetEnableSkillProduct. err:%v", err)
            break
        }
        if spList == nil || len(spList) == 0 {
            log.DebugWithCtx(ctx, "SkillProductMarking, EnableSkillProduct finish")
            break
        }

        for _, item := range spList {
            newTag, err := m.genTag(ctx, item)
            if err != nil {
                log.ErrorWithCtx(ctx, "SkillProductMarking fail to genTag. err:%v", "item: %+v", err, item)
                continue
            }
            if len(newTag) == 0 { // 无标签, 跳过
                m.store.DelRcmdSkillProductByProductId(ctx, item.ProductId)
                continue
            }

            newRCTD := &store.RcmdSkillProduct{
                Uid:            item.Uid,
                SkillId:        item.SkillId,
                SkillProductId: item.ProductId,
                Tag:            newTag,
                UpdateTime:     uint32(time.Now().Unix()),
            }
            err = m.store.UpsertRcmdSkillProduct(ctx, newRCTD)
            if err != nil {
                log.WarnWithCtx(ctx, "SkillProductMarking fail to UpsertRcmdCoachTagData. err:%v", "newRCTD: %+v", err, newRCTD)
            }
        }
    }

    // 处理关闭的技能商品
    for i := 0; i < macPageNum; i++ {
        spList, err := m.store.GetDisableSkillProduct(ctx, int64(i), pageSize)
        if err != nil {
            log.Errorf("SkillProductMarking fail to GetDisableSkillProduct. err:%v", err)
            return
        }
        if spList == nil || len(spList) == 0 {
            log.DebugWithCtx(ctx, "SkillProductMarking, DisableSkillProduct finish")
            return
        }

        for _, item := range spList {
            m.store.DelRcmdSkillProductByProductId(ctx, item.ProductId)
        }
    }
}


func (m *Mgr) genTag(ctx context.Context, sp *store.SkillProduct) ([]*store.RcmdTag, error) {
    tagFunc := []genTagFunc{m.genNewCoachTag, m.genFirstRoundTag, m.genOriginPriceTag}
    wg := sync.WaitGroup{}

    tmpTag := make([]*store.RcmdTag, len(tagFunc))
    for i, func_ := range tagFunc {
        wg.Add(1)
        go func(fi int, f genTagFunc) {
            defer wg.Done()
            tag, err := f(ctx, sp)
            if err != nil {
                log.WarnWithCtx(ctx, "genTag fail to genTag", "sp:%+v", "func_: %d",  "err:%v", sp, fi, err)
                return
            }
            if tag != nil {
                tmpTag[fi] = tag
            }
        }(i, func_)
    }

    wg.Wait()

    rsTag := make([]*store.RcmdTag, 0, len(tmpTag))
    for _, tag := range tmpTag {
        if tag != nil {
            rsTag = append(rsTag, tag)
        }
    }

    return rsTag, nil
}

type genTagFunc func(ctx context.Context, sp *store.SkillProduct) (*store.RcmdTag, error)

// genNewCoachTag 生成新大神标签
func (m *Mgr) genNewCoachTag(ctx context.Context, sp *store.SkillProduct) (*store.RcmdTag, error) {
    if sp.CreateTime.After(time.Now().AddDate(0, 0, -7)) {
        return store.BuildNewCoachTag(), nil
    }
    return nil, nil
}

// genFirstRoundTag 生成首单标签
func (m *Mgr) genFirstRoundTag(ctx context.Context, sp *store.SkillProduct) (*store.RcmdTag, error) {
    if sp.FirstRoundSwitch {
        return store.BuildFirstFoundTag(), nil
    }
    return nil, nil
}

// genGuaranteeWinTag 生成原价标签
func (m *Mgr) genOriginPriceTag(ctx context.Context, sp *store.SkillProduct) (*store.RcmdTag, error) {
    calPriceResp, err := m.rpcCli.ESportSkillCli.CalculatePrice(ctx, &esport_skill.CalculatePriceRequest{
        GameId:    sp.SkillId,
        CoachIds:  []uint32{sp.Uid},
        WithCache: true,
    })
    if err != nil {
        log.WarnWithCtx(ctx, "genOriginPriceTag fail to CalculatePrice", "sp:%+v", "err:%v", sp, err)
        return nil, err
    }
    if targetPrice, ok := calPriceResp.GetPriceMap()[sp.Uid]; ok {
        return store.BuildOriginPriceTag(targetPrice.GetPrice()), nil
    }

    return nil, nil
}

