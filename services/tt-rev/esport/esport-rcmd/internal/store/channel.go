package store

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/pkg/log"
	"time"
)

type CoachChannelInfo struct {
	CoachUid  uint32    `bson:"_id"`
	ChannelId uint32    `bson:"channel_id"`
	OnMic     bool      `bson:"on_mic"`
	Locked    bool      `bson:"locked"`
	UpdateAt  time.Time `bson:"update_at"`
}

func (s *Store) ensureIndex4CoachChannelInfo(ctx context.Context) {
	_, err := s.coachChannelColl.Collection.Indexes().CreateMany(context.Background(), []mongo.IndexModel{
		{
			Keys: bson.D{
				{
					Key:   "channel_id",
					Value: 1,
				},
			},
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ensureIndex4CoachChannelInfo err: %v", err)
	}
}

func (s *Store) BatGetChannelInfo(ctx context.Context, coachList []uint32) ([]*CoachChannelInfo, error) {
	if len(coachList) == 0 {
		return nil, nil
	}

	filter := bson.M{"_id": bson.M{"$in": coachList}}
	cursor, err := s.coachChannelColl.Collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var infoList []*CoachChannelInfo
	for cursor.Next(ctx) {
		var info CoachChannelInfo
		if err := cursor.Decode(&info); err != nil {
			return nil, err
		}
		infoList = append(infoList, &info)
	}
	if err := cursor.Err(); err != nil {
		return nil, err
	}
	return infoList, nil
}

func (s *Store) UpdateChannelInfo(ctx context.Context, coachUid, channelId uint32, locked bool) error {
	filter := bson.M{"_id": coachUid}
	update := bson.M{"$set": bson.M{"channel_id": channelId, "locked": locked, "update_at": time.Now()}}
	opt := options.Update().SetUpsert(true)
	_, err := s.coachChannelColl.Collection.UpdateOne(ctx, filter, update, opt)
	return err
}

func (s *Store) UpdateChannelMicStatus(ctx context.Context, coachUid, channelId uint32, onMic bool) error {
	filter := bson.M{"_id": coachUid}
	update := bson.M{"$set": bson.M{"channel_id": channelId, "on_mic": onMic, "update_at": time.Now()}}
	opt := options.Update().SetUpsert(true)
	_, err := s.coachChannelColl.Collection.UpdateOne(ctx, filter, update, opt)
	return err
}

func (s *Store) UpdateChannelLockStatus(ctx context.Context, channelId uint32, locked bool) error {
	filter := bson.M{"channel_id": channelId}
	update := bson.M{"$set": bson.M{"locked": locked, "update_at": time.Now()}}
	_, err := s.coachChannelColl.Collection.UpdateMany(ctx, filter, update)
	return err
}
