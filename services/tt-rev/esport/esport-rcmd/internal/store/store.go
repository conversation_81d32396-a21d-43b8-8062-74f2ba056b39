package store

import (
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mongo"
    "go.mongodb.org/mongo-driver/bson"
    "go.mongodb.org/mongo-driver/mongo/options"
    "golang.52tt.com/pkg/config"
)

const (
    skillProductCollName       = "skill_product"
    rcmdSkillProduct           = "rcmd_skill_product"
    strategyCollName           = "strategy"
    seqCollName                = "seq"
    sortParameterTableCollName = "sort_parameter_table"
    firstRoundOrderColl        = "first_round_order"
    recallSourceColl           = "recall_source"
    coachChannelColl           = "coach_channel"
)

type Store struct {
    client *mongo.ClientImpl

    seq                    *mongo.MongoCollectionImpl
    skillProductColl       *mongo.MongoCollectionImpl
    strategyColl           *mongo.MongoCollectionImpl
    rcmdSkillProductColl   *mongo.MongoCollectionImpl
    sortParameterTableColl *mongo.MongoCollectionImpl
    firstRoundOrderColl    *mongo.MongoCollectionImpl
    recallSourceColl       *mongo.MongoCollectionImpl
    coachChannelColl       *mongo.MongoCollectionImpl
}

func (s *Store) UpsertRcmdCoachTagData(ctx context.Context, rcmdCoachTagData *RcmdSkillProduct) error {
    panic("implement me")
}

func NewStore(ctx context.Context, cfg *config.MongoConfig) (*Store, error) {
    client, err := mongo.NewClient(ctx, cfg.OptionsForReplicaSet())
    if err != nil {
        return nil, err
    }

    c := &Store{
        client:                 client,
        skillProductColl:       client.CreateMongoCollection(cfg.Database, skillProductCollName),
        rcmdSkillProductColl:   client.CreateMongoCollection(cfg.Database, rcmdSkillProduct),
        strategyColl:           client.CreateMongoCollection(cfg.Database, strategyCollName),
        seq:                    client.CreateMongoCollection(cfg.Database, seqCollName),
        sortParameterTableColl: client.CreateMongoCollection(cfg.Database, sortParameterTableCollName),
        firstRoundOrderColl:    client.CreateMongoCollection(cfg.Database, firstRoundOrderColl),
        recallSourceColl:       client.CreateMongoCollection(cfg.Database, recallSourceColl),
        coachChannelColl:       client.CreateMongoCollection(cfg.Database, coachChannelColl),
    }
    c.ensureIndex(ctx)
    return c, nil
}

func (s *Store) Close(ctx context.Context) error {
    return s.client.Close(ctx)
}

func (s *Store) ensureIndex(ctx context.Context) {
    s.ensureIndex4CoachChannelInfo(ctx)
}

// genSeq 生成 size 个序列
// 1. 从 seq 集合 inc size 个大小，比返回最新seq值
// 2. 根据最新seq值生成 size 个序列
func (s *Store) genSeq(ctx context.Context, size uint32) ([]uint32, error) {
    filter := bson.M{"_id": "seq_id"}
    update := bson.M{"$inc": bson.M{"seq": size}}
    opts := options.FindOneAndUpdate().SetReturnDocument(options.After).SetUpsert(true)

    var result struct {
        Seq uint32 `bson:"seq"`
    }

    err := s.seq.Collection.FindOneAndUpdate(ctx, filter, update, opts).Decode(&result)
    if err != nil {
        return nil, err
    }

    seqs := make([]uint32, size)
    for i := uint32(0); i < size; i++ {
        seqs[i] = result.Seq - size + i + 1
    }

    return seqs, nil
}
