package main

import (
    "context"
    "go.mongodb.org/mongo-driver/bson"
    "go.mongodb.org/mongo-driver/mongo"
    "golang.52tt.com/pkg/config"
)

func main() {
    //cfg := config.MongoConfig{
    //    Addrs:    "10.64.236.130:27017",
    //    Database: "channel_lottery",
    //    UserName: "godman",
    //    Password: "thegodofman",
    //}
    cfg := config.MongoConfig{
        Addrs:    "10.208.7.167:27017",
        Database: "esport_trade",
        UserName: "esport_trade_rw",
        Password: "M*d71SCQAtBtram",
    }
    opt := cfg.OptionsForReplicaSet() // 优先读从库
    client, err := mongo.NewClient(opt)
    if err != nil {
        panic(err)
    }
    err = client.Connect(context.Background())
    if err != nil {
        panic(err)
    }

    coll := client.Database("channel_lottery").Collection("manual_grant_coupon_task")
    _, err = coll.UpdateMany(context.Background(), bson.M{"group_id":0}, bson.M{"$set": bson.M{"group_id": []uint32{}}})
    if err != nil {
        panic(err)
    }
}
