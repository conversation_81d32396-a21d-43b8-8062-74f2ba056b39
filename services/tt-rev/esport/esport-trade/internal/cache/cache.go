//go:generate mockgen -destination=../mocks/mock_cache.go -package=mocks golang.52tt.com/services/tt-rev/esport/esport-trade/internal/cache ICache
package cache

import (
	"context"
	"encoding/json"
	"fmt"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	pb "golang.52tt.com/protocol/services/esport-trade"
	"strconv"
	"time"
)

type Cache struct {
	cmder redis.Cmdable
}

func NewCache(ctx context.Context, cfg *redisConnect.RedisConfig) (*Cache, error) {
	client, err := redisConnect.NewClient(ctx, cfg)
	if err != nil {
		return nil, err
	}

	c := &Cache{
		cmder: client,
	}
	return c, nil
}

func (c *Cache) Close() error {
	return c.cmder.(redis.Client).Close()
}

func (c *Cache) GetRedisClient() redis.Cmdable {
	return c.cmder
}

func genImSvrMsgIdKey(orderId string, imType, eventType uint32) string {
	return fmt.Sprintf("esport_trade_im_%s_%d_%d", orderId, imType, eventType)
}

func genBothOngoingOrderKey(uidA, uidB uint32) string {
	if uidB < uidA {
		uidA, uidB = uidB, uidA
	}
	return fmt.Sprintf("esport_trade_both_ongoing_%d_%d", uidA, uidB)
}

// SetImSvrMsgId 缓存消息id
func (c *Cache) SetImSvrMsgId(ctx context.Context, orderId string, imType, eventType uint32, uid2MsgId map[uint32]uint64, ttl time.Duration) error {
	key := genImSvrMsgIdKey(orderId, imType, eventType)
	fields := make(map[string]interface{})
	for uid, msgId := range uid2MsgId {
		fields[fmt.Sprint(uid)] = msgId
	}

	err := c.cmder.HMSet(ctx, key, fields).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetImSvrMsgId fail to HMSet. orderId:%s, imType:%d, eventType:%d, uid2MsgId:%v, err:%v",
			orderId, imType, eventType, uid2MsgId, err)
		return err
	}

	c.cmder.Expire(ctx, key, ttl)
	return nil
}

// GetImSvrMsgId 获取消息id
func (c *Cache) GetImSvrMsgId(ctx context.Context, orderId string, imType, eventType uint32, uidList []uint32) (map[uint32]uint64, error) {
	out := make(map[uint32]uint64)
	key := genImSvrMsgIdKey(orderId, imType, eventType)
	fields := make([]string, 0)
	for _, uid := range uidList {
		fields = append(fields, fmt.Sprint(uid))
	}

	resultList, err := c.cmder.HMGet(ctx, key, fields...).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetImSvrMsgId fail to HMSet. orderId:%s, imType:%d, eventType:%d, uidList:%v, err:%v",
			orderId, imType, eventType, uidList, err)
		return out, err
	}

	for i, res := range resultList {
		uid := uidList[i]

		if res == nil {
			continue
		}
		if str, ok := res.(string); ok {
			msgId, _ := strconv.ParseUint(str, 10, 64)
			out[uid] = msgId
		}
	}

	return out, nil
}

// SetBothOngoingOrderList 设置双方正在进行的订单列表
func (c *Cache) SetBothOngoingOrderList(ctx context.Context, uidA, uidB uint32, list []*pb.OrderSimpleInfo) error {
	key := genBothOngoingOrderKey(uidA, uidB)

	b, err := json.Marshal(list)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetBothOngoingOrderList fail to json.Marshal. uidA:%d, uidB:%d, list:%+v, err:%v", uidA, uidB, list, err)
		return err
	}

	return c.cmder.Set(ctx, key, string(b), 24*time.Hour).Err()
}

// GetBothOngoingOrderList 获取双方正在进行的订单列表
func (c *Cache) GetBothOngoingOrderList(ctx context.Context, uidA, uidB uint32) ([]*pb.OrderSimpleInfo, bool, error) {
	list := make([]*pb.OrderSimpleInfo, 0)
	key := genBothOngoingOrderKey(uidA, uidB)
	b, err := c.cmder.Get(ctx, key).Bytes()
	if err != nil {
		if redis.IsNil(err) {
			return list, false, nil
		}

		log.ErrorWithCtx(ctx, "GetBothOngoingOrderList fail to Get. uidA:%d, uidB:%d, err:%v", uidA, uidB, err)
		return list, false, err
	}

	err = json.Unmarshal(b, &list)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBothOngoingOrderList fail to json.Unmarshal. uidA:%d, uidB:%d, err:%v", uidA, uidB, err)
		return list, false, err
	}

	return list, true, nil
}

// DeleteBothOngoingOrderList 删除双方正在进行的订单列表
func (c *Cache) DeleteBothOngoingOrderList(ctx context.Context, uidA, uidB uint32) error {
	key := genBothOngoingOrderKey(uidA, uidB)
	return c.cmder.Del(ctx, key).Err()
}
