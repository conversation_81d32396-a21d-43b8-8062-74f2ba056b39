package mgr

import (
    "bytes"
    "context"
    "encoding/json"
    "errors"
    "fmt"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/pkg/ttversion"
    "golang.52tt.com/protocol/app"
    "golang.52tt.com/protocol/app/esport_logic"
    "golang.52tt.com/protocol/common/status"
    pb "golang.52tt.com/protocol/services/esport-trade"
    "golang.52tt.com/protocol/services/esport_role"
    guild_cooperation "golang.52tt.com/protocol/services/guild-cooperation"
    imApiPb "golang.52tt.com/protocol/services/im-api"
    riskMngApiPb "golang.52tt.com/protocol/services/risk-mng-api"
    "golang.52tt.com/services/tt-rev/common/goroutineex"
    "golang.52tt.com/services/tt-rev/esport/common/collection/list"
    "golang.52tt.com/services/tt-rev/esport/esport-trade/internal/conf"
    "golang.52tt.com/services/tt-rev/esport/esport-trade/internal/store"
    "google.golang.org/grpc/codes"
    "sort"
    "strings"
    "text/template"
    "time"
)

func (m *Mgr) GetUserCoupon(ctx context.Context, request *pb.GetUserCouponRequest) (*pb.GetUserCouponResponse, error) {
    resp := &pb.GetUserCouponResponse{}

    // 先查缓存
    couponList, err := m.cache.GetUserCoupon(ctx, request.GetUid())
    if err == nil {
        resp.CouponList = couponList
        log.DebugWithCtx(ctx, "GetUserCoupon getFromCache: %+v", couponList)
        return resp, nil
    }

    // 再查db
    couponList, err = m.getUserCouponFromStore(ctx, request.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserCoupon getFromStore err: %v", err)
        return resp, err
    }
    resp.CouponList = couponList
    log.DebugWithCtx(ctx, "GetUserCoupon getFromStore: %+v", couponList)

    // 设置缓存
    m.cache.SetUserCoupon(ctx, request.GetUid(), couponList)
    return resp, nil
}

func (m *Mgr) getUserCouponFromStore(ctx context.Context, uid uint32) ([]*pb.Coupon, error) {
    couponList, err := m.mysqlStore.GetEsportCouponByUid(ctx, uid)
    if err != nil {
        return nil, err
    }

    pbCouponList := make([]*pb.Coupon, 0, len(couponList))
    for _, item := range couponList {
        couponConf, ok := m.localCache.GetCouponConfByID(item.ConfId)
        if !ok {
            log.WarnWithCtx(ctx, "coupon conf not found: %d", item.ConfId)
            continue
        }
        pbCouponList = append(pbCouponList, fillCouponPb(ctx, item, couponConf))
    }

    // 按最先生效/价值最大排序
    sort.Slice(pbCouponList, func(i, j int) bool {
        if pbCouponList[i].EffectTime == pbCouponList[j].EffectTime {
            return pbCouponList[i].ReducePrice > pbCouponList[j].ReducePrice
        }
        return pbCouponList[i].EffectTime < pbCouponList[j].EffectTime
    })

    return pbCouponList, nil
}

// RenderCouponInfo 渲染优惠券富文本
func RenderCouponInfo(ctx context.Context, mainTpl, itemTpl string, data *esport_logic.ShowCouponPopupInfo) (string, error) {
    var itemBuf bytes.Buffer
    iItemTpl := template.Must(template.New("item").Parse(itemTpl))
    err := iItemTpl.Execute(&itemBuf, data.GetCouponList())
    if err != nil {
        log.ErrorWithCtx(ctx, "RenderCouponInfo err: %v", err)
        return "", err
    }

    data.CouponInfo = itemBuf.String()
    iMainTpl := template.Must(template.New("main").Parse(mainTpl))
    var mainBuf bytes.Buffer
    err = iMainTpl.Execute(&mainBuf, data)
    if err != nil {
        log.ErrorWithCtx(ctx, "RenderCouponInfo err: %v", err)
        return "", err
    }

    return mainBuf.String(), nil
}

func getCouponEffectiveTime(conf *pb.CouponConfig) time.Time {
    effectiveDate := time.Now().AddDate(0, 0, int(conf.GetEffectiveDay()))
    return time.Date(effectiveDate.Year(), effectiveDate.Month(), effectiveDate.Day(), 0, 0, 0, 0, time.Local)
}

func getCouponExpireTime(conf *pb.CouponConfig) time.Time {
    effectiveTime := getCouponEffectiveTime(conf)
    add := 0 // 如果当天生效,且有效期为1天, 多给一天
    if effectiveTime.Day() == time.Now().Day() && conf.GetExpireDay() == 1 {
        add = 1
    }
    return effectiveTime.AddDate(0, 0, int(conf.GetExpireDay())+add)
}

func (m *Mgr) ClearCouponGainCache(ctx context.Context, uid uint32) error {
    err := m.cache.DelCouponGainFlag(ctx, m.cache.GenCouponGainFlagKey(uid))
    if err != nil {
        log.ErrorWithCtx(ctx, "ClearCouponGainCache, uid: %d, err: %v", uid, err)
        return err
    }

    err = m.cache.DelMarkShowRemainCoupon(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "ClearCouponGainCache, uid: %d, err: %v", uid, err)
        return err
    }
    return nil
}

// GetCouponById 根据优惠券id获取优惠券信息
func (m *Mgr) GetCouponById(ctx context.Context, uid uint32, couponId string) (*pb.Coupon, error) {
    // 查询优惠券的信息
    coupon, err := m.mysqlStore.GetEsportCouponById(ctx, uid, couponId)
    if err != nil {
        return nil, fmt.Errorf("GetCouponById, err: %w", err)
    }

    couponConf, ok := m.localCache.GetCouponConfByID(coupon.ConfId)
    if !ok {
        return nil, fmt.Errorf("GetCouponById, conf not found, confId: %d", coupon.ConfId)
    }

    return fillCouponPb(ctx, coupon, couponConf), nil
}

func (m *Mgr) GetCouponByIds(ctx context.Context, uid uint32, couponId ...string) ([]*pb.Coupon, error) {
    // 查询优惠券的信息
    coupons, err := m.mysqlStore.GetEsportCouponByIds(ctx, uid, couponId...)
    if err != nil {
        return nil, fmt.Errorf("GetCouponByIds, err: %w", err)
    }

    result := make([]*pb.Coupon, 0, len(coupons))
    for _, item := range coupons {
        couponConf, ok := m.localCache.GetCouponConfByID(item.ConfId)
        if !ok {
            log.WarnWithCtx(ctx, "GetCouponByIds, conf not found: %d", item.ConfId)
            continue
        }
        result = append(result, fillCouponPb(ctx, item, couponConf))
    }
    return result, nil
}

func (m *Mgr) GetUnreadManualGrantCoupon(ctx context.Context, uid uint32) ([]*pb.Coupon, error) {
    // 查询未读列表
    unreadList, err := m.mongoStore.GetUnreadManualGrantCoupon(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUnreadManualGrantCoupon err: %v", err)
        return nil, err
    }
    if len(unreadList) == 0 {
        return nil, nil
    }

    // 查询优惠券的信息
    coupons, err := m.mysqlStore.GetEsportCouponByIds(ctx, uid, unreadList...)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetEsportCouponByIds err: %v", err)
        return nil, err
    }

    // 拼接优惠券配置
    results := make([]*pb.Coupon, 0, len(coupons))
    now := time.Now()
    for _, item := range coupons {
        // 过滤一下，免得看到优惠券 过期的、已经使用的
        if item.Status != store.CouponStatusUnused {
            continue
        }
        if item.ExpireTime.Before(now) {
            continue
        }

        couponConf, ok := m.localCache.GetCouponConfByID(item.ConfId)
        if !ok {
            log.WarnWithCtx(ctx, "GetUnreadManualGrantCoupon, conf not found: %d", item.ConfId)
            continue
        }
        results = append(results, fillCouponPb(ctx, item, couponConf))
    }

    // 排序。EffectTime最早的在前面，如果EffectTime相同，ReducePrice最大的在前面
    sort.Slice(results, func(i, j int) bool {
        if results[i].EffectTime == results[j].EffectTime {
            return results[i].ReducePrice > results[j].ReducePrice
        }
        return results[i].EffectTime < results[j].EffectTime
    })

    return results, nil
}

func (m *Mgr) MarkManualGrantCouponRead(ctx context.Context, uid uint32) error {
    err := m.mongoStore.ClearUnreadManualGrantCoupon(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "MarkManualGrantCouponRead, uid: %d, err: %v", uid, err)
        return err
    }
    return nil
}

func (m *Mgr) GetUserCouponByPage(ctx context.Context, in *pb.GetUserCouponByPageRequest) (out *pb.GetUserCouponByPageResponse, err error) {
    out = &pb.GetUserCouponByPageResponse{}

    // 先查询总数
    total, err := m.mysqlStore.GetUserEsportCouponCount(ctx, in.GetUid(), in.GetCouponStatus())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserCouponByPage, GetUserEsportCouponCount, err: %v", err)
        return
    }
    out.Total = total

    // 分页查询
    limit, offset := getLimitOffset(in.GetPage(), in.GetPageSize())
    coupons, err := m.mysqlStore.GetUserEsportCouponPage(ctx, in.GetUid(), in.GetCouponStatus(), offset, limit+1) // 多查一条，判断是否有更多
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserCouponByPage, GetUserEsportCouponPage, err: %v", err)
        return
    }
    if len(coupons) > int(limit) {
        out.HasMore = true
        coupons = coupons[:limit]
    }

    // 组装优惠券配置
    out.CouponList = make([]*pb.Coupon, 0, len(coupons))
    for _, item := range coupons {
        couponConf, ok := m.localCache.GetCouponConfByID(item.ConfId)
        if !ok {
            log.WarnWithCtx(ctx, "GetUserCouponByPage, conf not found: %d", item.ConfId)
            continue
        }
        out.CouponList = append(out.CouponList, fillCouponPb(ctx, item, couponConf))
    }

    return
}

func fillCouponPb(ctx context.Context, coupon *store.EsportCoupon, couponConf *store.EsportCouponConf) *pb.Coupon {
    gsi := &pb.CouponGianSourceCommitOrder{}
    // 只有source=1的时候，才解析为CouponGianSourceCommitOrder，历史问题
    if coupon.GainSource == 1 && len(coupon.GainSourceInfo) > 0 {
        err := json.Unmarshal([]byte(coupon.GainSourceInfo), gsi)
        if err != nil {
            log.WarnWithCtx(ctx, "fillCouponPb, Unmarshal err: %v, GainSourceInfo: %s", err, coupon.GainSourceInfo)
        }
    }

    return &pb.Coupon{
        CouponId:             coupon.CouponId,
        GainSource:           coupon.GainSource,
        GainSourceInfo:       gsi,
        EffectTime:           uint32(coupon.EffectTime.Unix()),
        ExpireTime:           uint32(coupon.ExpireTime.Unix()),
        CreateTime:           uint32(coupon.CreateTime.Unix()),
        CoponName:            couponConf.Conf.GetCouponName(),
        CouponType:           couponConf.Conf.GetCouponType(),
        ReduceRequire:        couponConf.Conf.GetReduceRequire(),
        ReducePrice:          couponConf.Conf.GetReducePrice(),
        Discount:             couponConf.Conf.GetDiscount(),
        UsageLimitText:       couponConf.Conf.GetUsageLimitText(),
        ConfId:               couponConf.ID,
        UsageLimitNoOrderDay: couponConf.Conf.GetUsageLimitNoOrderDay(),
        UsageLimitType:       couponConf.Conf.GetUsageLimitType(),
        MustInCoachPool:      couponConf.Conf.GetMustInCoachPool(),
    }
}

func (m *Mgr) tryGetUserGrantCouponConf(ctx context.Context, uid uint32, orderId string) ([]*conf.GrantCouponItem, []func(), error) {
    var err error
    rollbackFunc := make([]func(), 0)
    defer func() { // 回滚占坑资源
        if err != nil {
            for _, f := range rollbackFunc {
                f()
            }
        }
        rollbackFunc = nil
    }()

    // 获取用户发放优惠券配置
    grantCouponConf, err := m.GetUserGrantCouponConf(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetTestGrantCouponConf, err: %v", err)
        return nil, nil, err
    }

    // 判断每周一次限制
    gainFlagKey := m.cache.GenCouponGainFlagKey(uid)
    err = m.cache.SetCouponGainFlagNx(ctx, gainFlagKey, orderId)
    if err != nil {
        log.ErrorWithCtx(ctx, "tryGetUserGrantCouponConf SetCouponGainFlagNx, err: %v", err)
        return nil, nil, fmt.Errorf("领取太频繁了~ 请稍后再试")
    }
    rollbackFunc = append(rollbackFunc, func() {
        err = m.cache.DelCouponGainFlag(ctx, gainFlagKey)
        if err != nil {
            log.WarnWithCtx(ctx, "tryGetUserGrantCouponConf DelCouponGainFlag, err: %v", err)
        }
    })

    // 不配置用户组发放数量限制, 按总发放数量限制
    if len(grantCouponConf.UserGroupGrantNum) == 0 {
        if grantCouponConf.GrantSumNum > 0 {
            nowGrantSum, err := m.cache.IncrTodayCouponGrantSum(ctx, 1)
            if err != nil {
                log.ErrorWithCtx(ctx, "IncrTodayCouponGrantSum, err: %v", err)
                return nil, nil, err
            }
            rollbackFunc = append(rollbackFunc, func() {
                _, err := m.cache.IncrTodayCouponGrantSum(ctx, -1)
                if err != nil {
                    log.WarnWithCtx(ctx, "IncrTodayCouponGrantSum, err: %v", err)
                }
            })
            if nowGrantSum <= grantCouponConf.GrantSumNum {
                return grantCouponConf.GrantCouponList, rollbackFunc, nil
            }
        } else { // 总数不限制,直接返回
            return grantCouponConf.GrantCouponList, rollbackFunc, nil
        }
    } else {
        // 配置了人群包发放数量限制, 判断是否超出
        groupList := make([]string, 0, len(grantCouponConf.UserGroupGrantNum))
        for k, _ := range grantCouponConf.UserGroupGrantNum {
            groupList = append(groupList, k)
        }

        var userGroupId []string
        userGroupId, err = m.GetUserHasGroup(ctx, uid, groupList)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetUserHasGroup, err: %v", err)
            return grantCouponConf.GrantCouponList, rollbackFunc, err
        }
        if len(userGroupId) == 0 {
            err = fmt.Errorf("用户不在任何人群包中")
            return grantCouponConf.GrantCouponList, rollbackFunc, err
        }

        // 判断各人群包发放数量是否超出
        for _, groupId := range userGroupId {
            if grantNum, ok := grantCouponConf.UserGroupGrantNum[groupId]; ok && grantNum > 0{
                var nowGroupGrantNum uint32
                nowGroupGrantNum, err := m.cache.IncrTodayUserGroupCouponGrantSum(ctx, groupId, 1)
                if err != nil {
                    log.ErrorWithCtx(ctx, "IncrTodayUserGroupCouponGrantSum, err: %v", err)
                    return grantCouponConf.GrantCouponList, rollbackFunc, err
                }
                if nowGroupGrantNum <= grantCouponConf.UserGroupGrantNum[groupId] {
                    rollbackFunc = append(rollbackFunc, func() {
                        _, err := m.cache.IncrTodayUserGroupCouponGrantSum(ctx, groupId, -1)
                        if err != nil {
                            log.WarnWithCtx(ctx, "IncrTodayUserGroupCouponGrantSum, err: %v", err)
                        }
                    })
                    return grantCouponConf.GrantCouponList, rollbackFunc, nil
                } else {
                    _, err := m.cache.IncrTodayUserGroupCouponGrantSum(ctx, groupId, -1) // 如果当前人群包发放数量超出, 回滚计数, 避免后续放大发放限制不生效
                    if err != nil {
                        log.WarnWithCtx(ctx, "IncrTodayUserGroupCouponGrantSum, err: %v", err)
                    }
                }
            } else { // 配置了人群包但不限量
                return grantCouponConf.GrantCouponList, rollbackFunc, nil
            }
        }
    }

    err = errors.New("已达到最大发放数量")
    return grantCouponConf.GrantCouponList, rollbackFunc, err
}

func (m *Mgr) GetUserGrantCouponConf(ctx context.Context, uid uint32) (*conf.GrantCouponConfItem, error) {
    grantCouponConf, err := m.bc.GetTestGrantCouponConf(uid)
    if err == nil {
        return grantCouponConf, nil
    } else {
        log.DebugWithCtx(ctx, "GetTestGrantCouponConf, err: %v", err)
        grantCouponConf, err = m.bc.GetActiveGrantCouponConf()
        if err != nil {
            log.ErrorWithCtx(ctx, "GetActiveGrantCouponConf, err: %v", err)
            return nil, err
        }
    }

    return grantCouponConf, nil
}

func (m *Mgr) GetUserHasGroup(ctx context.Context, uid uint32, groupList []string) ([]string, error) {
    hasGroup, err := m.userGroupCli.GetUserHasGroup(ctx, uid, groupList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserHasGroup, uid: %d, err: %v", uid, err)
        return nil, err
    }
    return list.IntersectStrSlice(hasGroup, groupList), nil
}

func (m *Mgr) GetCoachCouponUseTimes(ctx context.Context, coachList []uint32) (map[uint32]uint32, error) {
    return m.mongoStore.BatGetCoachCouponTimesToday(ctx, coachList)
}

var (
    couponMinVer = ttversion.Parse("电竞优惠券版本", "android-6.53.5", "ios-6.53.5", "pc-2.0.4")
)

func (m *Mgr) AutoGrantCoupon(ctx context.Context, in *pb.AutoGrantCouponRequest) (*pb.AutoGrantCouponResponse, error) {
    out := &pb.AutoGrantCouponResponse{}
    serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
    defer func() {
        log.InfoWithCtx(ctx, "AutoGrantCoupon in: %+v, out: %+v", in, out)
    }()

    // 检查开关
    if !m.bc.GetCouponConf().GainSwitch {
        log.ErrorWithCtx(ctx, "AutoGrantCoupon(preCheck=%t) fail, switch not open", in.PreCheck)
        return out, nil
    }

    // 新版本才可以领取
    if !couponMinVer.Atleast(serviceInfo.ClientType, serviceInfo.ClientVersion) {
        log.ErrorWithCtx(ctx, "AutoGrantCoupon(preCheck=%t) fail, low version", in.PreCheck)
        return out, nil
    }

    // 判断本周是否领取过
    grantFlag, err := m.cache.GetCouponGainFlag(ctx, in.Uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "AutoGrantCoupon(preCheck=%t) fail, GetCouponGainFlag err: %v", in.PreCheck, err)
        return out, err
    }
    if grantFlag != "" {
        log.ErrorWithCtx(ctx, "AutoGrantCoupon(preCheck=%t) fail, already gain this week", in.PreCheck)
        return out, nil
    }

    // 查用户信息
    accountResp, err := m.rpcCli.AccountCli.GetUserByUid(ctx, in.Uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "AutoGrantCoupon(preCheck=%t) fail, GetUserByUid err: %v", in.PreCheck, err)
        return out, err
    }
    // 查大神工会
    esportRoleResp, err := m.rpcCli.EsportRoleCli.GetUserESportRole(ctx, &esport_role.GetUserESportRoleReq{Uid: in.CoachUid})
    if err != nil {
        log.ErrorWithCtx(ctx, "AutoGrantCoupon(preCheck=%t) fail, GetUserESportRole err: %v", in.PreCheck, err)
        return out, err
    }

    // 注册少于30天不能领
    if m.bc.GetCouponConf().GainNeedReg30Day {
        regAt := time.Unix(int64(accountResp.GetRegisteredAt()), 0)
        if time.Now().Sub(regAt).Hours() < 24*30 {
            log.ErrorWithCtx(ctx, "AutoGrantCoupon(preCheck=%t) fail, account reg less than 30 days", in.PreCheck)
            return out, nil
        }
    }

    // 大神身份不能领
    if m.bc.GetCouponConf().GainNeedNoCoach {
        esportRoleResp, err := m.rpcCli.EsportRoleCli.GetUserESportRole(ctx, &esport_role.GetUserESportRoleReq{Uid: in.Uid})
        if err != nil {
            log.ErrorWithCtx(ctx, "AutoGrantCoupon(preCheck=%t) fail, GetUserESportRole err: %v", in.PreCheck, err)
            return out, err
        }
        if esportRoleResp.GetEsportRole() > 0 {
            log.ErrorWithCtx(ctx, "AutoGrantCoupon(preCheck=%t) fail, user has coach role", in.PreCheck)
            return out, nil
        }
    }

    // 检查风控
    riskPass, err := m.checkRiskAutoGrantCoupon(ctx, in.BaseReq, in.OrderTotalPrice,
        accountResp.GetCurrentGuildId(), in.OrderId, fmt.Sprintf("%d", in.Source))
    if err != nil {
        log.ErrorWithCtx(ctx, "AutoGrantCoupon(preCheck=%t) fail, checkRiskAutoGrantCoupon err: %v", in.PreCheck, err)
        return out, err
    }
    if !riskPass {
        log.ErrorWithCtx(ctx, "AutoGrantCoupon(preCheck=%t) fail, risk check not pass", in.PreCheck)
        return out, nil
    }

    // 检查库存和人群包
    couponConfList, rollbackFunc, err := m.tryGetUserGrantCouponConf(ctx, in.Uid, in.OrderId)
    if err != nil {
        log.ErrorWithCtx(ctx, "AutoGrantCoupon(preCheck=%t) fail, tryGetUserGrantCouponConf err: %v", in.PreCheck, err)
        return out, protocol.NewExactServerError(codes.OK, status.ErrEsportsCommonErr, err.Error())
    }

    // 预检查的请求，不用继续往下走了，但要回滚库存
    out.CanGrant = true
    if in.PreCheck {
        for _, f := range rollbackFunc {
            f()
        }
        return out, nil
    }

    var gainErr error
    defer func() {
        if gainErr != nil { // 真正发放失败, 回滚预扣库存
            for _, f := range rollbackFunc {
                f()
            }
        }
    }()

    gainSourceInfo := &pb.CouponGianSourceCommitOrder{
        CoachUid:     in.CoachUid,
        CoachGuildId: esportRoleResp.GetGuildId(),
        OrderId:      in.OrderId,
    }
    gainSourceInfoStr, _ := json.Marshal(gainSourceInfo)

    // 构造发放的优惠券的信息
    couponList := make([]*pb.Coupon, 0, len(couponConfList))
    couponRemain := make([]*store.EsportCoupon, 0, len(couponConfList))
    for _, item := range couponConfList {
        couponConf, ok := m.localCache.GetCouponConfByID(item.CouponConfId)
        if !ok {
            continue
        }
        for i := 0; i < int(item.Count); i++ {
            nowCnt := int64(0)
            nowCnt, gainErr = m.cache.GenAutoIncCouponId()
            if gainErr != nil {
                return out, gainErr
            }
            elems := &store.EsportCoupon{
                CouponId:       fmt.Sprintf("%s%08d", time.Now().Format("060102"), nowCnt),
                ConfId:         item.CouponConfId,
                Uid:            in.Uid,
                GainSource:     uint32(pb.CouponGainSource_GAIN_SOURCE_COMMIT_ORDER),
                GainSourceInfo: string(gainSourceInfoStr),
                EffectTime:     getCouponEffectiveTime(couponConf.Conf),
                ExpireTime:     getCouponExpireTime(couponConf.Conf),
                CreateTime:     time.Now(),
            }
            couponRemain = append(couponRemain, elems)
            couponList = append(couponList, &pb.Coupon{
                CouponId:        elems.CouponId,
                CoponName:       couponConf.Conf.GetCouponName(),
                CouponType:      couponConf.Conf.GetCouponType(),
                ReduceRequire:   couponConf.Conf.GetReduceRequire(),
                ReducePrice:     couponConf.Conf.GetReducePrice(),
                Discount:        couponConf.Conf.GetDiscount(),
                UsageLimitType:  couponConf.Conf.GetUsageLimitType(),
                UsageLimitText:  couponConf.Conf.GetUsageLimitText(),
                ConfId:          couponConf.ID,
                EffectTime:      uint32(elems.EffectTime.Unix()),
                ExpireTime:      uint32(elems.ExpireTime.Unix()),
                MustInCoachPool: couponConf.Conf.GetMustInCoachPool(),
            })
        }
    }
    // 按最先生效/价值最大排序
    sort.Slice(couponList, func(i, j int) bool {
        if couponList[i].EffectTime == couponList[j].EffectTime {
            return couponList[i].ReducePrice > couponList[j].ReducePrice
        }
        return couponList[i].EffectTime < couponList[j].EffectTime
    })
    out.CouponList = couponList

    // 发放优惠券
    gainErr = m.mysqlStore.BatInsertEsportCoupon(ctx, in.Uid, couponRemain)
    if gainErr != nil {
        log.ErrorWithCtx(ctx, "AutoGrantCoupon(preCheck=%t) fail, BatInsertEsportCoupon err: %v", in.PreCheck, gainErr)
        return out, gainErr
    }

    goroutineex.GoroutineWithTimeoutCtx(ctx, 5*time.Second, func(ctx context.Context) {
        // 删除券缓存
        m.cache.DelUserCoupon(ctx, in.Uid)
        // 优惠券变更推送
        m.NotifyCouponChange(ctx, in.Uid)
        // 推送电竞助手
        m.tradeImNotify.SendOfficialAccountMsg(ctx, "esport_coupon_gain", in.Uid, &imApiPb.Text{
            Content:   fmt.Sprintf("您已获得%d张电竞专区优惠券，下单时会自动优惠减免，快去下单试试吧~", len(couponList)),
            Highlight: "快去下单试试吧~",
            Url:       "tt://m.52tt.com/esport_zone?priority_home=true",
        })
    })

    return out, nil
}

func (m *Mgr) checkRiskAutoGrantCoupon(ctx context.Context, baseReq *app.BaseReq, amount, guildId uint32, orderId, sourceType string) (bool, error) {
    var ctxCancel func()
    ctx, ctxCancel = protogrpc.NewContextWithInfoTimeout(ctx, 500*time.Millisecond)
    defer ctxCancel()

    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        return false, fmt.Errorf("get service info failed")
    }

    guildResp, err := m.rpcCli.GuildCli.GetGuild(ctx, guildId)
    if err != nil {
        log.WarnWithCtx(ctx, "get guild info failed, guildId: %d, err: %w", guildId, err)
    }

    // 查询公会类型
    guildCooperationInfos, sErr := m.rpcCli.GuildCopeCli.GetGuildCooperationInfos(ctx, &guild_cooperation.GetGuildCooperationInfosReq{
        GuildId: guildId,
    })
    if sErr != nil {
        log.WarnWithCtx(ctx, "GetGuildCooperationInfos, uid:%d, orderId:%s, err:%v", serviceInfo.UserID, orderId, err)
    }
    guildType := make([]string, 0, 3)
    if guildCooperationInfos.GetIsAmuseCoopGuild() {
        guildType = append(guildType, "1")
    }
    if guildCooperationInfos.GetIsYuyinCoopGuild() {
        guildType = append(guildType, "2")
    }
    if guildCooperationInfos.GetIsEsportCoopGuild() {
        guildType = append(guildType, "3")
    }

    checkReq := &riskMngApiPb.CheckReq{
        Scene: "ESPORT_SEND_COUPON",
        SourceEntity: &riskMngApiPb.Entity{
            Uid: serviceInfo.UserID,
        },
        CustomParams: map[string]string{
            "source_type": sourceType,
            "guild_id":    fmt.Sprintf("%d", guildId),
            "guild_name":  guildResp.GetName(),
            "guild_type":  strings.Join(guildType, ","),
            "activity_id": "ESPORT_SEND_COUPON",
            "order_id":    orderId,
            "amount":      fmt.Sprintf("%d", amount/100),
        },
    }
    checkResp, err := m.rpcCli.RiskMngApiCli.CheckHelper(ctx, checkReq, baseReq)
    if err != nil {
        // 系统错误，风控非关键路径，可忽略系统错误
        log.ErrorWithCtx(ctx, "checkRiskAutoGrantCoupon risk-mng-api.Check failed, req:%+v, err: %v", checkReq, err)
        return true, nil
    }
    // 打个 info 拦截日志，方便排查，风控拦截日志不会很多
    log.InfoWithCtx(ctx, "checkRiskAutoGrantCoupon risk-mng-api.Check hit, req:%+v, resp:%+v", checkReq, checkResp)
    // 命中风控不拦截
    if checkResp.ErrCode >= 0 {
        return true, nil
    } else {
        return false, fmt.Errorf("risk-mng-api.Check hit, errCode: %d, errMsg: %s", checkResp.ErrCode, checkResp.ErrMsg)
    }
}
