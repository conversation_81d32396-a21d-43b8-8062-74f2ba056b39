package server

import (
    "context"
    "encoding/json"
    "golang.52tt.com/protocol/services/esport_internal"
    "golang.52tt.com/protocol/services/esport_rcmd"
    "golang.52tt.com/services/tt-rev/esport/common/collection/list"
    "golang.52tt.com/services/tt-rev/esport/common/collection/transform"
    "golang.52tt.com/services/tt-rev/esport/esport-logic/internal/mgr"
    "sort"
    "strings"
    "sync"
    "time"

    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app"
    "golang.52tt.com/protocol/app/esport_logic"
    "golang.52tt.com/protocol/common/status"
    esport_skill "golang.52tt.com/protocol/services/esport-skill"
    esport_statistics "golang.52tt.com/protocol/services/esport-statistics"
    esport_trade "golang.52tt.com/protocol/services/esport-trade"
    "golang.52tt.com/protocol/services/esport_hall"
    "golang.52tt.com/protocol/services/esport_role"
    "golang.52tt.com/services/tt-rev/esport/common"
    "google.golang.org/grpc/codes"
)

func (s *Server) GetHomePageSkillProductList(ctx context.Context, request *esport_logic.GetHomePageSkillProductListRequest) (*esport_logic.GetHomepageSkillProductListResponse, error) {
    resp := &esport_logic.GetHomepageSkillProductListResponse{}

    serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
    uid := request.GetUid()
    if uid == 0 {
        uid = serviceInfo.UserID
    }
    playerUid := serviceInfo.UserID
    if request.GetPlayerUid() != 0 {
        playerUid = request.GetPlayerUid()
    }
    if !s.checkSwitch(ctx, serviceInfo.UserID, request.GetUid(), true, true) {
        log.WarnWithCtx(ctx, "GetHomePageSkillProductList fail. req: +%v, err: switch close", request)
        return resp, nil
    }

    roleResp, err := s.eSportRoleService.GetUserESportRole(ctx, &esport_role.GetUserESportRoleReq{
        Uid: uid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetHomePageSkillProductList GetUserESportRole, uid: %d, err: %v", uid, err)
        return resp, err
    }

    resp.EsportCoachType = roleResp.GetEsportRole() // 不是电竞指导提前返回
    if resp.EsportCoachType == uint32(esport_logic.EsportCoachType_ESPORT_COACH_TYPE_UNSPECIFIED) {
        return resp, nil
    }

    guildInfo, err := s.guildCli.GetGuild(ctx, roleResp.GetGuildId())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetHomePageSkillProductList GetGuild, guildId: %d, err: %v", roleResp.GetGuildId(), err)
    }

    guildId := guildInfo.GetGuildId()
    if guildInfo.GetShortId() != 0 {
        guildId = guildInfo.GetShortId()
    }
    resp.GuildInfo = &esport_logic.GuildInfo{
        GuildId: guildId,
        Name:    guildInfo.GetName(),
    }

    resp.GodPageUrl = s.bc.GetGodPageUrl()
    resp.GodPageUrlWithMission = s.bc.GetGodPageUrlWithMission()
    resp.ProductList, err = s.getSkillProductList(ctx, uid, playerUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetHomePageSkillProductList getSkillProductList, uid: %d, err: %v", uid, err)
        return resp, err
    }

    coachLabelResp, err := s.eSportRoleService.BatchGetCoachLabel(ctx, &esport_role.BatchGetCoachLabelRequest{
        UidList: []uint32{uid},
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetHomePageSkillProductList failed to BatchGetCoachLabel, uid:%d, err:%v", uid, err)
        // 电竞标识错误不返回
    }
    resp.CoachLabelList, _, _, _ = s.getCoachLabelInfo(ctx, uid, 0)

    if coachLabelResp.GetCoachLabelMap() != nil {
        if label, ok := coachLabelResp.GetCoachLabelMap()[uid]; ok {
            resp.CoachLabel = &esport_logic.CoachLabel{
                Type:      esport_logic.LabelSourceType(label.GetType()),
                SourceUrl: label.GetSourceUrl(),
            }
            resp.CoachLabelList = append([]string{label.GetSourceUrl()}, resp.CoachLabelList...)
        }
    }
    return resp, nil
}

func (s *Server) getSkillProductList(ctx context.Context, uid, playerUid uint32) ([]*esport_logic.SkillProduct, error) {
    rs := make([]*esport_logic.SkillProduct, 0, 8)

    data, err := s.eSportHallService.GetVisibleSkillProductList(ctx, &esport_hall.GetVisibleSkillProductListRequest{
        Uid: uid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "getSkillProductList GetVisibleSkillProductList, uid: %d, err: %v", uid, err)
        return rs, err
    }

    gameIds := make([]uint32, 0, len(data.GetProductList()))
    for _, item := range data.GetProductList() {
        gameIds = append(gameIds, item.GetGameId())
    }
    if len(gameIds) == 0 {
        log.WarnWithCtx(ctx, "getSkillProductList GetVisibleSkillProductList, uid: %d, err: no visible skill")
        return rs, nil
    }
    gameConfigList, err := s.eSportSkillService.GetGameDetailByIds(ctx, &esport_skill.GetGameDetailByIdsRequest{
        GameIds: gameIds,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "getSkillProductList GetGameDetailByIds, gameIds: %+v, err: %v", gameIds, err)
        return rs, err
    }
    gameConfigMap := make(map[uint32]*esport_skill.EsportGameConfig) // 游戏配置
    for _, item := range gameConfigList.GetConfigList() {
        gameConfigMap[item.GetGameId()] = item
    }
    // 按游戏配置排序
    sort.Slice(data.ProductList, func(i, j int) bool {
        if gameConfigMap[data.ProductList[i].GetGameId()].GetGameType() !=
            gameConfigMap[data.ProductList[j].GetGameId()].GetGameType() {
            return gameConfigMap[data.ProductList[i].GetGameId()].GetGameType() < gameConfigMap[data.ProductList[j].GetGameId()].GetGameType()
        }
        return gameConfigMap[data.ProductList[i].GetGameId()].GetGameRank() < gameConfigMap[data.ProductList[j].GetGameId()].GetGameRank()
    })

    // 查询单数
    coachStat, err := s.eSportStatCli.GetCoachAllStatistics(ctx, &esport_statistics.GetCoachAllStatisticsRequest{
        Uid: uid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "getSkillProductList GetCoachAllStatistics, uid: %d, err: %v", uid, err)
        return rs, err
    }

    skillPricingResp, err := s.esportInternalCli.BatchGetSkillPricingInfo(ctx, &esport_internal.BatchGetSkillPricingInfoRequest{
        Uid:         playerUid,
        CoachUid:    uid,
        GameId:      gameIds,
        BuyAmount:   1,
        QueryOption: nil,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "getSkillProductList BatchGetSkillPricingInfo err: %v", err)
        return rs, err
    }
    priceMap := skillPricingResp.GetPriceMap()

    for _, item := range data.GetProductList() {
        gameConfig, ok := gameConfigMap[item.GetGameId()]
        if !ok {
            log.WarnWithCtx(ctx, "GetVisibleSkillProductList no game: %d", item.GetGameId())
            continue
        }

        // 查询评分
        esResp, err := s.orderService.GetEvaluateSummary(ctx, &esport_trade.GetEvaluateSummaryRequest{
            Uid:    item.Uid,
            GameId: item.GameId,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "getSkillProductList GetEvaluateSummary, uid: %d, gameId: %d, err: %v", item.Uid, item.GameId, err)
        }

        // 大神标识
        _, skillLabel, skillLabelUrl, famousPlayer := s.getCoachLabelInfo(ctx, item.Uid, item.GameId)

        rsItem := &esport_logic.SkillProduct{
            Id:                uint64(item.GetId()),
            Tag:               item.Tag,
            GameId:            item.GetGameId(),
            Name:              gameConfig.GetName(),
            Icon:              gameConfig.GetGameIcon(),
            Background:        gameConfig.GetGameBackground(),
            BackgroundColor:   gameConfig.GetGameColor(),
            Score:             esResp.GetSummary().GetScoreInfo().GetAvgScore(),
            Price:             &esport_logic.PriceInfo{},
            OrderNum:          coachStat.GetGameDataMap()[item.GameId].GetOrderNum(),
            IsFamousPlayer:    famousPlayer,
            SkillLabelList:    skillLabel,
            GuaranteeWinText:  common.GetGranteeWinText(item.GetIsGuaranteeWin(), item.GetGuaranteeWinTexts()),
            SkillLabelUrlList: skillLabelUrl,
        }
        if info, ok := priceMap[item.GetGameId()]; ok {
            rsItem.Price.Price = info.GetPrice()
            rsItem.Price.PriceUnit = info.GetUnit()
            rsItem.Price.MeasureCnt = info.GetMeasureCnt()
            rsItem.Price.MeasureUnit = info.GetMeasureUnit()
            rsItem.Price.MaxOrderCnt = item.GetMaxOrderCnt()
            rsItem.Price.HasFirstRoundDiscount = false
            rsItem.Price.FirstRoundPrice = 0
            rsItem.Price.HasDiscount = false
            rsItem.Price.DiscountPrice = 0
            rsItem.Price.DiscountType = 0
            rsItem.Price.DiscountDesc = ""
            if len(info.GetDiscountList()) > 0 {
                // 因为目前业务只允许一个优惠，所以直接取第一个
                // 判断用户使用的客户端版本号，如果不是符合新客价的版本，并且有新客价优惠，则使用顺位第二的优惠
                discountInfo := info.GetDiscountList()[0]
                rsItem.Price.HasDiscount = true
                rsItem.Price.DiscountPrice = discountInfo.GetPrice()
                rsItem.Price.DiscountType = uint32(discountInfo.GetType())
                rsItem.Price.DiscountDesc = discountInfo.GetDesc()
                // 额外处理原来的逻辑
                switch discountInfo.GetType() {
                case esport_internal.DiscountType_DISCOUNT_TYPE_FIRST_ORDER:
                    rsItem.Price.HasFirstRoundDiscount = true
                    rsItem.Price.FirstRoundPrice = discountInfo.GetPrice()
                case esport_internal.DiscountType_DISCOUNT_TYPE_NEW_CUSTOMER:
                    rsItem.Price.MaxOrderCnt = 1
                }
            }
        }

        rs = append(rs, rsItem)
    }

    return rs, nil
}

func (s *Server) GetInviteOrderRecommend(ctx context.Context, request *esport_logic.GetInviteOrderRecommendRequest) (*esport_logic.GetInviteOrderRecommendResponse, error) {
    resp := &esport_logic.GetInviteOrderRecommendResponse{}

    serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
    productList, err := s.getSkillProductList(ctx, serviceInfo.UserID, request.GetInviteUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetInviteOrderRecommend getSkillProductList, uid: %d, err: %v", serviceInfo.UserID, err)
        return resp, err
    }

    targetUserMostLike, err := s.eSportStatCli.GetUserMostPayGame(ctx, &esport_statistics.GetUserMostPayGameRequest{
        Uid: request.GetInviteUid(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetInviteOrderRecommend GetUserMostPayGame, uid: %d, err: %v", request.GetInviteUid(), err)
        return resp, err
    }
    for _, item := range productList {
        if item.GetGameId() == targetUserMostLike.GetGameId() {
            resp.Product = item
            return resp, nil
        }
    }

    if len(productList) > 0 {
        resp.Product = productList[0]
    }

    return resp, nil
}

func (s *Server) GetEsportAreaCoachList(ctx context.Context, req *esport_logic.GetEsportAreaCoachListRequest) (*esport_logic.GetEsportAreaCoachListResponse, error) {
    resp := &esport_logic.GetEsportAreaCoachListResponse{}
    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetEsportAreaCoachList ServiceInfoFromContext, err: get serviceInfo fail")
        return resp, nil
    }
    log.DebugWithCtx(ctx, "serviceInfo: %+v", serviceInfo)

    if !s.checkSwitch(ctx, serviceInfo.UserID, 0, true, false) {
        log.WarnWithCtx(ctx, "GetEsportAreaCoachList fail. req: +%v, err: switch close", req)
        return resp, nil
    }

    log.InfoWithCtx(ctx, "GetEsportAreaCoachList, req: %+v, resp: %+v", req, resp)

    // 获取大神基本信息
    coachInfoList, nextOffset, err := s.getCoachBaseInfoList(ctx, req, serviceInfo.UserID)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetEsportAreaCoachList getCoachBaseInfoList, req: %+v, err: %v", req, err)
        return resp, err
    }
    if len(coachInfoList) == 0 {
        log.WarnWithCtx(ctx, "GetEsportAreaCoachList getCoachBaseInfoList, req: %+v, no coach", req)
        return resp, nil
    }
    // 组装列表
    esportAreaCoachInfos, err := s.mgr.AssembleRecommendList(ctx, coachInfoList, req.GetGameId(), serviceInfo.UserID)

    if err != nil {
        log.ErrorWithCtx(ctx, "GetEsportAreaCoachList AssembleRecommendList err, req: %+v, err: %v", req, err)
        return resp, err
    }
    resp.CoachList = esportAreaCoachInfos
    resp.NextOffset = nextOffset

    if req.GetOffset() == 0 { // 第一页,返回详情页链接
        resp.CoachDetailUrl = s.bc.GetCoachDetailUrl()
    }

    log.InfoWithCtx(ctx, "GetEsportAreaCoachList, req: %+v, resp: %+v", req, resp)
    return resp, nil
}

// getCoachBaseInfoList 获取电竞专区的大神基本信息列表
func (s *Server) getCoachBaseInfoList(ctx context.Context, req *esport_logic.GetEsportAreaCoachListRequest, uid uint32) ([]*esport_logic.EsportAreaCoachInfo, uint32, error) {
    propertyList := toSvrGameProperty(req.GetSearchOptionList())

    switchResp, err := s.esportRcmdCli.GetNewRcmdSwitch(ctx, &esport_rcmd.GetNewRcmdSwitchRequest{
        Uid: uid,
    })
    if err != nil {
        log.WarnWithCtx(ctx, "getCoachBaseInfoList GetEsportAreaCoachList failed, req: %+v, err: %v", req, err)
    }
    sceneType := esport_rcmd.SceneType_SCENE_TYPE_ESPORT_AREA
    if req.GetFromSource() == uint32(esport_logic.AreaCoachListFromSource_AREA_COACH_LIST_FROM_SOURCE_ESPORT_TOP_TAB) {
        sceneType = esport_rcmd.SceneType_SCENE_TYPE_ESPORT_TAB
    }
    if switchResp.GetNewRcmdSwitch() {
        log.InfoWithCtx(ctx, "新推荐接口 getCoachBaseInfoList req: %+v", req)
        // 首页才需要推荐数据
        operationCoachUids := make([]uint32, 0)
        if req.GetOffset() == 0 {
            operationResp, err := s.eSportHallService.GetOperationCoachRecommendWithoutExposed(ctx, &esport_hall.GetOperationCoachRecommendWithoutExposedRequest{
                Uid:    uid,
                GameId: req.GetGameId(),
            })
            if err != nil {
                log.WarnWithCtx(ctx, "GetEsportAreaCoachList GetOperationCoachRecommendWithoutExposed req: %+v, err: %v", req, err)
            } else {
                operationCoachUids = transform.Map(operationResp.GetProductList(), func(item *esport_hall.GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem) uint32 {
                    return item.GetUid()
                })
            }
        }
        // 获取推荐数据，把运营推荐等等额外的uid传入，合并查询，特别是首页的场景
        rcmdResp, nextOffset, err := s.mgr.GetRcmdSkillProduct(ctx, uid, &esport_rcmd.GetEsportRcmdSkillProductReq{
            SceneType:    sceneType,
            SkillId:      req.GetGameId(),
            Offset:       req.GetOffset(),
            Limit:        req.GetLimit(),
            GameProperty: propertyList,
        }, operationCoachUids...)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetEsportAreaCoachList GetEsportRcmdSkillProduct req: %+v, err: %v", req, err)
            return []*esport_logic.EsportAreaCoachInfo{}, 0, err
        }
        // 如下调整排序的代码暂时没有用了，因为 GetRcmdSkillProduct 内部保证了额外传入的operationCoachUids会排在前面。如果后续有需求，可以再加上
        // 如果是有运营推荐的额外大神，则需要把运营推荐的抽取到前面，并且排序和operationCoachUids保持一致
        //if len(operationCoachUids) != 0 {
        //    // 建立一个operationCoachUids的排序索引map
        //    sortMap := mapz.ToMapG(operationCoachUids, func(i int, uid uint32) (uint32, int) {
        //        return uid, len(operationCoachUids) - i
        //    })
        //    sort.SliceStable(rcmdResp, func(i, j int) bool {
        //        iScore := sortMap[rcmdResp[i].GetUserProfile().GetUid()]
        //        jScore := sortMap[rcmdResp[j].GetUserProfile().GetUid()]
        //        return iScore >= jScore
        //    })
        //}
        return rcmdResp, nextOffset, nil
    } else {
        coachList, err := s.eSportHallService.GetGameCoachList(ctx, &esport_hall.GetGameCoachListRequest{
            GameId:       req.GetGameId(),
            Offset:       req.GetOffset(),
            Limit:        req.GetLimit(),
            PropertyList: propertyList,
            ReqSource:    uint32(esport_hall.GetGameCoachListRequest_REQ_SOURCE_ESPORT_AREA),
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "GetEsportAreaCoachList GetGameCoachList req: %+v, err: %v", req, err)
            return []*esport_logic.EsportAreaCoachInfo{}, coachList.GetNextOffset(), err
        }

        if len(coachList.GetCoachList()) == 0 {
            return []*esport_logic.EsportAreaCoachInfo{}, coachList.GetNextOffset(), err
        }

        return transform.Map(coachList.GetCoachList(), mgr.HallCoachInfoToLogicCoachInfoFn),
            req.GetOffset() + 1, nil
    }
}

func (s *Server) InviteOrder(ctx context.Context, request *esport_logic.InviteOrderRequest) (*esport_logic.InviteOrderResponse, error) {
    resp := &esport_logic.InviteOrderResponse{}
    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "InviteOrder, ServiceInfoFromContext err")
        return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    // 陪玩身份校验
    roleResp, err := s.eSportRoleService.GetUserESportRole(ctx, &esport_role.GetUserESportRoleReq{
        Uid: serviceInfo.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "InviteOrder.GetUserESportRole uid: %d, err: %v", serviceInfo.UserID, err)
        return resp, err
    }
    if roleResp.GetEsportRole() == uint32(esport_logic.EsportCoachType_ESPORT_COACH_TYPE_UNSPECIFIED) {
        return resp, protocol.NewExactServerError(codes.OK, status.ErrEsportsHallCantReceiveOrder)
    }

    // 拉黑判断
    blResp, blErr := s.userBlacklistCli.CheckIsInBlackList(ctx, serviceInfo.UserID, request.GetInviteUid())
    if blErr != nil {
        log.ErrorWithCtx(ctx, "InviteOrder CheckIsInBlackList, uid:%d, coachUid:%d, err:%v", request.GetInviteUid(), serviceInfo.UserID, blErr)
    }
    if blResp.GetBIn() {
        return resp, protocol.NewExactServerError(nil, status.ErrEsportsHallBlacklist, "您已拉黑对方，不可邀请下单")
    }

    blResp, blErr = s.userBlacklistCli.CheckIsInBlackList(ctx, request.GetInviteUid(), serviceInfo.UserID)
    if blErr != nil {
        log.ErrorWithCtx(ctx, "InviteOrder CheckIsInBlackList, uid:%d, coachUid:%d, err:%v", request.GetInviteUid(), serviceInfo.UserID, blErr)
    }
    if blResp.GetBIn() {
        return resp, protocol.NewExactServerError(nil, status.ErrEsportsHallBlacklist, "您已被对方拉黑，不可邀请下单")
    }

    // 未成年人判断
    realNameResp, err := s.TTCProxyClient.GetUserRealNameAuthInfoV2(ctx, uint64(request.GetInviteUid()), false, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "InviteOrder GetUserRealNameAuthInfoV2, uid:%d, err:%v", request.GetInviteUid(), err)
    }
    if !realNameResp.GetIsAdult() {
        return resp, protocol.NewExactServerError(codes.OK, status.ErrEsportsHallRiskControl, "对方不符合平台消费规定")
    }

    // 判断对方是否白名单用户
    res, err := s.eSportSkillService.GetSwitch(ctx, &esport_skill.GetSwitchRequest{Uid: request.GetInviteUid()})
    if err != nil {
        log.ErrorWithCtx(ctx, "InviteOrder failed to GetSwitch, req: %+v, err:%v", request, err)
    }
    if err == nil && res.GetSwitchStatus().GetMainSwitchStatus() == esport_skill.EsportSwitchStatus_SWITCH_STATUS_OFF {
        return resp, protocol.NewExactServerError(codes.OK, status.ErrEsportsHallInviteOrderInvalid, "不可邀请该用户下单")
    }

    // 查询产品信息
    productResp, err := s.eSportHallService.GetSkillProductInfo(ctx, &esport_hall.GetSkillProductInfoRequest{
        ProductId: uint32(request.GetSkillProductId()),
    })
    if err != nil {
        if protocol.IsErrorOf(err, status.ErrEsportsHallNoReceiveTime) {
            return resp, protocol.NewExactServerError(codes.OK, status.ErrEsportsHallNoReceiveTime, "当前不在接单时段，可修改接单时段后再邀请哦")
        }

        log.ErrorWithCtx(ctx, "GetSkillProductInfo, req: %+v, err: %v", request, err)
        return resp, err
    }
    product := productResp.GetProduct()

    // 判断单数
    if request.GetOrderCnt() > productResp.GetProduct().GetMaxOrderCnt() {
        return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "超过最大接单数")
    }

    gameDetail, err := s.eSportSkillService.GetGameDetailById(ctx, &esport_skill.GetGameDetailByIdRequest{
        GameId: productResp.GetProduct().GetGameId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetGameDetailById, gameId: %d, err: %v", productResp.GetProduct().GetGameId(), err)
        return resp, err
    }

    discountInfo := &esport_logic.OrderDiscountInfo{}
    if len(request.GetDiscountInfo()) > 0 {
        decodeErr := json.Unmarshal([]byte(request.GetDiscountInfo()), discountInfo)
        if decodeErr != nil {
            log.ErrorWithCtx(ctx, "fail to Unmarshal DiscountInfo:%s, err:%v", request.GetDiscountInfo(), decodeErr)
        }
    }

    // 检查是否能享受优惠
    newCustomerPrice := &esport_hall.NewCustomerPriceInfo{}
    firstRoundPrice := uint32(0)
    if discountInfo.GetUseNewCustomerDiscount() {
        var newCustomerRsp *esport_hall.GetNewCustomerPriceBySkillResponse
        newCustomerRsp, err = s.eSportHallService.GetNewCustomerPriceBySkill(ctx, &esport_hall.GetNewCustomerPriceBySkillRequest{
            PlayerUid: request.GetInviteUid(),
            CoachUid:  serviceInfo.UserID,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "GetNewCustomerPriceBySkill err:%v", err)
            return resp, err
        }
        newCustomerPrice = newCustomerRsp.GetPriceMap()[product.GameId]
        if newCustomerPrice == nil || !newCustomerPrice.GetHasNewCustomerDiscount() {
            return resp, protocol.NewExactServerError(nil, status.ErrEsportsOrderCannotPay, "新客优惠已更新，请确认后支付")
        }
        if request.GetOrderCnt() > 1 {
            return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "新客价只能下1局")
        }
    }
    if discountInfo.GetUseFirstRoundDiscount() {
        // 检查是否能享受首局优惠
        var rightRsp *esport_hall.CheckFirstRoundOrderRightResponse
        rightRsp, err = s.eSportHallService.CheckFirstRoundOrderRight(ctx, &esport_hall.CheckFirstRoundOrderRightRequest{
            PlayerUid:     request.GetInviteUid(),
            CoachUid:      serviceInfo.UserID,
            GameId:        productResp.GetProduct().GameId,
            CheckDayLimit: false,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "CheckFirstRoundOrderRight err:%v", err)
            return resp, err
        }
        firstRoundPrice = rightRsp.GetFirstRoundPrice()
    }

    // 构造订单价格信息
    priceInfo := &esport_hall.PriceInfo{
        Price:                 product.GetPrice(),
        PriceUnit:             "豆",
        MeasureCnt:            30,
        MeasureUnit:           "分钟",
        HasFirstRoundDiscount: discountInfo.GetUseFirstRoundDiscount(),
        FirstRoundPrice:       firstRoundPrice,
    }
    if product.GetUintType() == uint32(esport_skill.GAME_PRICING_UNIT_TYPE_GAME_PRICING_UNIT_TYPE_PER_GAME) {
        priceInfo.MeasureCnt = 1
        priceInfo.MeasureUnit = "局"
    }
    if discountInfo.GetUseNewCustomerDiscount() {
        priceInfo.HasDiscount = true
        priceInfo.DiscountPrice = newCustomerPrice.GetNewCustomerPrice()
        priceInfo.DiscountType = uint32(esport_logic.DiscountType_DISCOUNT_TYPE_NEW_CUSTOMER)
        priceInfo.DiscountDesc = "新客价"
    }
    if discountInfo.GetUseFirstRoundDiscount() {
        priceInfo.HasDiscount = true
        priceInfo.DiscountPrice = firstRoundPrice
        priceInfo.DiscountType = uint32(esport_logic.DiscountType_DISCOUNT_TYPE_FIRST_ROUND)
        priceInfo.DiscountDesc = "首局价"
    }

    s.eSportHallService.InviteOrder(ctx, &esport_hall.InviteOrderRequest{
        Uid:       serviceInfo.UserID,
        InviteUid: request.GetInviteUid(),
        ProductId: uint32(request.GetSkillProductId()),
        OrderCnt:  request.GetOrderCnt(),
        Comment:   request.OrderDesc,
        GameInfo: &esport_hall.GameInfo{
            Id:    gameDetail.GetConfig().GetGameId(),
            Name:  gameDetail.GetConfig().GetName(),
            Price: priceInfo,
            Icon:  gameDetail.GetConfig().GetGameIcon(),
            NewCustomerUseDetail: &esport_hall.NewCustomerUseDetail{
                UseNewCustomerDiscount: newCustomerPrice.GetHasNewCustomerDiscount(),
                NewCustomerPrice:       newCustomerPrice.GetNewCustomerPrice(),
                PlatBonusFee:           newCustomerPrice.GetPlatBonusFee(),
            },
        },
    })

    log.InfoWithCtx(ctx, "InviteOrder, req: %+v", request)
    return resp, nil
}

func (s *Server) HandleInviteOrder(c context.Context, request *esport_logic.HandleInviteOrderRequest) (*esport_logic.HandleInviteOrderResponse, error) {
    _, err := s.eSportHallService.HandleInviteOrder(c, &esport_hall.HandleInviteOrderRequest{
        InviteId: request.GetInviteId(),
        Status:   request.GetStatus(),
    })
    if err != nil {
        log.ErrorWithCtx(c, "HandleInviteOrder, req: %+v, err: %v", request, err)
    }

    log.InfoWithCtx(c, "HandleInviteOrder, req: %+v", request)
    return &esport_logic.HandleInviteOrderResponse{}, err
}

// ========================= internal =========================
func buildPriceInfo(price, maxOrderCnt uint32, unitType esport_skill.GAME_PRICING_UNIT_TYPE,
    firstRoundInfo *esport_hall.FirstRoundLabel, couponPriceInfo *esport_internal.CouponPriceResult) *esport_logic.PriceInfo {
    priceInfo := &esport_logic.PriceInfo{
        Price:                 price,
        PriceUnit:             "豆",
        MeasureCnt:            30,
        MeasureUnit:           "分钟",
        MaxOrderCnt:           maxOrderCnt,
        HasFirstRoundDiscount: firstRoundInfo.GetHasFirstRoundDiscount(),
        FirstRoundPrice:       firstRoundInfo.GetFirstRoundPrice(),
    }
    if unitType == esport_skill.GAME_PRICING_UNIT_TYPE_GAME_PRICING_UNIT_TYPE_PER_GAME {
        priceInfo.MeasureCnt = 1
        priceInfo.MeasureUnit = "局"
    }

    // 填充新的通用优惠信息
    if couponPriceInfo.GetHasPriceWithCoupon() {
        priceInfo.HasDiscount = true
        priceInfo.DiscountPrice = couponPriceInfo.GetPriceWithCoupon()
        priceInfo.DiscountType = uint32(esport_logic.DiscountType_DISCOUNT_TYPE_COUPON)
        priceInfo.DiscountDesc = "券后价"
    }
    if firstRoundInfo.GetHasFirstRoundDiscount() {
        priceInfo.HasDiscount = true
        priceInfo.DiscountPrice = firstRoundInfo.GetFirstRoundPrice()
        priceInfo.DiscountType = uint32(esport_logic.DiscountType_DISCOUNT_TYPE_FIRST_ROUND)
        priceInfo.DiscountDesc = "首局价"
    }
    return priceInfo
}

func tmpRemoveResourcePrefix(url string) string {
    idx := strings.LastIndex(url, "https://")

    if idx >= 0 && idx < len(url) {
        return url[idx:]
    }
    return url
}

func (s *Server) getUserProfileWithDyHead(ctx context.Context, uidList []uint32) (map[uint32]*app.UserProfile, error) {
    userprofileMap, err := s.userProfileCli.BatchGetUserProfileV2(ctx, uidList, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUserProfile uidList: %+v, err: %v", uidList, err)
        return nil, err
    }

    dyHeadMap, tErr := s.headDynamicImageCli.GetHeadDynamicImageMd5(ctx, uidList)
    if tErr != nil {
        log.ErrorWithCtx(ctx, "GetHeadDynamicImageMd5 uidList: %+v, err: %v", uidList, tErr)
        return nil, tErr
    }

    usernames := make([]string, 0, len(userprofileMap))
    invokeUid := uint32(0)
    for _, item := range userprofileMap {
        if invokeUid == 0 {
            invokeUid = item.GetUid()
        }
        usernames = append(usernames, item.GetAccount())
    }

    headImgMap, err := s.headImageCli.BatchGetHeadImageMd5(ctx, invokeUid, usernames)
    if err != nil {
        log.ErrorWithCtx(ctx, "getUserProfileWithDyHead.BatchGetHeadImageMd5, uidList: %+v, err: %v", uidList, err)
    }

    for _, item := range userprofileMap {
        item.HeadImgMd5 = headImgMap[item.GetAccount()]
        item.HeadDyImgMd5 = dyHeadMap[item.Uid]
    }

    return userprofileMap, nil
}

func toSvrGameProperty(propertyList []*esport_logic.GameProperty) []*esport_hall.GameProperty {
    svrPropertyList := make([]*esport_hall.GameProperty, 0, len(propertyList))
    for _, item := range propertyList {
        valList := make([]*esport_hall.GamePropertyVal, 0, len(item.GetValList()))
        for _, val := range item.GetValList() {
            valList = append(valList, &esport_hall.GamePropertyVal{
                Id:   val.Id,
                Name: val.GetName(),
            })
        }
        propertyType := item.GetPropertyType()
        if item.GetPropertyType() == uint32(esport_logic.GameProperty_PROPERTY_TYPE_GUARANTEE_WIN) {
            propertyType = uint32(esport_logic.GameProperty_PROPERTY_TYPE_CUSTOM)
        }
        svrPropertyList = append(svrPropertyList, &esport_hall.GameProperty{
            Id:           item.GetId(),
            Name:         item.GetName(),
            ValList:      valList,
            PropertyType: propertyType,
            SelectType:   item.GetSelectType(),
            Expose:       item.GetExpose(),
        })
    }
    return svrPropertyList
}

func (s *Server) getCoachLabelInfo(ctx context.Context, uid, gameId uint32) ([]string, []string, []string, bool) {
    coachLabel := make([]string, 0, 8)
    skillLabel := make([]string, 0, 8)
    skillLabelUrl := make([]string, 0, 8)
    famousPlayer := false
    coachLabelResp, err := s.eSportSkillService.BatchGetCoachLabelsForGame(ctx, &esport_skill.BatchGetCoachLabelsForGameRequest{
        GameId:   gameId,
        CoachIds: []uint32{uid},
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "getCoachLabelInfo BatchGetCoachLabelsForGame, uid: %d, gameId: %d, err: %v", uid, gameId, err)
    }

    if len(coachLabelResp.GetLabelList()) > 0 {
        coachLabelInfo := coachLabelResp.GetLabelList()[0]
        coachLabelOriginList, ok := coachLabelInfo.GetLabelMap()[uint32(esport_skill.LabelType_LABEL_TYPE_COACH)]
        if ok {
            for _, item := range coachLabelOriginList.GetLabelList() {
                coachLabel = append(coachLabel, item.GetLabelImage())
            }
        }
        skillLabelOriginList, ok := coachLabelInfo.GetLabelMap()[uint32(esport_skill.LabelType_LABEL_TYPE_SKILL)]
        if ok {
            for _, item := range skillLabelOriginList.GetLabelList() {
                skillLabel = append(skillLabel, item.GetLabelName())
                if len(item.GetLabelImage()) != 0 {
                    skillLabelUrl = append(skillLabelUrl, item.GetLabelImage())
                }
            }
        }
        famousPlayer = coachLabelInfo.GetIsRenowned()
    }
    return coachLabel, skillLabel, skillLabelUrl, famousPlayer
}

func (s *Server) batchGetCoachLabelInfo(ctx context.Context, gameId uint32, uidList []uint32) (map[uint32][]string, map[uint32][]string, map[uint32][]string, map[uint32]bool) {
    coachLabelMap := make(map[uint32][]string)
    skillLabelMap := make(map[uint32][]string)
    skillLabelUrlMap := make(map[uint32][]string)
    famousPlayerMap := make(map[uint32]bool)

    coachLabelResp, err := s.eSportSkillService.BatchGetCoachLabelsForGame(ctx, &esport_skill.BatchGetCoachLabelsForGameRequest{
        GameId:   gameId,
        CoachIds: uidList,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "batchGetCoachLabelInfo BatchGetCoachLabelsForGame, uidList: %+v, gameId: %d, err: %v", uidList, gameId, err)
        return coachLabelMap, skillLabelMap, skillLabelUrlMap, famousPlayerMap
    }

    for _, item := range coachLabelResp.GetLabelList() {
        coachLabelOriginList := item.GetLabelMap()[uint32(esport_skill.LabelType_LABEL_TYPE_COACH)].GetLabelList()
        coachLabel := make([]string, 0, len(coachLabelOriginList))
        for _, coachLabelItem := range coachLabelOriginList {
            coachLabel = append(coachLabel, coachLabelItem.GetLabelImage())
        }
        skillLabelOriginList := item.GetLabelMap()[uint32(esport_skill.LabelType_LABEL_TYPE_SKILL)].GetLabelList()
        skillLabel := make([]string, 0, len(skillLabelOriginList))
        skillLabelUrl := make([]string, 0, len(skillLabelOriginList))
        for _, skillLabelItem := range skillLabelOriginList {
            skillLabel = append(skillLabel, skillLabelItem.GetLabelName())
            if len(skillLabelItem.GetLabelImage()) != 0 {
                skillLabelUrl = append(skillLabelUrl, skillLabelItem.GetLabelImage())
            }
        }
        coachLabelMap[item.GetCoachId()] = coachLabel
        skillLabelMap[item.GetCoachId()] = skillLabel
        skillLabelUrlMap[item.GetCoachId()] = skillLabelUrl
        famousPlayerMap[item.GetCoachId()] = item.GetIsRenowned()
    }

    return coachLabelMap, skillLabelMap, skillLabelUrlMap, famousPlayerMap
}

func (s *Server) batGetEvaluateScoreSummary(ctx context.Context, gameId uint32, uidList []uint32) map[uint32]float32 {
    essMap := make(map[uint32]float32)
    essResp, err := s.orderService.BatGetEvaluateScoreSummary(ctx, &esport_trade.BatGetEvaluateScoreSummaryRequest{
        UidList: uidList,
        GameId:  gameId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "batGetEvaluateScoreSummary BatGetEvaluateScoreSummary, gameId: %d, uidList: %+v, err: %v", gameId, uidList, err)
        return essMap
    }

    for _, item := range essResp.GetSummaryList() {
        essMap[item.GetUid()] = item.GetAvgScore()
    }
    return essMap
}

func (s *Server) batGetUserOnline(ctx context.Context, uidList []uint32) (map[uint32]uint32, error) {
    rs := make(map[uint32]uint32)
    for _, uid := range uidList {
        rs[uid] = uint32(esport_logic.EsportAreaCoachInfo_ONLINE_STATUS_OFFLINE)
    }

    userPres, err := s.presenceV2Cli.BatchGetUserPres(ctx, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "batGetUserOnline, uidList: %+v, err: %v", uidList, err)
        return rs, err
    }

    for _, userPre := range userPres {
        if len(userPre.GetInfoList()) > 0 {
            rs[userPre.GetInfoList()[0].GetKey().GetUserId()] = uint32(esport_logic.EsportAreaCoachInfo_ONLINE_STATUS_ONLINE)
        }
    }

    return rs, nil
}

func (s *Server) ReportExposeCoach(ctx context.Context, request *esport_logic.ReportExposeCoachRequest) (*esport_logic.ReportExposeCoachResponse, error) {
    resp := &esport_logic.ReportExposeCoachResponse{}
    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "ReportExposeCoach fail, err: get serviceInfo fail")
        return resp, nil
    }
    log.InfoWithCtx(ctx, "ReportExposeCoach, uid: %d, gameId: %d, exposeCoachList: %+v", serviceInfo.UserID, request.GetGameId(), request.GetExposeCoachList())

    _, err := s.eSportHallService.ReportExposeCoach(ctx, &esport_hall.ReportExposeCoachRequest{
        Uid:             serviceInfo.UserID,
        ExposeCoachList: request.GetExposeCoachList(),
        GameId:          request.GetGameId(),
        ExposeType:      request.GetExposeType(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "ReportExposeCoach fail, request: %+v, err: %d", request, err)
    }
    return resp, err
}

func (s *Server) GetEsportAreaTopBannerList(ctx context.Context, request *esport_logic.GetEsportAreaTopBannerListRequest) (*esport_logic.GetEsportAreaTopBannerListResponse, error) {
    resp := &esport_logic.GetEsportAreaTopBannerListResponse{}

    conf := s.bc.GetEsportAreaTopBannerConf()
    for _, item := range conf {
        if len(item.ActiveTime) != 0 { // 非空为限时生效
            pass := false

            for _, atItem := range item.ActiveTime {
                activeTimePart := strings.Split(atItem, "-")
                if len(activeTimePart) != 2 {
                    log.WarnWithCtx(ctx, "GetEsportAreaTopBannerList, invalid activeTime: %s", item.ActiveTime)
                    continue
                }
                dayFlag := time.Now().Format("20060102")
                // 判断激活时间
                activeTimeStart, err := time.ParseInLocation("200601021504", dayFlag+activeTimePart[0], time.Local)
                if err != nil {
                    log.WarnWithCtx(ctx, "GetEsportAreaTopBannerList, invite activeTimeStart, err: %v", err)
                    continue
                }
                activeTimeEnd, err := time.ParseInLocation("200601021504", dayFlag+activeTimePart[1], time.Local)
                if err != nil {
                    log.WarnWithCtx(ctx, "GetEsportAreaTopBannerList, invite activeTimeEnd, err: %v", err)
                    continue
                }

                if activeTimeStart.After(activeTimeEnd) || activeTimeStart.Equal(activeTimeEnd) {
                    activeTimeEnd = activeTimeEnd.AddDate(0, 0, 1) // 跨天
                }

                now := time.Now()
                if activeTimeStart.Before(now) && activeTimeEnd.After(now) {
                    pass = true
                    break
                }
            }

            if !pass {
                continue
            }

        }

        resp.BannerList = append(resp.BannerList, &esport_logic.TopBannerInfo{
            Title:              item.Title,
            SubTitle:           item.SubTitle,
            Sort:               item.Sort,
            BackgroundIcon:     item.BackgroundIcon,
            OperationIcon:      item.OperationIcon,
            JumpLink:           item.JumpLink,
            BackgroundImg:      item.BackgroundImg,
            BackgroundImgSmall: item.BackgroundImgSmall,
            CollapseItemBg:     item.CollapseItemBg,
        })
    }

    sort.Slice(resp.BannerList, func(i, j int) bool {
        return resp.BannerList[i].Sort < resp.BannerList[j].Sort
    })

    return resp, nil
}

// GetBackRecallReCoach 获取新用户退出挽留展示的推荐大神（复用专区列表的结构）
func (s *Server) GetBackRecallReCoach(ctx context.Context, req *esport_logic.GetBackRecallReCoachRequest) (*esport_logic.GetBackRecallReCoachResponse, error) {
    resp := &esport_logic.GetBackRecallReCoachResponse{}
    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetBackRecallReCoach ServiceInfoFromContext, err: get serviceInfo fail")
        return resp, nil
    }

    if !s.checkSwitch(ctx, serviceInfo.UserID, 0, true, false) {
        log.WarnWithCtx(ctx, "GetBackRecallReCoach fail. req: +%v, err: switch close", req)
        return resp, nil
    }

    // 检查这个功能的开关有没有打开
    if !s.bc.GetBackRecallConf().IsShowBackRecallReCoach {
        return resp, nil
    }

    // 检查当前用户近90天有没有下过单
    list, err := s.eSportStatCli.GetUserOrderCoachList(ctx, &esport_statistics.GetUserOrderCoachListRequest{
        Uid:       serviceInfo.UserID,
        StartTime: time.Now().AddDate(0, 0, -90).Unix(),
        EndTime:   time.Now().Unix(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetBackRecallReCoach GetUserOrderCoachList, req: %+v, err: %v", req, err)
        return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "")
    }
    if len(list.GetCoachOrderTimeMap()) != 0 {
        log.InfoWithCtx(ctx, "GetBackRecallReCoach GetUserOrderCoachList, req: %+v, user(%d) 近90天下过单", req, serviceInfo.UserID)
        return resp, nil
    }

    log.InfoWithCtx(ctx, "GetBackRecallReCoach, req: %+v, resp: %+v", req, resp)

    // 获取推荐数据，把运营推荐等等额外的uid传入，合并查询，特别是首页的场景
    coachInfoList, _, err := s.mgr.GetRcmdSkillProduct(ctx, serviceInfo.UserID, &esport_rcmd.GetEsportRcmdSkillProductReq{
        SceneType: esport_rcmd.SceneType_SCENE_TYPE_ESPORT_BACK_RECALL,
        Offset:    0,
        Limit:     1,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetBackRecallReCoach GetEsportRcmdSkillProduct req: %+v, err: %v", req, err)
        return resp, err
    }

    if len(coachInfoList) == 0 {
        log.WarnWithCtx(ctx, "GetBackRecallReCoach getCoachBaseInfoList, req: %+v, no coach", req)
        return resp, nil
    }

    // 只要第一个
    coachInfoList = coachInfoList[:1]

    // 组装列表
    esportAreaCoachInfos, err := s.mgr.AssembleRecommendList(ctx, coachInfoList, coachInfoList[0].GetGameId(), serviceInfo.UserID)

    if err != nil {
        log.ErrorWithCtx(ctx, "GetBackRecallReCoach AssembleRecommendList err, req: %+v, err: %v", req, err)
        return resp, err
    }

    resp.Coach = esportAreaCoachInfos[0]
    resp.Switch = true
    resp.AutoPlayAudio = s.bc.GetBackRecallConf().IsAutoPlayAudio
    resp.Title = s.bc.GetBackRecallConf().Title

    log.InfoWithCtx(ctx, "GetBackRecallReCoach, req: %+v, resp: %+v", req, resp)
    return resp, nil
}

// GetNewCustomerTabSetting 获取专区新用户承接弹出页的数据
func (s *Server) GetNewCustomerTabSetting(ctx context.Context, request *esport_logic.GetNewCustomerTabSettingRequest) (*esport_logic.GetNewCustomerTabSettingResponse, error) {
    resp := &esport_logic.GetNewCustomerTabSettingResponse{
        Games:  make(map[int32]*esport_logic.GetNewCustomerTabSettingResponse_GameList),
        Videos: make(map[int32]*esport_logic.GetNewCustomerTabSettingResponse_Video),
    }
    defer func() {
        log.DebugWithCtx(ctx, "GetNewCustomerTabSetting, req: %+v, resp: %+v", request, resp)
    }()

    for k, v := range s.bc.GetXinYongHuChengJieConf().VideoMap {
        resp.Videos[k] = &esport_logic.GetNewCustomerTabSettingResponse_Video{
            Url: v.VideoUrl,
            Md5: v.Md5,
        }
    }

    // 并发获取不同类型的游戏的列表
    var mutex sync.Mutex
    list.ConcurrentForEach([]esport_logic.EsportGameType{
        esport_logic.EsportGameType_ESPORT_GAME_TYPE_MOBILE,
        esport_logic.EsportGameType_ESPORT_GAME_TYPE_PC,
    }, func(index int, item esport_logic.EsportGameType) {
        gameListResponse, err := s.eSportSkillService.GetGameList(ctx, &esport_skill.GetGameListRequest{
            GameType: esport_skill.GAME_TYPE(item),
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "GetNewCustomerTabSetting GetGameList, err: %v, game_type: %v", err, item)
            return
        }
        gameList := transform.Map(gameListResponse.GetItemList(), func(item *esport_skill.GameItem) *esport_logic.GameItem {
            return &esport_logic.GameItem{
                GameId:   item.GetGameId(),
                GameName: item.GetGameName(),
                GameIcon: item.GetGameIcon(),
            }
        })
        mutex.Lock()
        defer mutex.Unlock()
        resp.Games[int32(item)] = &esport_logic.GetNewCustomerTabSettingResponse_GameList{
            ItemList: gameList,
        }
    })

    return resp, nil
}

// PostNewCustomerTabSetting 提交专区新用户承接弹出页的数据
func (s *Server) PostNewCustomerTabSetting(ctx context.Context, request *esport_logic.PostNewCustomerTabSettingRequest) (*esport_logic.PostNewCustomerTabSettingResponse, error) {
    resp := &esport_logic.PostNewCustomerTabSettingResponse{}
    defer func() {
        log.DebugWithCtx(ctx, "PostNewCustomerTabSetting, req: %+v, resp: %+v", request, resp)
    }()

    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "PostNewCustomerTabSetting ServiceInfoFromContext, err: get serviceInfo fail")
        return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少用户信息")
    }

    _, err := s.eSportHallService.SaveUserTopGamePreSelectConfig(ctx, &esport_hall.SaveUserTopGamePreSelectConfigRequest{
        Uid:        serviceInfo.UserID,
        GameIdList: request.GetGameIds(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "PostNewCustomerTabSetting SaveUserTopGamePreSelectConfig, err: %v", err)
        return resp, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "保存错误")
    }
    return resp, nil
}
