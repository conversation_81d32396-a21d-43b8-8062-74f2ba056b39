package server

import (
    context "context"
    "encoding/hex"
    "errors"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/clients/account"
    auditTypes "golang.52tt.com/pkg/audit"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app"
    "golang.52tt.com/protocol/app/esport_logic"
    imPB "golang.52tt.com/protocol/app/im"
    errCode "golang.52tt.com/protocol/common/status"
    v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"
    esport_skill "golang.52tt.com/protocol/services/esport-skill"
    esport_statiticsPb "golang.52tt.com/protocol/services/esport-statistics"
    "golang.52tt.com/protocol/services/esport_grab_order"
    "golang.52tt.com/protocol/services/esport_hall"
    riskMngApiPb "golang.52tt.com/protocol/services/risk-mng-api"
    "golang.52tt.com/services/tt-rev/common/goroutineex"
    trade_im_notify "golang.52tt.com/services/tt-rev/esport/common/trade-im-notify"
    "golang.52tt.com/services/tt-rev/esport/esport-logic/internal/conf"
    "google.golang.org/grpc/codes"
    "math"
    "strings"
    "time"
)

const (
    gender           = "性别"
    requestTitle     = "要求"
    male             = "男"
    female           = "女"
    moreRequestTitle = "更多要求"
)

func (s *Server) CheckIfCanPublishOneKeyFindCoach(c context.Context, request *esport_logic.CheckIfCanPublishOneKeyFindCoachRequest) (*esport_logic.CheckIfCanPublishOneKeyFindCoachResponse, error) {
    out := &esport_logic.CheckIfCanPublishOneKeyFindCoachResponse{}
    defer func() {
        log.DebugWithCtx(c, "CheckIfCanPublishOneKeyFindCoach success, request:%+v, out:%+v", request, out)
    }()

    info, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "CheckIfCanPublishOneKeyFindCoach ServiceInfoFromContext fail. in:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, errCode.ErrRequestParamInvalid, "获取服务上下文失败")
    }

    checkResp, err := s.esportGrabOrderCli.CheckIfCouldPublish(c, &esport_grab_order.CheckIfCouldPublishRequest{
        Uid: info.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(c, "CheckIfCouldPublish failed to call svr, req:%+v, err:%v", request, err)
        return out, err
    }
    out.Reason = checkResp.GetReason()
    return out, nil
}

func (s *Server) GetGlobalOneKeyFindCfg(c context.Context, request *esport_logic.GetGlobalOneKeyFindCfgRequest) (*esport_logic.GetGlobalOneKeyFindCfgResponse, error) {
    out := &esport_logic.GetGlobalOneKeyFindCfgResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetGlobalOneKeyFindCfg success, request:%+v, out:%+v", request, out)
    }()

    switchRsp, err := s.esportGrabOrderCli.GetSwitch(c, &esport_grab_order.GetSwitchRequest{})
    if err != nil {
        log.ErrorWithCtx(c, "GetGlobalOneKeyFindCfg GetSwitch err: %v", err)
        return out, err
    }
    out.MaxGrabUserCnt = switchRsp.GetMaxGrabUserCnt()

    cfg := s.bc.GetGrabOrderCfg()
    out.CancelReasonList = cfg.CancelReasonList
    out.FakeHeadList = cfg.FakeHeadImgList
    out.ReportClickImRequireSecond = cfg.ReportClickImRequireSecond
    out.ReportClickImRequireCount = cfg.ReportClickImRequireCount
    for _, item := range cfg.GrabOrderLottieList {
        out.LottieList = append(out.LottieList, &esport_logic.OneKeyFindLottie{
            Url:  item.Url,
            Md5:  item.Md5,
            Type: item.Type,
        })
    }
    return out, nil
}

func (s *Server) GetOneKeyFindCoachEntry(c context.Context, request *esport_logic.GetOneKeyFindCoachEntryRequest) (*esport_logic.GetOneKeyFindCoachEntryResponse, error) {
    out := &esport_logic.GetOneKeyFindCoachEntryResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetOneKeyFindCoachEntry request:%+v, out:%+v", request, out)
    }()

    switchRsp, err := s.esportGrabOrderCli.GetSwitch(c, &esport_grab_order.GetSwitchRequest{})
    if err != nil {
        log.ErrorWithCtx(c, "GetOneKeyFindCoachEntry GetSwitch err: %v", err)
        return out, err
    }
    out.ShowEntry = switchRsp.GetMainSwitch()

    cfg := s.bc.GetGrabOrderCfg()
    out.EntryIcon = cfg.Icon
    out.EntrySmallIcon = cfg.SmallIcon
    out.EntryTitle = cfg.Title
    out.EntryDesc = cfg.Text
    out.HeartbeatInterval = cfg.HeartbeatInterval

    return out, nil
}

func (s *Server) GetOneKeyPublishCfg(c context.Context, request *esport_logic.GetOneKeyPublishCfgRequest) (*esport_logic.GetOneKeyPublishCfgResponse, error) {
    out := &esport_logic.GetOneKeyPublishCfgResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetOneKeyPublishCfg request:%+v, out:%+v", request, out)
    }()

    info, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "GetOneKeyPublishCfg ServiceInfoFromContext fail. in:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, errCode.ErrRequestParamInvalid, "获取服务上下文失败")
    }

    itemList := s.bc.GetOneKeyParamItems()
    for _, item := range itemList {
        if item.ParamType != uint32(esport_logic.EsportOneKeyFindParamType_ESPORT_ONE_KEY_FIND_PARAM_TYPE_DIALOG_SELECT) {
            out.ItemList = append(out.ItemList, &esport_logic.EsportOneKeyFindParamItem{
                ParamType: item.ParamType,
                Title:     item.Title,
                SelectTag: tranCfgSelectTag(item.OneKeySelect),
                Text:      transCfgText(item.OneKeyText),
            })
        } else {
            if item.OneKeyDialog != nil {
                dialog, err := s.transCfgDialog(c, item.OneKeyDialog, request.GetGameId(), info.UserID)
                if err != nil {
                    log.ErrorWithCtx(c, "GetOneKeyPublishCfg failed to call transCfgDialog, gameId:%d, err:%v", request.GetGameId(), err)
                    return out, err
                }
                paramItem := &esport_logic.EsportOneKeyFindParamItem{
                    ParamType:    item.ParamType,
                    Title:        item.Title,
                    DialogSelect: dialog,
                }
                out.ItemList = append(out.ItemList, paramItem)
            }
        }
    }
    return out, nil
}

func tranCfgSelectTag(cfgSelectTag *conf.OneKeySelect) *esport_logic.EsportOneKeySelectTag {
    if cfgSelectTag == nil {
        return nil
    }
    cfg := &esport_logic.EsportOneKeySelectTag{
        Tags:                   cfgSelectTag.Tags,
        MinSelect:              cfgSelectTag.MinSelect,
        MaxSelect:              cfgSelectTag.MaxSelect,
        DefaultSelectIndexList: cfgSelectTag.DefaultSelectIndexList,
    }
    // 非单选
    if !(cfg.MaxSelect == 1 && cfg.MinSelect == 1) {
        cfg.MaxSelect = uint32(math.Min(float64(cfg.MaxSelect), float64(len(cfg.Tags))))
    }
    return cfg
}

func transCfgText(cfgText *conf.OneKeyText) *esport_logic.EsportOneKeyText {
    if cfgText == nil {
        return nil
    }
    return &esport_logic.EsportOneKeyText{
        Tips:        cfgText.Text,
        TextMaxSize: cfgText.TextMaxSize,
    }
}

func (s *Server) transCfgDialog(c context.Context, cfgDialog *conf.OneKeyDialog, gameId, uid uint32) (*esport_logic.EsportOneKeyDialogSelect, error) {
    out := &esport_logic.EsportOneKeyDialogSelect{}
    if cfgDialog == nil {
        return out, nil
    }
    detailResp, err := s.eSportSkillService.GetGameDetailById(c, &esport_skill.GetGameDetailByIdRequest{
        GameId: gameId,
    })
    if err != nil {
        log.ErrorWithCtx(c, "GetOneKeyPublishCfg failed to call svr, gameId:%d, err:%v", gameId, err)
        return out, err
    }
    cardNameSet := make(map[string]*esport_skill.GameCardInfoItem)
    for _, cardItem := range detailResp.GetConfig().GetGameCardInfoItemList() {
        cardNameSet[cardItem.GetItemName()] = cardItem
    }

    gameCardListResp, err := s.eSportHallService.GetEsportGameCardList(c, &esport_hall.GetEsportGameCardListRequest{
        Uid: uid,
    })
    if err != nil {
        log.ErrorWithCtx(c, "GetOneKeyPublishCfg failed to call svr, gameId:%d, err:%v", gameId, err)
        return out, err
    }
    out = &esport_logic.EsportOneKeyDialogSelect{
        Title: cfgDialog.Title,
        Tips:  cfgDialog.Tips,
        List:  transCfgDialogSelectList(cfgDialog.SelectItem, cardNameSet),
    }
    infoNameSet := make(map[string]*esport_hall.EsportGameCardInfoItem)
    for _, item := range gameCardListResp.GetCardList() {
        if item.GameId == gameId {
            for _, infoItem := range item.InfoItemList {
                infoNameSet[infoItem.Title] = infoItem
            }
            // 取第一个
            break
        }
    }

    for _, item := range out.List {
        if info, ok := infoNameSet[item.Title]; ok {
            out.InfoItemList = append(out.InfoItemList, &esport_logic.EsportIMGameCardInfoItem{
                Title:   item.Title,
                Content: info.Content,
            })
        }
    }

    return out, nil
}

func transCfgDialogSelectList(cfgDialogSelectList []*conf.OneKeyDialogSelectList, nameSet map[string]*esport_skill.GameCardInfoItem) []*esport_logic.EsportOneKeyDialogSelectList {
    if cfgDialogSelectList == nil {
        return nil
    }
    var out []*esport_logic.EsportOneKeyDialogSelectList
    for _, cfgDialogSelect := range cfgDialogSelectList {
        if item, ok := nameSet[cfgDialogSelect.Title]; ok {
            out = append(out, &esport_logic.EsportOneKeyDialogSelectList{
                Title: cfgDialogSelect.Title,
                Data:  item.ItemList,
            })
        }
    }
    return out
}

func (s *Server) PublishOneKeyFindCoach(c context.Context, request *esport_logic.PublishOneKeyFindCoachRequest) (*esport_logic.PublishOneKeyFindCoachResponse, error) {
    out := &esport_logic.PublishOneKeyFindCoachResponse{}
    defer func() {
        log.InfoWithCtx(c, "PublishOneKeyFindCoach request:%+v, out:%+v", request, out)
    }()

    info, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "PublishOneKeyFindCoach ServiceInfoFromContext fail. in:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, errCode.ErrRequestParamInvalid, "获取服务上下文失败")
    }

    svrReq := &esport_grab_order.PublishOneKeyFindCoachRequest{
        Uid:    info.UserID,
        GameId: request.GetGameId(),
    }

    allCfg := s.bc.GetOneKeyParamItems()
    type fillStatus struct {
        hasFill bool
    }
    needFillTitle := make(map[string]*fillStatus)
    for _, item := range allCfg {
        if item.ParamType == uint32(esport_logic.EsportOneKeyFindParamType_ESPORT_ONE_KEY_FIND_PARAM_TYPE_SELECT_TAG) &&
            item.OneKeySelect != nil && item.OneKeySelect.MinSelect > 0 {
            needFillTitle[item.Title] = &fillStatus{}
        }
    }

    var needHandleGameCard bool
    var infoList = make([]*esport_hall.EsportGameCardInfoItem, 0)
    for _, item := range request.GetPropertyList() {
        if item.Title == "" {
            log.ErrorWithCtx(c, "PublishOneKeyFindCoach title is empty, request:%+v, item:%+v", request, item)
            return out, protocol.NewExactServerError(codes.OK, errCode.ErrRequestParamInvalid, "标题为空")
        }

        switch esport_logic.EsportOneKeyFindParamType(item.ParamType) {
        case esport_logic.EsportOneKeyFindParamType_ESPORT_ONE_KEY_FIND_PARAM_TYPE_SELECT_TAG:
            if _, ok := needFillTitle[item.Title]; ok {
                needFillTitle[item.Title].hasFill = true
            }
            // 检验选择项
            cfg := s.bc.GetOneKeyParamItemByTitle(item.Title)
            if cfg != nil && cfg.OneKeySelect != nil {
                // 大于等于minSelect，小于等于maxSelect, 小于等于tags数量
                if len(item.SelectTag.Tags) < int(cfg.OneKeySelect.MinSelect) ||
                    len(item.SelectTag.Tags) > len(cfg.OneKeySelect.Tags) ||
                    len(item.SelectTag.Tags) > int(cfg.OneKeySelect.MaxSelect) {
                    log.ErrorWithCtx(c, "PublishOneKeyFindCoach select tag size invalid, request:%+v, cfg:%+v", request, cfg)
                    return out, protocol.NewExactServerError(codes.OK, errCode.ErrRequestParamInvalid, "选择项数量不符合要求")
                }

                // 转为svr层请求
                if item.Title == gender {
                    if len(item.SelectTag.Tags) > 0 {
                        svrReq.Sex = item.SelectTag.Tags[0]
                    }
                } else if item.Title == requestTitle {
                    svrReq.TargetTags = item.SelectTag.Tags
                } else {
                    log.DebugWithCtx(c, "PublishOneKeyFindCoach not support select tag, item:%+v", item)
                }
            } else {
                log.ErrorWithCtx(c, "PublishOneKeyFindCoach not support select tag, item:%+v", item)
                return out, protocol.NewExactServerError(codes.OK, errCode.ErrRequestParamInvalid, "不支持的选择项")
            }

        case esport_logic.EsportOneKeyFindParamType_ESPORT_ONE_KEY_FIND_PARAM_TYPE_TEXT:
            cfg := s.bc.GetOneKeyParamItemByTitle(item.Title)
            if cfg != nil && cfg.OneKeyText != nil {
                svrReq.RequestText = item.Text.Tips
            } else {
                log.ErrorWithCtx(c, "PublishOneKeyFindCoach not support text, item:%+v", item)
                return out, protocol.NewExactServerError(codes.OK, errCode.ErrRequestParamInvalid, "不支持的填写内容")
            }

        case esport_logic.EsportOneKeyFindParamType_ESPORT_ONE_KEY_FIND_PARAM_TYPE_DIALOG_SELECT:
            needHandleGameCard = true
            for _, dialogItem := range item.DialogSelect.InfoItemList {
                infoList = append(infoList, &esport_hall.EsportGameCardInfoItem{
                    Title:   dialogItem.Title,
                    Content: dialogItem.Content,
                })
                svrReq.CardInfoList = append(svrReq.CardInfoList, &esport_grab_order.CardInfoSection{
                    Title:   dialogItem.Title,
                    Content: dialogItem.Content,
                })
            }
        }
    }

    for _, item := range needFillTitle {
        if !item.hasFill {
            log.ErrorWithCtx(c, "PublishOneKeyFindCoach not fill select tag, request:%+v, needFillTitle:%+v", request, needFillTitle)
            return out, protocol.NewExactServerError(codes.OK, errCode.ErrRequestParamInvalid, "未填写必填项")
        }
    }

    err := s.censorOneKeyCustomText(c, svrReq.RequestText)
    if err != nil {
        log.ErrorWithCtx(c, "PublishOneKeyFindCoach censorOneKeyCustomText err: %v, text: %s", err, svrReq.RequestText)
        return out, err
    }

    baseResp, err := s.publishRiskCheck(c, request)
    if err != nil {
        log.ErrorWithCtx(c, "PublishOneKeyFindCoach publishRiskCheck err: %v", err)
        out.BaseResp = baseResp
        return out, err
    }

    if needHandleGameCard {
        goroutineex.GoroutineWithTimeoutCtx(c, time.Second*5, func(ctx context.Context) {
            var userInfo *account.User
            userInfo, err = s.accountCli.GetUser(ctx, info.UserID)
            if err != nil {
                log.ErrorWithCtx(ctx, "PublishOneKeyFindCoach failed to call GetUser, req:%+v, err:%v", request, err)
                return
            }
            gameCardListResp, err := s.eSportHallService.GetEsportGameCardList(ctx, &esport_hall.GetEsportGameCardListRequest{
                Uid: info.UserID,
            })
            if err != nil {
                log.ErrorWithCtx(ctx, "GetOneKeyPublishCfg failed to call svr, req:%+v, err:%v", request, err)
                return
            }
            var cardInfo *esport_hall.EsportGameCardInfo
            for _, item := range gameCardListResp.GetCardList() {
                if item.GameId == request.GetGameId() {
                    cardInfo = item
                    break
                }
            }

            // 创建卡片
            if cardInfo == nil {
                cardInfoList := append([]*esport_hall.EsportGameCardInfoItem{&esport_hall.EsportGameCardInfoItem{
                    Title:   "游戏昵称",
                    Content: userInfo.GetNickname(),
                }}, infoList...)
                _, err = s.eSportHallService.CreateEsportGameCard(ctx, &esport_hall.CreateEsportGameCardRequest{
                    Uid:          info.UserID,
                    GameId:       request.GetGameId(),
                    InfoItemList: cardInfoList,
                })
                if err != nil {
                    log.ErrorWithCtx(ctx, "PublishOneKeyFindCoach gameCard failed to call CreateEsportGameCard, req:%+v, err:%v", request, err)
                    return
                }
                log.InfoWithCtx(ctx, "PublishOneKeyFindCoach gameCard CreateEsportGameCard success, req:%+v", request)
            } else {
                var needUpdate bool
                infoList, needUpdate = mergeCardInfoList(cardInfo.GetInfoItemList(), infoList)
                if needUpdate {
                    var noNeedNickName bool
                    for _, item := range infoList {
                        if item.GetTitle() == "游戏昵称" {
                            noNeedNickName = true
                            break
                        }
                    }
                    if !noNeedNickName {
                        infoList = append([]*esport_hall.EsportGameCardInfoItem{&esport_hall.EsportGameCardInfoItem{
                            Title:   "游戏昵称",
                            Content: userInfo.GetNickname(),
                        }}, infoList...)
                    }
                    updateReq := &esport_hall.UpdateEsportGameCardRequest{
                        Uid:          info.UserID,
                        GameId:       request.GetGameId(),
                        InfoItemList: infoList,
                        CardId:       cardInfo.GetCardId(),
                    }
                    _, err = s.eSportHallService.UpdateEsportGameCard(ctx, updateReq)
                    if err != nil {
                        log.ErrorWithCtx(ctx, "PublishOneKeyFindCoach failed to call UpdateEsportGameCard, req:%+v, err:%v", request, err)
                        return
                    }
                    log.InfoWithCtx(ctx, "PublishOneKeyFindCoach gameCard UpdateEsportGameCard success, req:%+v, updateReq:%+v", request, updateReq)
                } else {
                    log.DebugWithCtx(ctx, "PublishOneKeyFindCoach gameCard no need to update, req:%+v", request)
                }
            }

        })
    } else {
        log.DebugWithCtx(c, "PublishOneKeyFindCoach no need to handle gameCard, req:%+v", request)
    }

    log.DebugWithCtx(c, "PublishOneKeyFindCoach req:%+v, svrReq:%+v", request, svrReq)
    findCoachResp, err := s.esportGrabOrderCli.PublishOneKeyFindCoach(c, svrReq)
    if err != nil {
        log.ErrorWithCtx(c, "PublishOneKeyFindCoach failed to call svr, req:%+v, err:%v", request, err)
        return out, err
    }
    out.PublishId = findCoachResp.GetPublishId()
    return out, nil
}

func (s *Server) publishRiskCheck(ctx context.Context, request *esport_logic.PublishOneKeyFindCoachRequest) (*app.BaseResp, error) {

    baseReq := request.GetBaseReq()
    var ctxCancel func()
    ctx, ctxCancel = protogrpc.NewContextWithInfoTimeout(ctx, 500*time.Millisecond)
    defer ctxCancel()
    // 返回给客户端的 BaseResp
    baseResp := &app.BaseResp{}

    sv, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "publishRiskCheck failed to get service info, req:%+v", request)
        return baseResp, protocol.NewExactServerError(codes.OK, errCode.ErrRequestParamInvalid)
    }

    var gameName string
    gameResp, err := s.eSportSkillService.GetGameDetailById(ctx, &esport_skill.GetGameDetailByIdRequest{
        GameId: request.GetGameId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "publishRiskCheck failed to call GetGameDetailById, req:%+v, err:%v", request, err)
        return baseResp, protocol.NewExactServerError(codes.OK, errCode.ErrRequestParamInvalid)
    }
    gameName = gameResp.GetConfig().GetName()

    // 默认不限
    var targetGender = "0"
    var requestTagList string
    var moreRequest string
    for _, item := range request.GetPropertyList() {
        switch item.Title {
        case gender:
            if len(item.GetSelectTag().GetTags()) == 0 {
                continue
            }
            switch item.GetSelectTag().GetTags()[0] {
            case "男生":
                targetGender = "1"
            case "女生":
                targetGender = "2"
            default:
                targetGender = "0"
            }

        case requestTitle:
            requestTagList = strings.Join(item.GetSelectTag().GetTags(), ",")
        case moreRequestTitle:
            moreRequest = item.GetText().GetTips()
        }

    }

    checkReq := &riskMngApiPb.CheckReq{
        Scene: "PUBLISH_ONE_KEY_FIND",
        SourceEntity: &riskMngApiPb.Entity{
            Uid: sv.UserID,
        },

        // 通用参数传递
        CustomParams: map[string]string{
            "demand_gender":  targetGender,
            "demand_request": requestTagList,
            "other_request":  moreRequest,
            "tab_id":         gameName,
        },
    }

    log.DebugWithCtx(ctx, "publishRiskCheck checkReq.CustomParams:%+v", checkReq.CustomParams)

    checkResp, err := s.riskCli.CheckHelper(ctx, checkReq, baseReq)
    if errors.Is(ctx.Err(), context.DeadlineExceeded) {
        log.WarnWithCtx(ctx, "publishRiskCheck timeout, req:%+v, resp:%+v", checkReq, checkResp)
        return baseResp, nil
    }
    if err != nil {
        // 系统错误，风控非关键路径，可忽略系统错误
        log.ErrorWithCtx(ctx, "publishRiskCheck failed to call svr, req:%+v, err:%v", checkReq, err)
        return baseResp, nil
    }

    // 命中风控拦截
    if checkResp.GetErrCode() < 0 {
        // 建议打个 info 拦截日志，方便排查，风控拦截日志不会很多
        log.InfoWithCtx(ctx, "publishRiskCheck risk-mng-api.Check hit, req:%+v, resp:%+v", checkReq, checkResp)
        // 需要返回 ErrInfo 给客户端
        baseResp.ErrInfo = checkResp.GetErrInfo()
        // 返回错误码给客户端，并设置 gRPC 错误码为 OK
        return baseResp, protocol.NewExactServerError(codes.OK, int(checkResp.GetErrCode()), checkResp.GetErrMsg())
    } else {
        return baseResp, nil
    }
}

func (s *Server) censorOneKeyCustomText(ctx context.Context, customText string) error {
    if customText == "" {
        return nil
    }
    svrInfo, _ := protogrpc.ServiceInfoFromContext(ctx)

    userInfo, err := s.userProfileCli.GetUserProfileV2(ctx, svrInfo.UserID, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "censorOneKeyCustomText GetUserProfileV2 err: %v", err)
        return err
    }

    verifyRes, serr := s.censoringProxyCli.Text().SyncScanText(ctx, &v2.SyncTextCheckReq{
        Context: &v2.TaskContext{
            SceneCode: string(auditTypes.SCENE_CODE_ESPORT_ONE_KEY_CUSTOM_TEXT),
            AppId:     string(auditTypes.APP_ID_QUICKSILVER),
            Scenes:    []v2.Scene{v2.Scene_SCENE_DEFAULT},
            UserInfo: &v2.User{
                Id:       uint64(svrInfo.UserID),
                Nickname: userInfo.GetNickname(),
                Alias:    userInfo.GetAccountAlias(),
            },
            DeviceInfo: &v2.Device{
                Id: hex.EncodeToString(svrInfo.DeviceID),
                Ip: svrInfo.ClientIPAddr().String(),
            },
        },
        Text:  customText,
        Async: false,
    })
    if serr != nil {
        log.ErrorWithCtx(ctx, "censorOneKeyCustomText SyncScanText err: %v", serr)
        return serr
    }

    log.InfoWithCtx(ctx, "censorOneKeyCustomText SyncScanText, verifyRes: %+v, text: %s", verifyRes, customText)
    if v2.Suggestion_REJECT == v2.Suggestion(verifyRes.GetResult()) {
        return protocol.NewExactServerError(codes.OK, errCode.ErrRequestParamInvalid, "你输入的有违规敏感词，请重新输入")
    }

    return nil
}

func mergeCardInfoList(previousInfo []*esport_hall.EsportGameCardInfoItem, newInfo []*esport_hall.EsportGameCardInfoItem) (out []*esport_hall.EsportGameCardInfoItem, update bool) {
    if previousInfo == nil {
        return newInfo, true
    }
    if newInfo == nil {
        return previousInfo, false
    }
    out = make([]*esport_hall.EsportGameCardInfoItem, 0)

    titleMap := make(map[string]*esport_hall.EsportGameCardInfoItem)
    for _, item := range previousInfo {
        titleMap[item.GetTitle()] = item
        out = append(out, item)
    }
    for _, item := range newInfo {
        if info, ok := titleMap[item.GetTitle()]; ok {
            if info.GetContent() != item.GetContent() {
                info.Content = item.GetContent()
                update = true
            }
        } else {
            out = append(out, item)
            update = true
        }
    }
    return out, update
}

func (s *Server) CancelOneKeyFindCoach(c context.Context, request *esport_logic.CancelOneKeyFindCoachRequest) (*esport_logic.CancelOneKeyFindCoachResponse, error) {
    out := &esport_logic.CancelOneKeyFindCoachResponse{}
    defer func() {
        log.InfoWithCtx(c, "CancelOneKeyFindCoach request:%+v, out:%+v", request, out)
    }()

    info, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "CancelOneKeyFindCoach ServiceInfoFromContext fail. in:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, errCode.ErrRequestParamInvalid, "获取服务上下文失败")
    }

    _, err := s.esportGrabOrderCli.CancelOneKeyFindCoach(c, &esport_grab_order.CancelOneKeyFindCoachRequest{
        Uid:          info.UserID,
        PublishId:    request.GetPublishId(),
        CancelReason: request.GetCancelReason(),
    })
    if err != nil {
        log.ErrorWithCtx(c, "CancelOneKeyFindCoach failed to call svr, req:%+v, err:%v", request, err)
        return out, err
    }
    return out, nil
}

func (s *Server) StickOneKeyFindCoach(c context.Context, request *esport_logic.StickOneKeyFindCoachRequest) (*esport_logic.StickOneKeyFindCoachResponse, error) {
    out := &esport_logic.StickOneKeyFindCoachResponse{}
    defer func() {
        log.InfoWithCtx(c, "StickOneKeyFindCoach request:%+v, out:%+v", request, out)
    }()
    info, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "CancelOneKeyFindCoach ServiceInfoFromContext fail. in:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, errCode.ErrRequestParamInvalid, "获取服务上下文失败")
    }
    _, err := s.esportGrabOrderCli.StickOneKeyFindCoach(c, &esport_grab_order.StickOneKeyFindCoachRequest{
        Uid:       info.UserID,
        PublishId: request.GetPublishId(),
    })
    if err != nil {
        log.ErrorWithCtx(c, "StickOneKeyFindCoach failed to call svr, req:%+v, err:%v", request, err)
        return out, err
    }
    return out, nil
}

func (s *Server) GetGoingOneKeyFindCoach(c context.Context, request *esport_logic.GetGoingOneKeyFindCoachRequest) (*esport_logic.GetGoingOneKeyFindCoachResponse, error) {
    out := &esport_logic.GetGoingOneKeyFindCoachResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetGoingOneKeyFindCoach request:%+v, out:%+v", request, out)
    }()

    info, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "GetGoingOneKeyFindCoach ServiceInfoFromContext fail. in:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, errCode.ErrRequestParamInvalid, "获取服务上下文失败")
    }

    goingResp, err := s.esportGrabOrderCli.GetGoingOneKeyFindCoach(c, &esport_grab_order.GetGoingOneKeyFindCoachRequest{
        Uid: info.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(c, "GetGoingOneKeyFindCoach failed to call svr, req:%+v, err:%v", request, err)
        return out, err
    }

    var coachInfoList []*esport_logic.EsportAreaCoachInfo
    for _, item := range goingResp.GetGrabList() {
        coachInfoList = append(coachInfoList, &esport_logic.EsportAreaCoachInfo{
            UserProfile: &app.UserProfile{
                Uid: item.GetCoachUid(),
            },
            GameId: goingResp.GetGameId(),
        })
    }

    coachInfoMap := make(map[uint32]*esport_logic.EsportAreaCoachInfo)
    if len(coachInfoList) != 0 {
        coachInfoList, err = s.mgr.AssembleRecommendList(c, coachInfoList, goingResp.GetGameId(), info.UserID)
        if err != nil {
            log.ErrorWithCtx(c, "GetGoingOneKeyFindCoach failed to call AssembleRecommendList, gameId:%d, err:%v", goingResp.GetGameId(), err)
            return out, err
        }

        for _, coachInfo := range coachInfoList {
            coachInfoMap[coachInfo.UserProfile.Uid] = coachInfo
        }
        for _, item := range goingResp.GetGrabList() {
            out.GrabList = append(out.GrabList, &esport_logic.GrabOrderCoachInfo{
                Coach:             coachInfoMap[item.GetCoachUid()],
                GrabAudio:         item.GetGrabAudio(),
                GrabText:          item.GetGrabText(),
                GrabAudioDuration: item.GetGrabAudioDuration(),
                GrabType:          item.GetGrabType(),
            })
        }
    }
    out.PublishId = goingResp.GetPublishId()
    out.ExpireTs = goingResp.GetExpireTs()
    out.PublishTs = goingResp.GetPublishTs()
    return out, nil
}

func (s *Server) EsportReportClickIm(ctx context.Context, in *esport_logic.EsportReportClickImRequest) (*esport_logic.EsportReportClickImResponse, error) {
    out := &esport_logic.EsportReportClickImResponse{}
    log.InfoWithCtx(ctx, "EsportReportClickIm in: %+v", in)

    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        return out, protocol.NewExactServerError(codes.OK, errCode.ErrRequestParamInvalid, "获取服务上下文失败")
    }
    if in.GameId == 0 || in.CoachId == 0 || in.CoachId == svrInfo.UserID {
        return out, protocol.NewExactServerError(codes.OK, errCode.ErrRequestParamInvalid, "请求参数错误")
    }

    // 获取计数
    count, err := s.reportUserVisitIMPage(ctx, svrInfo.UserID, in.GetCoachId(), true)
    if err != nil {
        log.ErrorWithCtx(ctx, "EsportReportClickIm reportUserVisitIMPage err: %v", err)
        return out, err
    }
    log.InfoWithCtx(ctx, "EsportReportClickIm in: %+v, count: %d", in, count)

    // 当前IM推送消息1天显示1次，已显示的不再另外推送
    if count > 0 {
        _, err = s.reportUserVisitIMPage(ctx, svrInfo.UserID, in.GetCoachId(), false)
        if err != nil {
            log.WarnWithCtx(ctx, "EsportReportClickIm reportUserVisitIMPage err: %v", err)
        }
        return out, nil
    }

    // 当天首次访问的要推送
    _, err = s.esportImCli.SendOneSideImMsg(ctx, uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY), svrInfo.UserID, in.CoachId, 1, &trade_im_notify.ImMsg{Content: "Ta可能想跟你聊天~快主动跟Ta互动吧~"})
    if err != nil {
        log.WarnWithCtx(ctx, "EsportReportClickIm SendOneSideImMsg err: %v", err)
    }
    _, err = s.reportUserVisitIMPage(ctx, svrInfo.UserID, in.GetCoachId(), false)
    if err != nil {
        log.WarnWithCtx(ctx, "EsportReportClickIm reportUserVisitIMPage err: %v", err)
    }

    return out, nil
}

func (s *Server) reportUserVisitIMPage(ctx context.Context, uid, coachUid uint32, getOnly bool) (uint32, error) {
    reportResp, err := s.eSportStatCli.ReportUserVisitIMPage(ctx, &esport_statiticsPb.ReportUserVisitIMPageRequest{
        Uid:      uid,
        CoachUid: coachUid,
        GetOnly:  getOnly,
    })
    return reportResp.GetVisitCnt(), err
}

func (s *Server) EsportRegionHeartbeat(c context.Context, request *esport_logic.EsportRegionHeartbeatRequest) (*esport_logic.EsportRegionHeartbeatResponse, error) {
    out := &esport_logic.EsportRegionHeartbeatResponse{}
    defer func() {
        log.InfoWithCtx(c, "EsportRegionHeartbeat request:%+v, out:%+v", request, out)
    }()

    info, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "EsportRegionHeartbeat ServiceInfoFromContext fail. in:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, errCode.ErrRequestParamInvalid, "获取服务上下文失败")
    }
    _, err := s.esportGrabOrderCli.EsportRegionHeartbeat(c, &esport_grab_order.EsportRegionHeartbeatRequest{
        Uid: info.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(c, "EsportRegionHeartbeat failed to call svr, req:%+v, err:%v", request, err)
        return out, err
    }
    return out, nil
}
