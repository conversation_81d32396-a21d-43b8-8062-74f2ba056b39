// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/esport/esport-logic/internal/mgr (interfaces: Mgr)

// Package mgr is a generated GoMock package.
package mgr

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	info "gitlab.ttyuyin.com/avengers/tyr/core/service/basepb/info"
	app "golang.52tt.com/protocol/app"
	esport_logic "golang.52tt.com/protocol/app/esport_logic"
	esport_trade "golang.52tt.com/protocol/services/esport-trade"
	esport_rcmd "golang.52tt.com/protocol/services/esport_rcmd"
	risk_mng_api "golang.52tt.com/protocol/services/risk-mng-api"
)

// MockMgr is a mock of Mgr interface.
type MockMgr struct {
	ctrl     *gomock.Controller
	recorder *MockMgrMockRecorder
}

// MockMgrMockRecorder is the mock recorder for MockMgr.
type MockMgrMockRecorder struct {
	mock *MockMgr
}

// NewMockMgr creates a new mock instance.
func NewMockMgr(ctrl *gomock.Controller) *MockMgr {
	mock := &MockMgr{ctrl: ctrl}
	mock.recorder = &MockMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMgr) EXPECT() *MockMgrMockRecorder {
	return m.recorder
}

// AssembleRecommendList mocks base method.
func (m *MockMgr) AssembleRecommendList(arg0 context.Context, arg1 []*esport_logic.EsportAreaCoachInfo, arg2, arg3 uint32) ([]*esport_logic.EsportAreaCoachInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssembleRecommendList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*esport_logic.EsportAreaCoachInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AssembleRecommendList indicates an expected call of AssembleRecommendList.
func (mr *MockMgrMockRecorder) AssembleRecommendList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssembleRecommendList", reflect.TypeOf((*MockMgr)(nil).AssembleRecommendList), arg0, arg1, arg2, arg3)
}

// AsyncReportByLinkIssueCoupon mocks base method.
func (m *MockMgr) AsyncReportByLinkIssueCoupon(arg0 context.Context, arg1 uint32, arg2 []string, arg3 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AsyncReportByLinkIssueCoupon", arg0, arg1, arg2, arg3)
}

// AsyncReportByLinkIssueCoupon indicates an expected call of AsyncReportByLinkIssueCoupon.
func (mr *MockMgrMockRecorder) AsyncReportByLinkIssueCoupon(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncReportByLinkIssueCoupon", reflect.TypeOf((*MockMgr)(nil).AsyncReportByLinkIssueCoupon), arg0, arg1, arg2, arg3)
}

// AsyncReportByLinkOrderFinish mocks base method.
func (m *MockMgr) AsyncReportByLinkOrderFinish(arg0 context.Context, arg1 string, arg2 int32) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AsyncReportByLinkOrderFinish", arg0, arg1, arg2)
}

// AsyncReportByLinkOrderFinish indicates an expected call of AsyncReportByLinkOrderFinish.
func (mr *MockMgrMockRecorder) AsyncReportByLinkOrderFinish(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncReportByLinkOrderFinish", reflect.TypeOf((*MockMgr)(nil).AsyncReportByLinkOrderFinish), arg0, arg1, arg2)
}

// AsyncReportByLinkUseCoupon mocks base method.
func (m *MockMgr) AsyncReportByLinkUseCoupon(arg0 context.Context, arg1, arg2 uint32, arg3, arg4 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AsyncReportByLinkUseCoupon", arg0, arg1, arg2, arg3, arg4)
}

// AsyncReportByLinkUseCoupon indicates an expected call of AsyncReportByLinkUseCoupon.
func (mr *MockMgrMockRecorder) AsyncReportByLinkUseCoupon(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncReportByLinkUseCoupon", reflect.TypeOf((*MockMgr)(nil).AsyncReportByLinkUseCoupon), arg0, arg1, arg2, arg3, arg4)
}

// CanUserVisitOrderDetail mocks base method.
func (m *MockMgr) CanUserVisitOrderDetail(arg0 context.Context, arg1 uint32, arg2 *esport_trade.SkillProductOrderDetail) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CanUserVisitOrderDetail", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CanUserVisitOrderDetail indicates an expected call of CanUserVisitOrderDetail.
func (mr *MockMgrMockRecorder) CanUserVisitOrderDetail(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CanUserVisitOrderDetail", reflect.TypeOf((*MockMgr)(nil).CanUserVisitOrderDetail), arg0, arg1, arg2)
}

// CheckBeforePay mocks base method.
func (m *MockMgr) CheckBeforePay(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckBeforePay", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckBeforePay indicates an expected call of CheckBeforePay.
func (mr *MockMgrMockRecorder) CheckBeforePay(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBeforePay", reflect.TypeOf((*MockMgr)(nil).CheckBeforePay), arg0, arg1, arg2, arg3)
}

// CheckIsShowCustomerEntOnOrderByCoachUids mocks base method.
func (m *MockMgr) CheckIsShowCustomerEntOnOrderByCoachUids(arg0 context.Context, arg1 []uint32) (map[uint32]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIsShowCustomerEntOnOrderByCoachUids", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIsShowCustomerEntOnOrderByCoachUids indicates an expected call of CheckIsShowCustomerEntOnOrderByCoachUids.
func (mr *MockMgrMockRecorder) CheckIsShowCustomerEntOnOrderByCoachUids(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIsShowCustomerEntOnOrderByCoachUids", reflect.TypeOf((*MockMgr)(nil).CheckIsShowCustomerEntOnOrderByCoachUids), arg0, arg1)
}

// CheckOrderRemark mocks base method.
func (m *MockMgr) CheckOrderRemark(arg0 context.Context, arg1 *info.ServiceInfo, arg2 uint32, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckOrderRemark", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckOrderRemark indicates an expected call of CheckOrderRemark.
func (mr *MockMgrMockRecorder) CheckOrderRemark(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckOrderRemark", reflect.TypeOf((*MockMgr)(nil).CheckOrderRemark), arg0, arg1, arg2, arg3)
}

// CheckSwitch mocks base method.
func (m *MockMgr) CheckSwitch(arg0 context.Context, arg1, arg2 uint32, arg3, arg4 bool) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckSwitch", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckSwitch indicates an expected call of CheckSwitch.
func (mr *MockMgrMockRecorder) CheckSwitch(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckSwitch", reflect.TypeOf((*MockMgr)(nil).CheckSwitch), arg0, arg1, arg2, arg3, arg4)
}

// CheckUserCanOrderByRiskSys mocks base method.
func (m *MockMgr) CheckUserCanOrderByRiskSys(arg0 context.Context, arg1 *app.BaseReq, arg2 uint32, arg3 uint64) (bool, *risk_mng_api.CheckResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserCanOrderByRiskSys", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*risk_mng_api.CheckResp)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CheckUserCanOrderByRiskSys indicates an expected call of CheckUserCanOrderByRiskSys.
func (mr *MockMgrMockRecorder) CheckUserCanOrderByRiskSys(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserCanOrderByRiskSys", reflect.TypeOf((*MockMgr)(nil).CheckUserCanOrderByRiskSys), arg0, arg1, arg2, arg3)
}

// CheckUserCanSendCouponPackageByRiskSys mocks base method.
func (m *MockMgr) CheckUserCanSendCouponPackageByRiskSys(arg0 context.Context, arg1 *app.BaseReq, arg2, arg3 uint32, arg4, arg5 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserCanSendCouponPackageByRiskSys", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserCanSendCouponPackageByRiskSys indicates an expected call of CheckUserCanSendCouponPackageByRiskSys.
func (mr *MockMgrMockRecorder) CheckUserCanSendCouponPackageByRiskSys(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserCanSendCouponPackageByRiskSys", reflect.TypeOf((*MockMgr)(nil).CheckUserCanSendCouponPackageByRiskSys), arg0, arg1, arg2, arg3, arg4, arg5)
}

// CheckUserFirstRoundByRiskSys mocks base method.
func (m *MockMgr) CheckUserFirstRoundByRiskSys(arg0 context.Context, arg1 *app.BaseReq, arg2, arg3, arg4 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserFirstRoundByRiskSys", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserFirstRoundByRiskSys indicates an expected call of CheckUserFirstRoundByRiskSys.
func (mr *MockMgrMockRecorder) CheckUserFirstRoundByRiskSys(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserFirstRoundByRiskSys", reflect.TypeOf((*MockMgr)(nil).CheckUserFirstRoundByRiskSys), arg0, arg1, arg2, arg3, arg4)
}

// GetRcmdSkillProduct mocks base method.
func (m *MockMgr) GetRcmdSkillProduct(arg0 context.Context, arg1 uint32, arg2 *esport_rcmd.GetEsportRcmdSkillProductReq, arg3 ...uint32) ([]*esport_logic.EsportAreaCoachInfo, uint32, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRcmdSkillProduct", varargs...)
	ret0, _ := ret[0].([]*esport_logic.EsportAreaCoachInfo)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetRcmdSkillProduct indicates an expected call of GetRcmdSkillProduct.
func (mr *MockMgrMockRecorder) GetRcmdSkillProduct(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRcmdSkillProduct", reflect.TypeOf((*MockMgr)(nil).GetRcmdSkillProduct), varargs...)
}

// GetSkillProductList mocks base method.
func (m *MockMgr) GetSkillProductList(arg0 context.Context, arg1, arg2 uint32) ([]*esport_logic.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSkillProductList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*esport_logic.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSkillProductList indicates an expected call of GetSkillProductList.
func (mr *MockMgrMockRecorder) GetSkillProductList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSkillProductList", reflect.TypeOf((*MockMgr)(nil).GetSkillProductList), arg0, arg1, arg2)
}

// GetUGCRoomGameId mocks base method.
func (m *MockMgr) GetUGCRoomGameId(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUGCRoomGameId", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUGCRoomGameId indicates an expected call of GetUGCRoomGameId.
func (mr *MockMgrMockRecorder) GetUGCRoomGameId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUGCRoomGameId", reflect.TypeOf((*MockMgr)(nil).GetUGCRoomGameId), arg0, arg1)
}

// GetUserGameCardOnUGC mocks base method.
func (m *MockMgr) GetUserGameCardOnUGC(arg0 context.Context, arg1, arg2 uint32) (*UserGameCardOnUgc, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserGameCardOnUGC", arg0, arg1, arg2)
	ret0, _ := ret[0].(*UserGameCardOnUgc)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserGameCardOnUGC indicates an expected call of GetUserGameCardOnUGC.
func (mr *MockMgrMockRecorder) GetUserGameCardOnUGC(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserGameCardOnUGC", reflect.TypeOf((*MockMgr)(nil).GetUserGameCardOnUGC), arg0, arg1, arg2)
}

// IsUserMatchCrowGroup mocks base method.
func (m *MockMgr) IsUserMatchCrowGroup(arg0 context.Context, arg1 uint32, arg2 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsUserMatchCrowGroup", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsUserMatchCrowGroup indicates an expected call of IsUserMatchCrowGroup.
func (mr *MockMgrMockRecorder) IsUserMatchCrowGroup(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsUserMatchCrowGroup", reflect.TypeOf((*MockMgr)(nil).IsUserMatchCrowGroup), arg0, arg1, arg2)
}
