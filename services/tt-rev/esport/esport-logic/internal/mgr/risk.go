package mgr

import (
    "context"
    "errors"
    "fmt"
    "golang.52tt.com/pkg/log"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app"
    guild_cooperation "golang.52tt.com/protocol/services/guild-cooperation"
    riskMngApiPb "golang.52tt.com/protocol/services/risk-mng-api"
    user_online "golang.52tt.com/protocol/services/user-online"
    "strings"
    "time"
)

// CheckUserCanSendCouponPackageByRiskSys 检查用户是否可以发送优惠券包
// Scene: "ESPORT_SEND_COUPON"
func (m *MgrImpl) CheckUserCanSendCouponPackageByRiskSys(ctx context.Context, baseReq *app.BaseReq, amount, guildId uint32, orderId, sourceType string) (bool, error) {
    var ctxCancel func()
    ctx, ctxCancel = protogrpc.NewContextWithInfoTimeout(ctx, 500*time.Millisecond)
    defer ctxCancel()

    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        return false, fmt.Errorf("get service info failed")
    }

    guildResp, err := m.guildCli.GetGuild(ctx, guildId)
    if err != nil {
        log.WarnWithCtx(ctx, "get guild info failed, guildId: %d, err: %w", guildId, err)
    }

    // 查询公会类型
    guildCooperationInfos, sErr := m.guildCopeCli.GetGuildCooperationInfos(ctx, &guild_cooperation.GetGuildCooperationInfosReq{
        GuildId: guildId,
    })
    if sErr != nil {
        log.WarnWithCtx(ctx, "AsyncReportByLinkOrderFinish GetGuildCooperationInfos, uid:%d, orderId:%s, err:%v", serviceInfo.UserID, orderId, err)
    }
    guildType := make([]string, 0, 3)
    if guildCooperationInfos.GetIsAmuseCoopGuild() {
        guildType = append(guildType, "1")
    }
    if guildCooperationInfos.GetIsYuyinCoopGuild() {
        guildType = append(guildType, "2")
    }
    if guildCooperationInfos.GetIsEsportCoopGuild() {
        guildType = append(guildType, "3")
    }

    checkReq := &riskMngApiPb.CheckReq{
        Scene: "ESPORT_SEND_COUPON",
        SourceEntity: &riskMngApiPb.Entity{
            Uid: serviceInfo.UserID,
        },
        CustomParams: map[string]string{
            "source_type": sourceType,
            "guild_id":    fmt.Sprintf("%d", guildId),
            "guild_name":  guildResp.GetName(),
            "guild_type":  strings.Join(guildType, ","),
            "activity_id": "ESPORT_SEND_COUPON",
            "order_id":    orderId,
            "amount":      fmt.Sprintf("%d", amount/100),
        },
    }
    checkResp, err := m.riskMngApiCli.CheckHelper(ctx, checkReq, baseReq)
    if err != nil {
        // 系统错误，风控非关键路径，可忽略系统错误
        log.ErrorWithCtx(ctx, "CheckUserCanSendCouponPackageByRiskSys risk-mng-api.Check failed, req:%+v, err: %v", checkReq, err)
        return true, nil
    }
    // 打个 info 拦截日志，方便排查，风控拦截日志不会很多
    log.InfoWithCtx(ctx, "CheckUserCanSendCouponPackageByRiskSys risk-mng-api.Check hit, req:%+v, resp:%+v", checkReq, checkResp)
    // 命中风控不拦截
    if checkResp.ErrCode >= 0 {
        return true, nil
    } else {
        return false, fmt.Errorf("risk-mng-api.Check hit, errCode: %d, errMsg: %s", checkResp.ErrCode, checkResp.ErrMsg)
    }
}

// CheckUserCanOrderByRiskSys 检查用户是否可以下单
// Scene: "CONSUME"
func (m *MgrImpl) CheckUserCanOrderByRiskSys(ctx context.Context, baseReq *app.BaseReq, coachUid uint32, amount uint64, ) (bool, *riskMngApiPb.CheckResp, error) {
    var ctxCancel func()
    ctx, ctxCancel = protogrpc.NewContextWithInfoTimeout(ctx, 500*time.Millisecond)
    defer ctxCancel()

    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        return false, nil, fmt.Errorf("get service info failed")
    }
    // 查询大神coachUid的登陆信息
    info, serverError := m.userOnlineCli.BatchGetLastMultiOnlineInfo(ctx, []uint32{coachUid})
    if serverError != nil {
        return false, nil, fmt.Errorf("get user online info failed, err: %w", serverError)
    }
    // 挑选最近一次的登陆信息
    getLatestOnlineInfo := func(list []*user_online.OnlineInfo) *user_online.OnlineInfo {
        var latest *user_online.OnlineInfo
        for _, item := range list {
            if latest == nil || latest.GetOnlineAt() < item.GetOnlineAt() {
                latest = item
            }
        }
        return latest
    }
    coachOnlineInfo := getLatestOnlineInfo(info[0].GetOnlineInfoList())

    checkReq := &riskMngApiPb.CheckReq{
        Scene: "CONSUME",
        SourceEntity: &riskMngApiPb.Entity{
            Uid: serviceInfo.UserID,
        },
        CustomParams: map[string]string{
            "rcv_uid":      fmt.Sprintf("%d", coachUid),
            "rcv_deviceId": strings.ToLower(coachOnlineInfo.GetDeviceIdHex()),
            "rcv_ip":       coachOnlineInfo.GetClientIp(),
            "amount":       fmt.Sprintf("%d", amount/100),
            "consume_type": "2",
            "scene_id":     "3",
        },
    }
    checkResp, err := m.riskMngApiCli.CheckHelper(ctx, checkReq, baseReq)
    if errors.Is(ctx.Err(), context.DeadlineExceeded) { // 超时当通过,优先保证不影响业务
        log.WarnWithCtx(ctx, "CheckUserCanOrderByRiskSys timeout, req:%+v, resp:%+v", checkReq, checkResp)
        return true, nil, nil
    }
    if err != nil {
        // 系统错误，风控非关键路径，可忽略系统错误
        log.ErrorWithCtx(ctx, "CheckUserCanOrderByRiskSys risk-mng-api.Check failed, req:%+v, err: %v", checkReq, err)
        return true, nil, nil
    }
    // 打个 info 拦截日志，方便排查，风控拦截日志不会很多
    log.InfoWithCtx(ctx, "CheckUserCanOrderByRiskSys risk-mng-api.Check hit, req:%+v, resp:%+v", checkReq, checkResp)
    // 命中风控不拦截
    if checkResp.ErrCode >= 0 {
        return true, nil, nil
    } else {
        log.ErrorWithCtx(ctx, "CheckUserCanOrderByRiskSys risk-mng-api.Check hit, errCode: %d, errMsg: %s", checkResp.ErrCode, checkResp.ErrMsg)
        return false, checkResp, nil
    }
}

// CheckUserFirstRoundByRiskSys 通过风控系统检查用户是否能使用首单下单消费
func (m *MgrImpl) CheckUserFirstRoundByRiskSys(ctx context.Context, baseReq *app.BaseReq, uid, coachUid, totalPrice uint32) (bool, error) {
    log.DebugWithCtx(ctx, "CheckUserFirstRoundByRiskSys uid:%d, coachUid:%d", uid, coachUid)
    // 查询大神coachUid的登陆信息
    info, serverError := m.userOnlineCli.BatchGetLastMultiOnlineInfo(ctx, []uint32{coachUid})
    if serverError != nil {
        log.ErrorWithCtx(ctx, "CheckUserCanUseCouponByRiskSys get user online info failed, err: %v", serverError)
        return false, fmt.Errorf("get user online info failed, err: %w", serverError)
    }
    // 挑选最近一次的登陆信息
    getLatestOnlineInfo := func(list []*user_online.OnlineInfo) *user_online.OnlineInfo {
        var latest *user_online.OnlineInfo
        for _, item := range list {
            if latest == nil || latest.GetOnlineAt() < item.GetOnlineAt() {
                latest = item
            }
        }
        return latest
    }
    coachOnlineInfo := getLatestOnlineInfo(info[0].GetOnlineInfoList())

    r := &riskMngApiPb.CheckReq{
        Scene:        "ESPORT_USE_COUPON",
        SourceEntity: &riskMngApiPb.Entity{Uid: uid},
        CustomParams: map[string]string{
            "rcv_uid":      fmt.Sprintf("%d", coachUid),
            "rcv_deviceId": strings.ToLower(coachOnlineInfo.GetDeviceIdHex()),
            "rcv_ip":       coachOnlineInfo.GetClientIp(),
            "activity_id":  "esport_use_first_round",
            "amount":       fmt.Sprintf("%d", totalPrice/100),
        },
    }
    resp, err := m.riskMngApiCli.CheckHelper(ctx, r, baseReq)
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckUserFirstRoundByRiskSys risk-mng-api.Check err, req:%+v, err: %v", r, err)
        return false, err
    }
    if resp.ErrCode >= 0 {
        return true, nil
    }
    return false, nil
}
