package conf

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"golang.52tt.com/protocol/app/esport_logic"
	"io/ioutil"
	"os"
	"time"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"google.golang.org/grpc/codes"

	"gitlab.ttyuyin.com/tt-infra/tyr/log"
)

//go:generate mockgen -destination=../mocks/business_conf.go -package=mocks  golang.52tt.com/services/tt-rev/esport/esport-logic/internal/conf IBusinessConfManager
//go:generate mockgen -destination=../../../../../../protocol/services/mocks/esport-hall.go -package=mocks  golang.52tt.com/protocol/services/esport_hall EsportHallClient
//go:generate mockgen -destination=../../../../../../protocol/services/mocks/esport-rcmd.go -package=mocks  golang.52tt.com/protocol/services/esport_rcmd EsportRcmdServiceClient
const (
	BusinessConfPath = "/data/oss/conf-center/tt/"
	BusinessConfFile = "esport-logic.json"
)

var LastConfMd5Sum [md5.Size]byte

type GameEvaluateQuickWord struct {
	GameId             uint32               `json:"game_id"`              // 游戏id
	EvaluateQuickWords []*EvaluateQuickWord `json:"evaluate_quick_words"` // 评价快捷词配置
}

type EsportQuickReplyWord struct {
	CoachReplyWords    []string `json:"coach_reply_words"`    // 大神回复快捷词
	CustomerReplyWords []string `json:"customer_reply_words"` // 客户回复快捷词
}

type UserGroupApi struct {
	DspLpmAdminHost        string `json:"dsp_lpm_admin_host"`
	DspLpmOfflineGroupHost string `json:"dsp_lpm_offline_group_host"`
	DspLpmApiserverHost    string `json:"dsp_lpm_apiserver_host"`
}

type EvaluateQuickWord struct {
	Dimension uint32   `json:"dimension"` // 维度 1-服务 2-技术 3-声音
	Score     float32  `json:"score"`     // 评分 1-5 代表几颗星
	Words     []string `json:"words"`     // 快捷词
}

type ValidTime struct {
	BeginTime int64 `json:"begin_time"`
	EndTime   int64 `json:"end_time"`
}

type ValidTimeV2 struct {
	BeginTime string `json:"begin_time"`
	EndTime   string `json:"end_time"`
}

// ImSelfIntroCardItem 大神IM页访问介绍卡片内容选项
type ImSelfIntroCardItem struct {
	SelfTextIntro        bool     `json:"self_text_intro"`        // 个性签名
	GuaranteeWinningText bool     `json:"guarantee_winning_text"` // 包赢分段
	SelfVideoIntro       bool     `json:"self_video_intro"`       // 语音介绍
	SectionNameList      []string `json:"section_name_list"`      // 其他信息配置名称，用于完全匹配
}

type UGCGameMapToEsportGame struct {
	EsportGameId uint32 `json:"esport_game_id"`
	GameName     string `json:"game_name"`
}

type UGCChannelSetting struct {
	CrowdGroupId           string                             `json:"crowd_group_id"`
	UgcGameMapToEsportGame map[uint32]*UGCGameMapToEsportGame `json:"ugc_game_map_to_esport_game"`
	RecommendListLength    uint32                             `json:"recommend_list_length"`
}

type GrabOrderCfg struct {
	Icon                       string             `json:"icon"`                           // 抢单入口icon
	Title                      string             `json:"title"`                          // 抢单入口标题
	Text                       string             `json:"text"`                           // 抢单入口文案
	SmallIcon                  string             `json:"small_icon"`                     // 抢单入口小icon
	HeartbeatInterval          int64              `json:"heartbeat_interval"`             // 心跳间隔
	CancelReasonList           []string           `json:"cancel_reason_list"`             // 取消原因列表
	FakeHeadImgList            []string           `json:"fake_head_img_list"`             // 假头像列表
	GrabOrderLottieList        []*GrabOrderLottie `json:"grab_order_lottie_list"`         // 抢单lottie动画列表
	ReportClickImRequireSecond uint32             `json:"report_click_im_require_second"` // 上报点击IM 需要达到的秒数
	ReportClickImRequireCount  uint32             `json:"report_click_im_require_count"`  // 上报点击IM 需要达到的次数
}

type GrabOrderLottie struct {
	Url      string `json:"url"`
	Md5      string `json:"md5"`
	Type     uint32 `json:"type"`
	TypeDesc string `json:"type_desc"`
}

type OneKeyFindParmItem struct {
	ParamType    uint32        `json:"param_type"`
	Title        string        `json:"title"`
	OneKeyText   *OneKeyText   `json:"one_key_text"`
	OneKeySelect *OneKeySelect `json:"one_key_select"`
	OneKeyDialog *OneKeyDialog `json:"one_key_dialog"`
}

type OneKeyText struct {
	Text        string `json:"text"`
	TextMaxSize uint32 `json:"text_max_size"`
}

type OneKeySelect struct {
	Tags                   []string `json:"tags"`
	MinSelect              uint32   `json:"min_select"`
	MaxSelect              uint32   `json:"max_select"`
	DefaultSelectIndexList []uint32 `json:"default_select_index_list"`
}

type OneKeyDialog struct {
	Title      string                    `json:"title"`
	Tips       string                    `json:"tips"`
	SelectItem []*OneKeyDialogSelectList `json:"select_item"`
}

type OneKeyDialogSelectList struct {
	Title      string   `json:"title"`
	SelectList []string `json:"select_list"`
}
type CouponConf struct {
	GainCouponCoverTime         uint32 `json:"gain_coupon_cover_time"`          // 领取优惠券封面时间
	GainCouponCoverResource     string `json:"gain_coupon_cover_resource"`      // 领取优惠券封面资源
	GainCouponCoverMd5          string `json:"gain_coupon_cover_md5"`           // 领取优惠券封面md5
	LoginShowSwitch             bool   `json:"login_show_switch"`               // 登录页展示开关
	ShowUnreadManualGrantCoupon bool   `json:"show_unread_manual_grant_coupon"` // 是否展示未读手动发放优惠券
	ShowCouponEntrance          bool   `json:"show_coupon_entrance"`            // 是否展示 电竞列表 优惠券入口
	HomeShowCouponEntrance      bool   `json:"home_show_coupon_entrance"`       // 首页 是否展示优惠券入口
	HomeShowCouponBubbleForever bool   `json:"home_show_coupon_bubble_forever"` // 首页 是否一直显示优惠券气泡
	HomeCouponCornerImg         string `json:"home_coupon_corner_img"`          // 首页 优惠券角标图片
	HomeCouponBubbleExpireText  string `json:"home_coupon_bubble_expire_text"`  // 首页 优惠券气泡过期文案
	HomeCouponBubbleUnusedText  string `json:"home_coupon_bubble_unused_text"`  // 首页 优惠券气泡未使用文案
	HomeCouponBubbleTextType    uint32 `json:"home_coupon_bubble_text_type"`    // 首页 优惠券气泡文案类型 0-无参数 1-N张 2-N元
	HomeShowCouponIcon          bool   `json:"home_show_coupon_icon"`           // 首页 是否展示优惠券图标
}

/*
{
    "119":{
        "性别":{
            "show_style": 1,
            "val_list":[
                {
                    "val": "男",
                    "ui_style": 3,
                    "img_uri":"http://qwwwwww",
                    "text": "男"
                },
                {
                    "val": "女",
                    "ui_style": 3,
                    "img_uri":"http://qwwwwww",
                    "text": "女"
                }
            ]
        },
        "首单优惠":{
            "show_style": 1,
            "val_list":[
                {
                    "val": "首单",
                    "ui_style": 3,
                    "img_uri":"http://qwwwwww",
                    "text": "首单"
                }
            ]
        }
    }
}
*/

type GamePropertyConfItem struct {
	ShowStyle uint32 `json:"show_style"`
	ValList   map[string]*struct {
		UIStyle uint32 `json:"ui_style"`
		ImgUri  string `json:"img_uri"`
		Text    string `json:"text"`
	} `json:"val_list"`
}

// map[gameId][propertyName]配置
type GamePropertyConf map[uint32]map[string]*GamePropertyConfItem

func (g GamePropertyConf) GetPropertyConf(gameId uint32, propertyName string) *GamePropertyConfItem {
	if g != nil {
		if r, ok := g[gameId][propertyName]; ok {
			return r
		}
	}
	return &GamePropertyConfItem{
		ShowStyle: 0, // 默认值
		ValList: make(map[string]*struct {
			UIStyle uint32 `json:"ui_style"`
			ImgUri  string `json:"img_uri"`
			Text    string `json:"text"`
		}), // 默认空
	}
}

type BackRecallConf struct {
	IsShowBackRecallReCoach bool   `json:"is_show_back_recall_re_coach"` // 是否展示新用户退出挽留的推荐大神
	IsAutoPlayAudio         bool   `json:"is_auto_play_audio"`           // 是否自动播放语音
	Title                   string `json:"title"`                        // 标题
}

type XinYongHuChengJieVideoConf struct {
	VideoUrl string `json:"video_url"` // 视频url
	Md5      string `json:"md5"`       // 视频md5
}

type XinYongHuChengJieConf struct {
	VideoMap map[int32]*XinYongHuChengJieVideoConf `json:"video_map"` // 轮播的视频的配置，key 是性别枚举，和账号的性别枚举保持一致，value 是视频的url
}

type BusinessConf struct {
	ReasonText                    [][]string               `json:"reason_text"`
	GodPageUrl                    string                   `json:"god_page_url"`
	GodPageUrlWithMission         string                   `json:"god_page_url_with_mission"`
	VideoDescSwitch               bool                     `json:"videl_desc_switch"`
	ApplyAccessWhiteList          []uint32                 `json:"apply_access_white_list"`
	GameEvaluateQuickWord         []*GameEvaluateQuickWord `json:"game_evaluate_quick_words"` // 评价快捷词配置
	CoachDetailUrl                string                   `json:"coach_detail_url"`
	EntranceValidTime             *ValidTime               `json:"entrance_valid_time"`
	EntranceValidTimeV2           *ValidTimeV2             `json:"entrance_valid_time_v2"`
	OrderFinishEvaluateEntry      bool                     `json:"order_finish_evaluate_entry"`
	FamousPlayerTitle             string                   `json:"famous_player_title"`
	GameCardImgConfig             map[string]string        `json:"game_card_img_config"` // 游戏名片图片配置
	MaxAccount                    int                      `json:"max_account"`
	ImSelfIntroCardConfig         *ImSelfIntroCardItem     `json:"im_self_intro_card_config"`          // 大神IM页访问介绍卡片内容配置
	AudioIntroUrlPrefix           string                   `json:"audio_intro_url_prefix"`             // 语音介绍url前缀
	EsportQuickReplyWord          *EsportQuickReplyWord    `json:"esport_quick_reply_word"`            // 大神IM页快捷回复词配置
	ReportEntTimeOut              int64                    `json:"report_ent_time_out"`                // 订单完成后，举报入口的存留时间，单位秒
	CanCustomerViewOrderEntSwitch bool                     `json:"can_customer_view_order_ent_switch"` // 客服是否可以查看订单入口开关
	UGCChannelSetting             *UGCChannelSetting       `json:"ugc_channel_setting"`
	CouponConf                    *CouponConf              `json:"coupon_conf"` // 优惠券配置
	GamePropertyConf              GamePropertyConf         `json:"game_property_conf"`
	EsportAreaTopBannerConf       []*EsportAreaTopBanner   `json:"esport_area_top_banner_conf"`  // 电竞专区金刚位
	PersonalCoachCrowdIdList      []string                 `json:"personal_coach_crowd_id_list"` // 个人大神的人群包id列表
	UserGroupApi                  *UserGroupApi            `json:"user_group_api"`
	BackRecallConf                *BackRecallConf          `json:"back_recall_conf"`           // 退出挽留配置
	VisitButtonCfg                *VisitButtonCfg          `json:"visit_button_cfg"`           // 访问按钮配置
	XinYongHuChengJieConf         *XinYongHuChengJieConf   `json:"xin_yong_hu_cheng_jie_conf"` // 专区新用户承接
	GrabOrderCfg                  *GrabOrderCfg            `json:"grab_order_cfg"`             // 抢单入口配置
	OneKeyFindParamItem           []*OneKeyFindParmItem    `json:"one_key_find_param_item"`    // 一键找大神参数配置
}

type VisitButtonCfg struct {
	ButtonLottieUrl string `json:"button_lottie_url"` // 按钮lottie动画url
	ButtonLottieMd5 string `json:"button_lottie_md5"` // 按钮lottie动画md5
	ButtonJumpLink  string `json:"button_jump_link"`  // 按钮跳转链接
	FirstEnterTips  string `json:"first_enter_tips"`  // 首次进入提示
	NewVisitorTips  string `json:"new_visitor_tips"`  // 有新访客提示
}

func (c *BusinessConf) Parse(configFile string) (isChange bool, err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return false, err
	}

	md5Sum := md5.Sum(data)
	if md5Sum == LastConfMd5Sum {
		isChange = false
		return
	}

	err = json.Unmarshal(data, &c)
	if err != nil {
		return false, err
	}

	err = c.CheckConf()
	if err != nil {
		return false, err
	}

	LastConfMd5Sum = md5Sum

	if c.ReportEntTimeOut == 0 {
		// 如果是0，则重置为默认值 24小时
		c.ReportEntTimeOut = 24 * 60 * 60
	}

	log.Infof("BusinessConf : %+v", c)
	return true, nil
}

type entranceTimeStu struct {
	BeginTime time.Time
	EndTime   time.Time
	UnableStr string
}
type BusinessConfManager struct {
	Done chan interface{}
	//mutex sync.RWMutex
	conf         *BusinessConf
	entranceTime *entranceTimeStu
}

type EsportAreaTopBanner struct {
	Title              string   `json:"title"`
	SubTitle           string   `json:"sub_title"`
	Sort               uint32   `json:"sort"`
	BackgroundImg      string   `json:"background_img"`
	BackgroundImgSmall string   `json:"background_img_small"`
	BackgroundIcon     string   `json:"background_icon"`
	OperationIcon      string   `json:"operation_icon"`
	JumpLink           string   `json:"jump_link"`
	CollapseItemBg     string   `json:"collapse_item_bg"`
	ActiveTime         []string `json:"active_time"` // 每天生效时段 ["0200-0210", "1400-1410]
}

func (conf *BusinessConf) getEntranceTime() *entranceTimeStu {
	out := &entranceTimeStu{}
	if conf.EntranceValidTimeV2 == nil {
		return out
	}
	startTime, err := time.Parse("15:04", conf.EntranceValidTimeV2.BeginTime)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		startTime, _ = time.Parse("15:04", "00:00")
	}
	endTime, err := time.Parse("15:04", conf.EntranceValidTimeV2.EndTime)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		endTime, _ = time.Parse("15:04", "00:00")
	}

	// 开放时间为XX至XX，请在开放时间再来
	timeStr := fmt.Sprintf("开放时间为%s至%s，请在开放时间再来", conf.EntranceValidTimeV2.BeginTime,
		conf.EntranceValidTimeV2.EndTime)
	// 跨天
	if startTime.After(endTime) && !(endTime.Hour() == 0 && endTime.Minute() == 0) {
		timeStr = fmt.Sprintf("开放时间为%s至次日%s，请在开放时间再来", conf.EntranceValidTimeV2.BeginTime,
			conf.EntranceValidTimeV2.EndTime)
	}
	out.BeginTime = startTime
	out.EndTime = endTime
	out.UnableStr = timeStr
	return out

}

func NewBusinessConfManager() (*BusinessConfManager, error) {
	businessConf := &BusinessConf{}

	businessConfFilePath := BusinessConfPath + BusinessConfFile
	if devBusinessConfPath := os.Getenv("DEV_BUSINESS_CONF_PATH"); devBusinessConfPath != "" {
		businessConfFilePath = devBusinessConfPath + BusinessConfFile
	}
	_, err := businessConf.Parse(businessConfFilePath)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		return nil, err
	}

	confMgr := &BusinessConfManager{
		conf:         businessConf,
		Done:         make(chan interface{}),
		entranceTime: businessConf.getEntranceTime(),
	}
	log.Infof("NewBusinessConfManager %+v, entranceTime:%+v", businessConf, confMgr.entranceTime)
	go confMgr.Watch(businessConfFilePath)

	return confMgr, nil
}

func (bm *BusinessConfManager) Reload(file string) error {
	businessConf := &BusinessConf{}

	isChange, err := businessConf.Parse(file)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		return err
	}

	if isChange {
		//bm.mutex.Lock()
		bm.conf = businessConf
		//bm.mutex.Unlock()
		bm.entranceTime = businessConf.getEntranceTime()
		log.Infof("Reload %+v, entranceTime:%+v", businessConf, bm.entranceTime)
	}

	return nil
}

func (bm *BusinessConfManager) Watch(file string) {
	log.Infof("Watch start. file:%s", file)

	for {
		select {
		case _, ok := <-bm.Done:
			if !ok {
				log.Infof("Watch done")
				return
			}

		case <-time.After(30 * time.Second):
			log.Debugf("Watch check change")

			err := bm.Reload(file)
			if err != nil {
				log.Errorf("Watch Reload fail. file:%s, err:%v", file, err)
			}
		}
	}
}

func (bm *BusinessConfManager) Close() {
	close(bm.Done)
}

func (c *BusinessConf) CheckConf() error {
	return nil
}

func (bm *BusinessConfManager) GetReasonTextList(reasonType uint32) []string {
	if bm.conf != nil && len(bm.conf.ReasonText) >= int(reasonType) {
		return bm.conf.ReasonText[reasonType-1]
	}

	return []string{}
}

func (bm *BusinessConfManager) GetGodPageUrl() string {
	if bm.conf != nil && len(bm.conf.GodPageUrl) > 0 {
		return bm.conf.GodPageUrl
	}

	return ""
}

func (bm *BusinessConfManager) GetVideoDescSwitch() bool {
	if bm.conf != nil {
		return bm.conf.VideoDescSwitch
	}

	return false
}

func (bm *BusinessConfManager) CheckIfInPersonalApplyAccess(uid uint32) bool {
	for _, v := range bm.conf.ApplyAccessWhiteList {
		if v == uid {
			return true
		}
	}
	return false
}

// 失败则弹toast
func (bm *BusinessConfManager) CheckEntranceValid() error {
	now := time.Now().Unix()
	confBeginTime := bm.conf.EntranceValidTime.BeginTime
	confEndTime := bm.conf.EntranceValidTime.EndTime
	if now < confBeginTime || now > confEndTime {
		return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("开放时间为%s至%s，请在开放时间再来",
			time.Unix(confBeginTime, 0).Format("2006-01-02 15:04:05"),
			time.Unix(confEndTime, 0).Format("2006-01-02 15:04:05")))
	}
	return nil
}

func (bm *BusinessConfManager) GetEvaluateQuickWords(gameId uint32) []*EvaluateQuickWord {
	if bm.conf == nil {
		return []*EvaluateQuickWord{}
	}

	for _, v := range bm.conf.GameEvaluateQuickWord {
		if v.GameId == gameId {
			return v.EvaluateQuickWords
		}
	}

	// 若不存在该game_id的配置，则取默认配置
	for _, v := range bm.conf.GameEvaluateQuickWord {
		if v.GameId == 0 {
			return v.EvaluateQuickWords
		}
	}

	return []*EvaluateQuickWord{}
}

func (bm *BusinessConfManager) GetCoachDetailUrl() string {
	if bm.conf != nil && len(bm.conf.CoachDetailUrl) > 0 {
		return bm.conf.CoachDetailUrl
	}

	return "https://www.baidu.com"
}

func (bm *BusinessConfManager) GetOrderFinishEvaluateEntry() bool {
	if bm.conf != nil {
		return bm.conf.OrderFinishEvaluateEntry
	}

	return false
}

func (bm *BusinessConfManager) CheckEntranceValidV2() error {
	// 默认开启
	if bm.entranceTime == nil {
		return nil
	}
	// 相等则全天开启
	if bm.entranceTime.BeginTime.Equal(bm.entranceTime.EndTime) {
		return nil
	}

	currentTime := time.Now()
	// 将年月日设置为相同的日期，以便只比较小时和分钟
	startTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), bm.entranceTime.BeginTime.Hour(), bm.entranceTime.BeginTime.Minute(), 0, 0, time.Local)
	endTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), bm.entranceTime.EndTime.Hour(), bm.entranceTime.EndTime.Minute(), 0, 0, time.Local)
	log.Debugf("CheckEntranceValidV2 currentTime:%v, startTime:%v, endTime:%v", currentTime, startTime, endTime)
	if startTime.After(endTime) {
		if currentTime.After(startTime) || currentTime.Before(endTime) {
			return nil
		} else {
			return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, bm.entranceTime.UnableStr)
		}
	}
	if currentTime.After(startTime) && currentTime.Before(endTime) {
		return nil
	}
	return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, bm.entranceTime.UnableStr)
}

func (bm *BusinessConfManager) GetFamousPlayerTitle() string {
	if bm.conf != nil && len(bm.conf.FamousPlayerTitle) > 0 {
		return bm.conf.FamousPlayerTitle
	}

	return "指导经验"
}

func (bm *BusinessConfManager) GetGameCardImage(gameId uint32) string {
	if bm.conf.GameCardImgConfig == nil {
		return ""
	}

	return bm.conf.GameCardImgConfig[fmt.Sprintf("%d", gameId)]
}

func (bm *BusinessConfManager) GetMaxAccount() int {
	if bm.conf != nil && bm.conf.MaxAccount > 0 {
		return bm.conf.MaxAccount
	}

	return 50
}

func (bm *BusinessConfManager) GetImSelfIntroCardConfig() *ImSelfIntroCardItem {
	if bm.conf != nil || bm.conf.ImSelfIntroCardConfig == nil {
		return &ImSelfIntroCardItem{
			SelfTextIntro: true, // 兜底配置
		}
	}
	return bm.conf.ImSelfIntroCardConfig
}

func (bm *BusinessConfManager) GetAudioIntroUrlPrefix() string {
	if bm.conf == nil {
		return ""
	}

	return bm.conf.AudioIntroUrlPrefix
}

func (bm *BusinessConfManager) GetQuickReplyMsgList(listType uint32) []string {
	if bm.conf == nil || bm.conf.EsportQuickReplyWord == nil {
		return []string{}
	}
	switch listType {
	case uint32(esport_logic.QuickReplyType_QUICK_REPLY_TYPE_COACH):
		return bm.conf.EsportQuickReplyWord.CoachReplyWords
	case uint32(esport_logic.QuickReplyType_QUICK_REPLY_TYPE_PLAYER):
		return bm.conf.EsportQuickReplyWord.CustomerReplyWords
	}
	return []string{}
}

func (bm *BusinessConfManager) GetReportEntTimeOut() int64 {
	return bm.conf.ReportEntTimeOut
}

func (bm *BusinessConfManager) CanCustomerViewOrderEntSwitch() bool {
	return bm.conf.CanCustomerViewOrderEntSwitch
}

func (bm *BusinessConfManager) GetUGCChannelSetting() *UGCChannelSetting {
	if bm.conf.UGCChannelSetting != nil {
		return bm.conf.UGCChannelSetting
	}
	return &UGCChannelSetting{
		CrowdGroupId:           "",
		UgcGameMapToEsportGame: make(map[uint32]*UGCGameMapToEsportGame),
		RecommendListLength:    10,
	}
}

func (bm *BusinessConfManager) GetCouponConf() *CouponConf {
	return bm.conf.CouponConf
}
func (bm *BusinessConfManager) GetGamePropertyConf() GamePropertyConf {
	if bm.conf.CouponConf == nil {
		return make(map[uint32]map[string]*GamePropertyConfItem)
	}
	return bm.conf.GamePropertyConf
}

func (bm *BusinessConfManager) GetEsportAreaTopBannerConf() []*EsportAreaTopBanner {
	if bm.conf == nil {
		return nil
	}
	return bm.conf.EsportAreaTopBannerConf
}

func (bm *BusinessConfManager) GetPersonalCoachCrowdIdList() []string {
	if bm.conf == nil {
		return []string{}
	}
	return bm.conf.PersonalCoachCrowdIdList
}

func (bm *BusinessConfManager) GetUserGroupApi() *UserGroupApi {
	if bm.conf != nil {
		return bm.conf.UserGroupApi
	}
	return nil
}

func (bm *BusinessConfManager) GetBackRecallConf() *BackRecallConf {
	if bm.conf != nil && bm.conf.BackRecallConf != nil {
		if len(bm.conf.BackRecallConf.Title) == 0 {
			bm.conf.BackRecallConf.Title = "灵魂匹配到适合的大神"
		}
		return bm.conf.BackRecallConf
	}
	return &BackRecallConf{
		Title: "灵魂匹配到适合的大神",
	}
}
func (bm *BusinessConfManager) GetXinYongHuChengJieConf() *XinYongHuChengJieConf {
	if bm.conf != nil && bm.conf.XinYongHuChengJieConf != nil {
		return bm.conf.XinYongHuChengJieConf
	}
	return &XinYongHuChengJieConf{}
}

func (bm *BusinessConfManager) GetVisitButtonCfg() *VisitButtonCfg {
	if bm.conf != nil && bm.conf.VisitButtonCfg != nil {
		return bm.conf.VisitButtonCfg
	}
	return &VisitButtonCfg{}
}

func (bm *BusinessConfManager) GetGodPageUrlWithMission() string {
	if bm.conf != nil {
		return bm.conf.GodPageUrlWithMission
	}

	return ""
}

func (bm *BusinessConfManager) GetGrabOrderCfg() *GrabOrderCfg {
	if bm.conf != nil {
		return bm.conf.GrabOrderCfg
	}

	return &GrabOrderCfg{}
}

func (bm *BusinessConfManager) GetOneKeyParamItems() []*OneKeyFindParmItem {
	if bm.conf != nil {
		return bm.conf.OneKeyFindParamItem
	}
	return nil
}

func (bm *BusinessConfManager) GetOneKeyParamItemByTitle(title string) *OneKeyFindParmItem {
	if bm.conf != nil {
		for _, item := range bm.conf.OneKeyFindParamItem {
			if item.Title == title {
				return item
			}
		}
	}
	return nil
}
