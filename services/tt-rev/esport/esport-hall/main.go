package main

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/grpc"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	pb "golang.52tt.com/protocol/services/esport_hall"
	"golang.52tt.com/services/tt-rev/esport/esport-hall/internal/conf"

	"golang.52tt.com/services/tt-rev/esport/esport-hall/internal"

	// use server startup
	"gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/server"

	_ "golang.52tt.com/services/recommend-dialog/tools/grpc_proxy/enable"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/server" // 兼容tyr公共库

    _ "net/http/pprof"
)

func main() {
	var (
		svr *internal.Server
		cfg = &conf.StartConfig{}
		err error
	)

	// config file support yaml & json, default esport-hall.json/yaml
	if err := server.NewServer("esport-hall", cfg).
		AddGrpcServer(grpc.NewBuildOption().
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {

				// 初始化eventlink
				kafka.InitEventLinkSubWithGrpcSvr(s)
				if svr, err = internal.NewServer(ctx, cfg); err != nil {
					return err
				}

				// register custom grpc server
				pb.RegisterEsportHallServer(s, svr)
				return nil
			}),
		).
		WithCloseFunc(func(ctx context.Context) {
			// do something when server terminating
			svr.ShutDown()
		}).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
