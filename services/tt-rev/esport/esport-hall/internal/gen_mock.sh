#!/bin/bash

quicksilver-cli test interface ./conf
mockgen -destination=./mocks/mock_conf.go -package=mocks golang.52tt.com/services/tt-rev/esport/esport-hall/internal/conf IBusinessConfManager

quicksilver-cli test interface ./store
mockgen -destination=./mocks/mock_store.go -package=mocks golang.52tt.com/services/tt-rev/esport/esport-hall/internal/store IStore

quicksilver-cli test interface ./cache
mockgen -destination=./mocks/mock_cache.go -package=mocks golang.52tt.com/services/tt-rev/esport/esport-hall/internal/cache ICache
