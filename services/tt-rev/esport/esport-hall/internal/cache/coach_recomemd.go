package cache

import (
    "context"
    "fmt"
    "strconv"
    "time"

    "github.com/go-redis/redis/v8"
    "golang.52tt.com/pkg/log"
)

const (
    topNSkillProductExpire = 10 * time.Minute
)

func genSkillTopNSkillProductKey(skillId uint32) string {
    return fmt.Sprintf("esport_hall:skill_product_top_n:%d", skillId)
}

func genTagUidKey(originKey string) string {
    return fmt.Sprintf("esport_hall:%s", originKey)
}

func (c *Cache) UpdateSkillProductSearchIndex(ctx context.Context, tagUidSet map[string][]uint32) error {
    _, err := c.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
        for k, set := range tagUidSet {
            var val []interface{}
            for _, item := range set {
                val = append(val, item)
            }

            pipe.Del(ctx, genTagUidKey(k))
            pipe.SAdd(ctx, genTagUidKey(k), val)
            pipe.Expire(ctx, genTagUidKey(k), topNSkillProductExpire)
        }
        return nil
    })
    return err
}

// SearchCoachTagUidMap 搜索标签对应的uid
func (c *Cache) SearchCoachTagUidMap(ctx context.Context, searchKey []string) (map[string][]uint32, error) {
    cmdList := make([]*redis.StringSliceCmd, len(searchKey))
    _, err := c.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
        for i, sk := range searchKey {
            cmdList[i] = pipe.SMembers(ctx, genTagUidKey(sk))
        }
        return nil
    })
    if err != nil {
        return nil, err
    }

    searchRsMap := make(map[string][]uint32)
    for i, cmd := range cmdList {
        uidStrList, err := cmd.Result()
        if err != nil {
            log.ErrorWithCtx(ctx, "cmd.Result, i: %d, err: %v", i, err)
            continue
        }
        for _, uidStr := range uidStrList {
            uid, _ := strconv.Atoi(uidStr)
            if uid == 0 {
                continue
            }
            searchRsMap[searchKey[i]] = append(searchRsMap[searchKey[i]], uint32(uid))
        }
    }

    return searchRsMap, nil
}
