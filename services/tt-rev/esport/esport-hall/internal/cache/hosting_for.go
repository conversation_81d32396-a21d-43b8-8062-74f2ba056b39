package cache

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"strconv"
	"time"
)

// genUserToRealCoachKey 用户跟客服对应的真是大神uid
func genUserToRealCoachKey(uid, csUid uint32) string {
	return fmt.Sprintf("esport:hall:user_to_real_coach:%d:%d", uid, csUid)
}

func (c *Cache) GetUserToRealCoach(ctx context.Context, uid, csUid uint32) (uint32, error) {
	key := genUserToRealCoachKey(uid, csUid)
	val, err := c.cmder.Get(ctx, key).Uint64()
	if err != nil && !redis.IsNil(err) {
		return 0, err
	}
	return uint32(val), nil
}

func (c *Cache) DelUserToRealCoach(ctx context.Context, uid, csUid uint32) error {
	key := genUserToRealCoachKey(uid, csUid)
	return c.cmder.Del(ctx, key).Err()
}

func (c *Cache) SetUserToRealCoach(ctx context.Context, uid, csUid, realCoachUid uint32) error {
	key := genUserToRealCoachKey(uid, csUid)
	return c.cmder.Set(ctx, key, realCoachUid, 24*time.Hour).Err()
}

func (c *Cache) BatGetUserToRealCoach(ctx context.Context, csUid uint32, uidList []uint32) ([]uint32, error) {
	if len(uidList) == 0 {
		return nil, nil
	}
	keys := make([]string, 0, len(uidList))
	for _, uid := range uidList {
		keys = append(keys, genUserToRealCoachKey(uid, csUid))
	}
	vals, err := c.cmder.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, err
	}
	out := make([]uint32, 0, len(uidList))
	for _, val := range vals {
		coachUid, _ := strconv.Atoi(val.(string))
		if coachUid == 0 {
			continue
		}
		out = append(out, uint32(coachUid))
	}
	return out, nil
}
