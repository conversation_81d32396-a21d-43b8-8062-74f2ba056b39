package mgr

import (
    "context"
    "encoding/hex"
    "fmt"
    "time"

    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    auditTypes "golang.52tt.com/pkg/audit"
    "golang.52tt.com/pkg/config"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    protoGrpc "golang.52tt.com/pkg/protocol/grpc"
    pbApp "golang.52tt.com/protocol/app"
    "golang.52tt.com/protocol/app/esport_logic"
    imPB "golang.52tt.com/protocol/app/im"
    "golang.52tt.com/protocol/common/status"
    v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"
    esport_skill "golang.52tt.com/protocol/services/esport-skill"
    pb "golang.52tt.com/protocol/services/esport_hall"
    imApiPB "golang.52tt.com/protocol/services/im-api"
    im_notify "golang.52tt.com/services/tt-rev/esport/common/trade-im-notify"
    trade_im_notify "golang.52tt.com/services/tt-rev/esport/common/trade-im-notify"
    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/conf"
    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/rpc"
    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/user-game-card/store"
    "google.golang.org/grpc/codes"
)

var (
    imGameCardJumpUrl = "tt://m.52tt.com/esport_game_list_page?target_uid=%d"
)

type Mgr struct {
    st          *store.Store
    rpcCli      *rpc.Client
    esportImCli *trade_im_notify.TradeImNotify
    bc          conf.IBusinessConfManager
}

func NewMgr(ctx context.Context, mongoConfig *config.MongoConfig, rpcCli *rpc.Client, bc conf.IBusinessConfManager) (*Mgr, error) {
    esportImCli := trade_im_notify.NewTradeImNotify(rpcCli.AccountCli, rpcCli.SeqCli, rpcCli.TimelineCli, rpcCli.ImApiCli)

    st, err := store.NewStore(ctx, mongoConfig)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewMgr.NewStore, err: %v", err)
        return nil, err
    }

    log.InfoWithCtx(ctx, "NewMgr finish")
    return &Mgr{
        st:          st,
        rpcCli:      rpcCli,
        esportImCli: esportImCli,
        bc:          bc,
    }, nil
}

func (m *Mgr) Shutdown() {}

func (m *Mgr) GetEsportGameCardInfo(ctx context.Context, req *pb.GetEsportGameCardInfoRequest) (*pb.GetEsportGameCardInfoResponse, error) {
    resp := &pb.GetEsportGameCardInfoResponse{}
    if req.GetUid() == 0 {
        log.DebugWithCtx(ctx, "GetEsportGameCardInfo, req: %+v, err: empty req uid")
        return resp, nil
    }
    card, err := m.st.GetUserGameCardById(ctx, req.GetCardId())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetEsportGameCardInfo, req: %+v, err: %v", req, err)
        return resp, err
    }
    if card.ID == 0 {
        return resp, protocol.NewExactServerError(codes.OK, status.ErrEsportHallGameCardNotFound, "游戏名片已删除")
    }
    if card.UID != req.GetUid() {
        log.ErrorWithCtx(ctx, "GetEsportGameCardInfo, req: %+v, err: uid no match")
        return resp, nil
    }

    resp.InfoItemList = m.userGameCard2EsportGameCardInfoItemList(card)
    resp.GameId = card.GameId
    log.InfoWithCtx(ctx, "GetEsportGameCardInfo, req: %+v, resp: %+v", req, resp)
    return resp, nil
}

func (m *Mgr) CreateEsportGameCard(ctx context.Context, req *pb.CreateEsportGameCardRequest) (*pb.CreateEsportGameCardResponse, error) {
    var err error
    resp := &pb.CreateEsportGameCardResponse{}
    if req.GetUid() == 0 {
        log.DebugWithCtx(ctx, "CreateEsportGameCard, req: %+v, err: empty req uid")
        return resp, nil
    }

    card := &store.UserGameCard{
        UID:    req.GetUid(),
        GameId: req.GetGameId(),
        CardInfo: &store.CardInfo{
            ItemList: m.esportGameCardInfoItemList2UserGameCard(req.GetInfoItemList()),
        },
        CreateTime: time.Now(),
        UpdateTime: time.Now(),
    }
    card.ID, err = m.st.InsertUserGameCard(ctx, card)
    if err != nil {
        log.ErrorWithCtx(ctx, "CreateEsportGameCard, req: %+v, err: %v", req, err)
        return resp, err
    }

    ReportESportGameCardLog(ctx, ESportGameCardLogOPCreate, card)

    // 更新im卡片
    if req.GetMsgId() != 0 {
        m.updateCardSendIm(ctx, card, req.GetMsgId(), req.GetUid(), req.GetTargetUid(), false)
        ReportESportGameCardSendLog(ctx, req.GetUid(), req.GetTargetUid(), card.GameId, card.ID, ESportGameCardSendLogActionTypeUser)
    }
    log.InfoWithCtx(ctx, "CreateEsportGameCard, req: %+v, resp: %+v", req, resp)
    return resp, err
}

func (m *Mgr) UpdateEsportGameCard(ctx context.Context, req *pb.UpdateEsportGameCardRequest) (*pb.UpdateEsportGameCardResponse, error) {
    var err error
    resp := &pb.UpdateEsportGameCardResponse{}
    oldCard, err := m.st.GetUserGameCardById(ctx, req.GetCardId())
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateEsportGameCard.GetUserGameCardById, req: %+v, err: %v", req, err)
        return resp, err
    }

    card := &store.UserGameCard{
        ID:     req.GetCardId(),
        UID:    req.GetUid(),
        GameId: oldCard.GameId,
        CardInfo: &store.CardInfo{
            ItemList: m.esportGameCardInfoItemList2UserGameCard(req.GetInfoItemList()),
        },
    }
    err = m.st.UpdateUserGameCard(ctx, card)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateEsportGameCard, req: %+v, err: %v", req, err)
        return resp, err
    }

    ReportESportGameCardLog(ctx, ESportGameCardLogOPModify, card)

    if req.GetMsgId() != 0 {
        m.updateCardSendIm(ctx, card, req.GetMsgId(), req.GetUid(), req.GetTargetUid(), true)
        ReportESportGameCardSendLog(ctx, req.GetUid(), req.GetTargetUid(), card.GameId, card.ID, ESportGameCardSendLogActionTypeUser)
    }
    log.InfoWithCtx(ctx, "UpdateEsportGameCard, req: %+v, resp: %+v", req, resp)
    return resp, err
}

func (m *Mgr) GetEsportGameCardList(ctx context.Context, req *pb.GetEsportGameCardListRequest) (*pb.GetEsportGameCardListResponse, error) {
    resp := &pb.GetEsportGameCardListResponse{}

    cards, err := m.st.GetUserGameCardList(ctx, req.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetEsportGameCardList, req: %+v, err: %v", req, err)
        return resp, err
    }

    resp.CardList = m.batUserGameCard2EsportGameCardInfoList(cards)

    return resp, nil
}

func (m *Mgr) SendEsportGameCard(ctx context.Context, req *pb.SendEsportGameCardRequest) (*pb.SendEsportGameCardResponse, error) {
    resp := &pb.SendEsportGameCardResponse{}
    card, err := m.st.GetUserGameCardById(ctx, req.GetCardId())
    if err != nil {
        log.ErrorWithCtx(ctx, "SendEsportGameCard GetUserGameCardById, req: %+v, err: %v", req, err)
        return resp, err
    }
    if card == nil || card.CardInfo == nil || len(card.CardInfo.ItemList) == 0 {
        log.ErrorWithCtx(ctx, "SendEsportGameCard, req: %+v, err: invalid cardInfo", req)
        return resp, err
    }

    gameDetailResp, err := m.rpcCli.ESportSkillCli.GetGameDetailById(ctx, &esport_skill.GetGameDetailByIdRequest{
        GameId: card.GameId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SendEsportGameCard GetGameDetailById, req: %+v, err: %v", req, err)
        return resp, err
    }

    if !m.isInfoCompletion(gameDetailResp.GetConfig().GetGameCardInfoItemList(), card.CardInfo.ItemList) {
        return resp, protocol.NewExactServerError(codes.OK, status.ErrEsportHallGameCardNotFound, "请完善信息")
    }

    infoItemList := make([]*esport_logic.EsportIMGameCardInfoItem, 0, len(card.CardInfo.ItemList))
    userInfoMap := map[string]*store.UserGameCardInfoItem{}
    for _, item := range card.CardInfo.ItemList {
        userInfoMap[item.ItemName] = item
    }
    for i, item := range gameDetailResp.GetConfig().GetGameCardInfoItemList() {
        if i == 0 {
            continue
        }
        infoItemList = append(infoItemList, &esport_logic.EsportIMGameCardInfoItem{
            Title:   item.GetItemName(),
            Content: userInfoMap[item.GetItemName()].Content,
        })

    }

    // 发送方信息
    fromExtMsg := esport_logic.EsportGameCardImMsg{
        Title:  "这是我的游戏名片",
        Status: uint32(esport_logic.EsportGameCardImMsg_GAME_CARD_STATUS_CHANGE),
        CardInfo: &esport_logic.EsportIMGameCardInfo{
            GameIcon:     gameDetailResp.GetConfig().GetGameIcon(),
            TopContent:   card.CardInfo.ItemList[0].Content,
            InfoItemList: infoItemList,
            CardId:       card.ID,
            GameId:       card.GameId,
        },
        IsSysMsg:  false,
        OuterText: fmt.Sprintf("[游戏名片]%s", gameDetailResp.GetConfig().GetName()),
    }
    fromExtMsgByte, _ := proto.Marshal(&fromExtMsg)
    fromUserMsg := &im_notify.UserMsg{
        Uid:    req.GetUid(),
        ExtMsg: fromExtMsgByte,
    }

    // 接收方信息, fromExtMsg不要用指针
    toExtMsg := fromExtMsg
    toExtMsg.Status = 0
    toExtMsgByte, err := proto.Marshal(&toExtMsg)
    toUserMsg := &im_notify.UserMsg{
        Uid:    req.GetTargetUid(),
        ExtMsg: toExtMsgByte,
    }
    fromMsgId, toMsgId, err := m.esportImCli.SendImMsg(ctx, uint32(imPB.IM_MSG_TYPE_ESPORT_GAME_CARD), fromUserMsg, toUserMsg)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendEsportGameCard SendImMsg, req: %+v, err: %v", req, err)
        return resp, err
    }

    log.InfoWithCtx(ctx, "SendEsportGameCard, fromMsgId: %d, toMsgId: %d", fromMsgId, toMsgId)

    ReportESportGameCardSendLog(ctx, req.GetUid(), req.GetTargetUid(), card.GameId, card.ID, ESportGameCardSendLogActionTypeUser)
    return &pb.SendEsportGameCardResponse{}, nil
}

func (m *Mgr) DeleteEsportGameCard(ctx context.Context, req *pb.DeleteEsportGameCardRequest) (*pb.DeleteEsportGameCardResponse, error) {
    log.InfoWithCtx(ctx, "DeleteEsportGameCard, req: %+v", req)
    err := m.st.DelUserGameCard(ctx, req.GetUid(), req.GetCardId())
    if err != nil {
        log.ErrorWithCtx(ctx, "DeleteEsportGameCard, req: %+v, err: %v", req, err)
        return nil, err
    }

    ReportESportGameCardLog(ctx, ESportGameCardLogOPDelete, &store.UserGameCard{UID: req.GetUid(), ID: req.GetCardId()})
    return &pb.DeleteEsportGameCardResponse{}, nil
}

func (m *Mgr) AutoInitAndSendUserGameCard(ctx context.Context, uid, targetUid, gameId uint32) error {
    log.InfoWithCtx(ctx, "AutoInitAndSendUserGameCard, uid: %d, targetUid: %d, gameId: %d", uid, targetUid, gameId)

    time.Sleep(500 * time.Millisecond) // 延迟推送, 避免跟下单推送交叉

    // 获取电竞游戏配置
    gameDetailResp, err := m.rpcCli.ESportSkillCli.GetGameDetailById(ctx, &esport_skill.GetGameDetailByIdRequest{
        GameId: gameId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "AutoInitAndSendUserGameCard GetGameDetailById, gameId: %d, err: %v", gameId, err)
        return err
    }
    if len(gameDetailResp.GetConfig().GetGameCardInfoItemList()) == 0 {
        log.ErrorWithCtx(ctx, "AutoInitAndSendUserGameCard GetGameDetailById, gameId: %d, err: empty gameCardInfoItemList", gameId)
        return nil
    }

    // 尝试尝试从ugc游戏卡片同步
    err = m.trySyncUgcGameCard(ctx, uid, gameDetailResp.GetConfig())
    if err != nil {
        log.ErrorWithCtx(ctx, "AutoInitAndSendUserGameCard trySyncUgcGameCard, uid: %d, err: %v", uid, err)
        return err
    }

    card, err := m.st.GetUserGameCardByGameIdLatest(ctx, uid, gameId)
    if err != nil {
        log.ErrorWithCtx(ctx, "AutoInitAndSendUserGameCard GetUserGameCardByGameIdLatest, uid: %d, gameId: %d, err: %v", uid, gameId, err)
        return err
    }

    if card.ID == 0 { // 没有卡片/判断配置跟用户信息是否不匹配, 发送填写引导
        if len(gameDetailResp.GetConfig().GetGameCardInfoItemList()) == 0 {
            log.ErrorWithCtx(ctx, "AutoInitAndSendUserGameCard GetGameDetailById, gameId: %d, err: empty gameCardInfoItemList", gameId)
            return nil
        }
        infoItemList := make([]*esport_logic.EsportIMGameCardInfoItem, 0, 8)
        topContent := ""
        for i, item := range gameDetailResp.GetConfig().GetGameCardInfoItemList() {
            if i == 0 {
                topContent = item.GetTips()
                continue
            }
            infoItemList = append(infoItemList, &esport_logic.EsportIMGameCardInfoItem{
                Title:   item.ItemName,
                Content: "请填写",
            })
        }

        userGuidlExtMsgCardInfo := &esport_logic.EsportIMGameCardInfo{
            GameIcon:     gameDetailResp.GetConfig().GetGameIcon(),
            TopContent:   topContent,
            InfoItemList: infoItemList,
            GameId:       gameId,
        }
        userGuidlExtMsg := &esport_logic.EsportGameCardImMsg{
            Title:     "填写游戏名片, 迅速组队",
            Status:    uint32(esport_logic.EsportGameCardImMsg_GAME_CARD_STATUS_FILL),
            CardInfo:  userGuidlExtMsgCardInfo,
            IsSysMsg:  true,
            OuterText: fmt.Sprintf("[游戏名片]%s", gameDetailResp.GetConfig().GetName()),
        }
        userGuidlExtMsgByte, _ := proto.Marshal(userGuidlExtMsg)
        userGuildMsg := &im_notify.ImMsg{
            ExtMsg: userGuidlExtMsgByte,
        }
        _, err = m.esportImCli.SendOneSideImMsg(ctx, uint32(imPB.IM_MSG_TYPE_ESPORT_GAME_CARD), uid, targetUid, 0, userGuildMsg)
        if err != nil {
            log.ErrorWithCtx(ctx, "AutoInitAndSendUserGameCard SendOneSideImMsg, uid: %d, err: %v", uid, err)
        }
        log.InfoWithCtx(ctx, "AutoInitAndSendUserGameCard, uid: %d, targetUid: %d, userGuidlExtMsg: %+v", uid, targetUid, userGuidlExtMsg)
    } else if !m.isInfoCompletion(gameDetailResp.GetConfig().GetGameCardInfoItemList(), card.CardInfo.ItemList) { // 有卡片但信息不全
        userInfoMap := make(map[string]string)
        infoItemList := make([]*esport_logic.EsportIMGameCardInfoItem, 0, len(card.CardInfo.ItemList))
        for _, item := range card.CardInfo.ItemList {
            userInfoMap[item.ItemName] = item.Content
        }

        for i, item := range gameDetailResp.GetConfig().GetGameCardInfoItemList() {
            if i == 0 {
                continue
            }
            optMap := map[string]bool{}
            for _, opt := range item.GetItemList() {
                optMap[opt] = true
            }

            content := userInfoMap[item.GetItemName()]
            if content == "" || !optMap[content] {
                content = "请填写"
            }
            infoItemList = append(infoItemList, &esport_logic.EsportIMGameCardInfoItem{
                Title:   item.ItemName,
                Content: content,
            })
        }

        var topContent string
        if len(card.CardInfo.ItemList) > 0 {
            topContent = card.CardInfo.ItemList[0].Content
        } else {
            log.ErrorWithCtx(ctx, "AutoInitAndSendUserGameCard, card.CardInfo:%+v, card:%+v", card.CardInfo, card)
        }

        fromUserExtMsgCardInfo := esport_logic.EsportIMGameCardInfo{
            GameIcon:     gameDetailResp.GetConfig().GetGameIcon(),
            TopContent:   topContent,
            InfoItemList: infoItemList,
            CardId:       card.ID,
            GameId:       gameId,
        }
        fromUserExtMsg := esport_logic.EsportGameCardImMsg{
            Title:     "这是我的游戏名片(完善信息)",
            Status:    uint32(esport_logic.EsportGameCardImMsg_GAME_CARD_STATUS_CHANGE),
            CardInfo:  &fromUserExtMsgCardInfo,
            IsSysMsg:  true,
            OuterText: fmt.Sprintf("[游戏名片]%s", gameDetailResp.GetConfig().GetName()),
        }
        fromUserExtMsgByte, _ := proto.Marshal(&fromUserExtMsg)
        fromUserMsg := &im_notify.ImMsg{
            ExtMsg: fromUserExtMsgByte,
        }

        _, err = m.esportImCli.SendOneSideImMsg(ctx, uint32(imPB.IM_MSG_TYPE_ESPORT_GAME_CARD), uid, targetUid, 0, fromUserMsg)
        if err != nil {
            log.ErrorWithCtx(ctx, "AutoInitAndSendUserGameCard SendOneSideImMsg, uid: %d, err: %v", uid, err)
        }

    } else {
        if len(card.CardInfo.ItemList) == 0 {
            log.ErrorWithCtx(ctx, "AutoInitAndSendUserGameCard, cardId: %d, err: empty cardInfo", card.ID)
            return nil
        }
        userInfoMap := make(map[string]string)
        infoItemList := make([]*esport_logic.EsportIMGameCardInfoItem, 0, len(card.CardInfo.ItemList))
        for _, item := range card.CardInfo.ItemList {
            userInfoMap[item.ItemName] = item.Content
        }

        for i, item := range gameDetailResp.GetConfig().GetGameCardInfoItemList() {
            if i == 0 {
                continue
            }
            infoItemList = append(infoItemList, &esport_logic.EsportIMGameCardInfoItem{
                Title:   item.ItemName,
                Content: userInfoMap[item.GetItemName()],
            })
        }

        fromUserExtMsgCardInfo := esport_logic.EsportIMGameCardInfo{
            GameIcon:     gameDetailResp.GetConfig().GetGameIcon(),
            TopContent:   card.CardInfo.ItemList[0].Content,
            InfoItemList: infoItemList,
            CardId:       card.ID,
            GameId:       gameId,
        }
        title := "这是我的游戏名片"
        fromUserExtMsg := esport_logic.EsportGameCardImMsg{
            Title:     title + "（可切换卡片）",
            Status:    uint32(esport_logic.EsportGameCardImMsg_GAME_CARD_STATUS_CHANGE),
            CardInfo:  &fromUserExtMsgCardInfo,
            IsSysMsg:  true,
            OuterText: fmt.Sprintf("[游戏名片]%s", gameDetailResp.GetConfig().GetName()),
        }
        fromUserExtMsgByte, _ := proto.Marshal(&fromUserExtMsg)
        fromUserMsg := &im_notify.UserMsg{
            Uid:    uid,
            ExtMsg: fromUserExtMsgByte,
        }

        toUserExtMsg := fromUserExtMsg
        toUserExtMsg.Title = title
        toUserExtMsg.Status = 0
        toUserExtMsg.IsSysMsg = false
        toUserExtMsgByte, _ := proto.Marshal(&toUserExtMsg)
        toUserMsg := &im_notify.UserMsg{
            Uid:    targetUid,
            ExtMsg: toUserExtMsgByte,
        }

        _, _, err = m.esportImCli.SendImMsg(ctx, uint32(imPB.IM_MSG_TYPE_ESPORT_GAME_CARD), fromUserMsg, toUserMsg)
        if err != nil {
            log.ErrorWithCtx(ctx, "AutoInitAndSendUserGameCard SendImMsg, uid: %d, err: %v", uid, err)
            return nil
        }

        // 发送用户侧系统提示文案
        m.sendSingleSystemImNoExpose(ctx, targetUid, uid, "已将你的游戏名片发送给大神")

        log.InfoWithCtx(ctx, "AutoInitAndSendUserGameCard, fromUserExtMsg: %+v, toUserExtMsg: %+v", fromUserExtMsg, toUserExtMsg)

        ReportESportGameCardSendLog(ctx, uid, targetUid, gameId, card.ID, ESportGameCardSendLogActionTypeAuto)
    }
    // 大神侧下单就要提示发送游戏卡片文案, 如果收到卡片则这条消息不外显
    err = m.sendSingleSystemImWithHighLine(ctx, uid, targetUid,
        "发送我的游戏名片, ", "去发送", fmt.Sprintf(imGameCardJumpUrl, uid), false)
    if err != nil {
        log.ErrorWithCtx(ctx, "AutoInitAndSendUserGameCard sendSingleSystemImWithHighLine, uid: %d, err: %v", uid, err)
    }
    return nil
}

// ==================================== internal ===================================
func (m *Mgr) userGameCard2EsportGameCardInfoItemList(card *store.UserGameCard) []*pb.EsportGameCardInfoItem {
    itemList := make([]*pb.EsportGameCardInfoItem, 0)
    for _, item := range card.CardInfo.ItemList {
        itemList = append(itemList, &pb.EsportGameCardInfoItem{
            Title:   item.ItemName,
            Content: item.Content,
        })
    }
    return itemList
}

func (m *Mgr) batUserGameCard2EsportGameCardInfoList(cards []*store.UserGameCard) []*pb.EsportGameCardInfo {
    cardList := make([]*pb.EsportGameCardInfo, 0, len(cards))
    for _, card := range cards {
        itemList := m.userGameCard2EsportGameCardInfoItemList(card)
        cardList = append(cardList, &pb.EsportGameCardInfo{
            InfoItemList: itemList,
            CardId:       card.ID,
            GameId:       card.GameId,
        })
    }
    return cardList
}

func (m *Mgr) esportGameCardInfoItemList2UserGameCard(itemList []*pb.EsportGameCardInfoItem) []*store.UserGameCardInfoItem {
    infoItemList := make([]*store.UserGameCardInfoItem, 0)
    for _, item := range itemList {
        infoItemList = append(infoItemList, &store.UserGameCardInfoItem{
            ItemName: item.Title,
            Content:  item.Content,
        })
    }
    return infoItemList
}

func (m *Mgr) censoringText(ctx context.Context, userInfo *pbApp.UserProfile, text string) error {
    serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "CensoringText, err: get serviceInfo fail")
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    taskContext := &v2.TaskContext{
        SceneCode: string(auditTypes.SCENE_CODE_ESPORT_SKILL_TEXT),
        AppId:     string(auditTypes.APP_ID_QUICKSILVER),
        Scenes:    []v2.Scene{v2.Scene_SCENE_DEFAULT},
        UserInfo: &v2.User{
            Id:       uint64(userInfo.GetUid()),
            Alias:    userInfo.GetAccountAlias(),
            Nickname: userInfo.GetNickname(),
        },
        DeviceInfo: &v2.Device{
            Id: hex.EncodeToString(serviceInfo.DeviceID),
            Ip: serviceInfo.ClientIPAddr().String(),
        },
        BelongObjId: userInfo.GetAccountAlias(),
    }

    verifyRes, serr := m.rpcCli.CensoringProxyCli.Text().SyncScanText(ctx, &v2.SyncTextCheckReq{
        Context: taskContext,
        Text:    text,
        Async:   true,
    })
    if serr != nil {
        log.ErrorWithCtx(ctx, "CensoringText SyncScanText err uid:%d, err:%+v", userInfo.GetUid(), serr)
        return protocol.NewExactServerError(codes.OK, status.ErrEsportHallGameCardInfoInvalid, "内容审核失败")
    }
    log.InfoWithCtx(ctx, "CensoringText SyncScanText. content:%s uid:%d, verifyRes:%+v", text, userInfo.GetUid(), verifyRes)

    // 机审结果 REJECT 不保存记录，直接返回异常
    if v2.Suggestion_REJECT == v2.Suggestion(verifyRes.GetResult()) {
        return protocol.NewExactServerError(codes.OK, status.ErrEsportHallGameCardInfoInvalid, "内容审核不通过")
    }

    return nil
}

func (m *Mgr) updateCardSendIm(ctx context.Context, card *store.UserGameCard, msgId uint64, uid, targetUid uint32, isUpdate bool) error {
    gameDetailResp, err := m.rpcCli.ESportSkillCli.GetGameDetailById(ctx, &esport_skill.GetGameDetailByIdRequest{
        GameId: card.GameId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "updateCardSendIm GetGameDetailById, gameId:%d, err:%+v", card.GameId, err)
        return err
    }

    infoItemList := make([]*esport_logic.EsportIMGameCardInfoItem, 0, len(card.CardInfo.ItemList))
    for i := 1; i < len(card.CardInfo.ItemList); i++ {
        item := card.CardInfo.ItemList[i]
        infoItemList = append(infoItemList, &esport_logic.EsportIMGameCardInfoItem{
            Title:   item.ItemName,
            Content: item.Content,
        })
    }

    msgCardInfo := esport_logic.EsportIMGameCardInfo{
        GameIcon:     gameDetailResp.GetConfig().GetGameIcon(),
        TopContent:   card.CardInfo.ItemList[0].Content,
        InfoItemList: infoItemList,
        CardId:       card.ID,
        GameId:       card.GameId,
    }

    toStatus := uint32(esport_logic.EsportGameCardImMsg_GAME_CARD_STATUS_CHANGE)
    if isUpdate {
        toStatus = uint32(esport_logic.EsportGameCardImMsg_GAME_CARD_STATUS_CHANGED)
    }
    fromUserExtMsg := esport_logic.EsportGameCardImMsg{
        Title:     "这是我的游戏名片",
        Status:    toStatus,
        CardInfo:  &msgCardInfo,
        IsSysMsg:  true,
        OuterText: fmt.Sprintf("[游戏名片]%s", gameDetailResp.GetConfig().GetName()),
    }
    fromUserExtMsgByte, _ := proto.Marshal(&fromUserExtMsg)

    // 发送者原消息更新, 接收者收到一条新消息
    fromUserMsg := &im_notify.ImMsg{
        ServerMsgId: msgId,
        ExtMsg:      fromUserExtMsgByte,
    }

    _, err = m.esportImCli.SendOneSideImMsg(ctx, uint32(imPB.IM_MSG_TYPE_ESPORT_GAME_CARD), uid, targetUid, 0, fromUserMsg)
    if err != nil {
        log.ErrorWithCtx(ctx, "updateCardSendIm SendOneSideImMsg, fromUserExtMsg: %+v, err:%v", fromUserExtMsg, err)
    }

    if isUpdate {
        m.sendSingleSystemImNoExpose(ctx, targetUid, uid, "已将你修改的游戏名片发送给大神")
    } else {
        m.sendSingleSystemImNoExpose(ctx, targetUid, uid, "已将你的游戏名片发送给大神")
    }

    // 复制一份, 改为无状态/非系统消息样式
    toUserExtMsg := fromUserExtMsg
    toUserExtMsg.Status = uint32(esport_logic.EsportGameCardImMsg_GAME_CARD_STATUS_UNSPECIFIED)
    toUserExtMsg.IsSysMsg = false
    toUserExtMsgByte, _ := proto.Marshal(&toUserExtMsg)
    toUserMsg := &im_notify.ImMsg{
        ExtMsg: toUserExtMsgByte,
    }

    _, err = m.esportImCli.SendOneSideImMsg(ctx, uint32(imPB.IM_MSG_TYPE_ESPORT_GAME_CARD), uid, targetUid, 1, toUserMsg)
    if err != nil {
        log.ErrorWithCtx(ctx, "updateCardSendIm SendOneSideImMsg, toUserMsg: %+v, err:%v", toUserMsg, err)
    }

    log.InfoWithCtx(ctx, "updateCardSendIm SendOneSideImMsg, fromUserExtMsg: %+v, toUserExtMsg: %+v", fromUserExtMsg, toUserExtMsg)
    return nil
}

func (m *Mgr) sendSingleSystemImNoExpose(ctx context.Context, fromUid, toUid uint32, content string) error {
    sendReq := &imApiPB.Send1V1ExtMsgReq{
        From: &imApiPB.User{
            Uid: fromUid,
        },
        To: &imApiPB.User{
            Uid: toUid,
        },
        Msg: &imApiPB.ExtMsg{
            MsgType:       uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY),
            Content:       content,
            MsgSourceType: uint32(imPB.MsgSourceType_MSG_SOURCE_FROM_ESPORT),
        },
        Opt: &imApiPB.SendOption{
            IgnoreFrom:     true,
            HasMsgRedpoint: imApiPB.SendOption_BOOL_FALSE,
            HasMsgExposure: imApiPB.SendOption_BOOL_FALSE,
        },
        Namespace: "ESPORT_HALL",
    }
    _, err := m.rpcCli.ImApiCli.Send1V1ExtMsg(ctx, sendReq)
    if err != nil {
        log.ErrorWithCtx(ctx, "Send1V1ExtMsg, req: %+v, err: %v", sendReq, err)
    }
    return err
}

func (m *Mgr) sendSingleSystemImWithHighLine(ctx context.Context, fromUid, toUid uint32, content, highLine, url string, expose bool) error {
    opt := &imApiPB.SendOption{
        IgnoreFrom:     true,
        HasMsgRedpoint: imApiPB.SendOption_BOOL_FALSE,
        HasMsgExposure: imApiPB.SendOption_BOOL_FALSE,
    }
    if expose {
        opt = &imApiPB.SendOption{
            IgnoreFrom:     true,
            HasMsgRedpoint: imApiPB.SendOption_BOOL_TRUE,
            HasMsgExposure: imApiPB.SendOption_BOOL_TRUE,
        }
    }

    sysNotify := &imPB.SystemNotifyMsg{
        HighlightContent: highLine,
        JumpUrl:          url,
    }
    sysNotifyByte, _ := proto.Marshal(sysNotify)

    sendReq := &imApiPB.Send1V1ExtMsgReq{
        From: &imApiPB.User{
            Uid: fromUid,
        },
        To: &imApiPB.User{
            Uid: toUid,
        },
        Msg: &imApiPB.ExtMsg{
            MsgType:       uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY),
            Content:       content + sysNotify.HighlightContent,
            Ext:           sysNotifyByte,
            MsgSourceType: uint32(imPB.MsgSourceType_MSG_SOURCE_FROM_ESPORT),
        },
        Opt:       opt,
        Namespace: "ESPORT_HALL",
    }
    _, err := m.rpcCli.ImApiCli.Send1V1ExtMsg(ctx, sendReq)
    if err != nil {
        log.ErrorWithCtx(ctx, "Send1V1ExtMsg, req: %+v, err: %v", sendReq, err)
    }
    return nil
}

func (m *Mgr) isInfoCompletion(configInfoList []*esport_skill.GameCardInfoItem, userInfoList []*store.UserGameCardInfoItem) bool {
    userInfoMap := make(map[string]*store.UserGameCardInfoItem)
    for _, item := range userInfoList {
        userInfoMap[item.ItemName] = item
    }

    for _, item := range configInfoList {
        // 判断当前信息项用户是否有数据
        userInfo, ok := userInfoMap[item.GetItemName()]
        if !ok {
            return false
        }

        // 判断配置中是否有用户当前选择项
        optExist := false
        for _, optItem := range item.GetItemList() {
            if optItem == userInfo.Content {
                optExist = true
                break
            }
        }

        if len(item.GetItemList()) > 0 && !optExist {
            return false
        }
    }

    return true
}

func (m *Mgr) trySyncUgcGameCard(ctx context.Context, uid uint32, gameCardConf *esport_skill.EsportGameConfig) error {
    // 检查用户是否有名片, 如果一张都没有则根据用户线游戏卡片同步
    hasCard, err := m.st.HadUserGameCard(ctx, uid, gameCardConf.GetGameId())
    if err != nil {
        log.ErrorWithCtx(ctx, "trySyncUgcGameCard HadUserGameCard, uid: %d, err: %v", uid, err)
        return err
    }
    if !hasCard {
        // 获取ugc游戏卡片
        ugcGameCardResp, err := m.rpcCli.GameCardCli.GetGameCard(ctx, uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "trySyncUgcGameCard GetGameCard, uid: %d, err: %v", uid, err)
            return err
        }
        // 标记游戏名片可用的选项
        optMap := make(map[string]bool)
        for _, item := range gameCardConf.GetGameCardInfoItemList() {
            optMap[item.ItemName] = true
        }
        userGameCard := make([]*store.UserGameCard, 0, len(ugcGameCardResp))
        for _, ugcGameCard := range ugcGameCardResp {
            // ugc那边有没有该游戏的卡片
            if ugcGameCard.GameCardName != gameCardConf.GetName() {
                continue
            }

            cardInfo := &store.CardInfo{}
            itemCnt := 1

            // 昵称同步
            nicknameOpt := gameCardConf.GetGameCardInfoItemList()[0].GetItemName()
            for _, ucn := range ugcGameCard.GetGameNicknameList() {
                cardInfo.ItemList = append(cardInfo.ItemList, &store.UserGameCardInfoItem{
                    ItemName: nicknameOpt,
                    Content:  ucn.GetNickname(),
                })
                break
            }
            if len(cardInfo.ItemList) == 0 {
                continue // 无昵称 不同步了
            }

            // 同步其他项目
            for _, opt := range ugcGameCard.OptList {
                optName := opt.OptName // ugc游戏名片
                matchOptName := m.bc.GetCardOpt(opt.OptName)
                if matchOptName != "" {
                    optName = matchOptName
                }
                if !optMap[optName] || len(opt.GetValueList()) == 0 {
                    continue // 当前游戏名片没有该项, 或者没选项值, 跳过
                }

                cardInfo.ItemList = append(cardInfo.ItemList, &store.UserGameCardInfoItem{
                    ItemName: optName,
                    Content:  opt.ValueList[0],
                })
                itemCnt++
            }
            if itemCnt < len(gameCardConf.GetGameCardInfoItemList()) {
                continue
            }
            userGameCard = append(userGameCard, &store.UserGameCard{
                UID:        uid,
                GameId:     gameCardConf.GetGameId(),
                CardInfo:   cardInfo,
                CreateTime: time.Now(),
                UpdateTime: time.Now(),
            })
        }
        if len(userGameCard) > 0 {
            sErr := m.st.BatchInsertUserGameCard(ctx, userGameCard)
            if sErr != nil {
                log.ErrorWithCtx(ctx, "trySyncUgcGameCard BatchInsertUserGameCard, uid: %d, err: %v", uid, sErr)
            }
        }
    }

    return nil
}
