// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/esport/esport-hall/internal/user-game-card/store (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	store "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/user-game-card/store"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// BatchInsertUserGameCard mocks base method.
func (m *MockIStore) BatchInsertUserGameCard(arg0 context.Context, arg1 []*store.UserGameCard) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchInsertUserGameCard", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchInsertUserGameCard indicates an expected call of BatchInsertUserGameCard.
func (mr *MockIStoreMockRecorder) BatchInsertUserGameCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchInsertUserGameCard", reflect.TypeOf((*MockIStore)(nil).BatchInsertUserGameCard), arg0, arg1)
}

// DelUserGameCard mocks base method.
func (m *MockIStore) DelUserGameCard(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserGameCard", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserGameCard indicates an expected call of DelUserGameCard.
func (mr *MockIStoreMockRecorder) DelUserGameCard(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserGameCard", reflect.TypeOf((*MockIStore)(nil).DelUserGameCard), arg0, arg1, arg2)
}

// GenUserGameCardID mocks base method.
func (m *MockIStore) GenUserGameCardID(arg0 context.Context) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenUserGameCardID", arg0)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GenUserGameCardID indicates an expected call of GenUserGameCardID.
func (mr *MockIStoreMockRecorder) GenUserGameCardID(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenUserGameCardID", reflect.TypeOf((*MockIStore)(nil).GenUserGameCardID), arg0)
}

// GetUserGameCardByGameIdLatest mocks base method.
func (m *MockIStore) GetUserGameCardByGameIdLatest(arg0 context.Context, arg1, arg2 uint32) (*store.UserGameCard, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserGameCardByGameIdLatest", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.UserGameCard)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserGameCardByGameIdLatest indicates an expected call of GetUserGameCardByGameIdLatest.
func (mr *MockIStoreMockRecorder) GetUserGameCardByGameIdLatest(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserGameCardByGameIdLatest", reflect.TypeOf((*MockIStore)(nil).GetUserGameCardByGameIdLatest), arg0, arg1, arg2)
}

// GetUserGameCardById mocks base method.
func (m *MockIStore) GetUserGameCardById(arg0 context.Context, arg1 uint32) (*store.UserGameCard, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserGameCardById", arg0, arg1)
	ret0, _ := ret[0].(*store.UserGameCard)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserGameCardById indicates an expected call of GetUserGameCardById.
func (mr *MockIStoreMockRecorder) GetUserGameCardById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserGameCardById", reflect.TypeOf((*MockIStore)(nil).GetUserGameCardById), arg0, arg1)
}

// GetUserGameCardList mocks base method.
func (m *MockIStore) GetUserGameCardList(arg0 context.Context, arg1 uint32) ([]*store.UserGameCard, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserGameCardList", arg0, arg1)
	ret0, _ := ret[0].([]*store.UserGameCard)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserGameCardList indicates an expected call of GetUserGameCardList.
func (mr *MockIStoreMockRecorder) GetUserGameCardList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserGameCardList", reflect.TypeOf((*MockIStore)(nil).GetUserGameCardList), arg0, arg1)
}

// HadUserGameCard mocks base method.
func (m *MockIStore) HadUserGameCard(arg0 context.Context, arg1, arg2 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HadUserGameCard", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HadUserGameCard indicates an expected call of HadUserGameCard.
func (mr *MockIStoreMockRecorder) HadUserGameCard(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HadUserGameCard", reflect.TypeOf((*MockIStore)(nil).HadUserGameCard), arg0, arg1, arg2)
}

// IncCounter mocks base method.
func (m *MockIStore) IncCounter(arg0 context.Context, arg1 string) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncCounter", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// IncCounter indicates an expected call of IncCounter.
func (mr *MockIStoreMockRecorder) IncCounter(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncCounter", reflect.TypeOf((*MockIStore)(nil).IncCounter), arg0, arg1)
}

// InsertUserGameCard mocks base method.
func (m *MockIStore) InsertUserGameCard(arg0 context.Context, arg1 *store.UserGameCard) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertUserGameCard", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertUserGameCard indicates an expected call of InsertUserGameCard.
func (mr *MockIStoreMockRecorder) InsertUserGameCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertUserGameCard", reflect.TypeOf((*MockIStore)(nil).InsertUserGameCard), arg0, arg1)
}

// UpdateUserGameCard mocks base method.
func (m *MockIStore) UpdateUserGameCard(arg0 context.Context, arg1 *store.UserGameCard) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserGameCard", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserGameCard indicates an expected call of UpdateUserGameCard.
func (mr *MockIStoreMockRecorder) UpdateUserGameCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserGameCard", reflect.TypeOf((*MockIStore)(nil).UpdateUserGameCard), arg0, arg1)
}
