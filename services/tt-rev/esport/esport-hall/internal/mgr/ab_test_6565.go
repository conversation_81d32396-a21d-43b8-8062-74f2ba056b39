package mgr

import (
    "context"
    "golang.52tt.com/clients/ugc/friendship"
    "golang.52tt.com/pkg/log"
    esport_statistics "golang.52tt.com/protocol/services/esport-statistics"
    "golang.52tt.com/protocol/services/esport_role"
    "golang.52tt.com/services/tt-rev/esport/common/collection/list"
    "golang.52tt.com/services/tt-rev/esport/common/collection/mapz"
    "golang.52tt.com/services/tt-rev/esport/common/collection/transform"
    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/store"
    "math"
    "sort"
    "sync"
    "time"
)

// 使用有序列表实现的性能更好（1000个假数据，maxLen=50)
// BenchmarkAppendHeap
// BenchmarkAppendHeap/heap
// BenchmarkAppendHeap/heap-8         	  113522	     10194 ns/op
// BenchmarkAppendHeap/list
// BenchmarkAppendHeap/list-8         	  153615	      7836 ns/op
// BenchmarkAppendHeap/map
// BenchmarkAppendHeap/map-8          	   10000	    109406 ns/op

type coachEventInfo struct {
    uid       uint32
    timestamp int64
}

// topNSet 用于6565ab测的工具类
type topNSet struct {
    mu     sync.Mutex
    maxLen int
    items  []*coachEventInfo
}

func newMinHeap(maxLen int) *topNSet {
    return &topNSet{
        items:  make([]*coachEventInfo, 0, maxLen),
        mu:     sync.Mutex{},
        maxLen: maxLen,
    }
}

func (a *topNSet) append(data map[uint32]int64) {
    a.mu.Lock()
    defer a.mu.Unlock()
    for k, v := range data {
        if len(a.items) >= a.maxLen && // 列表的长度已经达到最大值
            a.items[a.maxLen-1].timestamp > v { // 当前的时间戳小于有效列表中最小的时间戳
            continue
        }
        a.push(k, v)
    }
    // 如果长度超过了最大值，则删除最小的
    if len(a.items) > a.maxLen {
        a.items = a.items[:a.maxLen]
    }
}

func (a *topNSet) push(uid uint32, timestamp int64) {
    // 获取有效的列表长度
    ll := int(math.Min(float64(a.maxLen), float64(len(a.items))))
    item := &coachEventInfo{uid: uid, timestamp: timestamp}
    if ll == 0 {
        a.items = append(a.items, item)
        return
    }
    // 遍历判断是否存在
    isExist := false
    for i := 0; i < ll; i++ {
        if a.items[i].uid == uid {
            isExist = true
            // 已经存在，则考虑是否需要更新
            if a.items[i].timestamp < timestamp {
                a.items[i].timestamp = timestamp
            }
            break
        }
    }
    if !isExist {
        // 不存在，则在ll位置插入
        a.items = append(a.items[:ll], item)
        ll += 1
    }
    // 然后重新排序
    sort.Slice(a.items[:ll], func(i, j int) bool {
        return a.items[i].timestamp > a.items[j].timestamp
    })

}

// getUidList 获取uid列表
func (a *topNSet) getUidList() []uint32 {
    if len(a.items) == 0 {
        return []uint32{}
    }
    result := make([]uint32, 0, len(a.items))
    for _, item := range a.items {
        result = append(result, item.uid)
    }
    return result
}

// 【6.56.5】列表页过滤关注/已下单/已私聊大神实验
func (m *Mgr) abTest6565(ctx context.Context, uid uint32, data []*store.SkillProduct, firstRoundAbleCoachUid []uint32, rcmdCtx *RcmdContext) []*store.SkillProduct {
    if uid == 0 {
        return data
    }
    // 获取用户的测试组
    testGroup := rcmdCtx.AbTest6565TestGroup
    if len(testGroup) == 0 {
        // 如果用户不在测试组中，则直接返回
        return data
    }
    // 获取配置
    cf := m.bc.GetABTestConfig()
    if len(cf.AbTest6565Config) == 0 {
        // 如果配置是空的，则直接返回
        log.DebugWithCtx(ctx, "abTest6565, uid: %d, config is empty", uid)
        return data
    }
    groupCf, ok := cf.AbTest6565Config[testGroup]
    if !ok {
        // 如果实验组没有配置，则直接返回
        log.DebugWithCtx(ctx, "abTest6565, uid: %d, group: %s, group config is empty", uid, testGroup)
        return data
    }
    // 预防 firstRoundAbleCoachUid 为nil
    if firstRoundAbleCoachUid == nil {
        firstRoundAbleCoachUid = make([]uint32, 0)
    }
    // 并发执行如下的处理
    coachUidSet := newMinHeap(int(groupCf.NearXCoach))
    wg := sync.WaitGroup{}
    // 从缓存中获取近x天的制定用户私聊过的大神uid
    // 根据配置 groupCf.NearXDay 算出开始和结束的时间戳
    endTime := time.Now().Unix()
    startTime := endTime - int64(groupCf.NearXDay)*24*60*60
    wg.Add(1)
    go func() {
        defer wg.Done()

        coachUids, err := m.cache.GetChatCoachesByTime(ctx, uid, startTime, endTime)
        if err != nil {
            log.ErrorWithCtx(ctx, "abTest6565.GetChatCoachesByTime, uid: %d, err: %v", uid, err)
            return
        }
        log.DebugWithCtx(ctx, "abTest6565.GetChatCoachesByTime, uid: %d, coachUids: %v ", uid, coachUids)
        // 减去首单大神
        coachUids = mapz.Diffk(coachUids, firstRoundAbleCoachUid)
        coachUidSet.append(coachUids)
    }()

    wg.Add(1)
    go func() {
        defer wg.Done()
        following, _, err := m.rpcCli.FriendShipCli.GetUserFollowingList(ctx, uid, false, friendship.NewLoadMoreByID(friendship.SortDescending, ""), 512)
        if err != nil {
            log.ErrorWithCtx(ctx, "abTest6565.GetUserFollowingList, uid: %d, err: %v", uid, err)
            return
        }
        followMap := make(map[uint32]int64)
        followList := make([]uint32, 0, len(following))
        for _, item := range following {
            // 如果关注时间小于开始时间，则不再继续
            if int64(item.GetCreateAt()) < startTime {
                break
            }
            followMap[item.GetToUid()] = int64(item.GetCreateAt())
            followList = append(followList, item.GetToUid())
        }
        if len(followMap) == 0 {
            log.DebugWithCtx(ctx, "abTest6565.GetUserFollowingList, uid: %d, followMap is empty", uid)
            return
        }
        roleResp, sErr := m.rpcCli.ESportRoleCli.BatchGetUserESportRole(ctx, &esport_role.BatchGetUserESportRoleReq{
            Uid: followList,
        })
        if sErr != nil {
            log.ErrorWithCtx(ctx, "abTest6565.BatCheckUserESportRole, uid: %d, err: %v", uid, sErr)
            return
        }
        // 经过 FlatMap 可以过滤出是大神的用户id
        roleIds := transform.FlatMap(roleResp.GetRoleList(), func(role *esport_role.UserESportRole) []uint32 {
            if role.GetEsportRole() == 0 {
                return []uint32{role.GetUid()}
            }
            return []uint32{}
        })

        log.DebugWithCtx(ctx, "abTest6565.GetUserFollowingList, uid: %d, roleIds: %v", uid, roleIds)
        igCoachIds := append(firstRoundAbleCoachUid, roleIds...)
        // 排除
        log.DebugWithCtx(ctx, "abTest6565.GetUserFollowingList, uid: %d, followMap: %v, igCoachIds: %+v", uid, followMap, igCoachIds)
        followMap = mapz.Diffk(followMap, igCoachIds)
        coachUidSet.append(followMap)
    }()

    wg.Add(1)
    go func() {
        defer wg.Done()
        orderCoachResp, err := m.rpcCli.ESportStatCli.GetUserOrderCoachList(ctx, &esport_statistics.GetUserOrderCoachListRequest{
            Uid:       uid,
            StartTime: startTime,
            EndTime:   endTime,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "abTest6565.GetUserOrderCoachList, uid: %d, err: %v", uid, err)
            return
        }
        coachData := orderCoachResp.GetCoachOrderTimeMap()
        log.DebugWithCtx(ctx, "abTest6565.GetUserOrderCoachList, uid: %d, coachData: %v", uid, coachData)
        // 减去首单大神
        coachData = mapz.Diffk(coachData, firstRoundAbleCoachUid)
        coachUidSet.append(coachData)
    }()
    wg.Wait()

    // 获取最多x个大神数据
    coachUids := coachUidSet.getUidList()
    log.DebugWithCtx(ctx, "abTest6565, uid: %d, testGroup: %s, coachUids: %v, firstRoundAbleCoachUid: %v", uid, testGroup, coachUids, firstRoundAbleCoachUid)
    //data 排除在 coachUids 中的大神
    data = list.Diff(data, coachUids, func(product *store.SkillProduct) uint32 {
        return product.Uid
    })
    return data
}
