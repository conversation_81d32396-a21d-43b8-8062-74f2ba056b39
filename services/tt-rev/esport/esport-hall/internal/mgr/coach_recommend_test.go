package mgr

import (
    "context"
    "fmt"
    "github.com/golang/mock/gomock"
    "github.com/stretchr/testify/assert"
    "go.mongodb.org/mongo-driver/bson/primitive"
    pbApp "golang.52tt.com/protocol/app"
    esport_skill "golang.52tt.com/protocol/services/esport-skill"
    pb "golang.52tt.com/protocol/services/esport_hall"
    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/conf"
    internalMocks "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/mocks"
    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/rpc"
    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/store"
    "sync"
    "testing"
    "time"
)

func TestMgr_UpdateNowCoachGroupByIdMapTiming(t *testing.T) {
    m := &Mgr{}
    controller := gomock.NewController(t)
    defer controller.Finish()
    skillCli := esport_skill.NewMockEsportSkillClient(controller)
    skillCli.EXPECT().GetAllGameSimpleInfo(gomock.Any(), gomock.Any()).Return(&esport_skill.GetAllGameSimpleInfoResponse{
        GameList: []*esport_skill.SimpleGameInfo{
            {
                GameId: 17,
            },
            {
                GameId: 119,
            },
        }}, nil).AnyTimes()
    rpcCliMock := &rpc.Client{
        ESportSkillCli: skillCli,
    }
    mockStore := internalMocks.NewMockIStore(controller)
    mockStore.EXPECT().DistinctUidBySkillId(gomock.Any(), gomock.Any()).Return([]uint32{1, 2, 3}, nil).AnyTimes()
    m.rpcCli = rpcCliMock
    m.store = mockStore
    m.UpdateNowCoachGroupByIdMapTiming()
}

func Test_GenTagUidKey(t *testing.T) {
    println(genTagUidKey(119, 4, "价格", "201-300豆"))
}

func TestMgr_GetGamePriceProperty(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    mockBC := internalMocks.NewMockIBusinessConfManager(ctrl)
    mgr := &Mgr{bc: mockBC}

    testCases := []struct {
        name     string
        gameId   uint32
        mockFunc func()
        expected *pb.GetGamePricePropertyResponse
    }{
        {
            name:   "Zero gameId",
            gameId: 0,
            mockFunc: func() {
                mockBC.EXPECT().GetSkillProductPriceSearchCfg(uint32(0)).Return([]string{})
            },
            expected: &pb.GetGamePricePropertyResponse{},
        },
        {
            name:   "Negative gameId",
            gameId: 0xFFFFFFFF, // -1 in uint32
            mockFunc: func() {
                mockBC.EXPECT().GetSkillProductPriceSearchCfg(uint32(0xFFFFFFFF)).Return([]string{})
            },
            expected: &pb.GetGamePricePropertyResponse{},
        },
        {
            name:   "Large gameId",
            gameId: 0xFFFFFFFF,
            mockFunc: func() {
                mockBC.EXPECT().GetSkillProductPriceSearchCfg(uint32(0xFFFFFFFF)).Return([]string{"100", "200", "300"})
            },
            expected: &pb.GetGamePricePropertyResponse{
                PriceProperty: &pb.GameProperty{
                    Name: "价格",
                    ValList: []*pb.GamePropertyVal{
                        {Id: 1, Name: "100"},
                        {Id: 2, Name: "200"},
                        {Id: 3, Name: "300"},
                    },
                },
            },
        },
    }

    for _, tc := range testCases {
        t.Run(tc.name, func(t *testing.T) {
            tc.mockFunc()
            resp, err := mgr.GetGamePriceProperty(context.Background(), tc.gameId)
            assert.NoError(t, err)
            assert.Equal(t, tc.expected, resp)
        })
    }
}

func TestMgr_GetGamePriceProperty_Concurrent(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    mockBC := internalMocks.NewMockIBusinessConfManager(ctrl)
    m := &Mgr{bc: mockBC}

    gameID := uint32(1)
    priceProperty := []string{"Price 1", "Price 2", "Price 3"}

    mockBC.EXPECT().GetSkillProductPriceSearchCfg(gameID).Return(priceProperty).AnyTimes()

    var wg sync.WaitGroup
    numConcurrentCalls := 100

    for i := 0; i < numConcurrentCalls; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()

            resp, err := m.GetGamePriceProperty(context.Background(), gameID)

            assert.NoError(t, err)
            assert.NotNil(t, resp)
            assert.NotNil(t, resp.PriceProperty)
            assert.Equal(t, "价格", resp.PriceProperty.Name)

            assert.Len(t, resp.PriceProperty.ValList, len(priceProperty))
            for i, val := range resp.PriceProperty.ValList {
                assert.Equal(t, uint32(i+1), val.Id)
                assert.Equal(t, priceProperty[i], val.Name)
            }
        }()
    }

    wg.Wait()
}

func TestMgr_UpdateUserOnline_ExactBatchSize(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    mockCache := internalMocks.NewMockICache(ctrl)
    m := &Mgr{cache: mockCache}

    // Prepare test data
    nowCoachMap = make(map[uint32]bool)
    for i := 1; i <= userOnlineBatchSize; i++ {
        nowCoachMap[uint32(i)] = true
    }

    expectedBatchUids := make([]uint32, 0, userOnlineBatchSize)
    for uid := range nowCoachMap {
        expectedBatchUids = append(expectedBatchUids, uid)
    }

    expectedUserOnlineMap := make(map[uint32]bool)
    for _, uid := range expectedBatchUids {
        expectedUserOnlineMap[uid] = true
    }

    // Set up expectations
    mockCache.EXPECT().BatchGetUserOnline(gomock.Any(), gomock.Any()).Return(expectedUserOnlineMap, nil).AnyTimes()

    // Call the function
    m.UpdateUserOnline()

    // Assert the result
    assert.Equal(t, expectedUserOnlineMap, userOnlineMap)
}

func TestMgr_UpdateUserOnline_FewerUsersThanBatchSize(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create a mock cache
    mockCache := internalMocks.NewMockICache(ctrl)

    // Create a new Mgr instance with the mock cache
    mgr := &Mgr{
        cache: mockCache,
    }

    // Set up the test data
    nowCoachMap = map[uint32]bool{
        1: true,
        2: true,
        3: true,
    }
    userOnlineBatchSize = 5

    // Set up the expected result
    expectedUserOnlineMap := map[uint32]bool{
        1: true,
        2: false,
        3: true,
    }

    // Set up the mock cache expectation
    mockCache.EXPECT().BatchGetUserOnline(gomock.Any(), gomock.Any()).Return(expectedUserOnlineMap, nil)

    // Call the UpdateUserOnline function
    mgr.UpdateUserOnline()

    // Assert the result
    assert.Equal(t, expectedUserOnlineMap, userOnlineMap)
}

func TestMgr_ReportExposeCoach(t *testing.T) {
    t.Skip()
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    mockBC := internalMocks.NewMockIBusinessConfManager(ctrl)
    mockCache := internalMocks.NewMockICache(ctrl)

    mgr := &Mgr{
        bc:    mockBC,
        cache: mockCache,
    }

    ctx := context.Background()
    request := &pb.ReportExposeCoachRequest{
        Uid:             1,
        ExposeCoachList: []uint32{1, 2, 3, 4, 5},
        GameId:          10,
    }

    expectedResp := &pb.ReportExposeCoachResponse{}

    // Set up expectations
    mockBC.EXPECT().GetRecommendValConfig().Return(&conf.RecommendValConfig{UnderlayTime: 60}).AnyTimes()
    mockCache.EXPECT().AddUserExposedCoach(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
    mockCache.EXPECT().BatchIncrRecommendTimes(ctx, request.GetGameId(), request.GetExposeCoachList(), gomock.Any()).Return(nil)

    // Call the function
    resp, err := mgr.ReportExposeCoach(ctx, request)

    // Assert the response and error
    assert.NoError(t, err)
    assert.Equal(t, expectedResp, resp)
}

func TestMgr_AddCoachRecommend_ValidRequest(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    mgr := NewTestMockMgr(ctrl)

    ctx := context.Background()
    req := &pb.AddCoachRecommendRequest{
        RecommendInfo: &pb.CoachRecommendInfo{
            Ttid:      "testuser",
            GameId:    1,
            CoachType: 1,
            Sort:      1.0,
            StartTime: uint32(time.Now().Unix()),
            EndTime:   uint32(time.Now().Add(time.Hour).Unix()),
        },
    }

    uid := uint32(123)
    productList := []*store.SkillProduct{
        {SkillId: 1},
    }

    MockAccountCli.EXPECT().GetUidByName(ctx, req.GetRecommendInfo().GetTtid()).Return(uid, "", nil)
    MockCache.EXPECT().GetUserSkillProduct(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("not found"))
    MockStore.EXPECT().FindSkillProductByUid(ctx, uid).Return(productList, nil)
    MockCache.EXPECT().SetUserSkillProduct(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
    MockStore.EXPECT().AddCoachRecommend(ctx, gomock.Any()).Return(nil)

    resp, err := mgr.AddCoachRecommend(ctx, req)

    assert.NoError(t, err)
    assert.Equal(t, &pb.AddCoachRecommendResponse{}, resp)
}

func TestMgr_GetCoachRecommend(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    m := NewTestMockMgr(ctrl)

    ctx := context.Background()
    req := &pb.GetCoachRecommendRequest{
        PageNum:       1,
        PageSize:      2,
        Ttid:          "test_ttid",
        GameId:        1,
        RecommendType: 1,
        StartTime:     1000,
        EndTime:       2000,
    }

    id1, _ := primitive.ObjectIDFromHex("1")
    id2, _ := primitive.ObjectIDFromHex("2")
    id3, _ := primitive.ObjectIDFromHex("3")
    mockData := []*store.CoachRecommend{
        {ID: id1, Uid: 1, Ttid: "test_ttid", GameId: 1, NickName: "Coach 1", CoachType: 1, Sort: 1.0, StartTime: 1100, EndTime: 1200, Deleted: false},
        {ID: id2, Uid: 2, Ttid: "test_ttid", GameId: 1, NickName: "Coach 2", CoachType: 1, Sort: 2.0, StartTime: 1300, EndTime: 1400, Deleted: false},
        {ID: id3, Uid: 3, Ttid: "test_ttid", GameId: 1, NickName: "Coach 3", CoachType: 2, Sort: 3.0, StartTime: 1500, EndTime: 1600, Deleted: false},
    }

    MockStore.EXPECT().CntCoachRecommend(ctx, req.GetTtid(), req.GetGameId(), req.GetRecommendType(), req.GetStartTime(), req.GetEndTime()).Return(int64(len(mockData)), nil)
    MockStore.EXPECT().GetCoachRecommend(ctx, req.GetTtid(), req.GetGameId(), req.GetRecommendType(), req.GetStartTime(), req.GetEndTime()).Return(mockData, nil)
    MockUserProfileCli.EXPECT().BatchGetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*pbApp.UserProfile{}, nil)
    _, err := m.GetCoachRecommend(ctx, req)

    assert.NoError(t, err)
}

func TestMgr_UpdateCoachRecommend(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    mgr := NewTestMockMgr(ctrl)

    ctx := context.Background()

    req := &pb.UpdateCoachRecommendRequest{
        RecommendInfo: &pb.CoachRecommendInfo{
            Id:        "coach_id",
            Ttid:      "coach_ttid",
            GameId:    1,
            CoachType: 1,
            Sort:      100,
            StartTime: **********,
            EndTime:   **********,
        },
    }

    uid := uint32(1234)

    MockAccountCli.EXPECT().GetUidByName(ctx, req.GetRecommendInfo().GetTtid()).Return(uid, "", nil)

    expectedCoachRecommend := &store.CoachRecommend{
        Uid:       uid,
        Ttid:      req.GetRecommendInfo().GetTtid(),
        GameId:    req.GetRecommendInfo().GetGameId(),
        CoachType: req.GetRecommendInfo().GetCoachType(),
        Sort:      req.GetRecommendInfo().GetSort(),
        StartTime: req.GetRecommendInfo().GetStartTime(),
        EndTime:   req.GetRecommendInfo().GetEndTime(),
    }

    MockStore.EXPECT().UpdateCoachRecommend(ctx, req.GetRecommendInfo().GetId(), expectedCoachRecommend).Return(nil)

    resp, err := mgr.UpdateCoachRecommend(ctx, req)

    assert.NoError(t, err)
    assert.Equal(t, &pb.UpdateCoachRecommendResponse{}, resp)
}
