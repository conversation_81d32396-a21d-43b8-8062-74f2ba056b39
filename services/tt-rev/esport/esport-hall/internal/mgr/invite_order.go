package mgr

import (
	"context"
	"github.com/jinzhu/copier"
	"golang.52tt.com/pkg/protocol"
	"time"

	"golang.52tt.com/services/tt-rev/esport/common"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app/esport_logic"
	imPB "golang.52tt.com/protocol/app/im"
	pb "golang.52tt.com/protocol/services/esport_hall"
	imApiPB "golang.52tt.com/protocol/services/im-api"
	trade_im_notify "golang.52tt.com/services/tt-rev/esport/common/trade-im-notify"
	"golang.52tt.com/services/tt-rev/esport/esport-hall/internal/store"
)

func (m *Mgr) InviteOrder(ctx context.Context, request *pb.InviteOrderRequest) (*pb.InviteOrderResponse, error) {
	resp := &pb.InviteOrderResponse{}
	product, err := m.findSkillProductByProductId(ctx, request.GetProductId())
	if err != nil {
		log.ErrorWithCtx(ctx, "findSkillProductByProductId, req: %+v, err: %v", request, err)
		return resp, err
	}

	// 记录邀请
	inviteId := m.store.GenInviteId(ctx)

	coachUserProfile, err := m.rpcCli.UserProfileCli.GetUserProfileV2(ctx, request.GetUid(), false)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserProfileV2, req: %+v, err: %v", request, err)
		return resp, err
	}

	// 计算总价
	price := request.GetGameInfo().GetPrice()
	totalPrice := price.GetPrice() * request.GetOrderCnt()
	coachTotalPrice := totalPrice
	if price.GetDiscountType() == uint32(esport_logic.DiscountType_DISCOUNT_TYPE_FIRST_ROUND) {
		totalPrice = price.GetDiscountPrice() + price.GetPrice()*(request.GetOrderCnt()-1)
		coachTotalPrice = totalPrice
	}
	if price.GetDiscountType() == uint32(esport_logic.DiscountType_DISCOUNT_TYPE_NEW_CUSTOMER) {
		totalPrice = price.GetDiscountPrice()
		coachTotalPrice = totalPrice + request.GetGameInfo().GetNewCustomerUseDetail().GetPlatBonusFee()
	}

	inviteInfo := &esport_logic.ProductOrder{
		Coach:     coachUserProfile,
		ProductId: uint64(request.GetProductId()),
		Name:      request.GetGameInfo().GetName(),
		Icon:      request.GetGameInfo().GetIcon(),
		PriceInfo: &esport_logic.PriceInfo{
			Price:                 request.GetGameInfo().GetPrice().GetPrice(),
			PriceUnit:             request.GetGameInfo().GetPrice().GetPriceUnit(),
			MeasureCnt:            request.GetGameInfo().GetPrice().GetMeasureCnt(),
			MeasureUnit:           request.GetGameInfo().GetPrice().GetMeasureUnit(),
			MaxOrderCnt:           m.bc.GetMaxOrderCntLimit(),
			HasFirstRoundDiscount: request.GetGameInfo().GetPrice().GetHasFirstRoundDiscount(),
			FirstRoundPrice:       request.GetGameInfo().GetPrice().GetFirstRoundPrice(),
			DiscountType:          request.GetGameInfo().GetPrice().GetDiscountType(),
			DiscountDesc:          request.GetGameInfo().GetPrice().GetDiscountDesc(),
			HasDiscount:           request.GetGameInfo().GetPrice().GetHasDiscount(),
			DiscountPrice:         request.GetGameInfo().GetPrice().GetDiscountPrice(),
		},
		Count:            request.GetOrderCnt(),
		TotalPrice:       totalPrice,
		GameId:           product.SkillId,
		GuaranteeWinText: common.GetGranteeWinText(product.GuaranteeWin, product.GetGuaranteeWinTexts()),
		CoachTotalPrice:  coachTotalPrice,
		NewCustomerUseDetail: &esport_logic.NewCustomerUseDetail{
			NewCustomerPrice:       request.GetGameInfo().GetNewCustomerUseDetail().GetNewCustomerPrice(),
			PlatBonusFee:           request.GetGameInfo().GetNewCustomerUseDetail().GetPlatBonusFee(),
			UseNewCustomerDiscount: request.GetGameInfo().GetNewCustomerUseDetail().GetUseNewCustomerDiscount(),
		},
	}

	extMsg := &esport_logic.EsportOrderImMsg{
		MsgTitle:     request.GetComment(),
		ProductOrder: inviteInfo,
		EventType:    uint32(esport_logic.EsportOrderImMsg_EVENT_TYPE_INVITE_ORDER),
		Status:       uint32(esport_logic.InviteOrderStatus_INVITE_ORDER_STATUS_VALID),
		CoachUid:     request.GetUid(),
		PlayerUid:    request.GetInviteUid(),
		EndTime:      time.Now().Add(1 * time.Minute).Unix(),
		InviteId:     inviteId,
	}
	playerExtMsg := m.genInviteImMsg(ctx, extMsg)

	// 推送im
	fromMsgId, toMsgId, err := m.esportImCli.SendOrderImMsg(ctx,
		&trade_im_notify.UserOrderImMsg{
			Uid: request.GetUid(),
			Ext: extMsg,
		},
		&trade_im_notify.UserOrderImMsg{
			Uid: request.GetInviteUid(),
			Ext: playerExtMsg,
		},
	)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendOrderImMsg, req: %+v, err: %v", request, err)
		return resp, err
	}

	// 推送系统消息
	err = m.sendSingleSystemIm(ctx, request.GetInviteUid(), request.GetUid(), "若老板15分钟内未付款，订单将自动取消哦")
	if err != nil {
		log.ErrorWithCtx(ctx, "sendSingleSystemIm, req: %+v, err: %v", request, err)
		return resp, err
	}

	err = m.store.InsertInviteOrderLog(ctx, &store.InviteOrderLog{
		InviteId:       inviteId,
		Uid:            request.GetUid(),
		InviteUid:      request.GetInviteUid(),
		FromMsgId:      fromMsgId,
		ToMsgId:        toMsgId,
		IMExtMsg:       extMsg,
		PlayerIMExtMsg: playerExtMsg,
		CreateTime:     time.Now(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "InsertInviteOrderLog, req: %+v, err: %v", request, err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "InviteOrder, req: %+v, extMsg: %+v err: %v", extMsg, request, err)

	return &pb.InviteOrderResponse{}, nil
}

func (m *Mgr) checkInviteOrderVersion(ctx context.Context, uid uint32) (isOldVersion bool, err error) {
	onlineInfo, err := m.rpcCli.UserOnlineCli.GetMobileOnlineInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMobileOnlineInfo err: %v", err)
		return
	}
	log.DebugWithCtx(ctx, "GetMobileOnlineInfo, uid: %v, onlineInfo: %+v", uid, onlineInfo)
	if onlineInfo.ClientVersion < protocol.FormatClientVersion(6, 51, 3) {
		isOldVersion = true
	}
	return
}

func (m *Mgr) genInviteImMsg(ctx context.Context, src *esport_logic.EsportOrderImMsg) *esport_logic.EsportOrderImMsg {
	isOldVersion, _ := m.checkInviteOrderVersion(ctx, src.PlayerUid)
	if !isOldVersion {
		return src
	}
	dst := &esport_logic.EsportOrderImMsg{}
	_ = copier.Copy(dst, src)
	// 针对老版本im消息展示特殊邀请总价
	dst.ProductOrder.TotalPrice = src.ProductOrder.PriceInfo.Price * src.ProductOrder.Count
	dst.ProductOrder.CoachTotalPrice = dst.ProductOrder.TotalPrice
	return dst
}

func (m *Mgr) HandleInviteOrder(ctx context.Context, request *pb.HandleInviteOrderRequest) (*pb.HandleInviteOrderResponse, error) {
	resp := &pb.HandleInviteOrderResponse{}
	inviteOrderLog, err := m.store.FindInviteOrderLogByInviteId(ctx, request.GetInviteId())
	if err != nil {
		log.ErrorWithCtx(ctx, "FindInviteOrderLogByInviteId, req: %+v, err: %v", request, err)
		return resp, err
	}

	extMsg := inviteOrderLog.IMExtMsg
	extMsg.Status = request.GetStatus()
	playerExtMsg := extMsg
	if inviteOrderLog.PlayerIMExtMsg != nil {
		playerExtMsg = inviteOrderLog.PlayerIMExtMsg
		playerExtMsg.Status = request.GetStatus()
	}

	// 更新邀请状态
	m.esportImCli.SendOrderImMsg(ctx,
		&trade_im_notify.UserOrderImMsg{
			Uid:         inviteOrderLog.Uid,
			ServerMsgId: inviteOrderLog.FromMsgId,
			Ext:         extMsg,
		},

		&trade_im_notify.UserOrderImMsg{
			Uid:         inviteOrderLog.InviteUid,
			ServerMsgId: inviteOrderLog.ToMsgId,
			Ext:         playerExtMsg,
		},
	)

	switch esport_logic.InviteOrderStatus(request.GetStatus()) {
	case esport_logic.InviteOrderStatus_INVITE_ORDER_STATUS_IGNORE:
		err = m.sendSingleSystemIm(ctx, inviteOrderLog.InviteUid, inviteOrderLog.Uid, "老板关闭订单, 订单已失效")
	case esport_logic.InviteOrderStatus_INVITE_ORDER_STATUS_EXPIRED:
		err = m.sendSingleSystemIm(ctx, inviteOrderLog.InviteUid, inviteOrderLog.Uid, "老板未付款，订单已失效")
	default:
		// do nothing
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "sendSingleSystemIm, req: %+v, err: %v", request, err)
		return resp, err
	}

	// 更新邀请状态
	err = m.store.UpdateInviteOrderLogStatus(ctx, request.GetInviteId(), request.GetStatus())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateInviteOrderLogStatus, req: %+v, err: %v", request, err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "HandleInviteOrder, req: %+v, extMsg: %+v err: %v", extMsg, request, err)

	return resp, nil
}

func (m *Mgr) sendSingleSystemIm(ctx context.Context, fromUid, toUid uint32, content string) error {
	sendReq := &imApiPB.Send1V1ExtMsgReq{
		From: &imApiPB.User{
			Uid: fromUid,
		},
		To: &imApiPB.User{
			Uid: toUid,
		},
		Msg: &imApiPB.ExtMsg{
			MsgType:       uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY),
			Content:       content,
			MsgSourceType: uint32(imPB.MsgSourceType_MSG_SOURCE_FROM_ESPORT),
		},
		Opt: &imApiPB.SendOption{
			IgnoreFrom:     true,
			HasMsgRedpoint: imApiPB.SendOption_BOOL_TRUE,
			HasMsgExposure: imApiPB.SendOption_BOOL_TRUE,
		},
		Namespace: "ESPORT_HALL",
	}
	_, err := m.rpcCli.ImApiCli.Send1V1ExtMsg(ctx, sendReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "Send1V1ExtMsg, req: %+v, err: %v", sendReq, err)
	}
	return err
}

func (m *Mgr) inviteOrderExpire() {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	expireInviteOrder, err := m.store.Find15MinAgoInviteOrderLog(ctx)
	if err != nil {
		log.Errorf("inviteOrderExpire fail to Find15MinAgoInviteOrderLog. err:%v", err)
		return
	}

	for _, item := range expireInviteOrder {
		_, err := m.HandleInviteOrder(ctx, &pb.HandleInviteOrderRequest{
			InviteId: item.InviteId,
			Status:   uint32(esport_logic.InviteOrderStatus_INVITE_ORDER_STATUS_EXPIRED),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "inviteOrderExpire fail to HandleInviteOrder. err:%v", err)
		}
	}

	log.InfoWithCtx(ctx, "inviteOrderExpire success, handle %d", len(expireInviteOrder))
}
