package mgr

import (
	"context"
	"fmt"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/esport_hall"
	"golang.52tt.com/services/tt-rev/esport/esport-hall/internal/store"
	"google.golang.org/grpc/codes"
	"time"
)

func (m *Mgr) SetFirstRoundSwitch(ctx context.Context, in *pb.SetFirstRoundSwitchRequest) error {
	log.InfoWithCtx(ctx, "SetFirstRoundSwitch in: %+v", in)

	// 检查总开关是否打开
	discountInfo := m.bc.GetFirstRoundDiscountInfo(in.GetGameId())
	// 并且in是打开
	if !discountInfo.IsOpen && in.GetIsOpen() {
		return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "当前技能不支持首局优惠")
	}

	if in.GetGameId() != 0 {
		// 检查当前存储状态
		skillInfo, err := m.store.FindSkillProductBySkillIdIgnoreStatus(ctx, in.Uid, in.GameId)
		if err != nil {
			log.ErrorWithCtx(ctx, "FindSkillProductBySkillIdIgnoreStatus err: %v", err)
			return err
		}
		if in.IsOpen == skillInfo.FirstRoundSwitch {
			return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "当前已在该状态")
		}
	}

	// 检查次数限制
	cnt, err := m.cache.GetFirstRoundSwitchCnt(ctx, in.Uid, in.GameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFirstRoundSwitchCnt err: %v", err)
		return err
	}
	if uint32(cnt) >= m.bc.GetFirstRoundMaxSwitchPerDay() {
		if in.IsOpen {
			return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "今日无法再开启，明日再来吧")
		} else {
			return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "今日无法再关闭，明日再来吧")
		}
	}

	if in.GetGameId() == 0 {
		err = m.store.SetCoachAllFirstRoundSwitch(ctx, in.Uid, in.IsOpen)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetCoachAllFirstRoundSwitch err: %v", err)
			return err
		}
	} else {
		// 更新状态
		err = m.store.SetFirstRoundSwitch(ctx, in.Uid, in.GameId, in.IsOpen)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetFirstRoundSwitch err: %v", err)
			return err
		}
	}

	// 增加操作次数
	err = m.cache.AddFirstRoundSwitchCnt(ctx, in.Uid, in.GameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddFirstRoundSwitchCnt err: %v", err)
		return err
	}

	// 上报数据
	ReportESportFirstDiscountLog(ctx, in.Uid, in.GameId, discountInfo.DiscountPrice, in.IsOpen)

	return nil
}

func (m *Mgr) GetFirstRoundDiscountInfo(ctx context.Context, in *pb.GetFirstRoundDiscountInfoRequest) (out *pb.GetFirstRoundDiscountInfoResponse, err error) {
	out = &pb.GetFirstRoundDiscountInfoResponse{}
	defer func() {
		log.DebugWithCtx(ctx, "GetFirstRoundDiscountInfo in: %+v, out: %+v, err: %v", in, out, err)
	}()

	discountInfo := m.bc.GetFirstRoundDiscountInfo(in.GetGameId())
	out.ShowSwitch = discountInfo.IsOpen
	out.FirstRoundPrice = discountInfo.DiscountPrice
	if !discountInfo.IsOpen {
		return
	}

	// 支持uid为0
	if in.GetGameId() == 0 {
		var hasOpen bool
		hasOpen, err = m.store.HasSkillProductOpenFirstRound(ctx, in.GetUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "HasSkillProductOpenFirstRound err: %v", err)
			return
		}
		out.IsOpen = hasOpen
	} else {
		var skillInfo *store.SkillProduct
		skillInfo, err = m.store.FindSkillProductBySkillIdIgnoreStatus(ctx, in.Uid, in.GameId)
		if err != nil {
			log.ErrorWithCtx(ctx, "FindSkillProductBySkillIdIgnoreStatus err: %v", err)
			return
		}
		out.IsOpen = skillInfo.FirstRoundSwitch
	}

	return
}

func (m *Mgr) GetFirstRoundDiscountGameList(ctx context.Context, in *pb.GetFirstRoundDiscountGameListRequest) (out *pb.GetFirstRoundDiscountGameListResponse, err error) {
	out = &pb.GetFirstRoundDiscountGameListResponse{GameList: make([]*pb.FirstRoundDiscountGameInfo, 0)}
	defer func() {
		log.DebugWithCtx(ctx, "GetFirstRoundDiscountGameList in: %+v, out: %+v, err: %v", in, out, err)
	}()

	discountMap := m.bc.GetAllFirstRoundDiscountInfo()
	if len(discountMap) == 0 {
		return
	}

	for _, discountInfo := range discountMap {
		if !discountInfo.IsOpen {
			continue
		}
		out.GameList = append(out.GameList, &pb.FirstRoundDiscountGameInfo{
			GameId:          discountInfo.GameId,
			FirstRoundPrice: discountInfo.DiscountPrice,
		})
	}
	return
}

func (m *Mgr) CheckFirstRoundOrderRight(ctx context.Context, in *pb.CheckFirstRoundOrderRightRequest) (out *pb.CheckFirstRoundOrderRightResponse, err error) {
	out = &pb.CheckFirstRoundOrderRightResponse{}
	defer func() {
		log.DebugWithCtx(ctx, "CheckFirstRoundOrderRight in: %+v, out: %+v, err: %v", in, out, err)
	}()

	// 检查总开关是否打开
	discountInfo := m.bc.GetFirstRoundDiscountInfo(in.GetGameId())
	if !discountInfo.IsOpen {
		err = protocol.NewExactServerError(codes.OK, status.ErrEsportsOrderCannotPay, "当前技能不支持首局优惠")
		return
	}
	out.FirstRoundPrice = discountInfo.DiscountPrice

	// 检查大神是否开启首局优惠
	skillInfo, err := m.store.FindSkillProductBySkillId(ctx, in.CoachUid, in.GameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "FindSkillProductBySkillId err: %v", err)
		return
	}
	if !skillInfo.FirstRoundSwitch {
		err = protocol.NewExactServerError(codes.OK, status.ErrEsportsOrderCannotPay, "大神已关闭首局优惠")
		return
	}

	// 用户是否向大神下过首局优惠的单了
	hasRecord, err := m.store.HasFirstRoundOrderRecord(ctx, in.PlayerUid, in.CoachUid, in.GameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "HasFirstRoundOrderRecord err: %v", err)
		return
	}
	if hasRecord {
		err = protocol.NewExactServerError(codes.OK, status.ErrEsportsOrderCannotPay, "您已向该大神下过首局优惠的单了")
		return
	}

	// 检查下单次数是否超过每日限制
	if !in.GetCheckDayLimit() {
		return
	}
	cnt, err := m.store.GetFirstRoundOrderCountToday(ctx, in.PlayerUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFirstRoundOrderCountToday err: %v", err)
		return
	}
	if uint32(cnt) >= m.bc.GetFirstRoundMaxOrderPerDay() {
		err = protocol.NewExactServerError(codes.OK, status.ErrEsportsOrderCannotPay, "今日无法继续享受首局优惠啦\n明天再来吧~")
		return
	}

	return
}

func (m *Mgr) GetFirstRoundOrderRight(ctx context.Context, in *pb.GetFirstRoundOrderRightRequest) (out *pb.GetFirstRoundOrderRightResponse, err error) {
	out = &pb.GetFirstRoundOrderRightResponse{}
	defer func() {
		log.DebugWithCtx(ctx, "GetFirstRoundOrderRight in: %+v, out: %+v, err: %v", in, out, err)
	}()

	// 检查总开关是否打开
	discountInfo := m.bc.GetFirstRoundDiscountInfo(in.GetGameId())
	if !discountInfo.IsOpen {
		return
	}
	out.FirstRoundPrice = discountInfo.DiscountPrice

	// 检查大神是否开启首局优惠
	skillInfo, err := m.store.FindSkillProductBySkillId(ctx, in.CoachUid, in.GameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "FindSkillProductBySkillId err: %v", err)
		return
	}
	if !skillInfo.FirstRoundSwitch {
		return
	}

	// 用户是否向大神下过首局优惠的单了
	hasRecord, err := m.store.HasFirstRoundOrderRecord(ctx, in.PlayerUid, in.CoachUid, in.GameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "HasFirstRoundOrderRecord err: %v", err)
		return
	}
	if hasRecord {
		return
	}
	out.HasFirstRoundDiscount = true

	// 检查下单次数是否超过每日限制
	cnt, err := m.store.GetFirstRoundOrderCountToday(ctx, in.PlayerUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFirstRoundOrderCountToday err: %v", err)
		return
	}
	if uint32(cnt) < m.bc.GetFirstRoundMaxOrderPerDay() {
		out.TodayCanUseFirstRoundDiscount = true
	}

	return
}

func (m *Mgr) ClearFirstRoundOrder(ctx context.Context, in *pb.ClearFirstRoundOrderRequest) (out *pb.ClearFirstRoundOrderResponse, err error) {
	out = &pb.ClearFirstRoundOrderResponse{}
	err = m.store.ClearFirstRoundOrderRecord(ctx, in.PlayerUid, in.CoachUid, in.GameId)
	return
}

func (m *Mgr) GetFirstRoundLabelByUid(ctx context.Context, in *pb.GetFirstRoundLabelByUidRequest) (out *pb.GetFirstRoundLabelByUidResponse, err error) {
	out = &pb.GetFirstRoundLabelByUidResponse{LabelMap: make(map[uint32]*pb.FirstRoundLabel)}
	defer func() {
		log.DebugWithCtx(ctx, "GetFirstRoundLabelByUid in: %+v, out: %+v, err: %v", in, out, err)
	}()

	// 检查总开关是否打开
	discountInfo := m.bc.GetFirstRoundDiscountInfo(in.GetGameId())
	if !discountInfo.IsOpen {
		return
	}

	// 获取开启首局优惠的大神列表
	productList, err := m.store.FindSkillProduct(ctx, in.CoachUidList, in.GameId, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "FindSkillProduct err: %v", err)
		return
	}

	// 获取玩家对首局优惠的使用情况
	useList, err := m.store.FindFirstRoundOrderRecord(ctx, in.PlayerUid, in.CoachUidList, in.GameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "FindFirstRoundOrderRecord err: %v", err)
		return
	}
	useMap := make(map[uint32]bool)
	for _, record := range useList {
		useMap[record.CoachUid] = true
	}

	for _, product := range productList {
		if !product.FirstRoundSwitch {
			continue
		}
		if useMap[product.Uid] {
			continue
		}
		if in.GetPlayerUid() == product.Uid { // 自己看自己不显示标识
			continue
		}

		out.LabelMap[product.Uid] = &pb.FirstRoundLabel{
			PlayerUid:             in.GetPlayerUid(),
			CoachUid:              product.Uid,
			GameId:                product.SkillId,
			HasFirstRoundDiscount: true,
			FirstRoundPrice:       discountInfo.DiscountPrice,
		}
	}

	return
}

func (m *Mgr) GetFirstRoundLabelBySkill(ctx context.Context, in *pb.GetFirstRoundLabelBySkillRequest) (out *pb.GetFirstRoundLabelBySkillResponse, err error) {
	out = &pb.GetFirstRoundLabelBySkillResponse{LabelMap: make(map[uint32]*pb.FirstRoundLabel)}
	defer func() {
		log.DebugWithCtx(ctx, "GetFirstRoundLabelBySkill in: %+v, out: %+v, err: %v", in, out, err)
	}()

	// 获取大神开启首局优惠的技能列表
	productList, err := m.store.FindSkillProduct(ctx, []uint32{in.CoachUid}, 0, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "FindSkillProduct err: %v", err)
		return
	}

	// 获取玩家对首局优惠的使用情况
	useList, err := m.store.FindFirstRoundOrderRecord(ctx, in.PlayerUid, []uint32{in.CoachUid}, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "FindFirstRoundOrderRecord err: %v", err)
		return
	}
	useMap := make(map[uint32]bool)
	for _, record := range useList {
		useMap[record.GameId] = true
	}

	for _, product := range productList {
		if !product.FirstRoundSwitch {
			continue
		}
		if useMap[product.SkillId] {
			continue
		}
		discountInfo := m.bc.GetFirstRoundDiscountInfo(product.SkillId)
		if !discountInfo.IsOpen {
			continue
		}
		if in.GetPlayerUid() == in.GetCoachUid() { // 自己看自己不显示标识
			continue
		}

		out.LabelMap[product.SkillId] = &pb.FirstRoundLabel{
			PlayerUid:             in.GetPlayerUid(),
			CoachUid:              in.GetCoachUid(),
			GameId:                product.SkillId,
			HasFirstRoundDiscount: true,
			FirstRoundPrice:       discountInfo.DiscountPrice,
		}
	}

	return
}

// SearchFirstRoundCoachListRandomly 搜索首局优惠大神列表
func (m *Mgr) SearchFirstRoundCoachListRandomly(ctx context.Context, uid uint32, pageSize uint32, page uint32, gameId uint32, excludeUid []uint32) ([]*pb.GameCoachInfo, error) {

	var data []*store.SkillProduct
	var err error

	if uid == 0 {
		log.ErrorWithCtx(ctx, "GetUGCReCoachCardInfo ServiceInfoFromContext err: get serviceInfo fail")
		data, err = m.store.SearchFirstRoundCoachList(ctx, pageSize, gameId, excludeUid)
		if err != nil {
			log.ErrorWithCtx(ctx, "SearchFirstRoundCoachList err: %v", err)
			return nil, err
		}
	} else {
		// 从缓存中获取用户可用的首单大神
		coachUids, err := m.cache.GetUserFirstRoundAvailableCoachUids(ctx, uid, gameId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserFirstRoundAvailableCoachUids err: %v", err)
			return nil, fmt.Errorf("GetUserFirstRoundAvailableCoachUids err: %w", err)
		}
		// 如果缓存没有，则说明用户没有下过单，直接返回全量的大神即可
		if len(coachUids) == 0 {
			coachUids = m.cache.GetFirstRoundCoachUid(ctx, gameId)
		}
		// 计算coachUids - excludeUid的差集
		availableCoachUids := make([]uint32, 0)
		excludeUidMap := make(map[uint32]bool)
		for _, uid := range excludeUid {
			excludeUidMap[uid] = true
		}
		for _, uid := range coachUids {
			if _, ok := excludeUidMap[uid]; !ok {
				availableCoachUids = append(availableCoachUids, uid)
			}
		}
		data, err = m.store.FindSkillProductByCoachUidsAndGameId(ctx, gameId, availableCoachUids)
		if err != nil {
			log.ErrorWithCtx(ctx, "FindSkillProductByCoachUidsAndGameId err: %v", err)
			return nil, fmt.Errorf("FindSkillProductByCoachUidsAndGameId err: %w", err)
		}
	}

	log.DebugWithCtx(ctx, "SearchFirstRoundCoachList data: %+v", data)
	// 更新价格
	m.updateSameGamePriceBatch(ctx, data)
	return m.skillProduct2PbGameCoachInfo(data), nil
}

// UpdateAllFirstRoundCoachUidCache 更新所有首局优惠大神uid本地缓存
// 先从数据库中获取所有首局优惠大神uid，然后更新本地缓存
func (m *Mgr) UpdateAllFirstRoundCoachUidCache() {
	log.InfoWithCtx(context.Background(), "UpdateAllFirstRoundCoachUidCache start")
	now := time.Now()
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
	defer cancel()
	coachList, err := m.store.GetAllFirstRoundCoachUids(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAllFirstRoundCoachUidCache GetAllFirstRoundCoachUids err: %v", err)
		return
	}
	// 根据skillId分类
	uidsMap := make(map[uint32][]uint32)
	for _, coach := range coachList {
		uidsMap[coach.SkillId] = append(uidsMap[coach.SkillId], coach.Uid)
	}
	// 保存
	for skillId, uids := range uidsMap {
		m.cache.SaveFirstRoundCoachUid(ctx, skillId, uids)
	}
	log.InfoWithCtx(ctx, "UpdateAllFirstRoundCoachUidCache end, cost: %v", time.Since(now))

	// ====================================更新用户可用的首单大神的缓存==============================================
	_ = m.ReBuildUserFirstRoundCache(ctx)

}

// ReBuildUserFirstRoundCache 重建用户首单缓存
// 1. 分页查询用户的首单记录
// 2. 根据用户id进行分类，然后再按照gameId进行分类，最后形成 用户-game-coachId列表 的map记录
// 3. 继续分页查询用户的首单记录，直到查询没有数据为止。
func (m *Mgr) ReBuildUserFirstRoundCache(ctx context.Context, uids ...uint32) error {
	log.DebugWithCtx(ctx, "ReBuildUserFirstRoundCache start")
	now := time.Now()
	defer func() {
		log.InfoWithCtx(ctx, "ReBuildUserFirstRoundCache end, cost: %v", time.Since(now))
	}()
	// Define the page size
	limit := uint32(100)

	// Define a map to store the user-game-coachId records
	userGameCoachMap := make(map[uint32]map[uint32][]uint32)

	var nextoffsetId primitive.ObjectID
	for i := 0; i < 10000; i++ {
		// Get the first round records by page
		records, err := m.store.SearchUserFirstRecordByPage(ctx, nextoffsetId, limit, uids)
		if err != nil {
			return err
		}

		// If there are no more records, break the loop
		if len(records) == 0 {
			break
		}

		// Classify the records by gameId and store them in the map
		for _, record := range records {
			if _, ok := userGameCoachMap[record.PlayerUid]; !ok {
				userGameCoachMap[record.PlayerUid] = make(map[uint32][]uint32)
			}
			userGameCoachMap[record.PlayerUid][record.GameId] = append(userGameCoachMap[record.PlayerUid][record.GameId], record.CoachUid)
		}
		nextoffsetId = records[len(records)-1].ID
	}
	// 对每个用户做操作
	for uid, gameMap := range userGameCoachMap {
		for gameId, alreadyOrderCoachUids := range gameMap {
			// 查询游戏开启了首单的大神
			coachUid := m.cache.GetFirstRoundCoachUid(ctx, gameId)
			// coachUid为空，说明没有开启首单的大神，则跳过
			if len(coachUid) == 0 {
				continue
			}
			// alreadyOrderCoachUids 建立一个索引
			alreadyOrderCoachUidsMap := make(map[uint32]bool)
			for _, coachUid := range alreadyOrderCoachUids {
				alreadyOrderCoachUidsMap[coachUid] = true
			}
			// 计算集合coachUid - alreadyOrderCoachUids的差集
			availableCoachUids := make([]uint32, 0)
			for _, uid := range coachUid {
				if _, ok := alreadyOrderCoachUidsMap[uid]; !ok {
					availableCoachUids = append(availableCoachUids, uid)
				}
			}
			// 保存到缓存
			_ = m.cache.SetUserFirstRoundAvailableCoachUids(ctx, uid, gameId, availableCoachUids)
		}
	}

	return nil
}

// GetUserFirstRoundAvailableCoachUids 获取用户首单可用大神
func (m *Mgr) GetUserFirstRoundAvailableCoachUids(ctx context.Context, uid uint32, gameId uint32) ([]uint32, error) {

	// 从缓存中获取用户可用的首单大神
	coachUids, err := m.cache.GetUserFirstRoundAvailableCoachUids(ctx, uid, gameId)
	if err != nil {
		return nil, fmt.Errorf("GetUserFirstRoundAvailableCoachUids err: %w", err)
	}
	// 如果缓存没有，则说明用户没有下过单，直接返回全量的大神即可
	if len(coachUids) == 0 {
		coachUids = m.cache.GetFirstRoundCoachUid(ctx, gameId)
	}
	return coachUids, nil
}
