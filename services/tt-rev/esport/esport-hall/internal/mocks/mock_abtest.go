// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/esport/esport-hall/internal/rpc (interfaces: IABTestClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIABTestClient is a mock of IABTestClient interface.
type MockIABTestClient struct {
	ctrl     *gomock.Controller
	recorder *MockIABTestClientMockRecorder
}

// MockIABTestClientMockRecorder is the mock recorder for MockIABTestClient.
type MockIABTestClientMockRecorder struct {
	mock *MockIABTestClient
}

// NewMockIABTestClient creates a new mock instance.
func NewMockIABTestClient(ctrl *gomock.Controller) *MockIABTestClient {
	mock := &MockIABTestClient{ctrl: ctrl}
	mock.recorder = &MockIABTestClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIABTestClient) EXPECT() *MockIABTestClientMockRecorder {
	return m.recorder
}

// GetABTestTypeMapSimpleByUid mocks base method.
func (m *MockIABTestClient) GetABTestTypeMapSimpleByUid(arg0 uint32) (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetABTestTypeMapSimpleByUid", arg0)
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetABTestTypeMapSimpleByUid indicates an expected call of GetABTestTypeMapSimpleByUid.
func (mr *MockIABTestClientMockRecorder) GetABTestTypeMapSimpleByUid(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetABTestTypeMapSimpleByUid", reflect.TypeOf((*MockIABTestClient)(nil).GetABTestTypeMapSimpleByUid), arg0)
}
