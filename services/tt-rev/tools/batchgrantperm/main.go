package main

import (
    "context"

    ttrevoperation "golang.52tt.com/protocol/services/tt-rev-operation"
)

func main() {

    cidList := []uint32{141288043, 185562983, 185562987, 185562986, 185563059, 172048167, 141288093, 141288110, 185563013, 185563030, 185563029, 141287995, 141288032, 141288040, 141288066, 141288076, 141288107, 185562982, 185563058, 141288006, 141288010, 185563005, 185563036, 185563038, 185563061, 185563010, 185563006, 185563062, 141288097, 185563008, 185563037, 185563032, 141288049, 141288055, 141288058, 141288080, 185562978, 185562981, 185562985, 185563035, 141288037, 185562980, 185563011, 185563004, 141288081, 141288012, 141287984, 141288061, 141288083, 185562984, 185563007, 185563009, 141288054, 185563034, 141288020, 172048168, 141288064, 185563012, 185563031, 141288016, 141288029, 141288014, 141288035, 141288047, 141288078, 185563033, 141288027, 141288106, 185563060, 141288048, 141288086, 141288088, 185562979}

    //piaCli, err := pia.NewClient()
    //if err != nil {
    //    panic(err)
    //}
    //_, err = piaCli.BatchGrantPiaRoomPermission(context.Background(), &piapb.BatchGrantPiaRoomPermissionReq{
    //    CidList: cidList,
    //})
    //if err != nil {
    //    panic(err)
    //}
    //
    //log.Infof("pia grant success")
    //
    //datingGameCli := channeldatinggame.NewClient()
    //
    //for _, cid := range cidList {
    //    _, err = datingGameCli.TypedStub().OpenDatingGameEntry(context.Background(), &channeldatinggamepb.OpenDatingGameEntryReq{
    //        ChannelId: cid,
    //        Level:     5,
    //    })
    //    if err != nil {
    //        log.Errorf("%d, %v", cid, err)
    //    }
    //}
    //
    //log.Infof("dating grant success")

    operationCli, err := ttrevoperation.NewClient(context.Background())
    if err != nil {
        panic(err)
    }

    opts := make([]*ttrevoperation.ChannelGameplayPerm, 0, len(cidList))
    for _, cid := range cidList {
        opts = append(opts, &ttrevoperation.ChannelGameplayPerm{
            ChannelId: cid,
            TagId:     0,
            Gameplay:  1,
        })
        opts = append(opts, &ttrevoperation.ChannelGameplayPerm{
            ChannelId: cid,
            TagId:     0,
            Gameplay:  2,
        })
        opts = append(opts, &ttrevoperation.ChannelGameplayPerm{
            ChannelId: cid,
            TagId:     0,
            Gameplay:  3,
        })
    }

    _, err = operationCli.BatchOperateChannelGameplayPerm(context.Background(), &ttrevoperation.BatchOperateChannelGameplayPermReq{
        OperationType: 1,
        Operations:    opts,
    })
    if err != nil {
        panic(err)
    }

}
