// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-live-pk/channel-live-pk.proto

package channel_live_pk // import "golang.52tt.com/protocol/services/channel-live-pk"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 邀请pk主播状态
type ApplyMultiPkStatus int32

const (
	ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_UNSPECIFIED ApplyMultiPkStatus = 0
	ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_INVITED     ApplyMultiPkStatus = 1
	ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_ACCEPTED    ApplyMultiPkStatus = 2
	ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_REFUSED     ApplyMultiPkStatus = 3
	ApplyMultiPkStatus_APPLY_MULTI_PK_STATUS_CANCEL      ApplyMultiPkStatus = 4
)

var ApplyMultiPkStatus_name = map[int32]string{
	0: "APPLY_MULTI_PK_STATUS_UNSPECIFIED",
	1: "APPLY_MULTI_PK_STATUS_INVITED",
	2: "APPLY_MULTI_PK_STATUS_ACCEPTED",
	3: "APPLY_MULTI_PK_STATUS_REFUSED",
	4: "APPLY_MULTI_PK_STATUS_CANCEL",
}
var ApplyMultiPkStatus_value = map[string]int32{
	"APPLY_MULTI_PK_STATUS_UNSPECIFIED": 0,
	"APPLY_MULTI_PK_STATUS_INVITED":     1,
	"APPLY_MULTI_PK_STATUS_ACCEPTED":    2,
	"APPLY_MULTI_PK_STATUS_REFUSED":     3,
	"APPLY_MULTI_PK_STATUS_CANCEL":      4,
}

func (x ApplyMultiPkStatus) String() string {
	return proto.EnumName(ApplyMultiPkStatus_name, int32(x))
}
func (ApplyMultiPkStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{0}
}

type EnumMultiPkMatchStatus int32

const (
	EnumMultiPkMatchStatus_MPK_Match_Close    EnumMultiPkMatchStatus = 0
	EnumMultiPkMatchStatus_MPK_Match_Running  EnumMultiPkMatchStatus = 1
	EnumMultiPkMatchStatus_MPK_Match_Applying EnumMultiPkMatchStatus = 2
)

var EnumMultiPkMatchStatus_name = map[int32]string{
	0: "MPK_Match_Close",
	1: "MPK_Match_Running",
	2: "MPK_Match_Applying",
}
var EnumMultiPkMatchStatus_value = map[string]int32{
	"MPK_Match_Close":    0,
	"MPK_Match_Running":  1,
	"MPK_Match_Applying": 2,
}

func (x EnumMultiPkMatchStatus) String() string {
	return proto.EnumName(EnumMultiPkMatchStatus_name, int32(x))
}
func (EnumMultiPkMatchStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{1}
}

// 处理PK邀请枚举
type HandleApplyMultiPkType int32

const (
	HandleApplyMultiPkType_HANDLE_APPLY_MULTI_PK_TYPE_UNSPECIFIED HandleApplyMultiPkType = 0
	HandleApplyMultiPkType_HANDLE_APPLY_MULTI_PK_TYPE_ACCEPT      HandleApplyMultiPkType = 1
	HandleApplyMultiPkType_HANDLE_APPLY_MULTI_PK_TYPE_REFUSE      HandleApplyMultiPkType = 2
)

var HandleApplyMultiPkType_name = map[int32]string{
	0: "HANDLE_APPLY_MULTI_PK_TYPE_UNSPECIFIED",
	1: "HANDLE_APPLY_MULTI_PK_TYPE_ACCEPT",
	2: "HANDLE_APPLY_MULTI_PK_TYPE_REFUSE",
}
var HandleApplyMultiPkType_value = map[string]int32{
	"HANDLE_APPLY_MULTI_PK_TYPE_UNSPECIFIED": 0,
	"HANDLE_APPLY_MULTI_PK_TYPE_ACCEPT":      1,
	"HANDLE_APPLY_MULTI_PK_TYPE_REFUSE":      2,
}

func (x HandleApplyMultiPkType) String() string {
	return proto.EnumName(HandleApplyMultiPkType_name, int32(x))
}
func (HandleApplyMultiPkType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{2}
}

// PK模式枚举
type MultiPkType int32

const (
	MultiPkType_MULTI_PK_TYPE_UNSPECIFIED MultiPkType = 0
	MultiPkType_MULTI_PK_TYPE_SINGLE      MultiPkType = 1
	MultiPkType_MULTI_PK_TYPE_TEAM        MultiPkType = 2
)

var MultiPkType_name = map[int32]string{
	0: "MULTI_PK_TYPE_UNSPECIFIED",
	1: "MULTI_PK_TYPE_SINGLE",
	2: "MULTI_PK_TYPE_TEAM",
}
var MultiPkType_value = map[string]int32{
	"MULTI_PK_TYPE_UNSPECIFIED": 0,
	"MULTI_PK_TYPE_SINGLE":      1,
	"MULTI_PK_TYPE_TEAM":        2,
}

func (x MultiPkType) String() string {
	return proto.EnumName(MultiPkType_name, int32(x))
}
func (MultiPkType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{3}
}

// PK阶段
type EnumMultiPkStatus int32

const (
	EnumMultiPkStatus_Multi_Pk_Invalid             EnumMultiPkStatus = 0
	EnumMultiPkStatus_Multi_Pk_Link_Mic            EnumMultiPkStatus = 1
	EnumMultiPkStatus_Multi_Pk_First_Kill          EnumMultiPkStatus = 2
	EnumMultiPkStatus_Multi_Pk_Common              EnumMultiPkStatus = 3
	EnumMultiPkStatus_Multi_Pk_Interact            EnumMultiPkStatus = 4
	EnumMultiPkStatus_MULTI_PK_END_BUT_LINKING_MIC EnumMultiPkStatus = 5
)

var EnumMultiPkStatus_name = map[int32]string{
	0: "Multi_Pk_Invalid",
	1: "Multi_Pk_Link_Mic",
	2: "Multi_Pk_First_Kill",
	3: "Multi_Pk_Common",
	4: "Multi_Pk_Interact",
	5: "MULTI_PK_END_BUT_LINKING_MIC",
}
var EnumMultiPkStatus_value = map[string]int32{
	"Multi_Pk_Invalid":             0,
	"Multi_Pk_Link_Mic":            1,
	"Multi_Pk_First_Kill":          2,
	"Multi_Pk_Common":              3,
	"Multi_Pk_Interact":            4,
	"MULTI_PK_END_BUT_LINKING_MIC": 5,
}

func (x EnumMultiPkStatus) String() string {
	return proto.EnumName(EnumMultiPkStatus_name, int32(x))
}
func (EnumMultiPkStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{4}
}

// pk结果枚举
type MultiPkResult int32

const (
	MultiPkResult_MultiPkResult_Invalid MultiPkResult = 0
	MultiPkResult_MultiPkResult_Win     MultiPkResult = 1
	MultiPkResult_MultiPkResult_Lost    MultiPkResult = 2
	MultiPkResult_MultiPkResult_Same    MultiPkResult = 3
)

var MultiPkResult_name = map[int32]string{
	0: "MultiPkResult_Invalid",
	1: "MultiPkResult_Win",
	2: "MultiPkResult_Lost",
	3: "MultiPkResult_Same",
}
var MultiPkResult_value = map[string]int32{
	"MultiPkResult_Invalid": 0,
	"MultiPkResult_Win":     1,
	"MultiPkResult_Lost":    2,
	"MultiPkResult_Same":    3,
}

func (x MultiPkResult) String() string {
	return proto.EnumName(MultiPkResult_name, int32(x))
}
func (MultiPkResult) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{5}
}

// pk队伍类型
type MultiPkTeamType int32

const (
	MultiPkTeamType_MultiPkTeamType_Invalid MultiPkTeamType = 0
	MultiPkTeamType_MultiPkTeamType_APPLY   MultiPkTeamType = 1
	MultiPkTeamType_MultiPkTeamType_MATCH   MultiPkTeamType = 2
)

var MultiPkTeamType_name = map[int32]string{
	0: "MultiPkTeamType_Invalid",
	1: "MultiPkTeamType_APPLY",
	2: "MultiPkTeamType_MATCH",
}
var MultiPkTeamType_value = map[string]int32{
	"MultiPkTeamType_Invalid": 0,
	"MultiPkTeamType_APPLY":   1,
	"MultiPkTeamType_MATCH":   2,
}

func (x MultiPkTeamType) String() string {
	return proto.EnumName(MultiPkTeamType_name, int32(x))
}
func (MultiPkTeamType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{6}
}

type EnumApply int32

const (
	EnumApply_accept EnumApply = 0
	EnumApply_reject EnumApply = 1
	EnumApply_delete EnumApply = 2
	EnumApply_cancel EnumApply = 3
	EnumApply_apply  EnumApply = 4
)

var EnumApply_name = map[int32]string{
	0: "accept",
	1: "reject",
	2: "delete",
	3: "cancel",
	4: "apply",
}
var EnumApply_value = map[string]int32{
	"accept": 0,
	"reject": 1,
	"delete": 2,
	"cancel": 3,
	"apply":  4,
}

func (x EnumApply) String() string {
	return proto.EnumName(EnumApply_name, int32(x))
}
func (EnumApply) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{7}
}

type EnumChannelLivePKStatus int32

const (
	EnumChannelLivePKStatus_IDLE   EnumChannelLivePKStatus = 0
	EnumChannelLivePKStatus_BEGIN  EnumChannelLivePKStatus = 1
	EnumChannelLivePKStatus_TOOL   EnumChannelLivePKStatus = 2
	EnumChannelLivePKStatus_LAST   EnumChannelLivePKStatus = 3
	EnumChannelLivePKStatus_PUNISH EnumChannelLivePKStatus = 4
	EnumChannelLivePKStatus_FINISH EnumChannelLivePKStatus = 5
)

var EnumChannelLivePKStatus_name = map[int32]string{
	0: "IDLE",
	1: "BEGIN",
	2: "TOOL",
	3: "LAST",
	4: "PUNISH",
	5: "FINISH",
}
var EnumChannelLivePKStatus_value = map[string]int32{
	"IDLE":   0,
	"BEGIN":  1,
	"TOOL":   2,
	"LAST":   3,
	"PUNISH": 4,
	"FINISH": 5,
}

func (x EnumChannelLivePKStatus) String() string {
	return proto.EnumName(EnumChannelLivePKStatus_name, int32(x))
}
func (EnumChannelLivePKStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{8}
}

// PK中，对面房间语音状态
type ChannelLiveOpponentMicFlag int32

const (
	ChannelLiveOpponentMicFlag_CPKMic_OPEN  ChannelLiveOpponentMicFlag = 0
	ChannelLiveOpponentMicFlag_CPKMic_CLOSE ChannelLiveOpponentMicFlag = 1
)

var ChannelLiveOpponentMicFlag_name = map[int32]string{
	0: "CPKMic_OPEN",
	1: "CPKMic_CLOSE",
}
var ChannelLiveOpponentMicFlag_value = map[string]int32{
	"CPKMic_OPEN":  0,
	"CPKMic_CLOSE": 1,
}

func (x ChannelLiveOpponentMicFlag) String() string {
	return proto.EnumName(ChannelLiveOpponentMicFlag_name, int32(x))
}
func (ChannelLiveOpponentMicFlag) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{9}
}

type ChannelLivePKMatchType int32

const (
	ChannelLivePKMatchType_CPK_Match_Nornal  ChannelLivePKMatchType = 0
	ChannelLivePKMatchType_CPK_Match_Rank    ChannelLivePKMatchType = 1
	ChannelLivePKMatchType_CPK_Match_rand    ChannelLivePKMatchType = 2
	ChannelLivePKMatchType_CPK_Match_Appoint ChannelLivePKMatchType = 3
)

var ChannelLivePKMatchType_name = map[int32]string{
	0: "CPK_Match_Nornal",
	1: "CPK_Match_Rank",
	2: "CPK_Match_rand",
	3: "CPK_Match_Appoint",
}
var ChannelLivePKMatchType_value = map[string]int32{
	"CPK_Match_Nornal":  0,
	"CPK_Match_Rank":    1,
	"CPK_Match_rand":    2,
	"CPK_Match_Appoint": 3,
}

func (x ChannelLivePKMatchType) String() string {
	return proto.EnumName(ChannelLivePKMatchType_name, int32(x))
}
func (ChannelLivePKMatchType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{10}
}

type ItemType int32

const (
	ItemType_Invalid         ItemType = 0
	ItemType_Effect_Type     ItemType = 1
	ItemType_Score_Type      ItemType = 2
	ItemType_Percent_Type    ItemType = 3
	ItemType_First_Kill_Type ItemType = 4
	ItemType_Deduct_Type     ItemType = 5
)

var ItemType_name = map[int32]string{
	0: "Invalid",
	1: "Effect_Type",
	2: "Score_Type",
	3: "Percent_Type",
	4: "First_Kill_Type",
	5: "Deduct_Type",
}
var ItemType_value = map[string]int32{
	"Invalid":         0,
	"Effect_Type":     1,
	"Score_Type":      2,
	"Percent_Type":    3,
	"First_Kill_Type": 4,
	"Deduct_Type":     5,
}

func (x ItemType) String() string {
	return proto.EnumName(ItemType_name, int32(x))
}
func (ItemType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{11}
}

type EnumPkMatch int32

const (
	EnumPkMatch_PKM_Match_Close   EnumPkMatch = 0
	EnumPkMatch_PKM_Matching      EnumPkMatch = 1
	EnumPkMatch_PKM_Match_Success EnumPkMatch = 2
)

var EnumPkMatch_name = map[int32]string{
	0: "PKM_Match_Close",
	1: "PKM_Matching",
	2: "PKM_Match_Success",
}
var EnumPkMatch_value = map[string]int32{
	"PKM_Match_Close":   0,
	"PKM_Matching":      1,
	"PKM_Match_Success": 2,
}

func (x EnumPkMatch) String() string {
	return proto.EnumName(EnumPkMatch_name, int32(x))
}
func (EnumPkMatch) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{12}
}

// 黑名单类型
type BlacklistType int32

const (
	BlacklistType_BLACKLIST_TYPE_UNSPECIFIED BlacklistType = 0
	BlacklistType_BLACKLIST_TYPE_PK          BlacklistType = 1
	BlacklistType_BLACKLIST_TYPE_MULTI_PK    BlacklistType = 2
	BlacklistType_BLACKLIST_TYPE_BOTH        BlacklistType = 3
)

var BlacklistType_name = map[int32]string{
	0: "BLACKLIST_TYPE_UNSPECIFIED",
	1: "BLACKLIST_TYPE_PK",
	2: "BLACKLIST_TYPE_MULTI_PK",
	3: "BLACKLIST_TYPE_BOTH",
}
var BlacklistType_value = map[string]int32{
	"BLACKLIST_TYPE_UNSPECIFIED": 0,
	"BLACKLIST_TYPE_PK":          1,
	"BLACKLIST_TYPE_MULTI_PK":    2,
	"BLACKLIST_TYPE_BOTH":        3,
}

func (x BlacklistType) String() string {
	return proto.EnumName(BlacklistType_name, int32(x))
}
func (BlacklistType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{13}
}

// 黑名单状态
type BlacklistStatus int32

const (
	BlacklistStatus_BLACKLIST_STATUS_UNSPECIFIED BlacklistStatus = 0
	BlacklistStatus_BLACKLIST_STATUS_EFFECTIVE   BlacklistStatus = 1
	BlacklistStatus_BLACKLIST_STATUS_EXPIRED     BlacklistStatus = 2
	BlacklistStatus_BLACKLIST_STATUS_WAITING     BlacklistStatus = 3
	BlacklistStatus_BLACKLIST_STATUS_RECALLED    BlacklistStatus = 4
)

var BlacklistStatus_name = map[int32]string{
	0: "BLACKLIST_STATUS_UNSPECIFIED",
	1: "BLACKLIST_STATUS_EFFECTIVE",
	2: "BLACKLIST_STATUS_EXPIRED",
	3: "BLACKLIST_STATUS_WAITING",
	4: "BLACKLIST_STATUS_RECALLED",
}
var BlacklistStatus_value = map[string]int32{
	"BLACKLIST_STATUS_UNSPECIFIED": 0,
	"BLACKLIST_STATUS_EFFECTIVE":   1,
	"BLACKLIST_STATUS_EXPIRED":     2,
	"BLACKLIST_STATUS_WAITING":     3,
	"BLACKLIST_STATUS_RECALLED":    4,
}

func (x BlacklistStatus) String() string {
	return proto.EnumName(BlacklistStatus_name, int32(x))
}
func (BlacklistStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{14}
}

type GetAppointPkInfoListReq_QueryType int32

const (
	GetAppointPkInfoListReq_Query_All    GetAppointPkInfoListReq_QueryType = 0
	GetAppointPkInfoListReq_Query_By_Uid GetAppointPkInfoListReq_QueryType = 1
	GetAppointPkInfoListReq_Query_By_Ts  GetAppointPkInfoListReq_QueryType = 2
)

var GetAppointPkInfoListReq_QueryType_name = map[int32]string{
	0: "Query_All",
	1: "Query_By_Uid",
	2: "Query_By_Ts",
}
var GetAppointPkInfoListReq_QueryType_value = map[string]int32{
	"Query_All":    0,
	"Query_By_Uid": 1,
	"Query_By_Ts":  2,
}

func (x GetAppointPkInfoListReq_QueryType) String() string {
	return proto.EnumName(GetAppointPkInfoListReq_QueryType_name, int32(x))
}
func (GetAppointPkInfoListReq_QueryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{93, 0}
}

// 排行榜，首杀那些需要神秘人的当时状态信息，所以定义一个神秘人信息结构
type UkwInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Level                uint32   `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`
	Medal                string   `protobuf:"bytes,5,opt,name=medal,proto3" json:"medal,omitempty"`
	HeadFrame            string   `protobuf:"bytes,6,opt,name=head_frame,json=headFrame,proto3" json:"head_frame,omitempty"`
	FakeUid              uint32   `protobuf:"varint,7,opt,name=fake_uid,json=fakeUid,proto3" json:"fake_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UkwInfo) Reset()         { *m = UkwInfo{} }
func (m *UkwInfo) String() string { return proto.CompactTextString(m) }
func (*UkwInfo) ProtoMessage()    {}
func (*UkwInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{0}
}
func (m *UkwInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UkwInfo.Unmarshal(m, b)
}
func (m *UkwInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UkwInfo.Marshal(b, m, deterministic)
}
func (dst *UkwInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UkwInfo.Merge(dst, src)
}
func (m *UkwInfo) XXX_Size() int {
	return xxx_messageInfo_UkwInfo.Size(m)
}
func (m *UkwInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UkwInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UkwInfo proto.InternalMessageInfo

func (m *UkwInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UkwInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UkwInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UkwInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *UkwInfo) GetMedal() string {
	if m != nil {
		return m.Medal
	}
	return ""
}

func (m *UkwInfo) GetHeadFrame() string {
	if m != nil {
		return m.HeadFrame
	}
	return ""
}

func (m *UkwInfo) GetFakeUid() uint32 {
	if m != nil {
		return m.FakeUid
	}
	return 0
}

// 获取直播多人PK权限
type GetLiveMultiPkPerReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveMultiPkPerReq) Reset()         { *m = GetLiveMultiPkPerReq{} }
func (m *GetLiveMultiPkPerReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveMultiPkPerReq) ProtoMessage()    {}
func (*GetLiveMultiPkPerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{1}
}
func (m *GetLiveMultiPkPerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveMultiPkPerReq.Unmarshal(m, b)
}
func (m *GetLiveMultiPkPerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveMultiPkPerReq.Marshal(b, m, deterministic)
}
func (dst *GetLiveMultiPkPerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveMultiPkPerReq.Merge(dst, src)
}
func (m *GetLiveMultiPkPerReq) XXX_Size() int {
	return xxx_messageInfo_GetLiveMultiPkPerReq.Size(m)
}
func (m *GetLiveMultiPkPerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveMultiPkPerReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveMultiPkPerReq proto.InternalMessageInfo

func (m *GetLiveMultiPkPerReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type GetLiveMultiPkPerResp struct {
	UidEntryMap          map[uint32]bool `protobuf:"bytes,1,rep,name=uid_entry_map,json=uidEntryMap,proto3" json:"uid_entry_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetLiveMultiPkPerResp) Reset()         { *m = GetLiveMultiPkPerResp{} }
func (m *GetLiveMultiPkPerResp) String() string { return proto.CompactTextString(m) }
func (*GetLiveMultiPkPerResp) ProtoMessage()    {}
func (*GetLiveMultiPkPerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{2}
}
func (m *GetLiveMultiPkPerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveMultiPkPerResp.Unmarshal(m, b)
}
func (m *GetLiveMultiPkPerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveMultiPkPerResp.Marshal(b, m, deterministic)
}
func (dst *GetLiveMultiPkPerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveMultiPkPerResp.Merge(dst, src)
}
func (m *GetLiveMultiPkPerResp) XXX_Size() int {
	return xxx_messageInfo_GetLiveMultiPkPerResp.Size(m)
}
func (m *GetLiveMultiPkPerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveMultiPkPerResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveMultiPkPerResp proto.InternalMessageInfo

func (m *GetLiveMultiPkPerResp) GetUidEntryMap() map[uint32]bool {
	if m != nil {
		return m.UidEntryMap
	}
	return nil
}

// 初始化组队信息
type IniLiveMultiPkTeamReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IniLiveMultiPkTeamReq) Reset()         { *m = IniLiveMultiPkTeamReq{} }
func (m *IniLiveMultiPkTeamReq) String() string { return proto.CompactTextString(m) }
func (*IniLiveMultiPkTeamReq) ProtoMessage()    {}
func (*IniLiveMultiPkTeamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{3}
}
func (m *IniLiveMultiPkTeamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IniLiveMultiPkTeamReq.Unmarshal(m, b)
}
func (m *IniLiveMultiPkTeamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IniLiveMultiPkTeamReq.Marshal(b, m, deterministic)
}
func (dst *IniLiveMultiPkTeamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IniLiveMultiPkTeamReq.Merge(dst, src)
}
func (m *IniLiveMultiPkTeamReq) XXX_Size() int {
	return xxx_messageInfo_IniLiveMultiPkTeamReq.Size(m)
}
func (m *IniLiveMultiPkTeamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IniLiveMultiPkTeamReq.DiscardUnknown(m)
}

var xxx_messageInfo_IniLiveMultiPkTeamReq proto.InternalMessageInfo

func (m *IniLiveMultiPkTeamReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type IniLiveMultiPkTeamResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IniLiveMultiPkTeamResp) Reset()         { *m = IniLiveMultiPkTeamResp{} }
func (m *IniLiveMultiPkTeamResp) String() string { return proto.CompactTextString(m) }
func (*IniLiveMultiPkTeamResp) ProtoMessage()    {}
func (*IniLiveMultiPkTeamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{4}
}
func (m *IniLiveMultiPkTeamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IniLiveMultiPkTeamResp.Unmarshal(m, b)
}
func (m *IniLiveMultiPkTeamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IniLiveMultiPkTeamResp.Marshal(b, m, deterministic)
}
func (dst *IniLiveMultiPkTeamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IniLiveMultiPkTeamResp.Merge(dst, src)
}
func (m *IniLiveMultiPkTeamResp) XXX_Size() int {
	return xxx_messageInfo_IniLiveMultiPkTeamResp.Size(m)
}
func (m *IniLiveMultiPkTeamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IniLiveMultiPkTeamResp.DiscardUnknown(m)
}

var xxx_messageInfo_IniLiveMultiPkTeamResp proto.InternalMessageInfo

// 多人pk邀请
type ApplyLiveMultiPkReq struct {
	ApplyUid             uint32   `protobuf:"varint,1,opt,name=apply_uid,json=applyUid,proto3" json:"apply_uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyLiveMultiPkReq) Reset()         { *m = ApplyLiveMultiPkReq{} }
func (m *ApplyLiveMultiPkReq) String() string { return proto.CompactTextString(m) }
func (*ApplyLiveMultiPkReq) ProtoMessage()    {}
func (*ApplyLiveMultiPkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{5}
}
func (m *ApplyLiveMultiPkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyLiveMultiPkReq.Unmarshal(m, b)
}
func (m *ApplyLiveMultiPkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyLiveMultiPkReq.Marshal(b, m, deterministic)
}
func (dst *ApplyLiveMultiPkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyLiveMultiPkReq.Merge(dst, src)
}
func (m *ApplyLiveMultiPkReq) XXX_Size() int {
	return xxx_messageInfo_ApplyLiveMultiPkReq.Size(m)
}
func (m *ApplyLiveMultiPkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyLiveMultiPkReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyLiveMultiPkReq proto.InternalMessageInfo

func (m *ApplyLiveMultiPkReq) GetApplyUid() uint32 {
	if m != nil {
		return m.ApplyUid
	}
	return 0
}

func (m *ApplyLiveMultiPkReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type ApplyLiveMultiPkResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyLiveMultiPkResp) Reset()         { *m = ApplyLiveMultiPkResp{} }
func (m *ApplyLiveMultiPkResp) String() string { return proto.CompactTextString(m) }
func (*ApplyLiveMultiPkResp) ProtoMessage()    {}
func (*ApplyLiveMultiPkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{6}
}
func (m *ApplyLiveMultiPkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyLiveMultiPkResp.Unmarshal(m, b)
}
func (m *ApplyLiveMultiPkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyLiveMultiPkResp.Marshal(b, m, deterministic)
}
func (dst *ApplyLiveMultiPkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyLiveMultiPkResp.Merge(dst, src)
}
func (m *ApplyLiveMultiPkResp) XXX_Size() int {
	return xxx_messageInfo_ApplyLiveMultiPkResp.Size(m)
}
func (m *ApplyLiveMultiPkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyLiveMultiPkResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyLiveMultiPkResp proto.InternalMessageInfo

type DisinviteChannelLiveMultiPkReq struct {
	ApplyUid             uint32   `protobuf:"varint,1,opt,name=apply_uid,json=applyUid,proto3" json:"apply_uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisinviteChannelLiveMultiPkReq) Reset()         { *m = DisinviteChannelLiveMultiPkReq{} }
func (m *DisinviteChannelLiveMultiPkReq) String() string { return proto.CompactTextString(m) }
func (*DisinviteChannelLiveMultiPkReq) ProtoMessage()    {}
func (*DisinviteChannelLiveMultiPkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{7}
}
func (m *DisinviteChannelLiveMultiPkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisinviteChannelLiveMultiPkReq.Unmarshal(m, b)
}
func (m *DisinviteChannelLiveMultiPkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisinviteChannelLiveMultiPkReq.Marshal(b, m, deterministic)
}
func (dst *DisinviteChannelLiveMultiPkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisinviteChannelLiveMultiPkReq.Merge(dst, src)
}
func (m *DisinviteChannelLiveMultiPkReq) XXX_Size() int {
	return xxx_messageInfo_DisinviteChannelLiveMultiPkReq.Size(m)
}
func (m *DisinviteChannelLiveMultiPkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DisinviteChannelLiveMultiPkReq.DiscardUnknown(m)
}

var xxx_messageInfo_DisinviteChannelLiveMultiPkReq proto.InternalMessageInfo

func (m *DisinviteChannelLiveMultiPkReq) GetApplyUid() uint32 {
	if m != nil {
		return m.ApplyUid
	}
	return 0
}

func (m *DisinviteChannelLiveMultiPkReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type DisinviteChannelLiveMultiPkResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisinviteChannelLiveMultiPkResp) Reset()         { *m = DisinviteChannelLiveMultiPkResp{} }
func (m *DisinviteChannelLiveMultiPkResp) String() string { return proto.CompactTextString(m) }
func (*DisinviteChannelLiveMultiPkResp) ProtoMessage()    {}
func (*DisinviteChannelLiveMultiPkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{8}
}
func (m *DisinviteChannelLiveMultiPkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisinviteChannelLiveMultiPkResp.Unmarshal(m, b)
}
func (m *DisinviteChannelLiveMultiPkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisinviteChannelLiveMultiPkResp.Marshal(b, m, deterministic)
}
func (dst *DisinviteChannelLiveMultiPkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisinviteChannelLiveMultiPkResp.Merge(dst, src)
}
func (m *DisinviteChannelLiveMultiPkResp) XXX_Size() int {
	return xxx_messageInfo_DisinviteChannelLiveMultiPkResp.Size(m)
}
func (m *DisinviteChannelLiveMultiPkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DisinviteChannelLiveMultiPkResp.DiscardUnknown(m)
}

var xxx_messageInfo_DisinviteChannelLiveMultiPkResp proto.InternalMessageInfo

// 获取最近多人PK的主播列表
type GetLiveMultiPkRecordListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveMultiPkRecordListReq) Reset()         { *m = GetLiveMultiPkRecordListReq{} }
func (m *GetLiveMultiPkRecordListReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveMultiPkRecordListReq) ProtoMessage()    {}
func (*GetLiveMultiPkRecordListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{9}
}
func (m *GetLiveMultiPkRecordListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveMultiPkRecordListReq.Unmarshal(m, b)
}
func (m *GetLiveMultiPkRecordListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveMultiPkRecordListReq.Marshal(b, m, deterministic)
}
func (dst *GetLiveMultiPkRecordListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveMultiPkRecordListReq.Merge(dst, src)
}
func (m *GetLiveMultiPkRecordListReq) XXX_Size() int {
	return xxx_messageInfo_GetLiveMultiPkRecordListReq.Size(m)
}
func (m *GetLiveMultiPkRecordListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveMultiPkRecordListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveMultiPkRecordListReq proto.InternalMessageInfo

func (m *GetLiveMultiPkRecordListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetLiveMultiPkRecordListResp struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveMultiPkRecordListResp) Reset()         { *m = GetLiveMultiPkRecordListResp{} }
func (m *GetLiveMultiPkRecordListResp) String() string { return proto.CompactTextString(m) }
func (*GetLiveMultiPkRecordListResp) ProtoMessage()    {}
func (*GetLiveMultiPkRecordListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{10}
}
func (m *GetLiveMultiPkRecordListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveMultiPkRecordListResp.Unmarshal(m, b)
}
func (m *GetLiveMultiPkRecordListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveMultiPkRecordListResp.Marshal(b, m, deterministic)
}
func (dst *GetLiveMultiPkRecordListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveMultiPkRecordListResp.Merge(dst, src)
}
func (m *GetLiveMultiPkRecordListResp) XXX_Size() int {
	return xxx_messageInfo_GetLiveMultiPkRecordListResp.Size(m)
}
func (m *GetLiveMultiPkRecordListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveMultiPkRecordListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveMultiPkRecordListResp proto.InternalMessageInfo

func (m *GetLiveMultiPkRecordListResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

// 处理收到PK邀请
type HandleLiveMultiPkApplyReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ApplyUid             uint32   `protobuf:"varint,2,opt,name=apply_uid,json=applyUid,proto3" json:"apply_uid,omitempty"`
	Oper                 uint32   `protobuf:"varint,3,opt,name=oper,proto3" json:"oper,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleLiveMultiPkApplyReq) Reset()         { *m = HandleLiveMultiPkApplyReq{} }
func (m *HandleLiveMultiPkApplyReq) String() string { return proto.CompactTextString(m) }
func (*HandleLiveMultiPkApplyReq) ProtoMessage()    {}
func (*HandleLiveMultiPkApplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{11}
}
func (m *HandleLiveMultiPkApplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleLiveMultiPkApplyReq.Unmarshal(m, b)
}
func (m *HandleLiveMultiPkApplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleLiveMultiPkApplyReq.Marshal(b, m, deterministic)
}
func (dst *HandleLiveMultiPkApplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleLiveMultiPkApplyReq.Merge(dst, src)
}
func (m *HandleLiveMultiPkApplyReq) XXX_Size() int {
	return xxx_messageInfo_HandleLiveMultiPkApplyReq.Size(m)
}
func (m *HandleLiveMultiPkApplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleLiveMultiPkApplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_HandleLiveMultiPkApplyReq proto.InternalMessageInfo

func (m *HandleLiveMultiPkApplyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HandleLiveMultiPkApplyReq) GetApplyUid() uint32 {
	if m != nil {
		return m.ApplyUid
	}
	return 0
}

func (m *HandleLiveMultiPkApplyReq) GetOper() uint32 {
	if m != nil {
		return m.Oper
	}
	return 0
}

type HandleLiveMultiPkApplyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleLiveMultiPkApplyResp) Reset()         { *m = HandleLiveMultiPkApplyResp{} }
func (m *HandleLiveMultiPkApplyResp) String() string { return proto.CompactTextString(m) }
func (*HandleLiveMultiPkApplyResp) ProtoMessage()    {}
func (*HandleLiveMultiPkApplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{12}
}
func (m *HandleLiveMultiPkApplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleLiveMultiPkApplyResp.Unmarshal(m, b)
}
func (m *HandleLiveMultiPkApplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleLiveMultiPkApplyResp.Marshal(b, m, deterministic)
}
func (dst *HandleLiveMultiPkApplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleLiveMultiPkApplyResp.Merge(dst, src)
}
func (m *HandleLiveMultiPkApplyResp) XXX_Size() int {
	return xxx_messageInfo_HandleLiveMultiPkApplyResp.Size(m)
}
func (m *HandleLiveMultiPkApplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleLiveMultiPkApplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_HandleLiveMultiPkApplyResp proto.InternalMessageInfo

// 开始随机匹配
type StartMultiPkMatchReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartMultiPkMatchReq) Reset()         { *m = StartMultiPkMatchReq{} }
func (m *StartMultiPkMatchReq) String() string { return proto.CompactTextString(m) }
func (*StartMultiPkMatchReq) ProtoMessage()    {}
func (*StartMultiPkMatchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{13}
}
func (m *StartMultiPkMatchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartMultiPkMatchReq.Unmarshal(m, b)
}
func (m *StartMultiPkMatchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartMultiPkMatchReq.Marshal(b, m, deterministic)
}
func (dst *StartMultiPkMatchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartMultiPkMatchReq.Merge(dst, src)
}
func (m *StartMultiPkMatchReq) XXX_Size() int {
	return xxx_messageInfo_StartMultiPkMatchReq.Size(m)
}
func (m *StartMultiPkMatchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartMultiPkMatchReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartMultiPkMatchReq proto.InternalMessageInfo

func (m *StartMultiPkMatchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type StartMultiPkMatchResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartMultiPkMatchResp) Reset()         { *m = StartMultiPkMatchResp{} }
func (m *StartMultiPkMatchResp) String() string { return proto.CompactTextString(m) }
func (*StartMultiPkMatchResp) ProtoMessage()    {}
func (*StartMultiPkMatchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{14}
}
func (m *StartMultiPkMatchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartMultiPkMatchResp.Unmarshal(m, b)
}
func (m *StartMultiPkMatchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartMultiPkMatchResp.Marshal(b, m, deterministic)
}
func (dst *StartMultiPkMatchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartMultiPkMatchResp.Merge(dst, src)
}
func (m *StartMultiPkMatchResp) XXX_Size() int {
	return xxx_messageInfo_StartMultiPkMatchResp.Size(m)
}
func (m *StartMultiPkMatchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartMultiPkMatchResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartMultiPkMatchResp proto.InternalMessageInfo

// 取消PK匹配
type CancelMultiPkMatchReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelMultiPkMatchReq) Reset()         { *m = CancelMultiPkMatchReq{} }
func (m *CancelMultiPkMatchReq) String() string { return proto.CompactTextString(m) }
func (*CancelMultiPkMatchReq) ProtoMessage()    {}
func (*CancelMultiPkMatchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{15}
}
func (m *CancelMultiPkMatchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelMultiPkMatchReq.Unmarshal(m, b)
}
func (m *CancelMultiPkMatchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelMultiPkMatchReq.Marshal(b, m, deterministic)
}
func (dst *CancelMultiPkMatchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelMultiPkMatchReq.Merge(dst, src)
}
func (m *CancelMultiPkMatchReq) XXX_Size() int {
	return xxx_messageInfo_CancelMultiPkMatchReq.Size(m)
}
func (m *CancelMultiPkMatchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelMultiPkMatchReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelMultiPkMatchReq proto.InternalMessageInfo

func (m *CancelMultiPkMatchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CancelMultiPkMatchResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelMultiPkMatchResp) Reset()         { *m = CancelMultiPkMatchResp{} }
func (m *CancelMultiPkMatchResp) String() string { return proto.CompactTextString(m) }
func (*CancelMultiPkMatchResp) ProtoMessage()    {}
func (*CancelMultiPkMatchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{16}
}
func (m *CancelMultiPkMatchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelMultiPkMatchResp.Unmarshal(m, b)
}
func (m *CancelMultiPkMatchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelMultiPkMatchResp.Marshal(b, m, deterministic)
}
func (dst *CancelMultiPkMatchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelMultiPkMatchResp.Merge(dst, src)
}
func (m *CancelMultiPkMatchResp) XXX_Size() int {
	return xxx_messageInfo_CancelMultiPkMatchResp.Size(m)
}
func (m *CancelMultiPkMatchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelMultiPkMatchResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelMultiPkMatchResp proto.InternalMessageInfo

// 取消多人PK组队
type CancelLiveMultiPkTeamReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelLiveMultiPkTeamReq) Reset()         { *m = CancelLiveMultiPkTeamReq{} }
func (m *CancelLiveMultiPkTeamReq) String() string { return proto.CompactTextString(m) }
func (*CancelLiveMultiPkTeamReq) ProtoMessage()    {}
func (*CancelLiveMultiPkTeamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{17}
}
func (m *CancelLiveMultiPkTeamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelLiveMultiPkTeamReq.Unmarshal(m, b)
}
func (m *CancelLiveMultiPkTeamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelLiveMultiPkTeamReq.Marshal(b, m, deterministic)
}
func (dst *CancelLiveMultiPkTeamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelLiveMultiPkTeamReq.Merge(dst, src)
}
func (m *CancelLiveMultiPkTeamReq) XXX_Size() int {
	return xxx_messageInfo_CancelLiveMultiPkTeamReq.Size(m)
}
func (m *CancelLiveMultiPkTeamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelLiveMultiPkTeamReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelLiveMultiPkTeamReq proto.InternalMessageInfo

func (m *CancelLiveMultiPkTeamReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CancelLiveMultiPkTeamResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelLiveMultiPkTeamResp) Reset()         { *m = CancelLiveMultiPkTeamResp{} }
func (m *CancelLiveMultiPkTeamResp) String() string { return proto.CompactTextString(m) }
func (*CancelLiveMultiPkTeamResp) ProtoMessage()    {}
func (*CancelLiveMultiPkTeamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{18}
}
func (m *CancelLiveMultiPkTeamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelLiveMultiPkTeamResp.Unmarshal(m, b)
}
func (m *CancelLiveMultiPkTeamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelLiveMultiPkTeamResp.Marshal(b, m, deterministic)
}
func (dst *CancelLiveMultiPkTeamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelLiveMultiPkTeamResp.Merge(dst, src)
}
func (m *CancelLiveMultiPkTeamResp) XXX_Size() int {
	return xxx_messageInfo_CancelLiveMultiPkTeamResp.Size(m)
}
func (m *CancelLiveMultiPkTeamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelLiveMultiPkTeamResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelLiveMultiPkTeamResp proto.InternalMessageInfo

type MultiPkTeamSimpleInfo struct {
	TeamId               uint32   `protobuf:"varint,1,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MultiPkTeamSimpleInfo) Reset()         { *m = MultiPkTeamSimpleInfo{} }
func (m *MultiPkTeamSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*MultiPkTeamSimpleInfo) ProtoMessage()    {}
func (*MultiPkTeamSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{19}
}
func (m *MultiPkTeamSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiPkTeamSimpleInfo.Unmarshal(m, b)
}
func (m *MultiPkTeamSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiPkTeamSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *MultiPkTeamSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiPkTeamSimpleInfo.Merge(dst, src)
}
func (m *MultiPkTeamSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_MultiPkTeamSimpleInfo.Size(m)
}
func (m *MultiPkTeamSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiPkTeamSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MultiPkTeamSimpleInfo proto.InternalMessageInfo

func (m *MultiPkTeamSimpleInfo) GetTeamId() uint32 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

func (m *MultiPkTeamSimpleInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 多人pk主播信息
type MultiPkAnchorInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MultiPkAnchorInfo) Reset()         { *m = MultiPkAnchorInfo{} }
func (m *MultiPkAnchorInfo) String() string { return proto.CompactTextString(m) }
func (*MultiPkAnchorInfo) ProtoMessage()    {}
func (*MultiPkAnchorInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{20}
}
func (m *MultiPkAnchorInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiPkAnchorInfo.Unmarshal(m, b)
}
func (m *MultiPkAnchorInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiPkAnchorInfo.Marshal(b, m, deterministic)
}
func (dst *MultiPkAnchorInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiPkAnchorInfo.Merge(dst, src)
}
func (m *MultiPkAnchorInfo) XXX_Size() int {
	return xxx_messageInfo_MultiPkAnchorInfo.Size(m)
}
func (m *MultiPkAnchorInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiPkAnchorInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MultiPkAnchorInfo proto.InternalMessageInfo

func (m *MultiPkAnchorInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 多人pk用户信息
type MultiPkUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UkwInfo              *UkwInfo `protobuf:"bytes,2,opt,name=ukw_info,json=ukwInfo,proto3" json:"ukw_info,omitempty"`
	PkSocre              uint64   `protobuf:"varint,3,opt,name=pk_socre,json=pkSocre,proto3" json:"pk_socre,omitempty"`
	KnightLevel          uint32   `protobuf:"varint,4,opt,name=knight_level,json=knightLevel,proto3" json:"knight_level,omitempty"`
	FirstKill            bool     `protobuf:"varint,5,opt,name=first_kill,json=firstKill,proto3" json:"first_kill,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MultiPkUserInfo) Reset()         { *m = MultiPkUserInfo{} }
func (m *MultiPkUserInfo) String() string { return proto.CompactTextString(m) }
func (*MultiPkUserInfo) ProtoMessage()    {}
func (*MultiPkUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{21}
}
func (m *MultiPkUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiPkUserInfo.Unmarshal(m, b)
}
func (m *MultiPkUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiPkUserInfo.Marshal(b, m, deterministic)
}
func (dst *MultiPkUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiPkUserInfo.Merge(dst, src)
}
func (m *MultiPkUserInfo) XXX_Size() int {
	return xxx_messageInfo_MultiPkUserInfo.Size(m)
}
func (m *MultiPkUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiPkUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MultiPkUserInfo proto.InternalMessageInfo

func (m *MultiPkUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MultiPkUserInfo) GetUkwInfo() *UkwInfo {
	if m != nil {
		return m.UkwInfo
	}
	return nil
}

func (m *MultiPkUserInfo) GetPkSocre() uint64 {
	if m != nil {
		return m.PkSocre
	}
	return 0
}

func (m *MultiPkUserInfo) GetKnightLevel() uint32 {
	if m != nil {
		return m.KnightLevel
	}
	return 0
}

func (m *MultiPkUserInfo) GetFirstKill() bool {
	if m != nil {
		return m.FirstKill
	}
	return false
}

// pk队伍房间信息
type MultiPkRoomInfo struct {
	TeamId               uint32             `protobuf:"varint,1,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	AnchorInfo           *MultiPkAnchorInfo `protobuf:"bytes,2,opt,name=anchor_info,json=anchorInfo,proto3" json:"anchor_info,omitempty"`
	TopUserList          []*MultiPkUserInfo `protobuf:"bytes,3,rep,name=top_user_list,json=topUserList,proto3" json:"top_user_list,omitempty"`
	PkScore              uint32             `protobuf:"varint,4,opt,name=pk_score,json=pkScore,proto3" json:"pk_score,omitempty"`
	PkRank               uint32             `protobuf:"varint,5,opt,name=pk_rank,json=pkRank,proto3" json:"pk_rank,omitempty"`
	ChannelId            uint32             `protobuf:"varint,6,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	IsSponsor            bool               `protobuf:"varint,7,opt,name=is_sponsor,json=isSponsor,proto3" json:"is_sponsor,omitempty"`
	KnightList           []*MultiPkUserInfo `protobuf:"bytes,8,rep,name=knight_list,json=knightList,proto3" json:"knight_list,omitempty"`
	PkResult             uint32             `protobuf:"varint,9,opt,name=pk_result,json=pkResult,proto3" json:"pk_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MultiPkRoomInfo) Reset()         { *m = MultiPkRoomInfo{} }
func (m *MultiPkRoomInfo) String() string { return proto.CompactTextString(m) }
func (*MultiPkRoomInfo) ProtoMessage()    {}
func (*MultiPkRoomInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{22}
}
func (m *MultiPkRoomInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiPkRoomInfo.Unmarshal(m, b)
}
func (m *MultiPkRoomInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiPkRoomInfo.Marshal(b, m, deterministic)
}
func (dst *MultiPkRoomInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiPkRoomInfo.Merge(dst, src)
}
func (m *MultiPkRoomInfo) XXX_Size() int {
	return xxx_messageInfo_MultiPkRoomInfo.Size(m)
}
func (m *MultiPkRoomInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiPkRoomInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MultiPkRoomInfo proto.InternalMessageInfo

func (m *MultiPkRoomInfo) GetTeamId() uint32 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

func (m *MultiPkRoomInfo) GetAnchorInfo() *MultiPkAnchorInfo {
	if m != nil {
		return m.AnchorInfo
	}
	return nil
}

func (m *MultiPkRoomInfo) GetTopUserList() []*MultiPkUserInfo {
	if m != nil {
		return m.TopUserList
	}
	return nil
}

func (m *MultiPkRoomInfo) GetPkScore() uint32 {
	if m != nil {
		return m.PkScore
	}
	return 0
}

func (m *MultiPkRoomInfo) GetPkRank() uint32 {
	if m != nil {
		return m.PkRank
	}
	return 0
}

func (m *MultiPkRoomInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MultiPkRoomInfo) GetIsSponsor() bool {
	if m != nil {
		return m.IsSponsor
	}
	return false
}

func (m *MultiPkRoomInfo) GetKnightList() []*MultiPkUserInfo {
	if m != nil {
		return m.KnightList
	}
	return nil
}

func (m *MultiPkRoomInfo) GetPkResult() uint32 {
	if m != nil {
		return m.PkResult
	}
	return 0
}

type MultiPkFirstKillUser struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UkwInfo              *UkwInfo `protobuf:"bytes,2,opt,name=ukw_info,json=ukwInfo,proto3" json:"ukw_info,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MultiPkFirstKillUser) Reset()         { *m = MultiPkFirstKillUser{} }
func (m *MultiPkFirstKillUser) String() string { return proto.CompactTextString(m) }
func (*MultiPkFirstKillUser) ProtoMessage()    {}
func (*MultiPkFirstKillUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{23}
}
func (m *MultiPkFirstKillUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiPkFirstKillUser.Unmarshal(m, b)
}
func (m *MultiPkFirstKillUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiPkFirstKillUser.Marshal(b, m, deterministic)
}
func (dst *MultiPkFirstKillUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiPkFirstKillUser.Merge(dst, src)
}
func (m *MultiPkFirstKillUser) XXX_Size() int {
	return xxx_messageInfo_MultiPkFirstKillUser.Size(m)
}
func (m *MultiPkFirstKillUser) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiPkFirstKillUser.DiscardUnknown(m)
}

var xxx_messageInfo_MultiPkFirstKillUser proto.InternalMessageInfo

func (m *MultiPkFirstKillUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MultiPkFirstKillUser) GetUkwInfo() *UkwInfo {
	if m != nil {
		return m.UkwInfo
	}
	return nil
}

func (m *MultiPkFirstKillUser) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// pk信息
type MultiPkInfo struct {
	PkId                 uint32                `protobuf:"varint,1,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	PkType               uint32                `protobuf:"varint,2,opt,name=pk_type,json=pkType,proto3" json:"pk_type,omitempty"`
	PkStatus             uint32                `protobuf:"varint,3,opt,name=pk_status,json=pkStatus,proto3" json:"pk_status,omitempty"`
	RoomInfoList         []*MultiPkRoomInfo    `protobuf:"bytes,4,rep,name=room_info_list,json=roomInfoList,proto3" json:"room_info_list,omitempty"`
	FirstKillUser        *MultiPkFirstKillUser `protobuf:"bytes,5,opt,name=first_kill_user,json=firstKillUser,proto3" json:"first_kill_user,omitempty"`
	PkEndTs              uint64                `protobuf:"varint,6,opt,name=pk_end_ts,json=pkEndTs,proto3" json:"pk_end_ts,omitempty"`
	PkStatusEndTs        uint32                `protobuf:"varint,7,opt,name=pk_status_end_ts,json=pkStatusEndTs,proto3" json:"pk_status_end_ts,omitempty"`
	IsAutoPkType         bool                  `protobuf:"varint,8,opt,name=is_auto_pk_type,json=isAutoPkType,proto3" json:"is_auto_pk_type,omitempty"`
	PkTeamType           uint32                `protobuf:"varint,9,opt,name=pk_team_type,json=pkTeamType,proto3" json:"pk_team_type,omitempty"`
	GetTs                int64                 `protobuf:"varint,10,opt,name=get_ts,json=getTs,proto3" json:"get_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *MultiPkInfo) Reset()         { *m = MultiPkInfo{} }
func (m *MultiPkInfo) String() string { return proto.CompactTextString(m) }
func (*MultiPkInfo) ProtoMessage()    {}
func (*MultiPkInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{24}
}
func (m *MultiPkInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiPkInfo.Unmarshal(m, b)
}
func (m *MultiPkInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiPkInfo.Marshal(b, m, deterministic)
}
func (dst *MultiPkInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiPkInfo.Merge(dst, src)
}
func (m *MultiPkInfo) XXX_Size() int {
	return xxx_messageInfo_MultiPkInfo.Size(m)
}
func (m *MultiPkInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiPkInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MultiPkInfo proto.InternalMessageInfo

func (m *MultiPkInfo) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *MultiPkInfo) GetPkType() uint32 {
	if m != nil {
		return m.PkType
	}
	return 0
}

func (m *MultiPkInfo) GetPkStatus() uint32 {
	if m != nil {
		return m.PkStatus
	}
	return 0
}

func (m *MultiPkInfo) GetRoomInfoList() []*MultiPkRoomInfo {
	if m != nil {
		return m.RoomInfoList
	}
	return nil
}

func (m *MultiPkInfo) GetFirstKillUser() *MultiPkFirstKillUser {
	if m != nil {
		return m.FirstKillUser
	}
	return nil
}

func (m *MultiPkInfo) GetPkEndTs() uint64 {
	if m != nil {
		return m.PkEndTs
	}
	return 0
}

func (m *MultiPkInfo) GetPkStatusEndTs() uint32 {
	if m != nil {
		return m.PkStatusEndTs
	}
	return 0
}

func (m *MultiPkInfo) GetIsAutoPkType() bool {
	if m != nil {
		return m.IsAutoPkType
	}
	return false
}

func (m *MultiPkInfo) GetPkTeamType() uint32 {
	if m != nil {
		return m.PkTeamType
	}
	return 0
}

func (m *MultiPkInfo) GetGetTs() int64 {
	if m != nil {
		return m.GetTs
	}
	return 0
}

// pk简单信息
type MultiPkSimpleInfo struct {
	PkId                 uint32   `protobuf:"varint,1,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	PkType               uint32   `protobuf:"varint,2,opt,name=pk_type,json=pkType,proto3" json:"pk_type,omitempty"`
	PkStatus             uint32   `protobuf:"varint,3,opt,name=pk_status,json=pkStatus,proto3" json:"pk_status,omitempty"`
	ChannelIdList        []uint32 `protobuf:"varint,4,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MultiPkSimpleInfo) Reset()         { *m = MultiPkSimpleInfo{} }
func (m *MultiPkSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*MultiPkSimpleInfo) ProtoMessage()    {}
func (*MultiPkSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{25}
}
func (m *MultiPkSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiPkSimpleInfo.Unmarshal(m, b)
}
func (m *MultiPkSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiPkSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *MultiPkSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiPkSimpleInfo.Merge(dst, src)
}
func (m *MultiPkSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_MultiPkSimpleInfo.Size(m)
}
func (m *MultiPkSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiPkSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MultiPkSimpleInfo proto.InternalMessageInfo

func (m *MultiPkSimpleInfo) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *MultiPkSimpleInfo) GetPkType() uint32 {
	if m != nil {
		return m.PkType
	}
	return 0
}

func (m *MultiPkSimpleInfo) GetPkStatus() uint32 {
	if m != nil {
		return m.PkStatus
	}
	return 0
}

func (m *MultiPkSimpleInfo) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

// pk匹配信息
type MultiPkMatchInfo struct {
	MatchStatus          uint32   `protobuf:"varint,1,opt,name=match_status,json=matchStatus,proto3" json:"match_status,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	SponsorUid           uint32   `protobuf:"varint,3,opt,name=sponsor_uid,json=sponsorUid,proto3" json:"sponsor_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MultiPkMatchInfo) Reset()         { *m = MultiPkMatchInfo{} }
func (m *MultiPkMatchInfo) String() string { return proto.CompactTextString(m) }
func (*MultiPkMatchInfo) ProtoMessage()    {}
func (*MultiPkMatchInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{26}
}
func (m *MultiPkMatchInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiPkMatchInfo.Unmarshal(m, b)
}
func (m *MultiPkMatchInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiPkMatchInfo.Marshal(b, m, deterministic)
}
func (dst *MultiPkMatchInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiPkMatchInfo.Merge(dst, src)
}
func (m *MultiPkMatchInfo) XXX_Size() int {
	return xxx_messageInfo_MultiPkMatchInfo.Size(m)
}
func (m *MultiPkMatchInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiPkMatchInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MultiPkMatchInfo proto.InternalMessageInfo

func (m *MultiPkMatchInfo) GetMatchStatus() uint32 {
	if m != nil {
		return m.MatchStatus
	}
	return 0
}

func (m *MultiPkMatchInfo) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *MultiPkMatchInfo) GetSponsorUid() uint32 {
	if m != nil {
		return m.SponsorUid
	}
	return 0
}

type GetMultiPkInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMultiPkInfoReq) Reset()         { *m = GetMultiPkInfoReq{} }
func (m *GetMultiPkInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetMultiPkInfoReq) ProtoMessage()    {}
func (*GetMultiPkInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{27}
}
func (m *GetMultiPkInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiPkInfoReq.Unmarshal(m, b)
}
func (m *GetMultiPkInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiPkInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetMultiPkInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiPkInfoReq.Merge(dst, src)
}
func (m *GetMultiPkInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetMultiPkInfoReq.Size(m)
}
func (m *GetMultiPkInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiPkInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiPkInfoReq proto.InternalMessageInfo

func (m *GetMultiPkInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetMultiPkInfoResp struct {
	PkInfo               *MultiPkInfo         `protobuf:"bytes,1,opt,name=pk_info,json=pkInfo,proto3" json:"pk_info,omitempty"`
	PkMatchInfo          *MultiPkMatchInfo    `protobuf:"bytes,2,opt,name=pk_match_info,json=pkMatchInfo,proto3" json:"pk_match_info,omitempty"`
	KnightList           []*MultiPkKnightInfo `protobuf:"bytes,3,rep,name=knight_list,json=knightList,proto3" json:"knight_list,omitempty"`
	SettleInfo           []byte               `protobuf:"bytes,4,opt,name=settle_info,json=settleInfo,proto3" json:"settle_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetMultiPkInfoResp) Reset()         { *m = GetMultiPkInfoResp{} }
func (m *GetMultiPkInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetMultiPkInfoResp) ProtoMessage()    {}
func (*GetMultiPkInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{28}
}
func (m *GetMultiPkInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiPkInfoResp.Unmarshal(m, b)
}
func (m *GetMultiPkInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiPkInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetMultiPkInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiPkInfoResp.Merge(dst, src)
}
func (m *GetMultiPkInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetMultiPkInfoResp.Size(m)
}
func (m *GetMultiPkInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiPkInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiPkInfoResp proto.InternalMessageInfo

func (m *GetMultiPkInfoResp) GetPkInfo() *MultiPkInfo {
	if m != nil {
		return m.PkInfo
	}
	return nil
}

func (m *GetMultiPkInfoResp) GetPkMatchInfo() *MultiPkMatchInfo {
	if m != nil {
		return m.PkMatchInfo
	}
	return nil
}

func (m *GetMultiPkInfoResp) GetKnightList() []*MultiPkKnightInfo {
	if m != nil {
		return m.KnightList
	}
	return nil
}

func (m *GetMultiPkInfoResp) GetSettleInfo() []byte {
	if m != nil {
		return m.SettleInfo
	}
	return nil
}

type MultiPkKnightInfo struct {
	AnchorUid            uint32             `protobuf:"varint,1,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	ChannelId            uint32             `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	KnightList           []*MultiPkUserInfo `protobuf:"bytes,3,rep,name=knight_list,json=knightList,proto3" json:"knight_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MultiPkKnightInfo) Reset()         { *m = MultiPkKnightInfo{} }
func (m *MultiPkKnightInfo) String() string { return proto.CompactTextString(m) }
func (*MultiPkKnightInfo) ProtoMessage()    {}
func (*MultiPkKnightInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{29}
}
func (m *MultiPkKnightInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiPkKnightInfo.Unmarshal(m, b)
}
func (m *MultiPkKnightInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiPkKnightInfo.Marshal(b, m, deterministic)
}
func (dst *MultiPkKnightInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiPkKnightInfo.Merge(dst, src)
}
func (m *MultiPkKnightInfo) XXX_Size() int {
	return xxx_messageInfo_MultiPkKnightInfo.Size(m)
}
func (m *MultiPkKnightInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiPkKnightInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MultiPkKnightInfo proto.InternalMessageInfo

func (m *MultiPkKnightInfo) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *MultiPkKnightInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MultiPkKnightInfo) GetKnightList() []*MultiPkUserInfo {
	if m != nil {
		return m.KnightList
	}
	return nil
}

// 开始PK
type StartLiveMultiPkReq struct {
	Uid                  uint32                   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PkId                 uint32                   `protobuf:"varint,2,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	PkType               uint32                   `protobuf:"varint,3,opt,name=pk_type,json=pkType,proto3" json:"pk_type,omitempty"`
	TeamList             []*MultiPkTeamSimpleInfo `protobuf:"bytes,4,rep,name=team_list,json=teamList,proto3" json:"team_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *StartLiveMultiPkReq) Reset()         { *m = StartLiveMultiPkReq{} }
func (m *StartLiveMultiPkReq) String() string { return proto.CompactTextString(m) }
func (*StartLiveMultiPkReq) ProtoMessage()    {}
func (*StartLiveMultiPkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{30}
}
func (m *StartLiveMultiPkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartLiveMultiPkReq.Unmarshal(m, b)
}
func (m *StartLiveMultiPkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartLiveMultiPkReq.Marshal(b, m, deterministic)
}
func (dst *StartLiveMultiPkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartLiveMultiPkReq.Merge(dst, src)
}
func (m *StartLiveMultiPkReq) XXX_Size() int {
	return xxx_messageInfo_StartLiveMultiPkReq.Size(m)
}
func (m *StartLiveMultiPkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartLiveMultiPkReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartLiveMultiPkReq proto.InternalMessageInfo

func (m *StartLiveMultiPkReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StartLiveMultiPkReq) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *StartLiveMultiPkReq) GetPkType() uint32 {
	if m != nil {
		return m.PkType
	}
	return 0
}

func (m *StartLiveMultiPkReq) GetTeamList() []*MultiPkTeamSimpleInfo {
	if m != nil {
		return m.TeamList
	}
	return nil
}

type StartLiveMultiPkResp struct {
	MultiPkInfo          *MultiPkInfo `protobuf:"bytes,1,opt,name=multi_pk_info,json=multiPkInfo,proto3" json:"multi_pk_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *StartLiveMultiPkResp) Reset()         { *m = StartLiveMultiPkResp{} }
func (m *StartLiveMultiPkResp) String() string { return proto.CompactTextString(m) }
func (*StartLiveMultiPkResp) ProtoMessage()    {}
func (*StartLiveMultiPkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{31}
}
func (m *StartLiveMultiPkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartLiveMultiPkResp.Unmarshal(m, b)
}
func (m *StartLiveMultiPkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartLiveMultiPkResp.Marshal(b, m, deterministic)
}
func (dst *StartLiveMultiPkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartLiveMultiPkResp.Merge(dst, src)
}
func (m *StartLiveMultiPkResp) XXX_Size() int {
	return xxx_messageInfo_StartLiveMultiPkResp.Size(m)
}
func (m *StartLiveMultiPkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartLiveMultiPkResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartLiveMultiPkResp proto.InternalMessageInfo

func (m *StartLiveMultiPkResp) GetMultiPkInfo() *MultiPkInfo {
	if m != nil {
		return m.MultiPkInfo
	}
	return nil
}

// 结束多人PK
type StopLiveMultiPkReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PkId                 uint32   `protobuf:"varint,2,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopLiveMultiPkReq) Reset()         { *m = StopLiveMultiPkReq{} }
func (m *StopLiveMultiPkReq) String() string { return proto.CompactTextString(m) }
func (*StopLiveMultiPkReq) ProtoMessage()    {}
func (*StopLiveMultiPkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{32}
}
func (m *StopLiveMultiPkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopLiveMultiPkReq.Unmarshal(m, b)
}
func (m *StopLiveMultiPkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopLiveMultiPkReq.Marshal(b, m, deterministic)
}
func (dst *StopLiveMultiPkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopLiveMultiPkReq.Merge(dst, src)
}
func (m *StopLiveMultiPkReq) XXX_Size() int {
	return xxx_messageInfo_StopLiveMultiPkReq.Size(m)
}
func (m *StopLiveMultiPkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StopLiveMultiPkReq.DiscardUnknown(m)
}

var xxx_messageInfo_StopLiveMultiPkReq proto.InternalMessageInfo

func (m *StopLiveMultiPkReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StopLiveMultiPkReq) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

type StopLiveMultiPkResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopLiveMultiPkResp) Reset()         { *m = StopLiveMultiPkResp{} }
func (m *StopLiveMultiPkResp) String() string { return proto.CompactTextString(m) }
func (*StopLiveMultiPkResp) ProtoMessage()    {}
func (*StopLiveMultiPkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{33}
}
func (m *StopLiveMultiPkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopLiveMultiPkResp.Unmarshal(m, b)
}
func (m *StopLiveMultiPkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopLiveMultiPkResp.Marshal(b, m, deterministic)
}
func (dst *StopLiveMultiPkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopLiveMultiPkResp.Merge(dst, src)
}
func (m *StopLiveMultiPkResp) XXX_Size() int {
	return xxx_messageInfo_StopLiveMultiPkResp.Size(m)
}
func (m *StopLiveMultiPkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StopLiveMultiPkResp.DiscardUnknown(m)
}

var xxx_messageInfo_StopLiveMultiPkResp proto.InternalMessageInfo

type GetOnlineKnightListReq struct {
	AnchorUid            uint32   `protobuf:"varint,1,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOnlineKnightListReq) Reset()         { *m = GetOnlineKnightListReq{} }
func (m *GetOnlineKnightListReq) String() string { return proto.CompactTextString(m) }
func (*GetOnlineKnightListReq) ProtoMessage()    {}
func (*GetOnlineKnightListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{34}
}
func (m *GetOnlineKnightListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOnlineKnightListReq.Unmarshal(m, b)
}
func (m *GetOnlineKnightListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOnlineKnightListReq.Marshal(b, m, deterministic)
}
func (dst *GetOnlineKnightListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOnlineKnightListReq.Merge(dst, src)
}
func (m *GetOnlineKnightListReq) XXX_Size() int {
	return xxx_messageInfo_GetOnlineKnightListReq.Size(m)
}
func (m *GetOnlineKnightListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOnlineKnightListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOnlineKnightListReq proto.InternalMessageInfo

func (m *GetOnlineKnightListReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

type GetOnlineKnightListResp struct {
	KnightList           []*MultiPkUserInfo `protobuf:"bytes,1,rep,name=knight_list,json=knightList,proto3" json:"knight_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetOnlineKnightListResp) Reset()         { *m = GetOnlineKnightListResp{} }
func (m *GetOnlineKnightListResp) String() string { return proto.CompactTextString(m) }
func (*GetOnlineKnightListResp) ProtoMessage()    {}
func (*GetOnlineKnightListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{35}
}
func (m *GetOnlineKnightListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOnlineKnightListResp.Unmarshal(m, b)
}
func (m *GetOnlineKnightListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOnlineKnightListResp.Marshal(b, m, deterministic)
}
func (dst *GetOnlineKnightListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOnlineKnightListResp.Merge(dst, src)
}
func (m *GetOnlineKnightListResp) XXX_Size() int {
	return xxx_messageInfo_GetOnlineKnightListResp.Size(m)
}
func (m *GetOnlineKnightListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOnlineKnightListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOnlineKnightListResp proto.InternalMessageInfo

func (m *GetOnlineKnightListResp) GetKnightList() []*MultiPkUserInfo {
	if m != nil {
		return m.KnightList
	}
	return nil
}

// 获取PK火力榜
type GetLiveMultiPkRankReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveMultiPkRankReq) Reset()         { *m = GetLiveMultiPkRankReq{} }
func (m *GetLiveMultiPkRankReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveMultiPkRankReq) ProtoMessage()    {}
func (*GetLiveMultiPkRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{36}
}
func (m *GetLiveMultiPkRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveMultiPkRankReq.Unmarshal(m, b)
}
func (m *GetLiveMultiPkRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveMultiPkRankReq.Marshal(b, m, deterministic)
}
func (dst *GetLiveMultiPkRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveMultiPkRankReq.Merge(dst, src)
}
func (m *GetLiveMultiPkRankReq) XXX_Size() int {
	return xxx_messageInfo_GetLiveMultiPkRankReq.Size(m)
}
func (m *GetLiveMultiPkRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveMultiPkRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveMultiPkRankReq proto.InternalMessageInfo

func (m *GetLiveMultiPkRankReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLiveMultiPkRankReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

type GetLiveMultiPkRankResp struct {
	RankList             []*MultiPkUserInfo `protobuf:"bytes,2,rep,name=rank_list,json=rankList,proto3" json:"rank_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetLiveMultiPkRankResp) Reset()         { *m = GetLiveMultiPkRankResp{} }
func (m *GetLiveMultiPkRankResp) String() string { return proto.CompactTextString(m) }
func (*GetLiveMultiPkRankResp) ProtoMessage()    {}
func (*GetLiveMultiPkRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{37}
}
func (m *GetLiveMultiPkRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveMultiPkRankResp.Unmarshal(m, b)
}
func (m *GetLiveMultiPkRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveMultiPkRankResp.Marshal(b, m, deterministic)
}
func (dst *GetLiveMultiPkRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveMultiPkRankResp.Merge(dst, src)
}
func (m *GetLiveMultiPkRankResp) XXX_Size() int {
	return xxx_messageInfo_GetLiveMultiPkRankResp.Size(m)
}
func (m *GetLiveMultiPkRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveMultiPkRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveMultiPkRankResp proto.InternalMessageInfo

func (m *GetLiveMultiPkRankResp) GetRankList() []*MultiPkUserInfo {
	if m != nil {
		return m.RankList
	}
	return nil
}

type GetLiveMultiPkInviteeStatusReq struct {
	ApplyUid             uint32   `protobuf:"varint,1,opt,name=apply_uid,json=applyUid,proto3" json:"apply_uid,omitempty"`
	TargetUidList        []uint32 `protobuf:"varint,2,rep,packed,name=target_uid_list,json=targetUidList,proto3" json:"target_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveMultiPkInviteeStatusReq) Reset()         { *m = GetLiveMultiPkInviteeStatusReq{} }
func (m *GetLiveMultiPkInviteeStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveMultiPkInviteeStatusReq) ProtoMessage()    {}
func (*GetLiveMultiPkInviteeStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{38}
}
func (m *GetLiveMultiPkInviteeStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveMultiPkInviteeStatusReq.Unmarshal(m, b)
}
func (m *GetLiveMultiPkInviteeStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveMultiPkInviteeStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetLiveMultiPkInviteeStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveMultiPkInviteeStatusReq.Merge(dst, src)
}
func (m *GetLiveMultiPkInviteeStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetLiveMultiPkInviteeStatusReq.Size(m)
}
func (m *GetLiveMultiPkInviteeStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveMultiPkInviteeStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveMultiPkInviteeStatusReq proto.InternalMessageInfo

func (m *GetLiveMultiPkInviteeStatusReq) GetApplyUid() uint32 {
	if m != nil {
		return m.ApplyUid
	}
	return 0
}

func (m *GetLiveMultiPkInviteeStatusReq) GetTargetUidList() []uint32 {
	if m != nil {
		return m.TargetUidList
	}
	return nil
}

type GetLiveMultiPkInviteeStatusResp struct {
	TargetUid2Status     map[uint32]uint32 `protobuf:"bytes,1,rep,name=targetUid2status,proto3" json:"targetUid2status,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetLiveMultiPkInviteeStatusResp) Reset()         { *m = GetLiveMultiPkInviteeStatusResp{} }
func (m *GetLiveMultiPkInviteeStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetLiveMultiPkInviteeStatusResp) ProtoMessage()    {}
func (*GetLiveMultiPkInviteeStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{39}
}
func (m *GetLiveMultiPkInviteeStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveMultiPkInviteeStatusResp.Unmarshal(m, b)
}
func (m *GetLiveMultiPkInviteeStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveMultiPkInviteeStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetLiveMultiPkInviteeStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveMultiPkInviteeStatusResp.Merge(dst, src)
}
func (m *GetLiveMultiPkInviteeStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetLiveMultiPkInviteeStatusResp.Size(m)
}
func (m *GetLiveMultiPkInviteeStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveMultiPkInviteeStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveMultiPkInviteeStatusResp proto.InternalMessageInfo

func (m *GetLiveMultiPkInviteeStatusResp) GetTargetUid2Status() map[uint32]uint32 {
	if m != nil {
		return m.TargetUid2Status
	}
	return nil
}

// 批量查询是否在多人pk， 支持uid，channelId查询
type BatCheckIsInMultiPkReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	CidList              []uint32 `protobuf:"varint,2,rep,packed,name=cid_list,json=cidList,proto3" json:"cid_list,omitempty"`
	IsNeedPkInfo         bool     `protobuf:"varint,3,opt,name=is_need_pk_info,json=isNeedPkInfo,proto3" json:"is_need_pk_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatCheckIsInMultiPkReq) Reset()         { *m = BatCheckIsInMultiPkReq{} }
func (m *BatCheckIsInMultiPkReq) String() string { return proto.CompactTextString(m) }
func (*BatCheckIsInMultiPkReq) ProtoMessage()    {}
func (*BatCheckIsInMultiPkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{40}
}
func (m *BatCheckIsInMultiPkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatCheckIsInMultiPkReq.Unmarshal(m, b)
}
func (m *BatCheckIsInMultiPkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatCheckIsInMultiPkReq.Marshal(b, m, deterministic)
}
func (dst *BatCheckIsInMultiPkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatCheckIsInMultiPkReq.Merge(dst, src)
}
func (m *BatCheckIsInMultiPkReq) XXX_Size() int {
	return xxx_messageInfo_BatCheckIsInMultiPkReq.Size(m)
}
func (m *BatCheckIsInMultiPkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatCheckIsInMultiPkReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatCheckIsInMultiPkReq proto.InternalMessageInfo

func (m *BatCheckIsInMultiPkReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatCheckIsInMultiPkReq) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

func (m *BatCheckIsInMultiPkReq) GetIsNeedPkInfo() bool {
	if m != nil {
		return m.IsNeedPkInfo
	}
	return false
}

type BatCheckIsInMultiPkResp struct {
	MapIdIspk            map[uint32]bool               `protobuf:"bytes,1,rep,name=map_id_ispk,json=mapIdIspk,proto3" json:"map_id_ispk,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapCidInfo           map[uint32]*MultiPkSimpleInfo `protobuf:"bytes,2,rep,name=map_cid_info,json=mapCidInfo,proto3" json:"map_cid_info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *BatCheckIsInMultiPkResp) Reset()         { *m = BatCheckIsInMultiPkResp{} }
func (m *BatCheckIsInMultiPkResp) String() string { return proto.CompactTextString(m) }
func (*BatCheckIsInMultiPkResp) ProtoMessage()    {}
func (*BatCheckIsInMultiPkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{41}
}
func (m *BatCheckIsInMultiPkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatCheckIsInMultiPkResp.Unmarshal(m, b)
}
func (m *BatCheckIsInMultiPkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatCheckIsInMultiPkResp.Marshal(b, m, deterministic)
}
func (dst *BatCheckIsInMultiPkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatCheckIsInMultiPkResp.Merge(dst, src)
}
func (m *BatCheckIsInMultiPkResp) XXX_Size() int {
	return xxx_messageInfo_BatCheckIsInMultiPkResp.Size(m)
}
func (m *BatCheckIsInMultiPkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatCheckIsInMultiPkResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatCheckIsInMultiPkResp proto.InternalMessageInfo

func (m *BatCheckIsInMultiPkResp) GetMapIdIspk() map[uint32]bool {
	if m != nil {
		return m.MapIdIspk
	}
	return nil
}

func (m *BatCheckIsInMultiPkResp) GetMapCidInfo() map[uint32]*MultiPkSimpleInfo {
	if m != nil {
		return m.MapCidInfo
	}
	return nil
}

// 批量查询是否在多人pk匹配
type BatCheckIsInMultiMatchReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatCheckIsInMultiMatchReq) Reset()         { *m = BatCheckIsInMultiMatchReq{} }
func (m *BatCheckIsInMultiMatchReq) String() string { return proto.CompactTextString(m) }
func (*BatCheckIsInMultiMatchReq) ProtoMessage()    {}
func (*BatCheckIsInMultiMatchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{42}
}
func (m *BatCheckIsInMultiMatchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatCheckIsInMultiMatchReq.Unmarshal(m, b)
}
func (m *BatCheckIsInMultiMatchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatCheckIsInMultiMatchReq.Marshal(b, m, deterministic)
}
func (dst *BatCheckIsInMultiMatchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatCheckIsInMultiMatchReq.Merge(dst, src)
}
func (m *BatCheckIsInMultiMatchReq) XXX_Size() int {
	return xxx_messageInfo_BatCheckIsInMultiMatchReq.Size(m)
}
func (m *BatCheckIsInMultiMatchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatCheckIsInMultiMatchReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatCheckIsInMultiMatchReq proto.InternalMessageInfo

func (m *BatCheckIsInMultiMatchReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatCheckIsInMultiMatchResp struct {
	MapIdIsMatch         map[uint32]bool `protobuf:"bytes,1,rep,name=map_id_is_match,json=mapIdIsMatch,proto3" json:"map_id_is_match,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatCheckIsInMultiMatchResp) Reset()         { *m = BatCheckIsInMultiMatchResp{} }
func (m *BatCheckIsInMultiMatchResp) String() string { return proto.CompactTextString(m) }
func (*BatCheckIsInMultiMatchResp) ProtoMessage()    {}
func (*BatCheckIsInMultiMatchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{43}
}
func (m *BatCheckIsInMultiMatchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatCheckIsInMultiMatchResp.Unmarshal(m, b)
}
func (m *BatCheckIsInMultiMatchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatCheckIsInMultiMatchResp.Marshal(b, m, deterministic)
}
func (dst *BatCheckIsInMultiMatchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatCheckIsInMultiMatchResp.Merge(dst, src)
}
func (m *BatCheckIsInMultiMatchResp) XXX_Size() int {
	return xxx_messageInfo_BatCheckIsInMultiMatchResp.Size(m)
}
func (m *BatCheckIsInMultiMatchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatCheckIsInMultiMatchResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatCheckIsInMultiMatchResp proto.InternalMessageInfo

func (m *BatCheckIsInMultiMatchResp) GetMapIdIsMatch() map[uint32]bool {
	if m != nil {
		return m.MapIdIsMatch
	}
	return nil
}

type HandlerApplyReq struct {
	Uid                  uint32    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32    `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Username             string    `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Nickname             string    `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	ApplyUid             uint32    `protobuf:"varint,5,opt,name=apply_uid,json=applyUid,proto3" json:"apply_uid,omitempty"`
	ApplyChannelId       uint32    `protobuf:"varint,6,opt,name=apply_channel_id,json=applyChannelId,proto3" json:"apply_channel_id,omitempty"`
	ApplyUsername        string    `protobuf:"bytes,7,opt,name=apply_username,json=applyUsername,proto3" json:"apply_username,omitempty"`
	ApplyNickname        string    `protobuf:"bytes,8,opt,name=apply_nickname,json=applyNickname,proto3" json:"apply_nickname,omitempty"`
	Oper                 EnumApply `protobuf:"varint,9,opt,name=oper,proto3,enum=channel_live_pk.EnumApply" json:"oper,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *HandlerApplyReq) Reset()         { *m = HandlerApplyReq{} }
func (m *HandlerApplyReq) String() string { return proto.CompactTextString(m) }
func (*HandlerApplyReq) ProtoMessage()    {}
func (*HandlerApplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{44}
}
func (m *HandlerApplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandlerApplyReq.Unmarshal(m, b)
}
func (m *HandlerApplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandlerApplyReq.Marshal(b, m, deterministic)
}
func (dst *HandlerApplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandlerApplyReq.Merge(dst, src)
}
func (m *HandlerApplyReq) XXX_Size() int {
	return xxx_messageInfo_HandlerApplyReq.Size(m)
}
func (m *HandlerApplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HandlerApplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_HandlerApplyReq proto.InternalMessageInfo

func (m *HandlerApplyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HandlerApplyReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *HandlerApplyReq) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *HandlerApplyReq) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *HandlerApplyReq) GetApplyUid() uint32 {
	if m != nil {
		return m.ApplyUid
	}
	return 0
}

func (m *HandlerApplyReq) GetApplyChannelId() uint32 {
	if m != nil {
		return m.ApplyChannelId
	}
	return 0
}

func (m *HandlerApplyReq) GetApplyUsername() string {
	if m != nil {
		return m.ApplyUsername
	}
	return ""
}

func (m *HandlerApplyReq) GetApplyNickname() string {
	if m != nil {
		return m.ApplyNickname
	}
	return ""
}

func (m *HandlerApplyReq) GetOper() EnumApply {
	if m != nil {
		return m.Oper
	}
	return EnumApply_accept
}

type HandlerApplyResp struct {
	PkBeginTime          uint32   `protobuf:"varint,1,opt,name=pk_begin_time,json=pkBeginTime,proto3" json:"pk_begin_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandlerApplyResp) Reset()         { *m = HandlerApplyResp{} }
func (m *HandlerApplyResp) String() string { return proto.CompactTextString(m) }
func (*HandlerApplyResp) ProtoMessage()    {}
func (*HandlerApplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{45}
}
func (m *HandlerApplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandlerApplyResp.Unmarshal(m, b)
}
func (m *HandlerApplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandlerApplyResp.Marshal(b, m, deterministic)
}
func (dst *HandlerApplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandlerApplyResp.Merge(dst, src)
}
func (m *HandlerApplyResp) XXX_Size() int {
	return xxx_messageInfo_HandlerApplyResp.Size(m)
}
func (m *HandlerApplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HandlerApplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_HandlerApplyResp proto.InternalMessageInfo

func (m *HandlerApplyResp) GetPkBeginTime() uint32 {
	if m != nil {
		return m.PkBeginTime
	}
	return 0
}

type CancelPKApplyReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ApplyUid             uint32   `protobuf:"varint,3,opt,name=apply_uid,json=applyUid,proto3" json:"apply_uid,omitempty"`
	ApplyChannelId       uint32   `protobuf:"varint,4,opt,name=apply_channel_id,json=applyChannelId,proto3" json:"apply_channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelPKApplyReq) Reset()         { *m = CancelPKApplyReq{} }
func (m *CancelPKApplyReq) String() string { return proto.CompactTextString(m) }
func (*CancelPKApplyReq) ProtoMessage()    {}
func (*CancelPKApplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{46}
}
func (m *CancelPKApplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelPKApplyReq.Unmarshal(m, b)
}
func (m *CancelPKApplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelPKApplyReq.Marshal(b, m, deterministic)
}
func (dst *CancelPKApplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelPKApplyReq.Merge(dst, src)
}
func (m *CancelPKApplyReq) XXX_Size() int {
	return xxx_messageInfo_CancelPKApplyReq.Size(m)
}
func (m *CancelPKApplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelPKApplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelPKApplyReq proto.InternalMessageInfo

func (m *CancelPKApplyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CancelPKApplyReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CancelPKApplyReq) GetApplyUid() uint32 {
	if m != nil {
		return m.ApplyUid
	}
	return 0
}

func (m *CancelPKApplyReq) GetApplyChannelId() uint32 {
	if m != nil {
		return m.ApplyChannelId
	}
	return 0
}

type CancelPKApplyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelPKApplyResp) Reset()         { *m = CancelPKApplyResp{} }
func (m *CancelPKApplyResp) String() string { return proto.CompactTextString(m) }
func (*CancelPKApplyResp) ProtoMessage()    {}
func (*CancelPKApplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{47}
}
func (m *CancelPKApplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelPKApplyResp.Unmarshal(m, b)
}
func (m *CancelPKApplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelPKApplyResp.Marshal(b, m, deterministic)
}
func (dst *CancelPKApplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelPKApplyResp.Merge(dst, src)
}
func (m *CancelPKApplyResp) XXX_Size() int {
	return xxx_messageInfo_CancelPKApplyResp.Size(m)
}
func (m *CancelPKApplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelPKApplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelPKApplyResp proto.InternalMessageInfo

// 主播设置PK状态接口
type SetPkStatusReq struct {
	Uid                  uint32                  `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32                  `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Status               EnumChannelLivePKStatus `protobuf:"varint,4,opt,name=status,proto3,enum=channel_live_pk.EnumChannelLivePKStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SetPkStatusReq) Reset()         { *m = SetPkStatusReq{} }
func (m *SetPkStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetPkStatusReq) ProtoMessage()    {}
func (*SetPkStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{48}
}
func (m *SetPkStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPkStatusReq.Unmarshal(m, b)
}
func (m *SetPkStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPkStatusReq.Marshal(b, m, deterministic)
}
func (dst *SetPkStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPkStatusReq.Merge(dst, src)
}
func (m *SetPkStatusReq) XXX_Size() int {
	return xxx_messageInfo_SetPkStatusReq.Size(m)
}
func (m *SetPkStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPkStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPkStatusReq proto.InternalMessageInfo

func (m *SetPkStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetPkStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetPkStatusReq) GetStatus() EnumChannelLivePKStatus {
	if m != nil {
		return m.Status
	}
	return EnumChannelLivePKStatus_IDLE
}

type SetPkStatusResp struct {
	TargetChannelId      uint32   `protobuf:"varint,1,opt,name=target_channel_id,json=targetChannelId,proto3" json:"target_channel_id,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPkStatusResp) Reset()         { *m = SetPkStatusResp{} }
func (m *SetPkStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetPkStatusResp) ProtoMessage()    {}
func (*SetPkStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{49}
}
func (m *SetPkStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPkStatusResp.Unmarshal(m, b)
}
func (m *SetPkStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPkStatusResp.Marshal(b, m, deterministic)
}
func (dst *SetPkStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPkStatusResp.Merge(dst, src)
}
func (m *SetPkStatusResp) XXX_Size() int {
	return xxx_messageInfo_SetPkStatusResp.Size(m)
}
func (m *SetPkStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPkStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPkStatusResp proto.InternalMessageInfo

func (m *SetPkStatusResp) GetTargetChannelId() uint32 {
	if m != nil {
		return m.TargetChannelId
	}
	return 0
}

func (m *SetPkStatusResp) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

// 连麦操作相关
type ApplyPkReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelLiveId        uint64   `protobuf:"varint,3,opt,name=channel_live_id,json=channelLiveId,proto3" json:"channel_live_id,omitempty"`
	TargetUid            uint32   `protobuf:"varint,4,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	TargetChannelId      uint32   `protobuf:"varint,5,opt,name=target_channel_id,json=targetChannelId,proto3" json:"target_channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyPkReq) Reset()         { *m = ApplyPkReq{} }
func (m *ApplyPkReq) String() string { return proto.CompactTextString(m) }
func (*ApplyPkReq) ProtoMessage()    {}
func (*ApplyPkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{50}
}
func (m *ApplyPkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyPkReq.Unmarshal(m, b)
}
func (m *ApplyPkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyPkReq.Marshal(b, m, deterministic)
}
func (dst *ApplyPkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyPkReq.Merge(dst, src)
}
func (m *ApplyPkReq) XXX_Size() int {
	return xxx_messageInfo_ApplyPkReq.Size(m)
}
func (m *ApplyPkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyPkReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyPkReq proto.InternalMessageInfo

func (m *ApplyPkReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplyPkReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ApplyPkReq) GetChannelLiveId() uint64 {
	if m != nil {
		return m.ChannelLiveId
	}
	return 0
}

func (m *ApplyPkReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *ApplyPkReq) GetTargetChannelId() uint32 {
	if m != nil {
		return m.TargetChannelId
	}
	return 0
}

type ApplyPkResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyPkResp) Reset()         { *m = ApplyPkResp{} }
func (m *ApplyPkResp) String() string { return proto.CompactTextString(m) }
func (*ApplyPkResp) ProtoMessage()    {}
func (*ApplyPkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{51}
}
func (m *ApplyPkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyPkResp.Unmarshal(m, b)
}
func (m *ApplyPkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyPkResp.Marshal(b, m, deterministic)
}
func (dst *ApplyPkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyPkResp.Merge(dst, src)
}
func (m *ApplyPkResp) XXX_Size() int {
	return xxx_messageInfo_ApplyPkResp.Size(m)
}
func (m *ApplyPkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyPkResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyPkResp proto.InternalMessageInfo

type GetChanneLivePkRankUserReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TargetUid            uint32   `protobuf:"varint,3,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	Off                  uint32   `protobuf:"varint,4,opt,name=off,proto3" json:"off,omitempty"`
	Cnt                  uint32   `protobuf:"varint,5,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChanneLivePkRankUserReq) Reset()         { *m = GetChanneLivePkRankUserReq{} }
func (m *GetChanneLivePkRankUserReq) String() string { return proto.CompactTextString(m) }
func (*GetChanneLivePkRankUserReq) ProtoMessage()    {}
func (*GetChanneLivePkRankUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{52}
}
func (m *GetChanneLivePkRankUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChanneLivePkRankUserReq.Unmarshal(m, b)
}
func (m *GetChanneLivePkRankUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChanneLivePkRankUserReq.Marshal(b, m, deterministic)
}
func (dst *GetChanneLivePkRankUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChanneLivePkRankUserReq.Merge(dst, src)
}
func (m *GetChanneLivePkRankUserReq) XXX_Size() int {
	return xxx_messageInfo_GetChanneLivePkRankUserReq.Size(m)
}
func (m *GetChanneLivePkRankUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChanneLivePkRankUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChanneLivePkRankUserReq proto.InternalMessageInfo

func (m *GetChanneLivePkRankUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChanneLivePkRankUserReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChanneLivePkRankUserReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetChanneLivePkRankUserReq) GetOff() uint32 {
	if m != nil {
		return m.Off
	}
	return 0
}

func (m *GetChanneLivePkRankUserReq) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type GetChanneLivePkRankUserResp struct {
	UserList             []*SendGiftUserInfo `protobuf:"bytes,1,rep,name=user_list,json=userList,proto3" json:"user_list,omitempty"`
	ChannelId            uint32              `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AnchorUid            uint32              `protobuf:"varint,3,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetChanneLivePkRankUserResp) Reset()         { *m = GetChanneLivePkRankUserResp{} }
func (m *GetChanneLivePkRankUserResp) String() string { return proto.CompactTextString(m) }
func (*GetChanneLivePkRankUserResp) ProtoMessage()    {}
func (*GetChanneLivePkRankUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{53}
}
func (m *GetChanneLivePkRankUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChanneLivePkRankUserResp.Unmarshal(m, b)
}
func (m *GetChanneLivePkRankUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChanneLivePkRankUserResp.Marshal(b, m, deterministic)
}
func (dst *GetChanneLivePkRankUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChanneLivePkRankUserResp.Merge(dst, src)
}
func (m *GetChanneLivePkRankUserResp) XXX_Size() int {
	return xxx_messageInfo_GetChanneLivePkRankUserResp.Size(m)
}
func (m *GetChanneLivePkRankUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChanneLivePkRankUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChanneLivePkRankUserResp proto.InternalMessageInfo

func (m *GetChanneLivePkRankUserResp) GetUserList() []*SendGiftUserInfo {
	if m != nil {
		return m.UserList
	}
	return nil
}

func (m *GetChanneLivePkRankUserResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChanneLivePkRankUserResp) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

type SendGiftUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Score                uint32   `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	FirstKill            bool     `protobuf:"varint,3,opt,name=first_kill,json=firstKill,proto3" json:"first_kill,omitempty"`
	UkwInfo              *UkwInfo `protobuf:"bytes,4,opt,name=ukw_info,json=ukwInfo,proto3" json:"ukw_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendGiftUserInfo) Reset()         { *m = SendGiftUserInfo{} }
func (m *SendGiftUserInfo) String() string { return proto.CompactTextString(m) }
func (*SendGiftUserInfo) ProtoMessage()    {}
func (*SendGiftUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{54}
}
func (m *SendGiftUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGiftUserInfo.Unmarshal(m, b)
}
func (m *SendGiftUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGiftUserInfo.Marshal(b, m, deterministic)
}
func (dst *SendGiftUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGiftUserInfo.Merge(dst, src)
}
func (m *SendGiftUserInfo) XXX_Size() int {
	return xxx_messageInfo_SendGiftUserInfo.Size(m)
}
func (m *SendGiftUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGiftUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SendGiftUserInfo proto.InternalMessageInfo

func (m *SendGiftUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendGiftUserInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *SendGiftUserInfo) GetFirstKill() bool {
	if m != nil {
		return m.FirstKill
	}
	return false
}

func (m *SendGiftUserInfo) GetUkwInfo() *UkwInfo {
	if m != nil {
		return m.UkwInfo
	}
	return nil
}

// 用户进房的时候拿PK信息
type GetPkInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPkInfoReq) Reset()         { *m = GetPkInfoReq{} }
func (m *GetPkInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetPkInfoReq) ProtoMessage()    {}
func (*GetPkInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{55}
}
func (m *GetPkInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPkInfoReq.Unmarshal(m, b)
}
func (m *GetPkInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPkInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetPkInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPkInfoReq.Merge(dst, src)
}
func (m *GetPkInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetPkInfoReq.Size(m)
}
func (m *GetPkInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPkInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPkInfoReq proto.InternalMessageInfo

func (m *GetPkInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetPkInfoResp struct {
	APkInfo              *PkSingleInfo `protobuf:"bytes,1,opt,name=a_pk_info,json=aPkInfo,proto3" json:"a_pk_info,omitempty"`
	BPkInfo              *PkSingleInfo `protobuf:"bytes,2,opt,name=b_pk_info,json=bPkInfo,proto3" json:"b_pk_info,omitempty"`
	PkCommonInfo         *PkCommonInfo `protobuf:"bytes,3,opt,name=pk_common_info,json=pkCommonInfo,proto3" json:"pk_common_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetPkInfoResp) Reset()         { *m = GetPkInfoResp{} }
func (m *GetPkInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetPkInfoResp) ProtoMessage()    {}
func (*GetPkInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{56}
}
func (m *GetPkInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPkInfoResp.Unmarshal(m, b)
}
func (m *GetPkInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPkInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetPkInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPkInfoResp.Merge(dst, src)
}
func (m *GetPkInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetPkInfoResp.Size(m)
}
func (m *GetPkInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPkInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPkInfoResp proto.InternalMessageInfo

func (m *GetPkInfoResp) GetAPkInfo() *PkSingleInfo {
	if m != nil {
		return m.APkInfo
	}
	return nil
}

func (m *GetPkInfoResp) GetBPkInfo() *PkSingleInfo {
	if m != nil {
		return m.BPkInfo
	}
	return nil
}

func (m *GetPkInfoResp) GetPkCommonInfo() *PkCommonInfo {
	if m != nil {
		return m.PkCommonInfo
	}
	return nil
}

// PK一方的信息
type PkSingleInfo struct {
	Uid                  uint32                     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32                     `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelLiveId        uint64                     `protobuf:"varint,3,opt,name=channel_live_id,json=channelLiveId,proto3" json:"channel_live_id,omitempty"`
	PkScore              uint32                     `protobuf:"varint,4,opt,name=pk_score,json=pkScore,proto3" json:"pk_score,omitempty"`
	EffectCnt            uint32                     `protobuf:"varint,5,opt,name=effect_cnt,json=effectCnt,proto3" json:"effect_cnt,omitempty"`
	MicFlag              ChannelLiveOpponentMicFlag `protobuf:"varint,6,opt,name=mic_flag,json=micFlag,proto3,enum=channel_live_pk.ChannelLiveOpponentMicFlag" json:"mic_flag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *PkSingleInfo) Reset()         { *m = PkSingleInfo{} }
func (m *PkSingleInfo) String() string { return proto.CompactTextString(m) }
func (*PkSingleInfo) ProtoMessage()    {}
func (*PkSingleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{57}
}
func (m *PkSingleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PkSingleInfo.Unmarshal(m, b)
}
func (m *PkSingleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PkSingleInfo.Marshal(b, m, deterministic)
}
func (dst *PkSingleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PkSingleInfo.Merge(dst, src)
}
func (m *PkSingleInfo) XXX_Size() int {
	return xxx_messageInfo_PkSingleInfo.Size(m)
}
func (m *PkSingleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PkSingleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PkSingleInfo proto.InternalMessageInfo

func (m *PkSingleInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PkSingleInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PkSingleInfo) GetChannelLiveId() uint64 {
	if m != nil {
		return m.ChannelLiveId
	}
	return 0
}

func (m *PkSingleInfo) GetPkScore() uint32 {
	if m != nil {
		return m.PkScore
	}
	return 0
}

func (m *PkSingleInfo) GetEffectCnt() uint32 {
	if m != nil {
		return m.EffectCnt
	}
	return 0
}

func (m *PkSingleInfo) GetMicFlag() ChannelLiveOpponentMicFlag {
	if m != nil {
		return m.MicFlag
	}
	return ChannelLiveOpponentMicFlag_CPKMic_OPEN
}

// PK公共数据
type PkCommonInfo struct {
	BeginTime            uint32                  `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	PkStatus             EnumChannelLivePKStatus `protobuf:"varint,2,opt,name=pk_status,json=pkStatus,proto3,enum=channel_live_pk.EnumChannelLivePKStatus" json:"pk_status,omitempty"`
	MicList              map[uint32]*PkMicSpace  `protobuf:"bytes,3,rep,name=mic_list,json=micList,proto3" json:"mic_list,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	FirstKillUid         uint32                  `protobuf:"varint,4,opt,name=first_kill_uid,json=firstKillUid,proto3" json:"first_kill_uid,omitempty"`
	FirstKillCid         uint32                  `protobuf:"varint,5,opt,name=first_kill_cid,json=firstKillCid,proto3" json:"first_kill_cid,omitempty"`
	IsExtraTime          bool                    `protobuf:"varint,6,opt,name=is_extra_time,json=isExtraTime,proto3" json:"is_extra_time,omitempty"`
	PkExtraTimeRule      string                  `protobuf:"bytes,7,opt,name=pk_extra_time_rule,json=pkExtraTimeRule,proto3" json:"pk_extra_time_rule,omitempty"`
	ExtraLeftTime        uint32                  `protobuf:"varint,8,opt,name=extra_left_time,json=extraLeftTime,proto3" json:"extra_left_time,omitempty"`
	IsOpenExtraTime      bool                    `protobuf:"varint,9,opt,name=is_open_extra_time,json=isOpenExtraTime,proto3" json:"is_open_extra_time,omitempty"`
	FirstKillUwkinfo     *UkwInfo                `protobuf:"bytes,10,opt,name=first_kill_uwkinfo,json=firstKillUwkinfo,proto3" json:"first_kill_uwkinfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *PkCommonInfo) Reset()         { *m = PkCommonInfo{} }
func (m *PkCommonInfo) String() string { return proto.CompactTextString(m) }
func (*PkCommonInfo) ProtoMessage()    {}
func (*PkCommonInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{58}
}
func (m *PkCommonInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PkCommonInfo.Unmarshal(m, b)
}
func (m *PkCommonInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PkCommonInfo.Marshal(b, m, deterministic)
}
func (dst *PkCommonInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PkCommonInfo.Merge(dst, src)
}
func (m *PkCommonInfo) XXX_Size() int {
	return xxx_messageInfo_PkCommonInfo.Size(m)
}
func (m *PkCommonInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PkCommonInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PkCommonInfo proto.InternalMessageInfo

func (m *PkCommonInfo) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *PkCommonInfo) GetPkStatus() EnumChannelLivePKStatus {
	if m != nil {
		return m.PkStatus
	}
	return EnumChannelLivePKStatus_IDLE
}

func (m *PkCommonInfo) GetMicList() map[uint32]*PkMicSpace {
	if m != nil {
		return m.MicList
	}
	return nil
}

func (m *PkCommonInfo) GetFirstKillUid() uint32 {
	if m != nil {
		return m.FirstKillUid
	}
	return 0
}

func (m *PkCommonInfo) GetFirstKillCid() uint32 {
	if m != nil {
		return m.FirstKillCid
	}
	return 0
}

func (m *PkCommonInfo) GetIsExtraTime() bool {
	if m != nil {
		return m.IsExtraTime
	}
	return false
}

func (m *PkCommonInfo) GetPkExtraTimeRule() string {
	if m != nil {
		return m.PkExtraTimeRule
	}
	return ""
}

func (m *PkCommonInfo) GetExtraLeftTime() uint32 {
	if m != nil {
		return m.ExtraLeftTime
	}
	return 0
}

func (m *PkCommonInfo) GetIsOpenExtraTime() bool {
	if m != nil {
		return m.IsOpenExtraTime
	}
	return false
}

func (m *PkCommonInfo) GetFirstKillUwkinfo() *UkwInfo {
	if m != nil {
		return m.FirstKillUwkinfo
	}
	return nil
}

type PkMicSpace struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nick                 string   `protobuf:"bytes,3,opt,name=nick,proto3" json:"nick,omitempty"`
	MicId                uint32   `protobuf:"varint,4,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	VoiceId              string   `protobuf:"bytes,5,opt,name=voice_id,json=voiceId,proto3" json:"voice_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PkMicSpace) Reset()         { *m = PkMicSpace{} }
func (m *PkMicSpace) String() string { return proto.CompactTextString(m) }
func (*PkMicSpace) ProtoMessage()    {}
func (*PkMicSpace) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{59}
}
func (m *PkMicSpace) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PkMicSpace.Unmarshal(m, b)
}
func (m *PkMicSpace) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PkMicSpace.Marshal(b, m, deterministic)
}
func (dst *PkMicSpace) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PkMicSpace.Merge(dst, src)
}
func (m *PkMicSpace) XXX_Size() int {
	return xxx_messageInfo_PkMicSpace.Size(m)
}
func (m *PkMicSpace) XXX_DiscardUnknown() {
	xxx_messageInfo_PkMicSpace.DiscardUnknown(m)
}

var xxx_messageInfo_PkMicSpace proto.InternalMessageInfo

func (m *PkMicSpace) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PkMicSpace) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *PkMicSpace) GetNick() string {
	if m != nil {
		return m.Nick
	}
	return ""
}

func (m *PkMicSpace) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *PkMicSpace) GetVoiceId() string {
	if m != nil {
		return m.VoiceId
	}
	return ""
}

type StartPkMatchReq struct {
	ChannelId            uint32                 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32                 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	MatchType            ChannelLivePKMatchType `protobuf:"varint,3,opt,name=match_type,json=matchType,proto3,enum=channel_live_pk.ChannelLivePKMatchType" json:"match_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *StartPkMatchReq) Reset()         { *m = StartPkMatchReq{} }
func (m *StartPkMatchReq) String() string { return proto.CompactTextString(m) }
func (*StartPkMatchReq) ProtoMessage()    {}
func (*StartPkMatchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{60}
}
func (m *StartPkMatchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartPkMatchReq.Unmarshal(m, b)
}
func (m *StartPkMatchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartPkMatchReq.Marshal(b, m, deterministic)
}
func (dst *StartPkMatchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartPkMatchReq.Merge(dst, src)
}
func (m *StartPkMatchReq) XXX_Size() int {
	return xxx_messageInfo_StartPkMatchReq.Size(m)
}
func (m *StartPkMatchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartPkMatchReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartPkMatchReq proto.InternalMessageInfo

func (m *StartPkMatchReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StartPkMatchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StartPkMatchReq) GetMatchType() ChannelLivePKMatchType {
	if m != nil {
		return m.MatchType
	}
	return ChannelLivePKMatchType_CPK_Match_Nornal
}

type StartPkMatchResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartPkMatchResp) Reset()         { *m = StartPkMatchResp{} }
func (m *StartPkMatchResp) String() string { return proto.CompactTextString(m) }
func (*StartPkMatchResp) ProtoMessage()    {}
func (*StartPkMatchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{61}
}
func (m *StartPkMatchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartPkMatchResp.Unmarshal(m, b)
}
func (m *StartPkMatchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartPkMatchResp.Marshal(b, m, deterministic)
}
func (dst *StartPkMatchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartPkMatchResp.Merge(dst, src)
}
func (m *StartPkMatchResp) XXX_Size() int {
	return xxx_messageInfo_StartPkMatchResp.Size(m)
}
func (m *StartPkMatchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartPkMatchResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartPkMatchResp proto.InternalMessageInfo

type CancelPkMatchReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelPkMatchReq) Reset()         { *m = CancelPkMatchReq{} }
func (m *CancelPkMatchReq) String() string { return proto.CompactTextString(m) }
func (*CancelPkMatchReq) ProtoMessage()    {}
func (*CancelPkMatchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{62}
}
func (m *CancelPkMatchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelPkMatchReq.Unmarshal(m, b)
}
func (m *CancelPkMatchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelPkMatchReq.Marshal(b, m, deterministic)
}
func (dst *CancelPkMatchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelPkMatchReq.Merge(dst, src)
}
func (m *CancelPkMatchReq) XXX_Size() int {
	return xxx_messageInfo_CancelPkMatchReq.Size(m)
}
func (m *CancelPkMatchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelPkMatchReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelPkMatchReq proto.InternalMessageInfo

func (m *CancelPkMatchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CancelPkMatchReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CancelPkMatchResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelPkMatchResp) Reset()         { *m = CancelPkMatchResp{} }
func (m *CancelPkMatchResp) String() string { return proto.CompactTextString(m) }
func (*CancelPkMatchResp) ProtoMessage()    {}
func (*CancelPkMatchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{63}
}
func (m *CancelPkMatchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelPkMatchResp.Unmarshal(m, b)
}
func (m *CancelPkMatchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelPkMatchResp.Marshal(b, m, deterministic)
}
func (dst *CancelPkMatchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelPkMatchResp.Merge(dst, src)
}
func (m *CancelPkMatchResp) XXX_Size() int {
	return xxx_messageInfo_CancelPkMatchResp.Size(m)
}
func (m *CancelPkMatchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelPkMatchResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelPkMatchResp proto.InternalMessageInfo

func (m *CancelPkMatchResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 取PK匹配类型和自己PK赛阶段
type GetPKMatchInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPKMatchInfoReq) Reset()         { *m = GetPKMatchInfoReq{} }
func (m *GetPKMatchInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetPKMatchInfoReq) ProtoMessage()    {}
func (*GetPKMatchInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{64}
}
func (m *GetPKMatchInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPKMatchInfoReq.Unmarshal(m, b)
}
func (m *GetPKMatchInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPKMatchInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetPKMatchInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPKMatchInfoReq.Merge(dst, src)
}
func (m *GetPKMatchInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetPKMatchInfoReq.Size(m)
}
func (m *GetPKMatchInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPKMatchInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPKMatchInfoReq proto.InternalMessageInfo

func (m *GetPKMatchInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPKMatchInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPKMatchInfoResp struct {
	PkMatchTy            ChannelLivePKMatchType `protobuf:"varint,1,opt,name=pk_match_ty,json=pkMatchTy,proto3,enum=channel_live_pk.ChannelLivePKMatchType" json:"pk_match_ty,omitempty"`
	CptInfo              *PkCompetitionInfo     `protobuf:"bytes,2,opt,name=cpt_info,json=cptInfo,proto3" json:"cpt_info,omitempty"`
	PkLimitInfo          *PkLimitInfo           `protobuf:"bytes,3,opt,name=pk_limit_info,json=pkLimitInfo,proto3" json:"pk_limit_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetPKMatchInfoResp) Reset()         { *m = GetPKMatchInfoResp{} }
func (m *GetPKMatchInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetPKMatchInfoResp) ProtoMessage()    {}
func (*GetPKMatchInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{65}
}
func (m *GetPKMatchInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPKMatchInfoResp.Unmarshal(m, b)
}
func (m *GetPKMatchInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPKMatchInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetPKMatchInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPKMatchInfoResp.Merge(dst, src)
}
func (m *GetPKMatchInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetPKMatchInfoResp.Size(m)
}
func (m *GetPKMatchInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPKMatchInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPKMatchInfoResp proto.InternalMessageInfo

func (m *GetPKMatchInfoResp) GetPkMatchTy() ChannelLivePKMatchType {
	if m != nil {
		return m.PkMatchTy
	}
	return ChannelLivePKMatchType_CPK_Match_Nornal
}

func (m *GetPKMatchInfoResp) GetCptInfo() *PkCompetitionInfo {
	if m != nil {
		return m.CptInfo
	}
	return nil
}

func (m *GetPKMatchInfoResp) GetPkLimitInfo() *PkLimitInfo {
	if m != nil {
		return m.PkLimitInfo
	}
	return nil
}

// 自己PK赛的阶段信息
type PkCompetitionInfo struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	LevelName            string   `protobuf:"bytes,2,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PkCompetitionInfo) Reset()         { *m = PkCompetitionInfo{} }
func (m *PkCompetitionInfo) String() string { return proto.CompactTextString(m) }
func (*PkCompetitionInfo) ProtoMessage()    {}
func (*PkCompetitionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{66}
}
func (m *PkCompetitionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PkCompetitionInfo.Unmarshal(m, b)
}
func (m *PkCompetitionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PkCompetitionInfo.Marshal(b, m, deterministic)
}
func (dst *PkCompetitionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PkCompetitionInfo.Merge(dst, src)
}
func (m *PkCompetitionInfo) XXX_Size() int {
	return xxx_messageInfo_PkCompetitionInfo.Size(m)
}
func (m *PkCompetitionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PkCompetitionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PkCompetitionInfo proto.InternalMessageInfo

func (m *PkCompetitionInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *PkCompetitionInfo) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

type PkLimitInfo struct {
	// 需要考虑没有配置次数限制的情况，这两字段都是"",客户端不显示限制提示？
	TimeRange            string   `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
	LimitCnt             string   `protobuf:"bytes,2,opt,name=limit_cnt,json=limitCnt,proto3" json:"limit_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PkLimitInfo) Reset()         { *m = PkLimitInfo{} }
func (m *PkLimitInfo) String() string { return proto.CompactTextString(m) }
func (*PkLimitInfo) ProtoMessage()    {}
func (*PkLimitInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{67}
}
func (m *PkLimitInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PkLimitInfo.Unmarshal(m, b)
}
func (m *PkLimitInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PkLimitInfo.Marshal(b, m, deterministic)
}
func (dst *PkLimitInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PkLimitInfo.Merge(dst, src)
}
func (m *PkLimitInfo) XXX_Size() int {
	return xxx_messageInfo_PkLimitInfo.Size(m)
}
func (m *PkLimitInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PkLimitInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PkLimitInfo proto.InternalMessageInfo

func (m *PkLimitInfo) GetTimeRange() string {
	if m != nil {
		return m.TimeRange
	}
	return ""
}

func (m *PkLimitInfo) GetLimitCnt() string {
	if m != nil {
		return m.LimitCnt
	}
	return ""
}

// 取道具列表接口
type GetMyToolListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMyToolListReq) Reset()         { *m = GetMyToolListReq{} }
func (m *GetMyToolListReq) String() string { return proto.CompactTextString(m) }
func (*GetMyToolListReq) ProtoMessage()    {}
func (*GetMyToolListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{68}
}
func (m *GetMyToolListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyToolListReq.Unmarshal(m, b)
}
func (m *GetMyToolListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyToolListReq.Marshal(b, m, deterministic)
}
func (dst *GetMyToolListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyToolListReq.Merge(dst, src)
}
func (m *GetMyToolListReq) XXX_Size() int {
	return xxx_messageInfo_GetMyToolListReq.Size(m)
}
func (m *GetMyToolListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyToolListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyToolListReq proto.InternalMessageInfo

func (m *GetMyToolListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMyToolListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetMyToolListResp struct {
	Items                []*ToolItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetMyToolListResp) Reset()         { *m = GetMyToolListResp{} }
func (m *GetMyToolListResp) String() string { return proto.CompactTextString(m) }
func (*GetMyToolListResp) ProtoMessage()    {}
func (*GetMyToolListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{69}
}
func (m *GetMyToolListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyToolListResp.Unmarshal(m, b)
}
func (m *GetMyToolListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyToolListResp.Marshal(b, m, deterministic)
}
func (dst *GetMyToolListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyToolListResp.Merge(dst, src)
}
func (m *GetMyToolListResp) XXX_Size() int {
	return xxx_messageInfo_GetMyToolListResp.Size(m)
}
func (m *GetMyToolListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyToolListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyToolListResp proto.InternalMessageInfo

func (m *GetMyToolListResp) GetItems() []*ToolItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type ToolItem struct {
	ItemId               string   `protobuf:"bytes,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemNs               int64    `protobuf:"varint,2,opt,name=item_ns,json=itemNs,proto3" json:"item_ns,omitempty"`
	BeUsed               bool     `protobuf:"varint,3,opt,name=beUsed,proto3" json:"beUsed,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ToolItem) Reset()         { *m = ToolItem{} }
func (m *ToolItem) String() string { return proto.CompactTextString(m) }
func (*ToolItem) ProtoMessage()    {}
func (*ToolItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{70}
}
func (m *ToolItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ToolItem.Unmarshal(m, b)
}
func (m *ToolItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ToolItem.Marshal(b, m, deterministic)
}
func (dst *ToolItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ToolItem.Merge(dst, src)
}
func (m *ToolItem) XXX_Size() int {
	return xxx_messageInfo_ToolItem.Size(m)
}
func (m *ToolItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ToolItem.DiscardUnknown(m)
}

var xxx_messageInfo_ToolItem proto.InternalMessageInfo

func (m *ToolItem) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *ToolItem) GetItemNs() int64 {
	if m != nil {
		return m.ItemNs
	}
	return 0
}

func (m *ToolItem) GetBeUsed() bool {
	if m != nil {
		return m.BeUsed
	}
	return false
}

type GetAnchorPkStatusReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAnchorPkStatusReq) Reset()         { *m = GetAnchorPkStatusReq{} }
func (m *GetAnchorPkStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetAnchorPkStatusReq) ProtoMessage()    {}
func (*GetAnchorPkStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{71}
}
func (m *GetAnchorPkStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorPkStatusReq.Unmarshal(m, b)
}
func (m *GetAnchorPkStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorPkStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetAnchorPkStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorPkStatusReq.Merge(dst, src)
}
func (m *GetAnchorPkStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetAnchorPkStatusReq.Size(m)
}
func (m *GetAnchorPkStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorPkStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorPkStatusReq proto.InternalMessageInfo

func (m *GetAnchorPkStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetAnchorPkStatusResp struct {
	ChannelLivePkStatus  uint32   `protobuf:"varint,1,opt,name=channel_live_pk_status,json=channelLivePkStatus,proto3" json:"channel_live_pk_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAnchorPkStatusResp) Reset()         { *m = GetAnchorPkStatusResp{} }
func (m *GetAnchorPkStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorPkStatusResp) ProtoMessage()    {}
func (*GetAnchorPkStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{72}
}
func (m *GetAnchorPkStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorPkStatusResp.Unmarshal(m, b)
}
func (m *GetAnchorPkStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorPkStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorPkStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorPkStatusResp.Merge(dst, src)
}
func (m *GetAnchorPkStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorPkStatusResp.Size(m)
}
func (m *GetAnchorPkStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorPkStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorPkStatusResp proto.InternalMessageInfo

func (m *GetAnchorPkStatusResp) GetChannelLivePkStatus() uint32 {
	if m != nil {
		return m.ChannelLivePkStatus
	}
	return 0
}

type AnchorInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OperUser             string   `protobuf:"bytes,3,opt,name=oper_user,json=operUser,proto3" json:"oper_user,omitempty"`
	OperTime             uint32   `protobuf:"varint,4,opt,name=oper_time,json=operTime,proto3" json:"oper_time,omitempty"`
	TagId                uint32   `protobuf:"varint,5,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	Account              string   `protobuf:"bytes,6,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,7,opt,name=nickname,proto3" json:"nickname,omitempty"`
	ChannelLiveStatus    uint32   `protobuf:"varint,8,opt,name=channel_live_status,json=channelLiveStatus,proto3" json:"channel_live_status,omitempty"`
	ChannelLivePkStatus  uint32   `protobuf:"varint,9,opt,name=channel_live_pk_status,json=channelLivePkStatus,proto3" json:"channel_live_pk_status,omitempty"`
	Authority            uint32   `protobuf:"varint,10,opt,name=authority,proto3" json:"authority,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorInfo) Reset()         { *m = AnchorInfo{} }
func (m *AnchorInfo) String() string { return proto.CompactTextString(m) }
func (*AnchorInfo) ProtoMessage()    {}
func (*AnchorInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{73}
}
func (m *AnchorInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorInfo.Unmarshal(m, b)
}
func (m *AnchorInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorInfo.Marshal(b, m, deterministic)
}
func (dst *AnchorInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorInfo.Merge(dst, src)
}
func (m *AnchorInfo) XXX_Size() int {
	return xxx_messageInfo_AnchorInfo.Size(m)
}
func (m *AnchorInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorInfo proto.InternalMessageInfo

func (m *AnchorInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AnchorInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AnchorInfo) GetOperUser() string {
	if m != nil {
		return m.OperUser
	}
	return ""
}

func (m *AnchorInfo) GetOperTime() uint32 {
	if m != nil {
		return m.OperTime
	}
	return 0
}

func (m *AnchorInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *AnchorInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *AnchorInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *AnchorInfo) GetChannelLiveStatus() uint32 {
	if m != nil {
		return m.ChannelLiveStatus
	}
	return 0
}

func (m *AnchorInfo) GetChannelLivePkStatus() uint32 {
	if m != nil {
		return m.ChannelLivePkStatus
	}
	return 0
}

func (m *AnchorInfo) GetAuthority() uint32 {
	if m != nil {
		return m.Authority
	}
	return 0
}

type GetItemConfigReq struct {
	ItemIdList           []string `protobuf:"bytes,1,rep,name=item_id_list,json=itemIdList,proto3" json:"item_id_list,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetItemConfigReq) Reset()         { *m = GetItemConfigReq{} }
func (m *GetItemConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetItemConfigReq) ProtoMessage()    {}
func (*GetItemConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{74}
}
func (m *GetItemConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetItemConfigReq.Unmarshal(m, b)
}
func (m *GetItemConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetItemConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetItemConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetItemConfigReq.Merge(dst, src)
}
func (m *GetItemConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetItemConfigReq.Size(m)
}
func (m *GetItemConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetItemConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetItemConfigReq proto.InternalMessageInfo

func (m *GetItemConfigReq) GetItemIdList() []string {
	if m != nil {
		return m.ItemIdList
	}
	return nil
}

func (m *GetItemConfigReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetItemConfigResp struct {
	ItemConfList         []*ItemConfig `protobuf:"bytes,1,rep,name=item_conf_list,json=itemConfList,proto3" json:"item_conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetItemConfigResp) Reset()         { *m = GetItemConfigResp{} }
func (m *GetItemConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetItemConfigResp) ProtoMessage()    {}
func (*GetItemConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{75}
}
func (m *GetItemConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetItemConfigResp.Unmarshal(m, b)
}
func (m *GetItemConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetItemConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetItemConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetItemConfigResp.Merge(dst, src)
}
func (m *GetItemConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetItemConfigResp.Size(m)
}
func (m *GetItemConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetItemConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetItemConfigResp proto.InternalMessageInfo

func (m *GetItemConfigResp) GetItemConfList() []*ItemConfig {
	if m != nil {
		return m.ItemConfList
	}
	return nil
}

type ItemConfig struct {
	ItemId               string                 `protobuf:"bytes,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Desc                 string                 `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	Icon                 string                 `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	EffectUrl            string                 `protobuf:"bytes,4,opt,name=effect_url,json=effectUrl,proto3" json:"effect_url,omitempty"`
	TargetEffectUrl      string                 `protobuf:"bytes,5,opt,name=target_effect_url,json=targetEffectUrl,proto3" json:"target_effect_url,omitempty"`
	Msg                  string                 `protobuf:"bytes,6,opt,name=msg,proto3" json:"msg,omitempty"`
	TargetMsg            string                 `protobuf:"bytes,7,opt,name=target_msg,json=targetMsg,proto3" json:"target_msg,omitempty"`
	GainMsg              string                 `protobuf:"bytes,8,opt,name=gain_msg,json=gainMsg,proto3" json:"gain_msg,omitempty"`
	Ty                   ItemType               `protobuf:"varint,9,opt,name=ty,proto3,enum=channel_live_pk.ItemType" json:"ty,omitempty"`
	Value                uint32                 `protobuf:"varint,10,opt,name=value,proto3" json:"value,omitempty"`
	Name                 string                 `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`
	Version              uint32                 `protobuf:"varint,12,opt,name=version,proto3" json:"version,omitempty"`
	MilestoneList        []*EffectItemMilestone `protobuf:"bytes,13,rep,name=milestone_list,json=milestoneList,proto3" json:"milestone_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ItemConfig) Reset()         { *m = ItemConfig{} }
func (m *ItemConfig) String() string { return proto.CompactTextString(m) }
func (*ItemConfig) ProtoMessage()    {}
func (*ItemConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{76}
}
func (m *ItemConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ItemConfig.Unmarshal(m, b)
}
func (m *ItemConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ItemConfig.Marshal(b, m, deterministic)
}
func (dst *ItemConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ItemConfig.Merge(dst, src)
}
func (m *ItemConfig) XXX_Size() int {
	return xxx_messageInfo_ItemConfig.Size(m)
}
func (m *ItemConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_ItemConfig.DiscardUnknown(m)
}

var xxx_messageInfo_ItemConfig proto.InternalMessageInfo

func (m *ItemConfig) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *ItemConfig) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ItemConfig) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *ItemConfig) GetEffectUrl() string {
	if m != nil {
		return m.EffectUrl
	}
	return ""
}

func (m *ItemConfig) GetTargetEffectUrl() string {
	if m != nil {
		return m.TargetEffectUrl
	}
	return ""
}

func (m *ItemConfig) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *ItemConfig) GetTargetMsg() string {
	if m != nil {
		return m.TargetMsg
	}
	return ""
}

func (m *ItemConfig) GetGainMsg() string {
	if m != nil {
		return m.GainMsg
	}
	return ""
}

func (m *ItemConfig) GetTy() ItemType {
	if m != nil {
		return m.Ty
	}
	return ItemType_Invalid
}

func (m *ItemConfig) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *ItemConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ItemConfig) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *ItemConfig) GetMilestoneList() []*EffectItemMilestone {
	if m != nil {
		return m.MilestoneList
	}
	return nil
}

type EffectItemMilestone struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Percent              uint32   `protobuf:"varint,2,opt,name=percent,proto3" json:"percent,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EffectItemMilestone) Reset()         { *m = EffectItemMilestone{} }
func (m *EffectItemMilestone) String() string { return proto.CompactTextString(m) }
func (*EffectItemMilestone) ProtoMessage()    {}
func (*EffectItemMilestone) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{77}
}
func (m *EffectItemMilestone) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EffectItemMilestone.Unmarshal(m, b)
}
func (m *EffectItemMilestone) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EffectItemMilestone.Marshal(b, m, deterministic)
}
func (dst *EffectItemMilestone) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EffectItemMilestone.Merge(dst, src)
}
func (m *EffectItemMilestone) XXX_Size() int {
	return xxx_messageInfo_EffectItemMilestone.Size(m)
}
func (m *EffectItemMilestone) XXX_DiscardUnknown() {
	xxx_messageInfo_EffectItemMilestone.DiscardUnknown(m)
}

var xxx_messageInfo_EffectItemMilestone proto.InternalMessageInfo

func (m *EffectItemMilestone) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *EffectItemMilestone) GetPercent() uint32 {
	if m != nil {
		return m.Percent
	}
	return 0
}

// 接受指定pk邀约
type AcceptAppointPkReq struct {
	MyUid                uint32   `protobuf:"varint,1,opt,name=my_uid,json=myUid,proto3" json:"my_uid,omitempty"`
	OtherUid             uint32   `protobuf:"varint,2,opt,name=other_uid,json=otherUid,proto3" json:"other_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AcceptAppointPkReq) Reset()         { *m = AcceptAppointPkReq{} }
func (m *AcceptAppointPkReq) String() string { return proto.CompactTextString(m) }
func (*AcceptAppointPkReq) ProtoMessage()    {}
func (*AcceptAppointPkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{78}
}
func (m *AcceptAppointPkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AcceptAppointPkReq.Unmarshal(m, b)
}
func (m *AcceptAppointPkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AcceptAppointPkReq.Marshal(b, m, deterministic)
}
func (dst *AcceptAppointPkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AcceptAppointPkReq.Merge(dst, src)
}
func (m *AcceptAppointPkReq) XXX_Size() int {
	return xxx_messageInfo_AcceptAppointPkReq.Size(m)
}
func (m *AcceptAppointPkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AcceptAppointPkReq.DiscardUnknown(m)
}

var xxx_messageInfo_AcceptAppointPkReq proto.InternalMessageInfo

func (m *AcceptAppointPkReq) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *AcceptAppointPkReq) GetOtherUid() uint32 {
	if m != nil {
		return m.OtherUid
	}
	return 0
}

func (m *AcceptAppointPkReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type AcceptAppointPkResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AcceptAppointPkResp) Reset()         { *m = AcceptAppointPkResp{} }
func (m *AcceptAppointPkResp) String() string { return proto.CompactTextString(m) }
func (*AcceptAppointPkResp) ProtoMessage()    {}
func (*AcceptAppointPkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{79}
}
func (m *AcceptAppointPkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AcceptAppointPkResp.Unmarshal(m, b)
}
func (m *AcceptAppointPkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AcceptAppointPkResp.Marshal(b, m, deterministic)
}
func (dst *AcceptAppointPkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AcceptAppointPkResp.Merge(dst, src)
}
func (m *AcceptAppointPkResp) XXX_Size() int {
	return xxx_messageInfo_AcceptAppointPkResp.Size(m)
}
func (m *AcceptAppointPkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AcceptAppointPkResp.DiscardUnknown(m)
}

var xxx_messageInfo_AcceptAppointPkResp proto.InternalMessageInfo

// 主播进房获取指定pk相关信息
type GetAppointPkInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAppointPkInfoReq) Reset()         { *m = GetAppointPkInfoReq{} }
func (m *GetAppointPkInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetAppointPkInfoReq) ProtoMessage()    {}
func (*GetAppointPkInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{80}
}
func (m *GetAppointPkInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAppointPkInfoReq.Unmarshal(m, b)
}
func (m *GetAppointPkInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAppointPkInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetAppointPkInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAppointPkInfoReq.Merge(dst, src)
}
func (m *GetAppointPkInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetAppointPkInfoReq.Size(m)
}
func (m *GetAppointPkInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAppointPkInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAppointPkInfoReq proto.InternalMessageInfo

func (m *GetAppointPkInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAppointPkInfoResp struct {
	PushEventInfo        *AppointPkEvent `protobuf:"bytes,1,opt,name=push_event_info,json=pushEventInfo,proto3" json:"push_event_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAppointPkInfoResp) Reset()         { *m = GetAppointPkInfoResp{} }
func (m *GetAppointPkInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetAppointPkInfoResp) ProtoMessage()    {}
func (*GetAppointPkInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{81}
}
func (m *GetAppointPkInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAppointPkInfoResp.Unmarshal(m, b)
}
func (m *GetAppointPkInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAppointPkInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetAppointPkInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAppointPkInfoResp.Merge(dst, src)
}
func (m *GetAppointPkInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetAppointPkInfoResp.Size(m)
}
func (m *GetAppointPkInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAppointPkInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAppointPkInfoResp proto.InternalMessageInfo

func (m *GetAppointPkInfoResp) GetPushEventInfo() *AppointPkEvent {
	if m != nil {
		return m.PushEventInfo
	}
	return nil
}

type AppointPkEvent struct {
	EventType            uint32   `protobuf:"varint,1,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`
	MyUid                uint32   `protobuf:"varint,2,opt,name=my_uid,json=myUid,proto3" json:"my_uid,omitempty"`
	OtherUid             uint32   `protobuf:"varint,3,opt,name=other_uid,json=otherUid,proto3" json:"other_uid,omitempty"`
	OtherAccount         string   `protobuf:"bytes,4,opt,name=other_account,json=otherAccount,proto3" json:"other_account,omitempty"`
	OtherNickname        string   `protobuf:"bytes,5,opt,name=other_nickname,json=otherNickname,proto3" json:"other_nickname,omitempty"`
	CountDownEndTime     uint32   `protobuf:"varint,6,opt,name=count_down_end_time,json=countDownEndTime,proto3" json:"count_down_end_time,omitempty"`
	OtherSex             int32    `protobuf:"varint,7,opt,name=other_sex,json=otherSex,proto3" json:"other_sex,omitempty"`
	CountDownTime        uint32   `protobuf:"varint,8,opt,name=count_down_time,json=countDownTime,proto3" json:"count_down_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AppointPkEvent) Reset()         { *m = AppointPkEvent{} }
func (m *AppointPkEvent) String() string { return proto.CompactTextString(m) }
func (*AppointPkEvent) ProtoMessage()    {}
func (*AppointPkEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{82}
}
func (m *AppointPkEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AppointPkEvent.Unmarshal(m, b)
}
func (m *AppointPkEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AppointPkEvent.Marshal(b, m, deterministic)
}
func (dst *AppointPkEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppointPkEvent.Merge(dst, src)
}
func (m *AppointPkEvent) XXX_Size() int {
	return xxx_messageInfo_AppointPkEvent.Size(m)
}
func (m *AppointPkEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_AppointPkEvent.DiscardUnknown(m)
}

var xxx_messageInfo_AppointPkEvent proto.InternalMessageInfo

func (m *AppointPkEvent) GetEventType() uint32 {
	if m != nil {
		return m.EventType
	}
	return 0
}

func (m *AppointPkEvent) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *AppointPkEvent) GetOtherUid() uint32 {
	if m != nil {
		return m.OtherUid
	}
	return 0
}

func (m *AppointPkEvent) GetOtherAccount() string {
	if m != nil {
		return m.OtherAccount
	}
	return ""
}

func (m *AppointPkEvent) GetOtherNickname() string {
	if m != nil {
		return m.OtherNickname
	}
	return ""
}

func (m *AppointPkEvent) GetCountDownEndTime() uint32 {
	if m != nil {
		return m.CountDownEndTime
	}
	return 0
}

func (m *AppointPkEvent) GetOtherSex() int32 {
	if m != nil {
		return m.OtherSex
	}
	return 0
}

func (m *AppointPkEvent) GetCountDownTime() uint32 {
	if m != nil {
		return m.CountDownTime
	}
	return 0
}

// 确定收到指定pk推送
type ConfirmAppointPkPushReq struct {
	MyUid                uint32   `protobuf:"varint,1,opt,name=my_uid,json=myUid,proto3" json:"my_uid,omitempty"`
	OtherUid             uint32   `protobuf:"varint,2,opt,name=other_uid,json=otherUid,proto3" json:"other_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmAppointPkPushReq) Reset()         { *m = ConfirmAppointPkPushReq{} }
func (m *ConfirmAppointPkPushReq) String() string { return proto.CompactTextString(m) }
func (*ConfirmAppointPkPushReq) ProtoMessage()    {}
func (*ConfirmAppointPkPushReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{83}
}
func (m *ConfirmAppointPkPushReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmAppointPkPushReq.Unmarshal(m, b)
}
func (m *ConfirmAppointPkPushReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmAppointPkPushReq.Marshal(b, m, deterministic)
}
func (dst *ConfirmAppointPkPushReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmAppointPkPushReq.Merge(dst, src)
}
func (m *ConfirmAppointPkPushReq) XXX_Size() int {
	return xxx_messageInfo_ConfirmAppointPkPushReq.Size(m)
}
func (m *ConfirmAppointPkPushReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmAppointPkPushReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmAppointPkPushReq proto.InternalMessageInfo

func (m *ConfirmAppointPkPushReq) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *ConfirmAppointPkPushReq) GetOtherUid() uint32 {
	if m != nil {
		return m.OtherUid
	}
	return 0
}

type ConfirmAppointPkPushResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmAppointPkPushResp) Reset()         { *m = ConfirmAppointPkPushResp{} }
func (m *ConfirmAppointPkPushResp) String() string { return proto.CompactTextString(m) }
func (*ConfirmAppointPkPushResp) ProtoMessage()    {}
func (*ConfirmAppointPkPushResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{84}
}
func (m *ConfirmAppointPkPushResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmAppointPkPushResp.Unmarshal(m, b)
}
func (m *ConfirmAppointPkPushResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmAppointPkPushResp.Marshal(b, m, deterministic)
}
func (dst *ConfirmAppointPkPushResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmAppointPkPushResp.Merge(dst, src)
}
func (m *ConfirmAppointPkPushResp) XXX_Size() int {
	return xxx_messageInfo_ConfirmAppointPkPushResp.Size(m)
}
func (m *ConfirmAppointPkPushResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmAppointPkPushResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmAppointPkPushResp proto.InternalMessageInfo

// 增加指定pk信息
type AddAppointPkInfoReq struct {
	Info                 *AppointPkInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *AddAppointPkInfoReq) Reset()         { *m = AddAppointPkInfoReq{} }
func (m *AddAppointPkInfoReq) String() string { return proto.CompactTextString(m) }
func (*AddAppointPkInfoReq) ProtoMessage()    {}
func (*AddAppointPkInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{85}
}
func (m *AddAppointPkInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAppointPkInfoReq.Unmarshal(m, b)
}
func (m *AddAppointPkInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAppointPkInfoReq.Marshal(b, m, deterministic)
}
func (dst *AddAppointPkInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAppointPkInfoReq.Merge(dst, src)
}
func (m *AddAppointPkInfoReq) XXX_Size() int {
	return xxx_messageInfo_AddAppointPkInfoReq.Size(m)
}
func (m *AddAppointPkInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAppointPkInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddAppointPkInfoReq proto.InternalMessageInfo

func (m *AddAppointPkInfoReq) GetInfo() *AppointPkInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type AddAppointPkInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAppointPkInfoResp) Reset()         { *m = AddAppointPkInfoResp{} }
func (m *AddAppointPkInfoResp) String() string { return proto.CompactTextString(m) }
func (*AddAppointPkInfoResp) ProtoMessage()    {}
func (*AddAppointPkInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{86}
}
func (m *AddAppointPkInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAppointPkInfoResp.Unmarshal(m, b)
}
func (m *AddAppointPkInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAppointPkInfoResp.Marshal(b, m, deterministic)
}
func (dst *AddAppointPkInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAppointPkInfoResp.Merge(dst, src)
}
func (m *AddAppointPkInfoResp) XXX_Size() int {
	return xxx_messageInfo_AddAppointPkInfoResp.Size(m)
}
func (m *AddAppointPkInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAppointPkInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddAppointPkInfoResp proto.InternalMessageInfo

// 指定pk信息
type AppointPkInfo struct {
	AppointId            uint32         `protobuf:"varint,1,opt,name=appoint_id,json=appointId,proto3" json:"appoint_id,omitempty"`
	Uid                  uint32         `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTs              uint32         `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32         `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	RivalList            []*PkRivalInfo `protobuf:"bytes,5,rep,name=rival_list,json=rivalList,proto3" json:"rival_list,omitempty"`
	UpdateTs             uint32         `protobuf:"varint,6,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	Operator             string         `protobuf:"bytes,7,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *AppointPkInfo) Reset()         { *m = AppointPkInfo{} }
func (m *AppointPkInfo) String() string { return proto.CompactTextString(m) }
func (*AppointPkInfo) ProtoMessage()    {}
func (*AppointPkInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{87}
}
func (m *AppointPkInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AppointPkInfo.Unmarshal(m, b)
}
func (m *AppointPkInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AppointPkInfo.Marshal(b, m, deterministic)
}
func (dst *AppointPkInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppointPkInfo.Merge(dst, src)
}
func (m *AppointPkInfo) XXX_Size() int {
	return xxx_messageInfo_AppointPkInfo.Size(m)
}
func (m *AppointPkInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AppointPkInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AppointPkInfo proto.InternalMessageInfo

func (m *AppointPkInfo) GetAppointId() uint32 {
	if m != nil {
		return m.AppointId
	}
	return 0
}

func (m *AppointPkInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AppointPkInfo) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *AppointPkInfo) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *AppointPkInfo) GetRivalList() []*PkRivalInfo {
	if m != nil {
		return m.RivalList
	}
	return nil
}

func (m *AppointPkInfo) GetUpdateTs() uint32 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *AppointPkInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

// pk对手信息
type PkRivalInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PkBeginTs            uint32   `protobuf:"varint,2,opt,name=pk_begin_ts,json=pkBeginTs,proto3" json:"pk_begin_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PkRivalInfo) Reset()         { *m = PkRivalInfo{} }
func (m *PkRivalInfo) String() string { return proto.CompactTextString(m) }
func (*PkRivalInfo) ProtoMessage()    {}
func (*PkRivalInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{88}
}
func (m *PkRivalInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PkRivalInfo.Unmarshal(m, b)
}
func (m *PkRivalInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PkRivalInfo.Marshal(b, m, deterministic)
}
func (dst *PkRivalInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PkRivalInfo.Merge(dst, src)
}
func (m *PkRivalInfo) XXX_Size() int {
	return xxx_messageInfo_PkRivalInfo.Size(m)
}
func (m *PkRivalInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PkRivalInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PkRivalInfo proto.InternalMessageInfo

func (m *PkRivalInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PkRivalInfo) GetPkBeginTs() uint32 {
	if m != nil {
		return m.PkBeginTs
	}
	return 0
}

// 更新指定pk信息
type UpdateAppointPkInfoReq struct {
	Info                 *AppointPkInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UpdateAppointPkInfoReq) Reset()         { *m = UpdateAppointPkInfoReq{} }
func (m *UpdateAppointPkInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateAppointPkInfoReq) ProtoMessage()    {}
func (*UpdateAppointPkInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{89}
}
func (m *UpdateAppointPkInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAppointPkInfoReq.Unmarshal(m, b)
}
func (m *UpdateAppointPkInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAppointPkInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpdateAppointPkInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAppointPkInfoReq.Merge(dst, src)
}
func (m *UpdateAppointPkInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpdateAppointPkInfoReq.Size(m)
}
func (m *UpdateAppointPkInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAppointPkInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAppointPkInfoReq proto.InternalMessageInfo

func (m *UpdateAppointPkInfoReq) GetInfo() *AppointPkInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type UpdateAppointPkInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAppointPkInfoResp) Reset()         { *m = UpdateAppointPkInfoResp{} }
func (m *UpdateAppointPkInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdateAppointPkInfoResp) ProtoMessage()    {}
func (*UpdateAppointPkInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{90}
}
func (m *UpdateAppointPkInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAppointPkInfoResp.Unmarshal(m, b)
}
func (m *UpdateAppointPkInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAppointPkInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpdateAppointPkInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAppointPkInfoResp.Merge(dst, src)
}
func (m *UpdateAppointPkInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpdateAppointPkInfoResp.Size(m)
}
func (m *UpdateAppointPkInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAppointPkInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAppointPkInfoResp proto.InternalMessageInfo

// 删除指定pk信息
type DelAppointPkInfoReq struct {
	AppointId            uint32   `protobuf:"varint,1,opt,name=appoint_id,json=appointId,proto3" json:"appoint_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelAppointPkInfoReq) Reset()         { *m = DelAppointPkInfoReq{} }
func (m *DelAppointPkInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelAppointPkInfoReq) ProtoMessage()    {}
func (*DelAppointPkInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{91}
}
func (m *DelAppointPkInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelAppointPkInfoReq.Unmarshal(m, b)
}
func (m *DelAppointPkInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelAppointPkInfoReq.Marshal(b, m, deterministic)
}
func (dst *DelAppointPkInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelAppointPkInfoReq.Merge(dst, src)
}
func (m *DelAppointPkInfoReq) XXX_Size() int {
	return xxx_messageInfo_DelAppointPkInfoReq.Size(m)
}
func (m *DelAppointPkInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelAppointPkInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelAppointPkInfoReq proto.InternalMessageInfo

func (m *DelAppointPkInfoReq) GetAppointId() uint32 {
	if m != nil {
		return m.AppointId
	}
	return 0
}

type DelAppointPkInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelAppointPkInfoResp) Reset()         { *m = DelAppointPkInfoResp{} }
func (m *DelAppointPkInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelAppointPkInfoResp) ProtoMessage()    {}
func (*DelAppointPkInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{92}
}
func (m *DelAppointPkInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelAppointPkInfoResp.Unmarshal(m, b)
}
func (m *DelAppointPkInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelAppointPkInfoResp.Marshal(b, m, deterministic)
}
func (dst *DelAppointPkInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelAppointPkInfoResp.Merge(dst, src)
}
func (m *DelAppointPkInfoResp) XXX_Size() int {
	return xxx_messageInfo_DelAppointPkInfoResp.Size(m)
}
func (m *DelAppointPkInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelAppointPkInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelAppointPkInfoResp proto.InternalMessageInfo

// 获取指定pk信息列表
type GetAppointPkInfoListReq struct {
	QueryType            uint32   `protobuf:"varint,1,opt,name=query_type,json=queryType,proto3" json:"query_type,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTs              uint32   `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	Page                 uint32   `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAppointPkInfoListReq) Reset()         { *m = GetAppointPkInfoListReq{} }
func (m *GetAppointPkInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetAppointPkInfoListReq) ProtoMessage()    {}
func (*GetAppointPkInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{93}
}
func (m *GetAppointPkInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAppointPkInfoListReq.Unmarshal(m, b)
}
func (m *GetAppointPkInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAppointPkInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetAppointPkInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAppointPkInfoListReq.Merge(dst, src)
}
func (m *GetAppointPkInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetAppointPkInfoListReq.Size(m)
}
func (m *GetAppointPkInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAppointPkInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAppointPkInfoListReq proto.InternalMessageInfo

func (m *GetAppointPkInfoListReq) GetQueryType() uint32 {
	if m != nil {
		return m.QueryType
	}
	return 0
}

func (m *GetAppointPkInfoListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAppointPkInfoListReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetAppointPkInfoListReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *GetAppointPkInfoListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetAppointPkInfoListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetAppointPkInfoListResp struct {
	InfoList             []*AppointPkInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	NextPage             uint32           `protobuf:"varint,2,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	TotalCnt             uint32           `protobuf:"varint,3,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetAppointPkInfoListResp) Reset()         { *m = GetAppointPkInfoListResp{} }
func (m *GetAppointPkInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetAppointPkInfoListResp) ProtoMessage()    {}
func (*GetAppointPkInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{94}
}
func (m *GetAppointPkInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAppointPkInfoListResp.Unmarshal(m, b)
}
func (m *GetAppointPkInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAppointPkInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetAppointPkInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAppointPkInfoListResp.Merge(dst, src)
}
func (m *GetAppointPkInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetAppointPkInfoListResp.Size(m)
}
func (m *GetAppointPkInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAppointPkInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAppointPkInfoListResp proto.InternalMessageInfo

func (m *GetAppointPkInfoListResp) GetInfoList() []*AppointPkInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetAppointPkInfoListResp) GetNextPage() uint32 {
	if m != nil {
		return m.NextPage
	}
	return 0
}

func (m *GetAppointPkInfoListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 客户端语音流变化上报
type ReportClientIDChangeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MicId                uint32   `protobuf:"varint,3,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	ChannelLiveId        uint64   `protobuf:"varint,4,opt,name=channel_live_id,json=channelLiveId,proto3" json:"channel_live_id,omitempty"`
	ClientId             string   `protobuf:"bytes,5,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ChannelVideoClientId string   `protobuf:"bytes,6,opt,name=channel_video_client_id,json=channelVideoClientId,proto3" json:"channel_video_client_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportClientIDChangeReq) Reset()         { *m = ReportClientIDChangeReq{} }
func (m *ReportClientIDChangeReq) String() string { return proto.CompactTextString(m) }
func (*ReportClientIDChangeReq) ProtoMessage()    {}
func (*ReportClientIDChangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{95}
}
func (m *ReportClientIDChangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportClientIDChangeReq.Unmarshal(m, b)
}
func (m *ReportClientIDChangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportClientIDChangeReq.Marshal(b, m, deterministic)
}
func (dst *ReportClientIDChangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportClientIDChangeReq.Merge(dst, src)
}
func (m *ReportClientIDChangeReq) XXX_Size() int {
	return xxx_messageInfo_ReportClientIDChangeReq.Size(m)
}
func (m *ReportClientIDChangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportClientIDChangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportClientIDChangeReq proto.InternalMessageInfo

func (m *ReportClientIDChangeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportClientIDChangeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReportClientIDChangeReq) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *ReportClientIDChangeReq) GetChannelLiveId() uint64 {
	if m != nil {
		return m.ChannelLiveId
	}
	return 0
}

func (m *ReportClientIDChangeReq) GetClientId() string {
	if m != nil {
		return m.ClientId
	}
	return ""
}

func (m *ReportClientIDChangeReq) GetChannelVideoClientId() string {
	if m != nil {
		return m.ChannelVideoClientId
	}
	return ""
}

type ReportClientIDChangeResp struct {
	MicList              []*PkMicSpace `protobuf:"bytes,1,rep,name=mic_list,json=micList,proto3" json:"mic_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReportClientIDChangeResp) Reset()         { *m = ReportClientIDChangeResp{} }
func (m *ReportClientIDChangeResp) String() string { return proto.CompactTextString(m) }
func (*ReportClientIDChangeResp) ProtoMessage()    {}
func (*ReportClientIDChangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{96}
}
func (m *ReportClientIDChangeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportClientIDChangeResp.Unmarshal(m, b)
}
func (m *ReportClientIDChangeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportClientIDChangeResp.Marshal(b, m, deterministic)
}
func (dst *ReportClientIDChangeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportClientIDChangeResp.Merge(dst, src)
}
func (m *ReportClientIDChangeResp) XXX_Size() int {
	return xxx_messageInfo_ReportClientIDChangeResp.Size(m)
}
func (m *ReportClientIDChangeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportClientIDChangeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportClientIDChangeResp proto.InternalMessageInfo

func (m *ReportClientIDChangeResp) GetMicList() []*PkMicSpace {
	if m != nil {
		return m.MicList
	}
	return nil
}

// 批量添加黑名单
type BatchAddBlacklistReq struct {
	BlackList            []*BatchAddBlacklistReq_BlackItem `protobuf:"bytes,1,rep,name=black_list,json=blackList,proto3" json:"black_list,omitempty"`
	Operator             string                            `protobuf:"bytes,6,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *BatchAddBlacklistReq) Reset()         { *m = BatchAddBlacklistReq{} }
func (m *BatchAddBlacklistReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddBlacklistReq) ProtoMessage()    {}
func (*BatchAddBlacklistReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{97}
}
func (m *BatchAddBlacklistReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddBlacklistReq.Unmarshal(m, b)
}
func (m *BatchAddBlacklistReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddBlacklistReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddBlacklistReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddBlacklistReq.Merge(dst, src)
}
func (m *BatchAddBlacklistReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddBlacklistReq.Size(m)
}
func (m *BatchAddBlacklistReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddBlacklistReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddBlacklistReq proto.InternalMessageInfo

func (m *BatchAddBlacklistReq) GetBlackList() []*BatchAddBlacklistReq_BlackItem {
	if m != nil {
		return m.BlackList
	}
	return nil
}

func (m *BatchAddBlacklistReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type BatchAddBlacklistReq_BlackItem struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTime            uint64   `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint64   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	BlackType            uint32   `protobuf:"varint,4,opt,name=black_type,json=blackType,proto3" json:"black_type,omitempty"`
	Reason               string   `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddBlacklistReq_BlackItem) Reset()         { *m = BatchAddBlacklistReq_BlackItem{} }
func (m *BatchAddBlacklistReq_BlackItem) String() string { return proto.CompactTextString(m) }
func (*BatchAddBlacklistReq_BlackItem) ProtoMessage()    {}
func (*BatchAddBlacklistReq_BlackItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{97, 0}
}
func (m *BatchAddBlacklistReq_BlackItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddBlacklistReq_BlackItem.Unmarshal(m, b)
}
func (m *BatchAddBlacklistReq_BlackItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddBlacklistReq_BlackItem.Marshal(b, m, deterministic)
}
func (dst *BatchAddBlacklistReq_BlackItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddBlacklistReq_BlackItem.Merge(dst, src)
}
func (m *BatchAddBlacklistReq_BlackItem) XXX_Size() int {
	return xxx_messageInfo_BatchAddBlacklistReq_BlackItem.Size(m)
}
func (m *BatchAddBlacklistReq_BlackItem) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddBlacklistReq_BlackItem.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddBlacklistReq_BlackItem proto.InternalMessageInfo

func (m *BatchAddBlacklistReq_BlackItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchAddBlacklistReq_BlackItem) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *BatchAddBlacklistReq_BlackItem) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *BatchAddBlacklistReq_BlackItem) GetBlackType() uint32 {
	if m != nil {
		return m.BlackType
	}
	return 0
}

func (m *BatchAddBlacklistReq_BlackItem) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

// 批量添加黑名单
type BatchAddBlacklistResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddBlacklistResp) Reset()         { *m = BatchAddBlacklistResp{} }
func (m *BatchAddBlacklistResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddBlacklistResp) ProtoMessage()    {}
func (*BatchAddBlacklistResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{98}
}
func (m *BatchAddBlacklistResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddBlacklistResp.Unmarshal(m, b)
}
func (m *BatchAddBlacklistResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddBlacklistResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddBlacklistResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddBlacklistResp.Merge(dst, src)
}
func (m *BatchAddBlacklistResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddBlacklistResp.Size(m)
}
func (m *BatchAddBlacklistResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddBlacklistResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddBlacklistResp proto.InternalMessageInfo

// 通过记录id列表批量删除黑名单
type BatchDelBlacklistReq struct {
	RecordIds            []uint32 `protobuf:"varint,1,rep,packed,name=record_ids,json=recordIds,proto3" json:"record_ids,omitempty"`
	RecallReason         string   `protobuf:"bytes,2,opt,name=recall_reason,json=recallReason,proto3" json:"recall_reason,omitempty"`
	Operator             string   `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelBlacklistReq) Reset()         { *m = BatchDelBlacklistReq{} }
func (m *BatchDelBlacklistReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelBlacklistReq) ProtoMessage()    {}
func (*BatchDelBlacklistReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{99}
}
func (m *BatchDelBlacklistReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelBlacklistReq.Unmarshal(m, b)
}
func (m *BatchDelBlacklistReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelBlacklistReq.Marshal(b, m, deterministic)
}
func (dst *BatchDelBlacklistReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelBlacklistReq.Merge(dst, src)
}
func (m *BatchDelBlacklistReq) XXX_Size() int {
	return xxx_messageInfo_BatchDelBlacklistReq.Size(m)
}
func (m *BatchDelBlacklistReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelBlacklistReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelBlacklistReq proto.InternalMessageInfo

func (m *BatchDelBlacklistReq) GetRecordIds() []uint32 {
	if m != nil {
		return m.RecordIds
	}
	return nil
}

func (m *BatchDelBlacklistReq) GetRecallReason() string {
	if m != nil {
		return m.RecallReason
	}
	return ""
}

func (m *BatchDelBlacklistReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

// 批量删除黑名单
type BatchDelBlacklistResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelBlacklistResp) Reset()         { *m = BatchDelBlacklistResp{} }
func (m *BatchDelBlacklistResp) String() string { return proto.CompactTextString(m) }
func (*BatchDelBlacklistResp) ProtoMessage()    {}
func (*BatchDelBlacklistResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{100}
}
func (m *BatchDelBlacklistResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelBlacklistResp.Unmarshal(m, b)
}
func (m *BatchDelBlacklistResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelBlacklistResp.Marshal(b, m, deterministic)
}
func (dst *BatchDelBlacklistResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelBlacklistResp.Merge(dst, src)
}
func (m *BatchDelBlacklistResp) XXX_Size() int {
	return xxx_messageInfo_BatchDelBlacklistResp.Size(m)
}
func (m *BatchDelBlacklistResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelBlacklistResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelBlacklistResp proto.InternalMessageInfo

// 获取黑名单记录
type GetBlacklistRecordReq struct {
	AnchorUidList        []uint32      `protobuf:"varint,1,rep,packed,name=anchor_uid_list,json=anchorUidList,proto3" json:"anchor_uid_list,omitempty"`
	BlackType            BlacklistType `protobuf:"varint,2,opt,name=black_type,json=blackType,proto3,enum=channel_live_pk.BlacklistType" json:"black_type,omitempty"`
	PageSize             uint32        `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Page                 uint32        `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	AccountList          []string      `protobuf:"bytes,5,rep,name=account_list,json=accountList,proto3" json:"account_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetBlacklistRecordReq) Reset()         { *m = GetBlacklistRecordReq{} }
func (m *GetBlacklistRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetBlacklistRecordReq) ProtoMessage()    {}
func (*GetBlacklistRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{101}
}
func (m *GetBlacklistRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBlacklistRecordReq.Unmarshal(m, b)
}
func (m *GetBlacklistRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBlacklistRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetBlacklistRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBlacklistRecordReq.Merge(dst, src)
}
func (m *GetBlacklistRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetBlacklistRecordReq.Size(m)
}
func (m *GetBlacklistRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBlacklistRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBlacklistRecordReq proto.InternalMessageInfo

func (m *GetBlacklistRecordReq) GetAnchorUidList() []uint32 {
	if m != nil {
		return m.AnchorUidList
	}
	return nil
}

func (m *GetBlacklistRecordReq) GetBlackType() BlacklistType {
	if m != nil {
		return m.BlackType
	}
	return BlacklistType_BLACKLIST_TYPE_UNSPECIFIED
}

func (m *GetBlacklistRecordReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetBlacklistRecordReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetBlacklistRecordReq) GetAccountList() []string {
	if m != nil {
		return m.AccountList
	}
	return nil
}

// 获取黑名单记录
type GetBlacklistRecordResp struct {
	BlackList            []*GetBlacklistRecordResp_BlackRecord `protobuf:"bytes,1,rep,name=black_list,json=blackList,proto3" json:"black_list,omitempty"`
	Total                uint64                                `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *GetBlacklistRecordResp) Reset()         { *m = GetBlacklistRecordResp{} }
func (m *GetBlacklistRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetBlacklistRecordResp) ProtoMessage()    {}
func (*GetBlacklistRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{102}
}
func (m *GetBlacklistRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBlacklistRecordResp.Unmarshal(m, b)
}
func (m *GetBlacklistRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBlacklistRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetBlacklistRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBlacklistRecordResp.Merge(dst, src)
}
func (m *GetBlacklistRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetBlacklistRecordResp.Size(m)
}
func (m *GetBlacklistRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBlacklistRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBlacklistRecordResp proto.InternalMessageInfo

func (m *GetBlacklistRecordResp) GetBlackList() []*GetBlacklistRecordResp_BlackRecord {
	if m != nil {
		return m.BlackList
	}
	return nil
}

func (m *GetBlacklistRecordResp) GetTotal() uint64 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetBlacklistRecordResp_BlackRecord struct {
	RecordId             uint32   `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTime            uint64   `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint64   `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Status               uint32   `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	Reason               string   `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason,omitempty"`
	Account              string   `protobuf:"bytes,7,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,8,opt,name=nickname,proto3" json:"nickname,omitempty"`
	ContractStatus       string   `protobuf:"bytes,9,opt,name=contract_status,json=contractStatus,proto3" json:"contract_status,omitempty"`
	GuildId              string   `protobuf:"bytes,10,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildName            string   `protobuf:"bytes,11,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	Operator             string   `protobuf:"bytes,12,opt,name=operator,proto3" json:"operator,omitempty"`
	OperationTime        uint64   `protobuf:"varint,13,opt,name=operation_time,json=operationTime,proto3" json:"operation_time,omitempty"`
	BlackType            uint32   `protobuf:"varint,14,opt,name=black_type,json=blackType,proto3" json:"black_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBlacklistRecordResp_BlackRecord) Reset()         { *m = GetBlacklistRecordResp_BlackRecord{} }
func (m *GetBlacklistRecordResp_BlackRecord) String() string { return proto.CompactTextString(m) }
func (*GetBlacklistRecordResp_BlackRecord) ProtoMessage()    {}
func (*GetBlacklistRecordResp_BlackRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{102, 0}
}
func (m *GetBlacklistRecordResp_BlackRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBlacklistRecordResp_BlackRecord.Unmarshal(m, b)
}
func (m *GetBlacklistRecordResp_BlackRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBlacklistRecordResp_BlackRecord.Marshal(b, m, deterministic)
}
func (dst *GetBlacklistRecordResp_BlackRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBlacklistRecordResp_BlackRecord.Merge(dst, src)
}
func (m *GetBlacklistRecordResp_BlackRecord) XXX_Size() int {
	return xxx_messageInfo_GetBlacklistRecordResp_BlackRecord.Size(m)
}
func (m *GetBlacklistRecordResp_BlackRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBlacklistRecordResp_BlackRecord.DiscardUnknown(m)
}

var xxx_messageInfo_GetBlacklistRecordResp_BlackRecord proto.InternalMessageInfo

func (m *GetBlacklistRecordResp_BlackRecord) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *GetBlacklistRecordResp_BlackRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetBlacklistRecordResp_BlackRecord) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetBlacklistRecordResp_BlackRecord) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetBlacklistRecordResp_BlackRecord) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetBlacklistRecordResp_BlackRecord) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *GetBlacklistRecordResp_BlackRecord) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GetBlacklistRecordResp_BlackRecord) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GetBlacklistRecordResp_BlackRecord) GetContractStatus() string {
	if m != nil {
		return m.ContractStatus
	}
	return ""
}

func (m *GetBlacklistRecordResp_BlackRecord) GetGuildId() string {
	if m != nil {
		return m.GuildId
	}
	return ""
}

func (m *GetBlacklistRecordResp_BlackRecord) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *GetBlacklistRecordResp_BlackRecord) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *GetBlacklistRecordResp_BlackRecord) GetOperationTime() uint64 {
	if m != nil {
		return m.OperationTime
	}
	return 0
}

func (m *GetBlacklistRecordResp_BlackRecord) GetBlackType() uint32 {
	if m != nil {
		return m.BlackType
	}
	return 0
}

// 批量查询是否在黑名单
type BatchCheckIfInBlacklistReq struct {
	UidList              []uint32      `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	BlackType            BlacklistType `protobuf:"varint,2,opt,name=black_type,json=blackType,proto3,enum=channel_live_pk.BlacklistType" json:"black_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BatchCheckIfInBlacklistReq) Reset()         { *m = BatchCheckIfInBlacklistReq{} }
func (m *BatchCheckIfInBlacklistReq) String() string { return proto.CompactTextString(m) }
func (*BatchCheckIfInBlacklistReq) ProtoMessage()    {}
func (*BatchCheckIfInBlacklistReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{103}
}
func (m *BatchCheckIfInBlacklistReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckIfInBlacklistReq.Unmarshal(m, b)
}
func (m *BatchCheckIfInBlacklistReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckIfInBlacklistReq.Marshal(b, m, deterministic)
}
func (dst *BatchCheckIfInBlacklistReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckIfInBlacklistReq.Merge(dst, src)
}
func (m *BatchCheckIfInBlacklistReq) XXX_Size() int {
	return xxx_messageInfo_BatchCheckIfInBlacklistReq.Size(m)
}
func (m *BatchCheckIfInBlacklistReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckIfInBlacklistReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckIfInBlacklistReq proto.InternalMessageInfo

func (m *BatchCheckIfInBlacklistReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchCheckIfInBlacklistReq) GetBlackType() BlacklistType {
	if m != nil {
		return m.BlackType
	}
	return BlacklistType_BLACKLIST_TYPE_UNSPECIFIED
}

// 批量查询是否在黑名单
type BatchCheckIfInBlacklistResp struct {
	BlackMap             map[uint32]bool `protobuf:"bytes,1,rep,name=black_map,json=blackMap,proto3" json:"black_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchCheckIfInBlacklistResp) Reset()         { *m = BatchCheckIfInBlacklistResp{} }
func (m *BatchCheckIfInBlacklistResp) String() string { return proto.CompactTextString(m) }
func (*BatchCheckIfInBlacklistResp) ProtoMessage()    {}
func (*BatchCheckIfInBlacklistResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{104}
}
func (m *BatchCheckIfInBlacklistResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckIfInBlacklistResp.Unmarshal(m, b)
}
func (m *BatchCheckIfInBlacklistResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckIfInBlacklistResp.Marshal(b, m, deterministic)
}
func (dst *BatchCheckIfInBlacklistResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckIfInBlacklistResp.Merge(dst, src)
}
func (m *BatchCheckIfInBlacklistResp) XXX_Size() int {
	return xxx_messageInfo_BatchCheckIfInBlacklistResp.Size(m)
}
func (m *BatchCheckIfInBlacklistResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckIfInBlacklistResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckIfInBlacklistResp proto.InternalMessageInfo

func (m *BatchCheckIfInBlacklistResp) GetBlackMap() map[uint32]bool {
	if m != nil {
		return m.BlackMap
	}
	return nil
}

// 更新黑名单
type UpdateBlacklistReq struct {
	RecordId             uint32        `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	Uid                  uint32        `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	BlackType            BlacklistType `protobuf:"varint,3,opt,name=black_type,json=blackType,proto3,enum=channel_live_pk.BlacklistType" json:"black_type,omitempty"`
	BeginTime            uint64        `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint64        `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdateBlacklistReq) Reset()         { *m = UpdateBlacklistReq{} }
func (m *UpdateBlacklistReq) String() string { return proto.CompactTextString(m) }
func (*UpdateBlacklistReq) ProtoMessage()    {}
func (*UpdateBlacklistReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{105}
}
func (m *UpdateBlacklistReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBlacklistReq.Unmarshal(m, b)
}
func (m *UpdateBlacklistReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBlacklistReq.Marshal(b, m, deterministic)
}
func (dst *UpdateBlacklistReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBlacklistReq.Merge(dst, src)
}
func (m *UpdateBlacklistReq) XXX_Size() int {
	return xxx_messageInfo_UpdateBlacklistReq.Size(m)
}
func (m *UpdateBlacklistReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBlacklistReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBlacklistReq proto.InternalMessageInfo

func (m *UpdateBlacklistReq) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *UpdateBlacklistReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateBlacklistReq) GetBlackType() BlacklistType {
	if m != nil {
		return m.BlackType
	}
	return BlacklistType_BLACKLIST_TYPE_UNSPECIFIED
}

func (m *UpdateBlacklistReq) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *UpdateBlacklistReq) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

// 更新黑名单
type UpdateBlacklistResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateBlacklistResp) Reset()         { *m = UpdateBlacklistResp{} }
func (m *UpdateBlacklistResp) String() string { return proto.CompactTextString(m) }
func (*UpdateBlacklistResp) ProtoMessage()    {}
func (*UpdateBlacklistResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{106}
}
func (m *UpdateBlacklistResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBlacklistResp.Unmarshal(m, b)
}
func (m *UpdateBlacklistResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBlacklistResp.Marshal(b, m, deterministic)
}
func (dst *UpdateBlacklistResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBlacklistResp.Merge(dst, src)
}
func (m *UpdateBlacklistResp) XXX_Size() int {
	return xxx_messageInfo_UpdateBlacklistResp.Size(m)
}
func (m *UpdateBlacklistResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBlacklistResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBlacklistResp proto.InternalMessageInfo

// 通过uid列表批量删除黑名单
type BatchDelBlacklistByUidReq struct {
	UidList              []uint32      `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	BlackType            BlacklistType `protobuf:"varint,2,opt,name=black_type,json=blackType,proto3,enum=channel_live_pk.BlacklistType" json:"black_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BatchDelBlacklistByUidReq) Reset()         { *m = BatchDelBlacklistByUidReq{} }
func (m *BatchDelBlacklistByUidReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelBlacklistByUidReq) ProtoMessage()    {}
func (*BatchDelBlacklistByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{107}
}
func (m *BatchDelBlacklistByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelBlacklistByUidReq.Unmarshal(m, b)
}
func (m *BatchDelBlacklistByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelBlacklistByUidReq.Marshal(b, m, deterministic)
}
func (dst *BatchDelBlacklistByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelBlacklistByUidReq.Merge(dst, src)
}
func (m *BatchDelBlacklistByUidReq) XXX_Size() int {
	return xxx_messageInfo_BatchDelBlacklistByUidReq.Size(m)
}
func (m *BatchDelBlacklistByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelBlacklistByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelBlacklistByUidReq proto.InternalMessageInfo

func (m *BatchDelBlacklistByUidReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchDelBlacklistByUidReq) GetBlackType() BlacklistType {
	if m != nil {
		return m.BlackType
	}
	return BlacklistType_BLACKLIST_TYPE_UNSPECIFIED
}

// 批量删除黑名单
type BatchDelBlacklistByUidResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelBlacklistByUidResp) Reset()         { *m = BatchDelBlacklistByUidResp{} }
func (m *BatchDelBlacklistByUidResp) String() string { return proto.CompactTextString(m) }
func (*BatchDelBlacklistByUidResp) ProtoMessage()    {}
func (*BatchDelBlacklistByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_pk_57ddfa79d2ab3441, []int{108}
}
func (m *BatchDelBlacklistByUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelBlacklistByUidResp.Unmarshal(m, b)
}
func (m *BatchDelBlacklistByUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelBlacklistByUidResp.Marshal(b, m, deterministic)
}
func (dst *BatchDelBlacklistByUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelBlacklistByUidResp.Merge(dst, src)
}
func (m *BatchDelBlacklistByUidResp) XXX_Size() int {
	return xxx_messageInfo_BatchDelBlacklistByUidResp.Size(m)
}
func (m *BatchDelBlacklistByUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelBlacklistByUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelBlacklistByUidResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*UkwInfo)(nil), "channel_live_pk.UkwInfo")
	proto.RegisterType((*GetLiveMultiPkPerReq)(nil), "channel_live_pk.GetLiveMultiPkPerReq")
	proto.RegisterType((*GetLiveMultiPkPerResp)(nil), "channel_live_pk.GetLiveMultiPkPerResp")
	proto.RegisterMapType((map[uint32]bool)(nil), "channel_live_pk.GetLiveMultiPkPerResp.UidEntryMapEntry")
	proto.RegisterType((*IniLiveMultiPkTeamReq)(nil), "channel_live_pk.IniLiveMultiPkTeamReq")
	proto.RegisterType((*IniLiveMultiPkTeamResp)(nil), "channel_live_pk.IniLiveMultiPkTeamResp")
	proto.RegisterType((*ApplyLiveMultiPkReq)(nil), "channel_live_pk.ApplyLiveMultiPkReq")
	proto.RegisterType((*ApplyLiveMultiPkResp)(nil), "channel_live_pk.ApplyLiveMultiPkResp")
	proto.RegisterType((*DisinviteChannelLiveMultiPkReq)(nil), "channel_live_pk.DisinviteChannelLiveMultiPkReq")
	proto.RegisterType((*DisinviteChannelLiveMultiPkResp)(nil), "channel_live_pk.DisinviteChannelLiveMultiPkResp")
	proto.RegisterType((*GetLiveMultiPkRecordListReq)(nil), "channel_live_pk.GetLiveMultiPkRecordListReq")
	proto.RegisterType((*GetLiveMultiPkRecordListResp)(nil), "channel_live_pk.GetLiveMultiPkRecordListResp")
	proto.RegisterType((*HandleLiveMultiPkApplyReq)(nil), "channel_live_pk.HandleLiveMultiPkApplyReq")
	proto.RegisterType((*HandleLiveMultiPkApplyResp)(nil), "channel_live_pk.HandleLiveMultiPkApplyResp")
	proto.RegisterType((*StartMultiPkMatchReq)(nil), "channel_live_pk.StartMultiPkMatchReq")
	proto.RegisterType((*StartMultiPkMatchResp)(nil), "channel_live_pk.StartMultiPkMatchResp")
	proto.RegisterType((*CancelMultiPkMatchReq)(nil), "channel_live_pk.CancelMultiPkMatchReq")
	proto.RegisterType((*CancelMultiPkMatchResp)(nil), "channel_live_pk.CancelMultiPkMatchResp")
	proto.RegisterType((*CancelLiveMultiPkTeamReq)(nil), "channel_live_pk.CancelLiveMultiPkTeamReq")
	proto.RegisterType((*CancelLiveMultiPkTeamResp)(nil), "channel_live_pk.CancelLiveMultiPkTeamResp")
	proto.RegisterType((*MultiPkTeamSimpleInfo)(nil), "channel_live_pk.MultiPkTeamSimpleInfo")
	proto.RegisterType((*MultiPkAnchorInfo)(nil), "channel_live_pk.MultiPkAnchorInfo")
	proto.RegisterType((*MultiPkUserInfo)(nil), "channel_live_pk.MultiPkUserInfo")
	proto.RegisterType((*MultiPkRoomInfo)(nil), "channel_live_pk.MultiPkRoomInfo")
	proto.RegisterType((*MultiPkFirstKillUser)(nil), "channel_live_pk.MultiPkFirstKillUser")
	proto.RegisterType((*MultiPkInfo)(nil), "channel_live_pk.MultiPkInfo")
	proto.RegisterType((*MultiPkSimpleInfo)(nil), "channel_live_pk.MultiPkSimpleInfo")
	proto.RegisterType((*MultiPkMatchInfo)(nil), "channel_live_pk.MultiPkMatchInfo")
	proto.RegisterType((*GetMultiPkInfoReq)(nil), "channel_live_pk.GetMultiPkInfoReq")
	proto.RegisterType((*GetMultiPkInfoResp)(nil), "channel_live_pk.GetMultiPkInfoResp")
	proto.RegisterType((*MultiPkKnightInfo)(nil), "channel_live_pk.MultiPkKnightInfo")
	proto.RegisterType((*StartLiveMultiPkReq)(nil), "channel_live_pk.StartLiveMultiPkReq")
	proto.RegisterType((*StartLiveMultiPkResp)(nil), "channel_live_pk.StartLiveMultiPkResp")
	proto.RegisterType((*StopLiveMultiPkReq)(nil), "channel_live_pk.StopLiveMultiPkReq")
	proto.RegisterType((*StopLiveMultiPkResp)(nil), "channel_live_pk.StopLiveMultiPkResp")
	proto.RegisterType((*GetOnlineKnightListReq)(nil), "channel_live_pk.GetOnlineKnightListReq")
	proto.RegisterType((*GetOnlineKnightListResp)(nil), "channel_live_pk.GetOnlineKnightListResp")
	proto.RegisterType((*GetLiveMultiPkRankReq)(nil), "channel_live_pk.GetLiveMultiPkRankReq")
	proto.RegisterType((*GetLiveMultiPkRankResp)(nil), "channel_live_pk.GetLiveMultiPkRankResp")
	proto.RegisterType((*GetLiveMultiPkInviteeStatusReq)(nil), "channel_live_pk.GetLiveMultiPkInviteeStatusReq")
	proto.RegisterType((*GetLiveMultiPkInviteeStatusResp)(nil), "channel_live_pk.GetLiveMultiPkInviteeStatusResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "channel_live_pk.GetLiveMultiPkInviteeStatusResp.TargetUid2statusEntry")
	proto.RegisterType((*BatCheckIsInMultiPkReq)(nil), "channel_live_pk.BatCheckIsInMultiPkReq")
	proto.RegisterType((*BatCheckIsInMultiPkResp)(nil), "channel_live_pk.BatCheckIsInMultiPkResp")
	proto.RegisterMapType((map[uint32]*MultiPkSimpleInfo)(nil), "channel_live_pk.BatCheckIsInMultiPkResp.MapCidInfoEntry")
	proto.RegisterMapType((map[uint32]bool)(nil), "channel_live_pk.BatCheckIsInMultiPkResp.MapIdIspkEntry")
	proto.RegisterType((*BatCheckIsInMultiMatchReq)(nil), "channel_live_pk.BatCheckIsInMultiMatchReq")
	proto.RegisterType((*BatCheckIsInMultiMatchResp)(nil), "channel_live_pk.BatCheckIsInMultiMatchResp")
	proto.RegisterMapType((map[uint32]bool)(nil), "channel_live_pk.BatCheckIsInMultiMatchResp.MapIdIsMatchEntry")
	proto.RegisterType((*HandlerApplyReq)(nil), "channel_live_pk.HandlerApplyReq")
	proto.RegisterType((*HandlerApplyResp)(nil), "channel_live_pk.HandlerApplyResp")
	proto.RegisterType((*CancelPKApplyReq)(nil), "channel_live_pk.CancelPKApplyReq")
	proto.RegisterType((*CancelPKApplyResp)(nil), "channel_live_pk.CancelPKApplyResp")
	proto.RegisterType((*SetPkStatusReq)(nil), "channel_live_pk.SetPkStatusReq")
	proto.RegisterType((*SetPkStatusResp)(nil), "channel_live_pk.SetPkStatusResp")
	proto.RegisterType((*ApplyPkReq)(nil), "channel_live_pk.ApplyPkReq")
	proto.RegisterType((*ApplyPkResp)(nil), "channel_live_pk.ApplyPkResp")
	proto.RegisterType((*GetChanneLivePkRankUserReq)(nil), "channel_live_pk.GetChanneLivePkRankUserReq")
	proto.RegisterType((*GetChanneLivePkRankUserResp)(nil), "channel_live_pk.GetChanneLivePkRankUserResp")
	proto.RegisterType((*SendGiftUserInfo)(nil), "channel_live_pk.SendGiftUserInfo")
	proto.RegisterType((*GetPkInfoReq)(nil), "channel_live_pk.GetPkInfoReq")
	proto.RegisterType((*GetPkInfoResp)(nil), "channel_live_pk.GetPkInfoResp")
	proto.RegisterType((*PkSingleInfo)(nil), "channel_live_pk.PkSingleInfo")
	proto.RegisterType((*PkCommonInfo)(nil), "channel_live_pk.PkCommonInfo")
	proto.RegisterMapType((map[uint32]*PkMicSpace)(nil), "channel_live_pk.PkCommonInfo.MicListEntry")
	proto.RegisterType((*PkMicSpace)(nil), "channel_live_pk.PkMicSpace")
	proto.RegisterType((*StartPkMatchReq)(nil), "channel_live_pk.StartPkMatchReq")
	proto.RegisterType((*StartPkMatchResp)(nil), "channel_live_pk.StartPkMatchResp")
	proto.RegisterType((*CancelPkMatchReq)(nil), "channel_live_pk.CancelPkMatchReq")
	proto.RegisterType((*CancelPkMatchResp)(nil), "channel_live_pk.CancelPkMatchResp")
	proto.RegisterType((*GetPKMatchInfoReq)(nil), "channel_live_pk.GetPKMatchInfoReq")
	proto.RegisterType((*GetPKMatchInfoResp)(nil), "channel_live_pk.GetPKMatchInfoResp")
	proto.RegisterType((*PkCompetitionInfo)(nil), "channel_live_pk.PkCompetitionInfo")
	proto.RegisterType((*PkLimitInfo)(nil), "channel_live_pk.PkLimitInfo")
	proto.RegisterType((*GetMyToolListReq)(nil), "channel_live_pk.GetMyToolListReq")
	proto.RegisterType((*GetMyToolListResp)(nil), "channel_live_pk.GetMyToolListResp")
	proto.RegisterType((*ToolItem)(nil), "channel_live_pk.ToolItem")
	proto.RegisterType((*GetAnchorPkStatusReq)(nil), "channel_live_pk.GetAnchorPkStatusReq")
	proto.RegisterType((*GetAnchorPkStatusResp)(nil), "channel_live_pk.GetAnchorPkStatusResp")
	proto.RegisterType((*AnchorInfo)(nil), "channel_live_pk.AnchorInfo")
	proto.RegisterType((*GetItemConfigReq)(nil), "channel_live_pk.GetItemConfigReq")
	proto.RegisterType((*GetItemConfigResp)(nil), "channel_live_pk.GetItemConfigResp")
	proto.RegisterType((*ItemConfig)(nil), "channel_live_pk.ItemConfig")
	proto.RegisterType((*EffectItemMilestone)(nil), "channel_live_pk.EffectItemMilestone")
	proto.RegisterType((*AcceptAppointPkReq)(nil), "channel_live_pk.AcceptAppointPkReq")
	proto.RegisterType((*AcceptAppointPkResp)(nil), "channel_live_pk.AcceptAppointPkResp")
	proto.RegisterType((*GetAppointPkInfoReq)(nil), "channel_live_pk.GetAppointPkInfoReq")
	proto.RegisterType((*GetAppointPkInfoResp)(nil), "channel_live_pk.GetAppointPkInfoResp")
	proto.RegisterType((*AppointPkEvent)(nil), "channel_live_pk.AppointPkEvent")
	proto.RegisterType((*ConfirmAppointPkPushReq)(nil), "channel_live_pk.ConfirmAppointPkPushReq")
	proto.RegisterType((*ConfirmAppointPkPushResp)(nil), "channel_live_pk.ConfirmAppointPkPushResp")
	proto.RegisterType((*AddAppointPkInfoReq)(nil), "channel_live_pk.AddAppointPkInfoReq")
	proto.RegisterType((*AddAppointPkInfoResp)(nil), "channel_live_pk.AddAppointPkInfoResp")
	proto.RegisterType((*AppointPkInfo)(nil), "channel_live_pk.AppointPkInfo")
	proto.RegisterType((*PkRivalInfo)(nil), "channel_live_pk.PkRivalInfo")
	proto.RegisterType((*UpdateAppointPkInfoReq)(nil), "channel_live_pk.UpdateAppointPkInfoReq")
	proto.RegisterType((*UpdateAppointPkInfoResp)(nil), "channel_live_pk.UpdateAppointPkInfoResp")
	proto.RegisterType((*DelAppointPkInfoReq)(nil), "channel_live_pk.DelAppointPkInfoReq")
	proto.RegisterType((*DelAppointPkInfoResp)(nil), "channel_live_pk.DelAppointPkInfoResp")
	proto.RegisterType((*GetAppointPkInfoListReq)(nil), "channel_live_pk.GetAppointPkInfoListReq")
	proto.RegisterType((*GetAppointPkInfoListResp)(nil), "channel_live_pk.GetAppointPkInfoListResp")
	proto.RegisterType((*ReportClientIDChangeReq)(nil), "channel_live_pk.ReportClientIDChangeReq")
	proto.RegisterType((*ReportClientIDChangeResp)(nil), "channel_live_pk.ReportClientIDChangeResp")
	proto.RegisterType((*BatchAddBlacklistReq)(nil), "channel_live_pk.BatchAddBlacklistReq")
	proto.RegisterType((*BatchAddBlacklistReq_BlackItem)(nil), "channel_live_pk.BatchAddBlacklistReq.BlackItem")
	proto.RegisterType((*BatchAddBlacklistResp)(nil), "channel_live_pk.BatchAddBlacklistResp")
	proto.RegisterType((*BatchDelBlacklistReq)(nil), "channel_live_pk.BatchDelBlacklistReq")
	proto.RegisterType((*BatchDelBlacklistResp)(nil), "channel_live_pk.BatchDelBlacklistResp")
	proto.RegisterType((*GetBlacklistRecordReq)(nil), "channel_live_pk.GetBlacklistRecordReq")
	proto.RegisterType((*GetBlacklistRecordResp)(nil), "channel_live_pk.GetBlacklistRecordResp")
	proto.RegisterType((*GetBlacklistRecordResp_BlackRecord)(nil), "channel_live_pk.GetBlacklistRecordResp.BlackRecord")
	proto.RegisterType((*BatchCheckIfInBlacklistReq)(nil), "channel_live_pk.BatchCheckIfInBlacklistReq")
	proto.RegisterType((*BatchCheckIfInBlacklistResp)(nil), "channel_live_pk.BatchCheckIfInBlacklistResp")
	proto.RegisterMapType((map[uint32]bool)(nil), "channel_live_pk.BatchCheckIfInBlacklistResp.BlackMapEntry")
	proto.RegisterType((*UpdateBlacklistReq)(nil), "channel_live_pk.UpdateBlacklistReq")
	proto.RegisterType((*UpdateBlacklistResp)(nil), "channel_live_pk.UpdateBlacklistResp")
	proto.RegisterType((*BatchDelBlacklistByUidReq)(nil), "channel_live_pk.BatchDelBlacklistByUidReq")
	proto.RegisterType((*BatchDelBlacklistByUidResp)(nil), "channel_live_pk.BatchDelBlacklistByUidResp")
	proto.RegisterEnum("channel_live_pk.ApplyMultiPkStatus", ApplyMultiPkStatus_name, ApplyMultiPkStatus_value)
	proto.RegisterEnum("channel_live_pk.EnumMultiPkMatchStatus", EnumMultiPkMatchStatus_name, EnumMultiPkMatchStatus_value)
	proto.RegisterEnum("channel_live_pk.HandleApplyMultiPkType", HandleApplyMultiPkType_name, HandleApplyMultiPkType_value)
	proto.RegisterEnum("channel_live_pk.MultiPkType", MultiPkType_name, MultiPkType_value)
	proto.RegisterEnum("channel_live_pk.EnumMultiPkStatus", EnumMultiPkStatus_name, EnumMultiPkStatus_value)
	proto.RegisterEnum("channel_live_pk.MultiPkResult", MultiPkResult_name, MultiPkResult_value)
	proto.RegisterEnum("channel_live_pk.MultiPkTeamType", MultiPkTeamType_name, MultiPkTeamType_value)
	proto.RegisterEnum("channel_live_pk.EnumApply", EnumApply_name, EnumApply_value)
	proto.RegisterEnum("channel_live_pk.EnumChannelLivePKStatus", EnumChannelLivePKStatus_name, EnumChannelLivePKStatus_value)
	proto.RegisterEnum("channel_live_pk.ChannelLiveOpponentMicFlag", ChannelLiveOpponentMicFlag_name, ChannelLiveOpponentMicFlag_value)
	proto.RegisterEnum("channel_live_pk.ChannelLivePKMatchType", ChannelLivePKMatchType_name, ChannelLivePKMatchType_value)
	proto.RegisterEnum("channel_live_pk.ItemType", ItemType_name, ItemType_value)
	proto.RegisterEnum("channel_live_pk.EnumPkMatch", EnumPkMatch_name, EnumPkMatch_value)
	proto.RegisterEnum("channel_live_pk.BlacklistType", BlacklistType_name, BlacklistType_value)
	proto.RegisterEnum("channel_live_pk.BlacklistStatus", BlacklistStatus_name, BlacklistStatus_value)
	proto.RegisterEnum("channel_live_pk.GetAppointPkInfoListReq_QueryType", GetAppointPkInfoListReq_QueryType_name, GetAppointPkInfoListReq_QueryType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelLivePkClient is the client API for ChannelLivePk service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelLivePkClient interface {
	// 获取直播多人PK权限
	GetLiveMultiPkPer(ctx context.Context, in *GetLiveMultiPkPerReq, opts ...grpc.CallOption) (*GetLiveMultiPkPerResp, error)
	// 初始化组队信息
	IniLiveMultiPkTeam(ctx context.Context, in *IniLiveMultiPkTeamReq, opts ...grpc.CallOption) (*IniLiveMultiPkTeamResp, error)
	// 多人pk邀请
	ApplyLiveMultiPk(ctx context.Context, in *ApplyLiveMultiPkReq, opts ...grpc.CallOption) (*ApplyLiveMultiPkResp, error)
	// 取消多人pk邀请
	DisinviteChannelLiveMultiPk(ctx context.Context, in *DisinviteChannelLiveMultiPkReq, opts ...grpc.CallOption) (*DisinviteChannelLiveMultiPkResp, error)
	// 获取最近多人PK的主播列表
	GetLiveMultiPkRecordList(ctx context.Context, in *GetLiveMultiPkRecordListReq, opts ...grpc.CallOption) (*GetLiveMultiPkRecordListResp, error)
	// 处理收到PK邀请
	HandleLiveMultiPkApply(ctx context.Context, in *HandleLiveMultiPkApplyReq, opts ...grpc.CallOption) (*HandleLiveMultiPkApplyResp, error)
	// 开始随机匹配
	StartMultiPkMatch(ctx context.Context, in *StartMultiPkMatchReq, opts ...grpc.CallOption) (*StartMultiPkMatchResp, error)
	// 取消PK匹配
	CancelMultiPkMatch(ctx context.Context, in *CancelMultiPkMatchReq, opts ...grpc.CallOption) (*CancelMultiPkMatchResp, error)
	// 取消多人PK组队
	CancelLiveMultiPkTeam(ctx context.Context, in *CancelLiveMultiPkTeamReq, opts ...grpc.CallOption) (*CancelLiveMultiPkTeamResp, error)
	// 开始PK
	StartLiveMultiPk(ctx context.Context, in *StartLiveMultiPkReq, opts ...grpc.CallOption) (*StartLiveMultiPkResp, error)
	// 结束多人PK
	StopLiveMultiPk(ctx context.Context, in *StopLiveMultiPkReq, opts ...grpc.CallOption) (*StopLiveMultiPkResp, error)
	// 获取被邀请人的状态
	GetLiveMultiPkInviteeStatus(ctx context.Context, in *GetLiveMultiPkInviteeStatusReq, opts ...grpc.CallOption) (*GetLiveMultiPkInviteeStatusResp, error)
	// 获取在房骑士列表
	GetOnlineKnightList(ctx context.Context, in *GetOnlineKnightListReq, opts ...grpc.CallOption) (*GetOnlineKnightListResp, error)
	// 获取PK火力榜
	GetLiveMultiPkRank(ctx context.Context, in *GetLiveMultiPkRankReq, opts ...grpc.CallOption) (*GetLiveMultiPkRankResp, error)
	// 获取多人pk信息
	GetMultiPkInfo(ctx context.Context, in *GetMultiPkInfoReq, opts ...grpc.CallOption) (*GetMultiPkInfoResp, error)
	// 批量查询是否在多人pk
	BatCheckIsInMultiPk(ctx context.Context, in *BatCheckIsInMultiPkReq, opts ...grpc.CallOption) (*BatCheckIsInMultiPkResp, error)
	// 批量查询是否在多人pk匹配
	BatCheckIsInMultiMatch(ctx context.Context, in *BatCheckIsInMultiMatchReq, opts ...grpc.CallOption) (*BatCheckIsInMultiMatchResp, error)
	// ==========================直播PK和预约PK=============================
	HandlerApply(ctx context.Context, in *HandlerApplyReq, opts ...grpc.CallOption) (*HandlerApplyResp, error)
	CancelPKApply(ctx context.Context, in *CancelPKApplyReq, opts ...grpc.CallOption) (*CancelPKApplyResp, error)
	SetPkStatus(ctx context.Context, in *SetPkStatusReq, opts ...grpc.CallOption) (*SetPkStatusResp, error)
	ApplyPk(ctx context.Context, in *ApplyPkReq, opts ...grpc.CallOption) (*ApplyPkResp, error)
	GetChanneLivePkRankUser(ctx context.Context, in *GetChanneLivePkRankUserReq, opts ...grpc.CallOption) (*GetChanneLivePkRankUserResp, error)
	GetPkInfo(ctx context.Context, in *GetPkInfoReq, opts ...grpc.CallOption) (*GetPkInfoResp, error)
	StartPkMatch(ctx context.Context, in *StartPkMatchReq, opts ...grpc.CallOption) (*StartPkMatchResp, error)
	CancelPkMatch(ctx context.Context, in *CancelPkMatchReq, opts ...grpc.CallOption) (*CancelPkMatchResp, error)
	GetPKMatchInfo(ctx context.Context, in *GetPKMatchInfoReq, opts ...grpc.CallOption) (*GetPKMatchInfoResp, error)
	GetMyToolList(ctx context.Context, in *GetMyToolListReq, opts ...grpc.CallOption) (*GetMyToolListResp, error)
	// 获取主播pk状态
	GetAnchorPkStatus(ctx context.Context, in *GetAnchorPkStatusReq, opts ...grpc.CallOption) (*GetAnchorPkStatusResp, error)
	GetItemConfig(ctx context.Context, in *GetItemConfigReq, opts ...grpc.CallOption) (*GetItemConfigResp, error)
	AcceptAppointPk(ctx context.Context, in *AcceptAppointPkReq, opts ...grpc.CallOption) (*AcceptAppointPkResp, error)
	GetAppointPkInfo(ctx context.Context, in *GetAppointPkInfoReq, opts ...grpc.CallOption) (*GetAppointPkInfoResp, error)
	ConfirmAppointPkPush(ctx context.Context, in *ConfirmAppointPkPushReq, opts ...grpc.CallOption) (*ConfirmAppointPkPushResp, error)
	AddAppointPkInfo(ctx context.Context, in *AddAppointPkInfoReq, opts ...grpc.CallOption) (*AddAppointPkInfoResp, error)
	UpdateAppointPkInfo(ctx context.Context, in *UpdateAppointPkInfoReq, opts ...grpc.CallOption) (*UpdateAppointPkInfoResp, error)
	DelAppointPkInfo(ctx context.Context, in *DelAppointPkInfoReq, opts ...grpc.CallOption) (*DelAppointPkInfoResp, error)
	GetAppointPkInfoList(ctx context.Context, in *GetAppointPkInfoListReq, opts ...grpc.CallOption) (*GetAppointPkInfoListResp, error)
	ReportClientIDChange(ctx context.Context, in *ReportClientIDChangeReq, opts ...grpc.CallOption) (*ReportClientIDChangeResp, error)
	// =========================== 直播pk黑名单管理 ===========================
	BatchAddBlacklist(ctx context.Context, in *BatchAddBlacklistReq, opts ...grpc.CallOption) (*BatchAddBlacklistResp, error)
	BatchDelBlacklist(ctx context.Context, in *BatchDelBlacklistReq, opts ...grpc.CallOption) (*BatchDelBlacklistResp, error)
	GetBlacklistRecord(ctx context.Context, in *GetBlacklistRecordReq, opts ...grpc.CallOption) (*GetBlacklistRecordResp, error)
	BatchCheckIfInBlacklist(ctx context.Context, in *BatchCheckIfInBlacklistReq, opts ...grpc.CallOption) (*BatchCheckIfInBlacklistResp, error)
	UpdateBlacklist(ctx context.Context, in *UpdateBlacklistReq, opts ...grpc.CallOption) (*UpdateBlacklistResp, error)
	BatchDelBlacklistByUid(ctx context.Context, in *BatchDelBlacklistByUidReq, opts ...grpc.CallOption) (*BatchDelBlacklistByUidResp, error)
}

type channelLivePkClient struct {
	cc *grpc.ClientConn
}

func NewChannelLivePkClient(cc *grpc.ClientConn) ChannelLivePkClient {
	return &channelLivePkClient{cc}
}

func (c *channelLivePkClient) GetLiveMultiPkPer(ctx context.Context, in *GetLiveMultiPkPerReq, opts ...grpc.CallOption) (*GetLiveMultiPkPerResp, error) {
	out := new(GetLiveMultiPkPerResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/GetLiveMultiPkPer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) IniLiveMultiPkTeam(ctx context.Context, in *IniLiveMultiPkTeamReq, opts ...grpc.CallOption) (*IniLiveMultiPkTeamResp, error) {
	out := new(IniLiveMultiPkTeamResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/IniLiveMultiPkTeam", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) ApplyLiveMultiPk(ctx context.Context, in *ApplyLiveMultiPkReq, opts ...grpc.CallOption) (*ApplyLiveMultiPkResp, error) {
	out := new(ApplyLiveMultiPkResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/ApplyLiveMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) DisinviteChannelLiveMultiPk(ctx context.Context, in *DisinviteChannelLiveMultiPkReq, opts ...grpc.CallOption) (*DisinviteChannelLiveMultiPkResp, error) {
	out := new(DisinviteChannelLiveMultiPkResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/DisinviteChannelLiveMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) GetLiveMultiPkRecordList(ctx context.Context, in *GetLiveMultiPkRecordListReq, opts ...grpc.CallOption) (*GetLiveMultiPkRecordListResp, error) {
	out := new(GetLiveMultiPkRecordListResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/GetLiveMultiPkRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) HandleLiveMultiPkApply(ctx context.Context, in *HandleLiveMultiPkApplyReq, opts ...grpc.CallOption) (*HandleLiveMultiPkApplyResp, error) {
	out := new(HandleLiveMultiPkApplyResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/HandleLiveMultiPkApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) StartMultiPkMatch(ctx context.Context, in *StartMultiPkMatchReq, opts ...grpc.CallOption) (*StartMultiPkMatchResp, error) {
	out := new(StartMultiPkMatchResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/StartMultiPkMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) CancelMultiPkMatch(ctx context.Context, in *CancelMultiPkMatchReq, opts ...grpc.CallOption) (*CancelMultiPkMatchResp, error) {
	out := new(CancelMultiPkMatchResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/CancelMultiPkMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) CancelLiveMultiPkTeam(ctx context.Context, in *CancelLiveMultiPkTeamReq, opts ...grpc.CallOption) (*CancelLiveMultiPkTeamResp, error) {
	out := new(CancelLiveMultiPkTeamResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/CancelLiveMultiPkTeam", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) StartLiveMultiPk(ctx context.Context, in *StartLiveMultiPkReq, opts ...grpc.CallOption) (*StartLiveMultiPkResp, error) {
	out := new(StartLiveMultiPkResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/StartLiveMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) StopLiveMultiPk(ctx context.Context, in *StopLiveMultiPkReq, opts ...grpc.CallOption) (*StopLiveMultiPkResp, error) {
	out := new(StopLiveMultiPkResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/StopLiveMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) GetLiveMultiPkInviteeStatus(ctx context.Context, in *GetLiveMultiPkInviteeStatusReq, opts ...grpc.CallOption) (*GetLiveMultiPkInviteeStatusResp, error) {
	out := new(GetLiveMultiPkInviteeStatusResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/GetLiveMultiPkInviteeStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) GetOnlineKnightList(ctx context.Context, in *GetOnlineKnightListReq, opts ...grpc.CallOption) (*GetOnlineKnightListResp, error) {
	out := new(GetOnlineKnightListResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/GetOnlineKnightList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) GetLiveMultiPkRank(ctx context.Context, in *GetLiveMultiPkRankReq, opts ...grpc.CallOption) (*GetLiveMultiPkRankResp, error) {
	out := new(GetLiveMultiPkRankResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/GetLiveMultiPkRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) GetMultiPkInfo(ctx context.Context, in *GetMultiPkInfoReq, opts ...grpc.CallOption) (*GetMultiPkInfoResp, error) {
	out := new(GetMultiPkInfoResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/GetMultiPkInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) BatCheckIsInMultiPk(ctx context.Context, in *BatCheckIsInMultiPkReq, opts ...grpc.CallOption) (*BatCheckIsInMultiPkResp, error) {
	out := new(BatCheckIsInMultiPkResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/BatCheckIsInMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) BatCheckIsInMultiMatch(ctx context.Context, in *BatCheckIsInMultiMatchReq, opts ...grpc.CallOption) (*BatCheckIsInMultiMatchResp, error) {
	out := new(BatCheckIsInMultiMatchResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/BatCheckIsInMultiMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) HandlerApply(ctx context.Context, in *HandlerApplyReq, opts ...grpc.CallOption) (*HandlerApplyResp, error) {
	out := new(HandlerApplyResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/HandlerApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) CancelPKApply(ctx context.Context, in *CancelPKApplyReq, opts ...grpc.CallOption) (*CancelPKApplyResp, error) {
	out := new(CancelPKApplyResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/CancelPKApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) SetPkStatus(ctx context.Context, in *SetPkStatusReq, opts ...grpc.CallOption) (*SetPkStatusResp, error) {
	out := new(SetPkStatusResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/SetPkStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) ApplyPk(ctx context.Context, in *ApplyPkReq, opts ...grpc.CallOption) (*ApplyPkResp, error) {
	out := new(ApplyPkResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/ApplyPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) GetChanneLivePkRankUser(ctx context.Context, in *GetChanneLivePkRankUserReq, opts ...grpc.CallOption) (*GetChanneLivePkRankUserResp, error) {
	out := new(GetChanneLivePkRankUserResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/GetChanneLivePkRankUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) GetPkInfo(ctx context.Context, in *GetPkInfoReq, opts ...grpc.CallOption) (*GetPkInfoResp, error) {
	out := new(GetPkInfoResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/GetPkInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) StartPkMatch(ctx context.Context, in *StartPkMatchReq, opts ...grpc.CallOption) (*StartPkMatchResp, error) {
	out := new(StartPkMatchResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/StartPkMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) CancelPkMatch(ctx context.Context, in *CancelPkMatchReq, opts ...grpc.CallOption) (*CancelPkMatchResp, error) {
	out := new(CancelPkMatchResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/CancelPkMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) GetPKMatchInfo(ctx context.Context, in *GetPKMatchInfoReq, opts ...grpc.CallOption) (*GetPKMatchInfoResp, error) {
	out := new(GetPKMatchInfoResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/GetPKMatchInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) GetMyToolList(ctx context.Context, in *GetMyToolListReq, opts ...grpc.CallOption) (*GetMyToolListResp, error) {
	out := new(GetMyToolListResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/GetMyToolList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) GetAnchorPkStatus(ctx context.Context, in *GetAnchorPkStatusReq, opts ...grpc.CallOption) (*GetAnchorPkStatusResp, error) {
	out := new(GetAnchorPkStatusResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/GetAnchorPkStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) GetItemConfig(ctx context.Context, in *GetItemConfigReq, opts ...grpc.CallOption) (*GetItemConfigResp, error) {
	out := new(GetItemConfigResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/GetItemConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) AcceptAppointPk(ctx context.Context, in *AcceptAppointPkReq, opts ...grpc.CallOption) (*AcceptAppointPkResp, error) {
	out := new(AcceptAppointPkResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/AcceptAppointPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) GetAppointPkInfo(ctx context.Context, in *GetAppointPkInfoReq, opts ...grpc.CallOption) (*GetAppointPkInfoResp, error) {
	out := new(GetAppointPkInfoResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/GetAppointPkInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) ConfirmAppointPkPush(ctx context.Context, in *ConfirmAppointPkPushReq, opts ...grpc.CallOption) (*ConfirmAppointPkPushResp, error) {
	out := new(ConfirmAppointPkPushResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/ConfirmAppointPkPush", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) AddAppointPkInfo(ctx context.Context, in *AddAppointPkInfoReq, opts ...grpc.CallOption) (*AddAppointPkInfoResp, error) {
	out := new(AddAppointPkInfoResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/SetAppointPkInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) UpdateAppointPkInfo(ctx context.Context, in *UpdateAppointPkInfoReq, opts ...grpc.CallOption) (*UpdateAppointPkInfoResp, error) {
	out := new(UpdateAppointPkInfoResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/UpdateAppointPkInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) DelAppointPkInfo(ctx context.Context, in *DelAppointPkInfoReq, opts ...grpc.CallOption) (*DelAppointPkInfoResp, error) {
	out := new(DelAppointPkInfoResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/DelAppointPkInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) GetAppointPkInfoList(ctx context.Context, in *GetAppointPkInfoListReq, opts ...grpc.CallOption) (*GetAppointPkInfoListResp, error) {
	out := new(GetAppointPkInfoListResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/GetAppointPkInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) ReportClientIDChange(ctx context.Context, in *ReportClientIDChangeReq, opts ...grpc.CallOption) (*ReportClientIDChangeResp, error) {
	out := new(ReportClientIDChangeResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/ReportClientIDChange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) BatchAddBlacklist(ctx context.Context, in *BatchAddBlacklistReq, opts ...grpc.CallOption) (*BatchAddBlacklistResp, error) {
	out := new(BatchAddBlacklistResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/BatchAddBlacklist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) BatchDelBlacklist(ctx context.Context, in *BatchDelBlacklistReq, opts ...grpc.CallOption) (*BatchDelBlacklistResp, error) {
	out := new(BatchDelBlacklistResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/BatchDelBlacklist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) GetBlacklistRecord(ctx context.Context, in *GetBlacklistRecordReq, opts ...grpc.CallOption) (*GetBlacklistRecordResp, error) {
	out := new(GetBlacklistRecordResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/GetBlacklistRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) BatchCheckIfInBlacklist(ctx context.Context, in *BatchCheckIfInBlacklistReq, opts ...grpc.CallOption) (*BatchCheckIfInBlacklistResp, error) {
	out := new(BatchCheckIfInBlacklistResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/BatchCheckIfInBlacklist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) UpdateBlacklist(ctx context.Context, in *UpdateBlacklistReq, opts ...grpc.CallOption) (*UpdateBlacklistResp, error) {
	out := new(UpdateBlacklistResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/UpdateBlacklist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLivePkClient) BatchDelBlacklistByUid(ctx context.Context, in *BatchDelBlacklistByUidReq, opts ...grpc.CallOption) (*BatchDelBlacklistByUidResp, error) {
	out := new(BatchDelBlacklistByUidResp)
	err := c.cc.Invoke(ctx, "/channel_live_pk.ChannelLivePk/BatchDelBlacklistByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelLivePkServer is the server API for ChannelLivePk service.
type ChannelLivePkServer interface {
	// 获取直播多人PK权限
	GetLiveMultiPkPer(context.Context, *GetLiveMultiPkPerReq) (*GetLiveMultiPkPerResp, error)
	// 初始化组队信息
	IniLiveMultiPkTeam(context.Context, *IniLiveMultiPkTeamReq) (*IniLiveMultiPkTeamResp, error)
	// 多人pk邀请
	ApplyLiveMultiPk(context.Context, *ApplyLiveMultiPkReq) (*ApplyLiveMultiPkResp, error)
	// 取消多人pk邀请
	DisinviteChannelLiveMultiPk(context.Context, *DisinviteChannelLiveMultiPkReq) (*DisinviteChannelLiveMultiPkResp, error)
	// 获取最近多人PK的主播列表
	GetLiveMultiPkRecordList(context.Context, *GetLiveMultiPkRecordListReq) (*GetLiveMultiPkRecordListResp, error)
	// 处理收到PK邀请
	HandleLiveMultiPkApply(context.Context, *HandleLiveMultiPkApplyReq) (*HandleLiveMultiPkApplyResp, error)
	// 开始随机匹配
	StartMultiPkMatch(context.Context, *StartMultiPkMatchReq) (*StartMultiPkMatchResp, error)
	// 取消PK匹配
	CancelMultiPkMatch(context.Context, *CancelMultiPkMatchReq) (*CancelMultiPkMatchResp, error)
	// 取消多人PK组队
	CancelLiveMultiPkTeam(context.Context, *CancelLiveMultiPkTeamReq) (*CancelLiveMultiPkTeamResp, error)
	// 开始PK
	StartLiveMultiPk(context.Context, *StartLiveMultiPkReq) (*StartLiveMultiPkResp, error)
	// 结束多人PK
	StopLiveMultiPk(context.Context, *StopLiveMultiPkReq) (*StopLiveMultiPkResp, error)
	// 获取被邀请人的状态
	GetLiveMultiPkInviteeStatus(context.Context, *GetLiveMultiPkInviteeStatusReq) (*GetLiveMultiPkInviteeStatusResp, error)
	// 获取在房骑士列表
	GetOnlineKnightList(context.Context, *GetOnlineKnightListReq) (*GetOnlineKnightListResp, error)
	// 获取PK火力榜
	GetLiveMultiPkRank(context.Context, *GetLiveMultiPkRankReq) (*GetLiveMultiPkRankResp, error)
	// 获取多人pk信息
	GetMultiPkInfo(context.Context, *GetMultiPkInfoReq) (*GetMultiPkInfoResp, error)
	// 批量查询是否在多人pk
	BatCheckIsInMultiPk(context.Context, *BatCheckIsInMultiPkReq) (*BatCheckIsInMultiPkResp, error)
	// 批量查询是否在多人pk匹配
	BatCheckIsInMultiMatch(context.Context, *BatCheckIsInMultiMatchReq) (*BatCheckIsInMultiMatchResp, error)
	// ==========================直播PK和预约PK=============================
	HandlerApply(context.Context, *HandlerApplyReq) (*HandlerApplyResp, error)
	CancelPKApply(context.Context, *CancelPKApplyReq) (*CancelPKApplyResp, error)
	SetPkStatus(context.Context, *SetPkStatusReq) (*SetPkStatusResp, error)
	ApplyPk(context.Context, *ApplyPkReq) (*ApplyPkResp, error)
	GetChanneLivePkRankUser(context.Context, *GetChanneLivePkRankUserReq) (*GetChanneLivePkRankUserResp, error)
	GetPkInfo(context.Context, *GetPkInfoReq) (*GetPkInfoResp, error)
	StartPkMatch(context.Context, *StartPkMatchReq) (*StartPkMatchResp, error)
	CancelPkMatch(context.Context, *CancelPkMatchReq) (*CancelPkMatchResp, error)
	GetPKMatchInfo(context.Context, *GetPKMatchInfoReq) (*GetPKMatchInfoResp, error)
	GetMyToolList(context.Context, *GetMyToolListReq) (*GetMyToolListResp, error)
	// 获取主播pk状态
	GetAnchorPkStatus(context.Context, *GetAnchorPkStatusReq) (*GetAnchorPkStatusResp, error)
	GetItemConfig(context.Context, *GetItemConfigReq) (*GetItemConfigResp, error)
	AcceptAppointPk(context.Context, *AcceptAppointPkReq) (*AcceptAppointPkResp, error)
	GetAppointPkInfo(context.Context, *GetAppointPkInfoReq) (*GetAppointPkInfoResp, error)
	ConfirmAppointPkPush(context.Context, *ConfirmAppointPkPushReq) (*ConfirmAppointPkPushResp, error)
	AddAppointPkInfo(context.Context, *AddAppointPkInfoReq) (*AddAppointPkInfoResp, error)
	UpdateAppointPkInfo(context.Context, *UpdateAppointPkInfoReq) (*UpdateAppointPkInfoResp, error)
	DelAppointPkInfo(context.Context, *DelAppointPkInfoReq) (*DelAppointPkInfoResp, error)
	GetAppointPkInfoList(context.Context, *GetAppointPkInfoListReq) (*GetAppointPkInfoListResp, error)
	ReportClientIDChange(context.Context, *ReportClientIDChangeReq) (*ReportClientIDChangeResp, error)
	// =========================== 直播pk黑名单管理 ===========================
	BatchAddBlacklist(context.Context, *BatchAddBlacklistReq) (*BatchAddBlacklistResp, error)
	BatchDelBlacklist(context.Context, *BatchDelBlacklistReq) (*BatchDelBlacklistResp, error)
	GetBlacklistRecord(context.Context, *GetBlacklistRecordReq) (*GetBlacklistRecordResp, error)
	BatchCheckIfInBlacklist(context.Context, *BatchCheckIfInBlacklistReq) (*BatchCheckIfInBlacklistResp, error)
	UpdateBlacklist(context.Context, *UpdateBlacklistReq) (*UpdateBlacklistResp, error)
	BatchDelBlacklistByUid(context.Context, *BatchDelBlacklistByUidReq) (*BatchDelBlacklistByUidResp, error)
}

func RegisterChannelLivePkServer(s *grpc.Server, srv ChannelLivePkServer) {
	s.RegisterService(&_ChannelLivePk_serviceDesc, srv)
}

func _ChannelLivePk_GetLiveMultiPkPer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveMultiPkPerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).GetLiveMultiPkPer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/GetLiveMultiPkPer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).GetLiveMultiPkPer(ctx, req.(*GetLiveMultiPkPerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_IniLiveMultiPkTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IniLiveMultiPkTeamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).IniLiveMultiPkTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/IniLiveMultiPkTeam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).IniLiveMultiPkTeam(ctx, req.(*IniLiveMultiPkTeamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_ApplyLiveMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyLiveMultiPkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).ApplyLiveMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/ApplyLiveMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).ApplyLiveMultiPk(ctx, req.(*ApplyLiveMultiPkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_DisinviteChannelLiveMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisinviteChannelLiveMultiPkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).DisinviteChannelLiveMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/DisinviteChannelLiveMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).DisinviteChannelLiveMultiPk(ctx, req.(*DisinviteChannelLiveMultiPkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_GetLiveMultiPkRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveMultiPkRecordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).GetLiveMultiPkRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/GetLiveMultiPkRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).GetLiveMultiPkRecordList(ctx, req.(*GetLiveMultiPkRecordListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_HandleLiveMultiPkApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleLiveMultiPkApplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).HandleLiveMultiPkApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/HandleLiveMultiPkApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).HandleLiveMultiPkApply(ctx, req.(*HandleLiveMultiPkApplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_StartMultiPkMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartMultiPkMatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).StartMultiPkMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/StartMultiPkMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).StartMultiPkMatch(ctx, req.(*StartMultiPkMatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_CancelMultiPkMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelMultiPkMatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).CancelMultiPkMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/CancelMultiPkMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).CancelMultiPkMatch(ctx, req.(*CancelMultiPkMatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_CancelLiveMultiPkTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelLiveMultiPkTeamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).CancelLiveMultiPkTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/CancelLiveMultiPkTeam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).CancelLiveMultiPkTeam(ctx, req.(*CancelLiveMultiPkTeamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_StartLiveMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartLiveMultiPkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).StartLiveMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/StartLiveMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).StartLiveMultiPk(ctx, req.(*StartLiveMultiPkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_StopLiveMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopLiveMultiPkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).StopLiveMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/StopLiveMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).StopLiveMultiPk(ctx, req.(*StopLiveMultiPkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_GetLiveMultiPkInviteeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveMultiPkInviteeStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).GetLiveMultiPkInviteeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/GetLiveMultiPkInviteeStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).GetLiveMultiPkInviteeStatus(ctx, req.(*GetLiveMultiPkInviteeStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_GetOnlineKnightList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnlineKnightListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).GetOnlineKnightList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/GetOnlineKnightList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).GetOnlineKnightList(ctx, req.(*GetOnlineKnightListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_GetLiveMultiPkRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveMultiPkRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).GetLiveMultiPkRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/GetLiveMultiPkRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).GetLiveMultiPkRank(ctx, req.(*GetLiveMultiPkRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_GetMultiPkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiPkInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).GetMultiPkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/GetMultiPkInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).GetMultiPkInfo(ctx, req.(*GetMultiPkInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_BatCheckIsInMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatCheckIsInMultiPkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).BatCheckIsInMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/BatCheckIsInMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).BatCheckIsInMultiPk(ctx, req.(*BatCheckIsInMultiPkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_BatCheckIsInMultiMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatCheckIsInMultiMatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).BatCheckIsInMultiMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/BatCheckIsInMultiMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).BatCheckIsInMultiMatch(ctx, req.(*BatCheckIsInMultiMatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_HandlerApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandlerApplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).HandlerApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/HandlerApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).HandlerApply(ctx, req.(*HandlerApplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_CancelPKApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelPKApplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).CancelPKApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/CancelPKApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).CancelPKApply(ctx, req.(*CancelPKApplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_SetPkStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPkStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).SetPkStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/SetPkStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).SetPkStatus(ctx, req.(*SetPkStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_ApplyPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyPkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).ApplyPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/ApplyPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).ApplyPk(ctx, req.(*ApplyPkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_GetChanneLivePkRankUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChanneLivePkRankUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).GetChanneLivePkRankUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/GetChanneLivePkRankUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).GetChanneLivePkRankUser(ctx, req.(*GetChanneLivePkRankUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_GetPkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPkInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).GetPkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/GetPkInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).GetPkInfo(ctx, req.(*GetPkInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_StartPkMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartPkMatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).StartPkMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/StartPkMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).StartPkMatch(ctx, req.(*StartPkMatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_CancelPkMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelPkMatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).CancelPkMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/CancelPkMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).CancelPkMatch(ctx, req.(*CancelPkMatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_GetPKMatchInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPKMatchInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).GetPKMatchInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/GetPKMatchInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).GetPKMatchInfo(ctx, req.(*GetPKMatchInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_GetMyToolList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMyToolListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).GetMyToolList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/GetMyToolList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).GetMyToolList(ctx, req.(*GetMyToolListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_GetAnchorPkStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorPkStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).GetAnchorPkStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/GetAnchorPkStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).GetAnchorPkStatus(ctx, req.(*GetAnchorPkStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_GetItemConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetItemConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).GetItemConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/GetItemConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).GetItemConfig(ctx, req.(*GetItemConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_AcceptAppointPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcceptAppointPkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).AcceptAppointPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/AcceptAppointPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).AcceptAppointPk(ctx, req.(*AcceptAppointPkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_GetAppointPkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppointPkInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).GetAppointPkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/GetAppointPkInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).GetAppointPkInfo(ctx, req.(*GetAppointPkInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_ConfirmAppointPkPush_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmAppointPkPushReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).ConfirmAppointPkPush(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/ConfirmAppointPkPush",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).ConfirmAppointPkPush(ctx, req.(*ConfirmAppointPkPushReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_AddAppointPkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAppointPkInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).AddAppointPkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/SetAppointPkInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).AddAppointPkInfo(ctx, req.(*AddAppointPkInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_UpdateAppointPkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAppointPkInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).UpdateAppointPkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/UpdateAppointPkInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).UpdateAppointPkInfo(ctx, req.(*UpdateAppointPkInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_DelAppointPkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelAppointPkInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).DelAppointPkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/DelAppointPkInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).DelAppointPkInfo(ctx, req.(*DelAppointPkInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_GetAppointPkInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppointPkInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).GetAppointPkInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/GetAppointPkInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).GetAppointPkInfoList(ctx, req.(*GetAppointPkInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_ReportClientIDChange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportClientIDChangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).ReportClientIDChange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/ReportClientIDChange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).ReportClientIDChange(ctx, req.(*ReportClientIDChangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_BatchAddBlacklist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddBlacklistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).BatchAddBlacklist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/BatchAddBlacklist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).BatchAddBlacklist(ctx, req.(*BatchAddBlacklistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_BatchDelBlacklist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelBlacklistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).BatchDelBlacklist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/BatchDelBlacklist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).BatchDelBlacklist(ctx, req.(*BatchDelBlacklistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_GetBlacklistRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlacklistRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).GetBlacklistRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/GetBlacklistRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).GetBlacklistRecord(ctx, req.(*GetBlacklistRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_BatchCheckIfInBlacklist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckIfInBlacklistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).BatchCheckIfInBlacklist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/BatchCheckIfInBlacklist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).BatchCheckIfInBlacklist(ctx, req.(*BatchCheckIfInBlacklistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_UpdateBlacklist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlacklistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).UpdateBlacklist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/UpdateBlacklist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).UpdateBlacklist(ctx, req.(*UpdateBlacklistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLivePk_BatchDelBlacklistByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelBlacklistByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLivePkServer).BatchDelBlacklistByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_pk.ChannelLivePk/BatchDelBlacklistByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLivePkServer).BatchDelBlacklistByUid(ctx, req.(*BatchDelBlacklistByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelLivePk_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_live_pk.ChannelLivePk",
	HandlerType: (*ChannelLivePkServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLiveMultiPkPer",
			Handler:    _ChannelLivePk_GetLiveMultiPkPer_Handler,
		},
		{
			MethodName: "IniLiveMultiPkTeam",
			Handler:    _ChannelLivePk_IniLiveMultiPkTeam_Handler,
		},
		{
			MethodName: "ApplyLiveMultiPk",
			Handler:    _ChannelLivePk_ApplyLiveMultiPk_Handler,
		},
		{
			MethodName: "DisinviteChannelLiveMultiPk",
			Handler:    _ChannelLivePk_DisinviteChannelLiveMultiPk_Handler,
		},
		{
			MethodName: "GetLiveMultiPkRecordList",
			Handler:    _ChannelLivePk_GetLiveMultiPkRecordList_Handler,
		},
		{
			MethodName: "HandleLiveMultiPkApply",
			Handler:    _ChannelLivePk_HandleLiveMultiPkApply_Handler,
		},
		{
			MethodName: "StartMultiPkMatch",
			Handler:    _ChannelLivePk_StartMultiPkMatch_Handler,
		},
		{
			MethodName: "CancelMultiPkMatch",
			Handler:    _ChannelLivePk_CancelMultiPkMatch_Handler,
		},
		{
			MethodName: "CancelLiveMultiPkTeam",
			Handler:    _ChannelLivePk_CancelLiveMultiPkTeam_Handler,
		},
		{
			MethodName: "StartLiveMultiPk",
			Handler:    _ChannelLivePk_StartLiveMultiPk_Handler,
		},
		{
			MethodName: "StopLiveMultiPk",
			Handler:    _ChannelLivePk_StopLiveMultiPk_Handler,
		},
		{
			MethodName: "GetLiveMultiPkInviteeStatus",
			Handler:    _ChannelLivePk_GetLiveMultiPkInviteeStatus_Handler,
		},
		{
			MethodName: "GetOnlineKnightList",
			Handler:    _ChannelLivePk_GetOnlineKnightList_Handler,
		},
		{
			MethodName: "GetLiveMultiPkRank",
			Handler:    _ChannelLivePk_GetLiveMultiPkRank_Handler,
		},
		{
			MethodName: "GetMultiPkInfo",
			Handler:    _ChannelLivePk_GetMultiPkInfo_Handler,
		},
		{
			MethodName: "BatCheckIsInMultiPk",
			Handler:    _ChannelLivePk_BatCheckIsInMultiPk_Handler,
		},
		{
			MethodName: "BatCheckIsInMultiMatch",
			Handler:    _ChannelLivePk_BatCheckIsInMultiMatch_Handler,
		},
		{
			MethodName: "HandlerApply",
			Handler:    _ChannelLivePk_HandlerApply_Handler,
		},
		{
			MethodName: "CancelPKApply",
			Handler:    _ChannelLivePk_CancelPKApply_Handler,
		},
		{
			MethodName: "SetPkStatus",
			Handler:    _ChannelLivePk_SetPkStatus_Handler,
		},
		{
			MethodName: "ApplyPk",
			Handler:    _ChannelLivePk_ApplyPk_Handler,
		},
		{
			MethodName: "GetChanneLivePkRankUser",
			Handler:    _ChannelLivePk_GetChanneLivePkRankUser_Handler,
		},
		{
			MethodName: "GetPkInfo",
			Handler:    _ChannelLivePk_GetPkInfo_Handler,
		},
		{
			MethodName: "StartPkMatch",
			Handler:    _ChannelLivePk_StartPkMatch_Handler,
		},
		{
			MethodName: "CancelPkMatch",
			Handler:    _ChannelLivePk_CancelPkMatch_Handler,
		},
		{
			MethodName: "GetPKMatchInfo",
			Handler:    _ChannelLivePk_GetPKMatchInfo_Handler,
		},
		{
			MethodName: "GetMyToolList",
			Handler:    _ChannelLivePk_GetMyToolList_Handler,
		},
		{
			MethodName: "GetAnchorPkStatus",
			Handler:    _ChannelLivePk_GetAnchorPkStatus_Handler,
		},
		{
			MethodName: "GetItemConfig",
			Handler:    _ChannelLivePk_GetItemConfig_Handler,
		},
		{
			MethodName: "AcceptAppointPk",
			Handler:    _ChannelLivePk_AcceptAppointPk_Handler,
		},
		{
			MethodName: "GetAppointPkInfo",
			Handler:    _ChannelLivePk_GetAppointPkInfo_Handler,
		},
		{
			MethodName: "ConfirmAppointPkPush",
			Handler:    _ChannelLivePk_ConfirmAppointPkPush_Handler,
		},
		{
			MethodName: "SetAppointPkInfo",
			Handler:    _ChannelLivePk_AddAppointPkInfo_Handler,
		},
		{
			MethodName: "UpdateAppointPkInfo",
			Handler:    _ChannelLivePk_UpdateAppointPkInfo_Handler,
		},
		{
			MethodName: "DelAppointPkInfo",
			Handler:    _ChannelLivePk_DelAppointPkInfo_Handler,
		},
		{
			MethodName: "GetAppointPkInfoList",
			Handler:    _ChannelLivePk_GetAppointPkInfoList_Handler,
		},
		{
			MethodName: "ReportClientIDChange",
			Handler:    _ChannelLivePk_ReportClientIDChange_Handler,
		},
		{
			MethodName: "BatchAddBlacklist",
			Handler:    _ChannelLivePk_BatchAddBlacklist_Handler,
		},
		{
			MethodName: "BatchDelBlacklist",
			Handler:    _ChannelLivePk_BatchDelBlacklist_Handler,
		},
		{
			MethodName: "GetBlacklistRecord",
			Handler:    _ChannelLivePk_GetBlacklistRecord_Handler,
		},
		{
			MethodName: "BatchCheckIfInBlacklist",
			Handler:    _ChannelLivePk_BatchCheckIfInBlacklist_Handler,
		},
		{
			MethodName: "UpdateBlacklist",
			Handler:    _ChannelLivePk_UpdateBlacklist_Handler,
		},
		{
			MethodName: "BatchDelBlacklistByUid",
			Handler:    _ChannelLivePk_BatchDelBlacklistByUid_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-live-pk/channel-live-pk.proto",
}

func init() {
	proto.RegisterFile("channel-live-pk/channel-live-pk.proto", fileDescriptor_channel_live_pk_57ddfa79d2ab3441)
}

var fileDescriptor_channel_live_pk_57ddfa79d2ab3441 = []byte{
	// 5594 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x7c, 0x4d, 0x6c, 0xe3, 0x48,
	0x76, 0x70, 0x53, 0x92, 0x6d, 0xe9, 0xc9, 0xb2, 0xd9, 0x74, 0xfb, 0x4f, 0xfd, 0x33, 0xdd, 0xdc,
	0xe9, 0xe9, 0x1e, 0xcf, 0x4e, 0xf7, 0xb7, 0x3d, 0xdf, 0xec, 0xdf, 0x64, 0x76, 0xd7, 0x96, 0x65,
	0xb7, 0x60, 0xd9, 0xad, 0xa5, 0xe5, 0x99, 0xfd, 0x19, 0x0c, 0xc1, 0x96, 0xca, 0x6e, 0xae, 0x28,
	0x92, 0x23, 0x52, 0x9e, 0xf1, 0x62, 0x91, 0x43, 0x02, 0x24, 0x40, 0x0e, 0x8b, 0xfc, 0xec, 0x29,
	0xb9, 0x24, 0x08, 0x90, 0xdc, 0x82, 0x00, 0x49, 0x90, 0x4b, 0x80, 0x1c, 0x72, 0x58, 0x20, 0x87,
	0x00, 0xc9, 0x31, 0x40, 0x72, 0x4e, 0x80, 0x00, 0xb9, 0xe6, 0x18, 0xd4, 0xab, 0x22, 0x59, 0x24,
	0x4b, 0x3f, 0xee, 0xcc, 0x9e, 0x2c, 0xbe, 0x7a, 0xf5, 0xaa, 0xea, 0xd5, 0xab, 0xf7, 0x5e, 0xbd,
	0xf7, 0xca, 0xf0, 0xb0, 0xf7, 0xca, 0x72, 0x5d, 0xe2, 0xbc, 0xeb, 0xd8, 0x97, 0xe4, 0x5d, 0x7f,
	0xf0, 0x34, 0xf3, 0xfd, 0xc4, 0x1f, 0x79, 0xa1, 0xa7, 0xad, 0x72, 0xb0, 0x49, 0xc1, 0xa6, 0x3f,
	0xd0, 0xff, 0x56, 0x81, 0xa5, 0xb3, 0xc1, 0xe7, 0x2d, 0xf7, 0xdc, 0xd3, 0x54, 0x28, 0x8e, 0xed,
	0xfe, 0x96, 0x72, 0x5f, 0x79, 0x5c, 0x33, 0xe8, 0x4f, 0x6d, 0x0b, 0x96, 0xac, 0x5e, 0xcf, 0x1b,
	0xbb, 0xe1, 0x56, 0xe1, 0xbe, 0xf2, 0xb8, 0x62, 0x44, 0x9f, 0x5a, 0x1d, 0xca, 0xae, 0xdd, 0x1b,
	0xb8, 0xd6, 0x90, 0x6c, 0x15, 0xb1, 0x29, 0xfe, 0xd6, 0x6e, 0xc1, 0x82, 0x43, 0x2e, 0x89, 0xb3,
	0x55, 0x42, 0x4a, 0xec, 0x83, 0x42, 0x87, 0xa4, 0x6f, 0x39, 0x5b, 0x0b, 0x88, 0xce, 0x3e, 0xb4,
	0xbb, 0x00, 0xaf, 0x88, 0xd5, 0x37, 0xcf, 0x47, 0x94, 0xd2, 0x22, 0x36, 0x55, 0x28, 0xe4, 0x80,
	0x02, 0xb4, 0x6d, 0x28, 0x9f, 0x5b, 0x03, 0x62, 0xd2, 0x79, 0x2d, 0x21, 0xb5, 0x25, 0xfa, 0x7d,
	0x66, 0xf7, 0xf5, 0xaf, 0xc1, 0xad, 0x43, 0x12, 0xb6, 0xed, 0x4b, 0x72, 0x3c, 0x76, 0x42, 0xbb,
	0x33, 0xe8, 0x90, 0x91, 0x41, 0x3e, 0xa3, 0x5d, 0xc6, 0x76, 0xdf, 0x74, 0xec, 0x20, 0xdc, 0x52,
	0xee, 0x17, 0x69, 0x97, 0xb1, 0xdd, 0x6f, 0xdb, 0x41, 0xa8, 0xff, 0xb5, 0x02, 0xeb, 0x92, 0x3e,
	0x81, 0xaf, 0xfd, 0x18, 0x6a, 0xb4, 0x13, 0x71, 0xc3, 0xd1, 0x95, 0x39, 0xb4, 0x7c, 0xec, 0x59,
	0x7d, 0xf6, 0x8d, 0x27, 0x19, 0x7e, 0x3d, 0x91, 0x76, 0x7f, 0x72, 0x66, 0xf7, 0x9b, 0xb4, 0xeb,
	0xb1, 0xe5, 0xe3, 0x5f, 0xa3, 0x3a, 0x4e, 0x20, 0xf5, 0xef, 0x80, 0x9a, 0x45, 0xa0, 0xbc, 0x1e,
	0x90, 0xab, 0x88, 0xd7, 0x03, 0x72, 0x45, 0xf9, 0x73, 0x69, 0x39, 0x63, 0x82, 0x9c, 0x2e, 0x1b,
	0xec, 0xe3, 0xdb, 0x85, 0x6f, 0x2a, 0xfa, 0xdb, 0xb0, 0xde, 0x72, 0x6d, 0x61, 0xd8, 0x2e, 0xb1,
	0x86, 0x74, 0xa9, 0xb9, 0x0d, 0xd3, 0xb7, 0x60, 0x43, 0x86, 0x1a, 0xf8, 0xfa, 0xf7, 0x61, 0x6d,
	0xd7, 0xf7, 0x9d, 0x2b, 0xa1, 0x8d, 0x92, 0xb8, 0x0d, 0x15, 0x8b, 0x82, 0xcd, 0x84, 0x50, 0x19,
	0x01, 0x67, 0x76, 0x9f, 0x6e, 0x4e, 0x68, 0x8d, 0x2e, 0x48, 0x88, 0xad, 0x05, 0x6c, 0xad, 0x30,
	0x08, 0xdd, 0x81, 0x0d, 0xb8, 0x95, 0x27, 0x19, 0xf8, 0xfa, 0x27, 0x70, 0x6f, 0xdf, 0x0e, 0x6c,
	0xf7, 0xd2, 0x0e, 0x49, 0x83, 0xf1, 0xef, 0x4b, 0x1c, 0xf5, 0x01, 0xbc, 0x31, 0x95, 0x7a, 0xe0,
	0xeb, 0x4f, 0xe1, 0x76, 0x7a, 0x9f, 0x0c, 0xd2, 0xf3, 0x46, 0x28, 0x03, 0x72, 0xb6, 0x7d, 0x0b,
	0xee, 0x4c, 0xee, 0x10, 0xf8, 0xd3, 0x64, 0xea, 0x53, 0xd8, 0x7e, 0x6e, 0xb9, 0x7d, 0x87, 0x08,
	0xbd, 0x91, 0x2b, 0xd2, 0x91, 0xd2, 0x2b, 0x2f, 0x64, 0x56, 0xae, 0x41, 0xc9, 0xf3, 0xc9, 0x08,
	0x0f, 0x54, 0xcd, 0xc0, 0xdf, 0xfa, 0x1d, 0xa8, 0x4f, 0xa2, 0x1f, 0xf8, 0xfa, 0x63, 0xb8, 0x75,
	0x1a, 0x5a, 0xa3, 0x90, 0x37, 0x1c, 0x5b, 0x61, 0xef, 0x95, 0x7c, 0x89, 0x9b, 0xb0, 0x2e, 0xc1,
	0x0c, 0x7c, 0x2a, 0x5d, 0x0d, 0xcb, 0xed, 0x11, 0x67, 0x36, 0x8d, 0x2d, 0xd8, 0x90, 0xa1, 0x06,
	0xbe, 0xfe, 0x55, 0xd8, 0x62, 0x2d, 0x73, 0x49, 0xe9, 0x6d, 0xd8, 0x9e, 0x80, 0x1d, 0xf8, 0xfa,
	0x1e, 0xac, 0x0b, 0xa0, 0x53, 0x7b, 0xe8, 0x3b, 0x04, 0xd5, 0xd3, 0x26, 0x2c, 0x85, 0xc4, 0x1a,
	0x9a, 0x31, 0xad, 0x45, 0xfa, 0xd9, 0xea, 0x47, 0x03, 0x14, 0x92, 0x01, 0x1e, 0xc2, 0xcd, 0x88,
	0x55, 0x6e, 0xef, 0x95, 0x37, 0x92, 0xab, 0x37, 0xfd, 0x6f, 0x14, 0x58, 0xe5, 0x78, 0x67, 0x01,
	0x99, 0x80, 0xa5, 0xbd, 0x07, 0xe5, 0xf1, 0xe0, 0x73, 0xd3, 0x76, 0xcf, 0x3d, 0x1c, 0xa3, 0xfa,
	0x6c, 0x2b, 0xa7, 0x16, 0xb8, 0x0a, 0x35, 0x96, 0xc6, 0x5c, 0x97, 0x6e, 0x43, 0xd9, 0x1f, 0x98,
	0x81, 0xd7, 0x1b, 0x31, 0xfd, 0x58, 0x32, 0x96, 0xfc, 0xc1, 0x29, 0xfd, 0xd4, 0x1e, 0xc0, 0xf2,
	0xc0, 0xb5, 0x2f, 0x5e, 0x85, 0xa6, 0xa8, 0x25, 0xab, 0x0c, 0xd6, 0x46, 0x5d, 0x79, 0x17, 0xe0,
	0xdc, 0x1e, 0x05, 0xa1, 0x39, 0xb0, 0x1d, 0xa6, 0x30, 0xcb, 0x46, 0x05, 0x21, 0x47, 0xb6, 0xe3,
	0xe8, 0xbf, 0x5d, 0x8c, 0xe7, 0x6d, 0x78, 0xde, 0x70, 0x3a, 0x77, 0x1a, 0x50, 0xb5, 0x90, 0x09,
	0xe2, 0x0a, 0xf4, 0xdc, 0x0a, 0x72, 0xfc, 0x32, 0xc0, 0x4a, 0x78, 0xb7, 0x0f, 0xb5, 0xd0, 0xf3,
	0xcd, 0x71, 0x40, 0x46, 0xec, 0x14, 0x14, 0x51, 0x3f, 0xde, 0x9f, 0x44, 0x26, 0x62, 0xa7, 0x51,
	0x0d, 0x3d, 0x9f, 0x7e, 0xd0, 0xb3, 0x12, 0x31, 0xa5, 0xe7, 0x8d, 0x08, 0x5f, 0x35, 0x65, 0x0a,
	0xfd, 0xa4, 0xd3, 0xf7, 0x07, 0xe6, 0xc8, 0x72, 0x07, 0xb8, 0xdc, 0x9a, 0xb1, 0xe8, 0x0f, 0x0c,
	0xcb, 0x1d, 0x50, 0x56, 0x44, 0x63, 0xd8, 0x7d, 0x34, 0x10, 0x35, 0xa3, 0xc2, 0x21, 0x2d, 0x54,
	0x16, 0x76, 0x60, 0x06, 0xbe, 0xe7, 0x06, 0xde, 0x08, 0x4d, 0x44, 0xd9, 0xa8, 0xd8, 0xc1, 0x29,
	0x03, 0x68, 0xbb, 0x50, 0x8d, 0x78, 0x4d, 0x67, 0x5d, 0x9e, 0x73, 0xd6, 0xc0, 0x37, 0x83, 0x4e,
	0xfa, 0x36, 0x54, 0xe8, 0xcc, 0x48, 0x30, 0x76, 0xc2, 0xad, 0x0a, 0x3b, 0xb1, 0x3e, 0xd5, 0x33,
	0x63, 0x27, 0xd4, 0x7f, 0x06, 0xb7, 0x78, 0xdf, 0x83, 0x68, 0x77, 0x28, 0x91, 0x2f, 0x4b, 0x8a,
	0xd2, 0x8b, 0x2f, 0x66, 0x16, 0xaf, 0xff, 0xa2, 0x08, 0x55, 0x3e, 0x3c, 0xa2, 0xaf, 0xc1, 0x82,
	0x3f, 0x48, 0x24, 0xa0, 0xe4, 0x0f, 0x5a, 0x7d, 0xce, 0xd9, 0xf0, 0xca, 0x27, 0xfc, 0x84, 0x2c,
	0xfa, 0x83, 0xee, 0x95, 0x4f, 0xf8, 0xc2, 0x82, 0xd0, 0x0a, 0xc7, 0x01, 0xa7, 0x5d, 0xf6, 0x07,
	0xa7, 0xf8, 0xad, 0x1d, 0xc0, 0xca, 0xc8, 0xf3, 0x86, 0x38, 0x5f, 0xc6, 0xbb, 0xd2, 0x74, 0xde,
	0x45, 0x82, 0x68, 0x2c, 0x8f, 0xf8, 0x2f, 0xe4, 0xde, 0x31, 0xac, 0x26, 0x92, 0x8c, 0xf2, 0x83,
	0xfb, 0x5b, 0x7d, 0xf6, 0x70, 0x12, 0xa1, 0x14, 0x23, 0x8d, 0xda, 0x79, 0x8a, 0xaf, 0x75, 0x9c,
	0x33, 0x71, 0xfb, 0x66, 0x18, 0xa0, 0x30, 0xe0, 0xb9, 0x6a, 0xba, 0xfd, 0x6e, 0xa0, 0x3d, 0x02,
	0x35, 0x5e, 0x4f, 0x84, 0xc2, 0x7c, 0x86, 0x5a, 0xb4, 0x2c, 0x86, 0xf8, 0x10, 0x56, 0xed, 0xc0,
	0xb4, 0xc6, 0xa1, 0x67, 0x46, 0x9c, 0x29, 0xa3, 0xe0, 0x2c, 0xdb, 0xc1, 0xee, 0x38, 0xf4, 0x3a,
	0x8c, 0x3f, 0xf7, 0x61, 0x99, 0x36, 0xd3, 0x43, 0x85, 0x38, 0x6c, 0xef, 0xc1, 0x47, 0xbd, 0x84,
	0x18, 0xeb, 0xb0, 0x48, 0xcd, 0x54, 0x18, 0x6c, 0xc1, 0x7d, 0xe5, 0x71, 0xd1, 0x58, 0xb8, 0x20,
	0x61, 0x37, 0xd0, 0x7f, 0x4b, 0x89, 0xd5, 0x8f, 0xa0, 0xbe, 0xbe, 0xc4, 0xcd, 0x79, 0x0b, 0x56,
	0x13, 0xb1, 0x48, 0x76, 0xa7, 0x66, 0xd4, 0x62, 0xd9, 0x40, 0xdb, 0xf4, 0x19, 0xa8, 0xa2, 0xa6,
	0xc6, 0x69, 0x3c, 0x80, 0xe5, 0x21, 0xfd, 0x88, 0x68, 0xb3, 0xd9, 0x54, 0x11, 0xc6, 0xc9, 0x8b,
	0xd6, 0xae, 0x90, 0xb2, 0x76, 0xda, 0x1b, 0x50, 0xe5, 0x67, 0x0d, 0x0d, 0x18, 0x9b, 0x18, 0x70,
	0x10, 0xb5, 0xce, 0xcf, 0xe0, 0xe6, 0x21, 0x09, 0x05, 0xa1, 0xa4, 0x16, 0x20, 0x2d, 0xc6, 0x4a,
	0x56, 0x8c, 0xff, 0x47, 0x01, 0x2d, 0xdb, 0x29, 0xf0, 0xb5, 0xf7, 0x91, 0x37, 0x78, 0x60, 0x14,
	0x14, 0x99, 0x3b, 0x93, 0x44, 0x06, 0xbb, 0x2c, 0xfa, 0xec, 0x10, 0x34, 0xa1, 0xe6, 0x0f, 0x4c,
	0xb6, 0x46, 0xe1, 0xb4, 0x3d, 0x98, 0xd4, 0x39, 0x66, 0x8d, 0x51, 0xf5, 0x05, 0x3e, 0x35, 0xd2,
	0x9a, 0x83, 0xe9, 0xbb, 0x89, 0x6a, 0xf3, 0x08, 0x51, 0x73, 0xba, 0x83, 0xb2, 0x8b, 0x84, 0xa1,
	0x43, 0xd8, 0x4c, 0xa8, 0xce, 0x5b, 0x36, 0x80, 0x81, 0x28, 0xb2, 0xfe, 0x8b, 0x44, 0x54, 0x12,
	0x12, 0x94, 0x5f, 0x5c, 0x65, 0x27, 0x4a, 0xa4, 0xc2, 0x20, 0xdc, 0x41, 0x12, 0xd8, 0x59, 0xc8,
	0xaa, 0xc4, 0x5d, 0xd9, 0xcc, 0xaf, 0xa5, 0xf3, 0xf4, 0x3f, 0x54, 0x60, 0x0d, 0xbd, 0x85, 0x8c,
	0xdf, 0x96, 0x57, 0x6b, 0xb1, 0x54, 0x17, 0xe4, 0x52, 0x5d, 0x4c, 0x49, 0x75, 0x03, 0x2a, 0x78,
	0x9e, 0x04, 0x85, 0xf2, 0xd6, 0xa4, 0x89, 0xa5, 0xad, 0xbf, 0x51, 0xa6, 0x1d, 0x71, 0x72, 0x3f,
	0xe0, 0x3e, 0x4f, 0xc6, 0xeb, 0xd3, 0xbe, 0x07, 0xb5, 0x21, 0xfd, 0x34, 0xaf, 0x23, 0x35, 0xd5,
	0x61, 0xf2, 0xa1, 0x7f, 0x00, 0xda, 0x69, 0xe8, 0xf9, 0xaf, 0xb5, 0x68, 0x7d, 0x9d, 0xb2, 0x2c,
	0xd3, 0x39, 0xf0, 0xf5, 0x6f, 0xc0, 0xc6, 0x21, 0x09, 0x5f, 0xb8, 0x8e, 0xed, 0x92, 0xa3, 0x98,
	0xc3, 0xfc, 0x54, 0x4c, 0xd9, 0x65, 0xfd, 0x13, 0xd8, 0x94, 0x76, 0x0c, 0xfc, 0xec, 0x0e, 0x2b,
	0xaf, 0xb1, 0xc3, 0xcf, 0xb3, 0x37, 0x21, 0x6a, 0x6c, 0xe5, 0xab, 0x4d, 0xcf, 0xb3, 0x90, 0x9d,
	0xe7, 0xc7, 0xb8, 0xc0, 0x1c, 0xa5, 0xc0, 0xd7, 0x3e, 0x84, 0x0a, 0x35, 0xe8, 0x89, 0x22, 0x99,
	0x67, 0x92, 0x65, 0xda, 0x05, 0xa7, 0x48, 0xe0, 0x5e, 0x9a, 0x70, 0x0b, 0x7d, 0x7e, 0xc2, 0xb4,
	0xd4, 0xcc, 0x6b, 0xc4, 0x5b, 0xb0, 0x9a, 0x5c, 0x23, 0x44, 0x65, 0x56, 0x8b, 0xef, 0x12, 0x38,
	0xcc, 0xbf, 0x2a, 0xf0, 0xc6, 0xd4, 0x71, 0x02, 0x5f, 0x1b, 0x81, 0x1a, 0x77, 0x7a, 0x16, 0x2b,
	0x4e, 0xba, 0xa0, 0x83, 0x19, 0x37, 0xc4, 0x1c, 0xad, 0x27, 0xdd, 0x0c, 0x21, 0x76, 0x61, 0xcc,
	0xd1, 0xaf, 0x37, 0x60, 0x5d, 0x8a, 0x3a, 0xeb, 0xea, 0x58, 0x13, 0xaf, 0x8e, 0x63, 0xd8, 0xd8,
	0xb3, 0xc2, 0xc6, 0x2b, 0xd2, 0x1b, 0xb4, 0x82, 0x96, 0x2b, 0x48, 0xf5, 0xe4, 0x2b, 0x0d, 0x6d,
	0xea, 0x65, 0xf4, 0x7f, 0x8f, 0x37, 0x31, 0xd3, 0xe9, 0x12, 0xd2, 0x8f, 0x4f, 0x59, 0x31, 0x32,
	0x9d, 0x27, 0x84, 0xf4, 0xf9, 0x41, 0xfa, 0xef, 0x02, 0x6c, 0x4a, 0xc7, 0x0d, 0x7c, 0xed, 0x63,
	0xa8, 0x0e, 0x2d, 0x9f, 0x1a, 0x2e, 0x3b, 0xf0, 0x07, 0x13, 0x2f, 0xda, 0x13, 0xba, 0x3f, 0x39,
	0xb6, 0xfc, 0x56, 0xbf, 0x15, 0x50, 0xa3, 0x4f, 0xf9, 0x56, 0x19, 0x46, 0xdf, 0xda, 0x8f, 0xa8,
	0x65, 0xf3, 0x4d, 0x3a, 0x75, 0xae, 0xf7, 0x29, 0xe5, 0x6f, 0x5e, 0x87, 0x72, 0xc3, 0xee, 0xd3,
	0xf9, 0x33, 0xd2, 0x30, 0x8c, 0x01, 0xf5, 0x5f, 0x83, 0x95, 0xf4, 0xc0, 0xd7, 0xb9, 0xc0, 0xd7,
	0x2d, 0x58, 0xcd, 0x10, 0x97, 0x74, 0xff, 0xa6, 0xd8, 0x7d, 0x8a, 0xa9, 0x11, 0x74, 0xa2, 0xb0,
	0xd1, 0x5f, 0x87, 0xed, 0xdc, 0xba, 0xe2, 0x9b, 0xdc, 0x94, 0xeb, 0xeb, 0x3f, 0x28, 0x50, 0x9f,
	0xd4, 0x31, 0xf0, 0xb5, 0x3e, 0xac, 0xc6, 0x9b, 0xc5, 0x6c, 0x2a, 0xdf, 0xb0, 0x0f, 0x67, 0xb3,
	0x35, 0xa6, 0x12, 0xed, 0x19, 0x02, 0x18, 0x6f, 0x97, 0x87, 0x02, 0xa8, 0xfe, 0x5d, 0xb8, 0x99,
	0x43, 0xb9, 0x56, 0x84, 0xe4, 0xef, 0x0b, 0xb0, 0xca, 0x6e, 0xc9, 0xa3, 0x29, 0x77, 0xef, 0x19,
	0x76, 0xb3, 0x0e, 0x65, 0xea, 0x9f, 0x8a, 0x21, 0xad, 0xe8, 0x3b, 0x15, 0xee, 0x2a, 0x65, 0xc2,
	0x5d, 0x29, 0x2d, 0xb4, 0x90, 0xd1, 0x42, 0x8f, 0x41, 0x65, 0x8d, 0xb9, 0x4b, 0xcc, 0x0a, 0xc2,
	0x1b, 0xf1, 0xf0, 0x0f, 0x61, 0x85, 0x93, 0x89, 0x26, 0xb1, 0x84, 0x03, 0xd5, 0x18, 0xad, 0x68,
	0x26, 0x31, 0x5a, 0x3c, 0x9f, 0xb2, 0x80, 0x76, 0x12, 0x4d, 0xea, 0x09, 0x0f, 0x25, 0x50, 0xa7,
	0x75, 0xe5, 0x59, 0x3d, 0xb7, 0x5b, 0x4d, 0x77, 0x3c, 0x64, 0xac, 0x62, 0x61, 0x86, 0xaf, 0x83,
	0x9a, 0x66, 0x60, 0xe0, 0x6b, 0x3a, 0x7a, 0x52, 0x2f, 0xc9, 0x85, 0xed, 0x9a, 0xa1, 0x3d, 0x24,
	0x91, 0xaf, 0xe8, 0x0f, 0xf6, 0x28, 0xac, 0x6b, 0x0f, 0x89, 0xfe, 0x3b, 0x0a, 0xa8, 0xec, 0x2e,
	0xdf, 0x39, 0x7a, 0x7d, 0xd6, 0xa7, 0x58, 0x58, 0x9c, 0x83, 0x85, 0x25, 0x19, 0x0b, 0xf5, 0x35,
	0xb8, 0x99, 0x99, 0x4b, 0xe0, 0xeb, 0xbf, 0xa9, 0xc0, 0xca, 0x29, 0x09, 0x3b, 0x83, 0xc4, 0x6e,
	0x5c, 0x7b, 0x7e, 0xdf, 0x83, 0x45, 0xae, 0xf5, 0x4b, 0xc8, 0xcf, 0xc7, 0x52, 0x7e, 0x0a, 0xd1,
	0xa8, 0xce, 0x11, 0x1f, 0x8d, 0xf7, 0xd3, 0x3f, 0x81, 0xd5, 0xd4, 0x24, 0x02, 0x5f, 0xdb, 0x81,
	0x9b, 0xdc, 0x40, 0xe5, 0x9c, 0x63, 0x6e, 0xb9, 0x1a, 0xe2, 0x35, 0x57, 0xd8, 0x07, 0x3e, 0xbf,
	0x97, 0xf1, 0x2e, 0xfc, 0xa5, 0x02, 0x80, 0x2b, 0xee, 0x0c, 0x5e, 0x6b, 0x7d, 0xc2, 0x85, 0x02,
	0x17, 0xc4, 0x77, 0xa1, 0x14, 0x5f, 0x28, 0xe8, 0x9a, 0x5a, 0xd9, 0xd0, 0x5c, 0x29, 0x13, 0x9a,
	0x93, 0xaf, 0x68, 0x41, 0xba, 0x22, 0xbd, 0x06, 0xd5, 0x78, 0xc6, 0x81, 0xaf, 0xff, 0xae, 0x02,
	0xf5, 0xc3, 0xa8, 0x1d, 0x39, 0x88, 0x7e, 0x04, 0xde, 0x01, 0x5f, 0x67, 0x45, 0xe9, 0x99, 0x16,
	0xb3, 0x33, 0x55, 0xa1, 0xe8, 0x9d, 0x9f, 0xf3, 0x15, 0xd0, 0x9f, 0x14, 0xd2, 0x73, 0x43, 0x3e,
	0x5b, 0xfa, 0x53, 0xff, 0x23, 0x05, 0xc3, 0x88, 0xf2, 0x29, 0x05, 0xbe, 0xf6, 0x1d, 0xa8, 0x24,
	0xf1, 0x10, 0xa6, 0x15, 0xf3, 0x97, 0x8c, 0x53, 0xe2, 0xf6, 0x0f, 0xed, 0xf3, 0x30, 0xf1, 0x6f,
	0xc6, 0x51, 0x34, 0x64, 0xf6, 0x0a, 0x04, 0xb7, 0xab, 0x98, 0x75, 0xbb, 0x7e, 0xae, 0x80, 0x9a,
	0x25, 0x2e, 0x61, 0xd3, 0x2d, 0x58, 0x60, 0xf1, 0x16, 0xee, 0x1a, 0xe0, 0x47, 0x26, 0xbe, 0x54,
	0xcc, 0xc4, 0x97, 0x52, 0xb1, 0x8a, 0xd2, 0x9c, 0xb1, 0x0a, 0xfd, 0x5d, 0x58, 0x3e, 0xa4, 0x12,
	0x3e, 0xe7, 0xa5, 0xef, 0x1f, 0x15, 0xa8, 0x09, 0xf8, 0x81, 0xaf, 0x7d, 0x0b, 0x2a, 0x56, 0xc6,
	0x77, 0xbf, 0x9b, 0x1b, 0x96, 0xda, 0x3f, 0xf7, 0x82, 0xdb, 0xbf, 0x25, 0x8b, 0x07, 0x3e, 0xbe,
	0x05, 0x95, 0x97, 0x71, 0xd7, 0xc2, 0x5c, 0x5d, 0x5f, 0xf2, 0xae, 0x0d, 0x58, 0xf1, 0x07, 0x66,
	0xcf, 0x1b, 0x0e, 0x3d, 0x37, 0x71, 0x68, 0xe4, 0xfd, 0x1b, 0x88, 0xc5, 0xa2, 0x1c, 0xbe, 0xf0,
	0xa5, 0xff, 0xa7, 0x02, 0xcb, 0x22, 0xf9, 0x5f, 0xdd, 0x09, 0x9c, 0x12, 0x42, 0xbb, 0x0b, 0x40,
	0xce, 0xcf, 0x49, 0x2f, 0x34, 0x13, 0x41, 0xae, 0x30, 0x48, 0xc3, 0x0d, 0xb5, 0x03, 0x28, 0x0f,
	0xed, 0x9e, 0x79, 0xee, 0x58, 0x17, 0x68, 0x81, 0x56, 0x9e, 0xbd, 0x93, 0x5b, 0xa2, 0xa0, 0xc1,
	0x5e, 0xf8, 0xbe, 0xe7, 0x12, 0x37, 0x3c, 0xb6, 0x7b, 0x07, 0x8e, 0x75, 0x61, 0x2c, 0x0d, 0xd9,
	0x0f, 0xfd, 0x9f, 0x4a, 0x74, 0xad, 0xc9, 0xe2, 0x33, 0xba, 0x49, 0xc9, 0xe8, 0x26, 0xad, 0x29,
	0x46, 0x32, 0x0a, 0xd7, 0x54, 0x9f, 0x49, 0xcc, 0xa3, 0xc9, 0xa6, 0x2f, 0x5c, 0x69, 0x77, 0xa6,
	0xee, 0xd0, 0x93, 0x63, 0xbb, 0x47, 0x8f, 0x19, 0xf3, 0x37, 0xe8, 0xec, 0xf1, 0xd0, 0xbd, 0x09,
	0x2b, 0x62, 0x3c, 0x2a, 0xd6, 0x62, 0xcb, 0x49, 0x9c, 0xc9, 0xee, 0x67, 0xb0, 0x7a, 0xb1, 0x16,
	0x4b, 0xb0, 0x1a, 0x76, 0x9f, 0xda, 0x47, 0x3b, 0x30, 0xc9, 0x17, 0xe1, 0xc8, 0x62, 0x6b, 0x5f,
	0xc4, 0x83, 0x54, 0xb5, 0x83, 0x26, 0x85, 0xe1, 0xea, 0xdf, 0x01, 0xcd, 0x1f, 0x08, 0x38, 0xe6,
	0x68, 0xec, 0x44, 0x96, 0x7d, 0xd5, 0x1f, 0xc4, 0x88, 0xc6, 0xd8, 0x21, 0x54, 0x08, 0x18, 0xa6,
	0x43, 0xce, 0x43, 0x46, 0xb2, 0xcc, 0x02, 0x58, 0x08, 0x6e, 0x93, 0xf3, 0x30, 0x22, 0x6a, 0x07,
	0xa6, 0xe7, 0x13, 0x57, 0x1c, 0xbd, 0x82, 0xa3, 0xaf, 0xda, 0xc1, 0x0b, 0x9f, 0xb8, 0xc9, 0x0c,
	0x0e, 0x40, 0x13, 0x57, 0xfc, 0xf9, 0x00, 0x85, 0x1c, 0x66, 0x1c, 0x6b, 0x35, 0xe1, 0x07, 0xeb,
	0x51, 0xff, 0x18, 0x96, 0x45, 0x96, 0x4a, 0xfc, 0xb3, 0xaf, 0xa5, 0x3d, 0xd8, 0xdb, 0x92, 0xfd,
	0x39, 0xb6, 0x7b, 0xa7, 0xbe, 0xd5, 0x23, 0xa2, 0xf3, 0xf6, 0xeb, 0x00, 0x49, 0xc3, 0xb5, 0x92,
	0x90, 0x1a, 0x94, 0xa8, 0x17, 0xc4, 0xbd, 0x35, 0xfc, 0xad, 0xad, 0xc3, 0x22, 0x95, 0x93, 0x78,
	0x63, 0x17, 0x86, 0x76, 0x8f, 0x9d, 0x9b, 0x4b, 0xcf, 0xee, 0x91, 0xc8, 0x22, 0x55, 0x8c, 0x25,
	0xfc, 0x6e, 0xf5, 0xa9, 0x0b, 0xb3, 0x8a, 0x01, 0x05, 0x21, 0xf7, 0x31, 0x5d, 0x79, 0xe5, 0x33,
	0x0e, 0xda, 0x01, 0x00, 0x0b, 0x39, 0xc5, 0x51, 0x8f, 0x95, 0x67, 0x8f, 0xa6, 0x9d, 0xaf, 0xce,
	0x11, 0x0e, 0xd6, 0xbd, 0xf2, 0x09, 0xbd, 0xc4, 0xf0, 0x9f, 0xba, 0x06, 0x6a, 0x7a, 0x2e, 0x81,
	0xaf, 0x37, 0x62, 0x17, 0x6b, 0x4a, 0x72, 0x66, 0x86, 0x82, 0xd1, 0x9f, 0xc5, 0xbe, 0x51, 0x42,
	0x79, 0x96, 0x8e, 0xde, 0xc7, 0x60, 0x1e, 0x9f, 0xe9, 0x7c, 0x7a, 0x5d, 0x92, 0x8c, 0xf9, 0x77,
	0x16, 0xde, 0x4b, 0x91, 0x09, 0x7c, 0xed, 0x10, 0xaa, 0x71, 0x9c, 0x2e, 0x64, 0x72, 0x74, 0x1d,
	0x96, 0xf1, 0x58, 0x5d, 0xf7, 0x4a, 0xfb, 0x10, 0xca, 0x3d, 0x3f, 0x9c, 0x9e, 0xdd, 0x40, 0xcd,
	0xe0, 0x93, 0xd0, 0x0e, 0x6d, 0xae, 0xc0, 0x97, 0x7a, 0x3e, 0x0b, 0xb6, 0x7d, 0x0f, 0xbd, 0x5c,
	0xc7, 0x1e, 0xda, 0xa1, 0xa8, 0xff, 0xef, 0x48, 0x68, 0xb4, 0x29, 0x52, 0x14, 0x2a, 0x8c, 0x3f,
	0xf4, 0xe7, 0x70, 0x33, 0x47, 0x3f, 0x49, 0x82, 0x2b, 0x62, 0x12, 0xfc, 0x2e, 0x00, 0xfe, 0x30,
	0xd1, 0x73, 0x67, 0xe2, 0x5c, 0x41, 0xc8, 0x89, 0x35, 0x24, 0x7a, 0x0b, 0xaa, 0xc2, 0x28, 0xe8,
	0xc4, 0xa0, 0xce, 0xb0, 0xdc, 0x0b, 0xa6, 0x59, 0x2b, 0x46, 0x85, 0x42, 0x0c, 0x0a, 0xa0, 0x5e,
	0x33, 0x9b, 0x76, 0x2f, 0x3e, 0x1a, 0x65, 0x04, 0x34, 0xdc, 0x90, 0x0a, 0xcd, 0x21, 0x09, 0x8f,
	0xaf, 0xba, 0x9e, 0xe7, 0x4c, 0x4c, 0x7c, 0xce, 0x12, 0x1a, 0x26, 0x00, 0x22, 0x91, 0xc0, 0xd7,
	0x9e, 0xc2, 0x82, 0x1d, 0x92, 0x61, 0x14, 0x01, 0xd9, 0xce, 0x31, 0x8a, 0x62, 0xb7, 0x42, 0x32,
	0x34, 0x18, 0x9e, 0xde, 0x85, 0x72, 0x04, 0xd2, 0x36, 0x61, 0x89, 0x02, 0x23, 0xd1, 0xa9, 0x18,
	0x8b, 0xf4, 0x93, 0xc5, 0x0c, 0xb1, 0xc1, 0x65, 0x46, 0xa2, 0xc8, 0x1a, 0x4e, 0x02, 0x6d, 0x03,
	0x16, 0x5f, 0x92, 0xb3, 0x80, 0xf4, 0xb9, 0x9f, 0xc2, 0xbf, 0xf4, 0xf7, 0x31, 0xff, 0xcf, 0xf2,
	0x55, 0xa2, 0x73, 0x3f, 0x43, 0xa6, 0xdb, 0x18, 0xf8, 0xca, 0x76, 0x0b, 0x7c, 0xed, 0x3d, 0xd8,
	0xc8, 0x2c, 0x24, 0x1d, 0x22, 0x5f, 0x13, 0x0c, 0x71, 0xd4, 0x51, 0xff, 0x65, 0x01, 0x60, 0x5a,
	0x8a, 0x71, 0x8e, 0x8b, 0x0f, 0xbd, 0x7e, 0xb1, 0xc4, 0x08, 0xbf, 0x74, 0x52, 0x00, 0x26, 0x3b,
	0xa2, 0x46, 0xd4, 0xee, 0x4c, 0x9b, 0x61, 0x23, 0xaa, 0xf5, 0x75, 0x58, 0x0c, 0xad, 0x8b, 0xc4,
	0xc1, 0x5e, 0x08, 0xad, 0x8b, 0x56, 0x4a, 0x59, 0x2e, 0x4e, 0xae, 0xd8, 0x58, 0xca, 0x5c, 0x61,
	0x9f, 0xc0, 0x5a, 0x6a, 0xed, 0x7c, 0xe1, 0xcc, 0xf8, 0xdc, 0x14, 0x16, 0xce, 0x8d, 0xf1, 0x64,
	0x5e, 0x55, 0x26, 0xf2, 0x4a, 0xbb, 0x03, 0x15, 0x6b, 0x1c, 0xbe, 0xf2, 0x46, 0x76, 0x78, 0x85,
	0xf6, 0x87, 0xfa, 0xb3, 0x11, 0x40, 0x3f, 0x40, 0x79, 0xa5, 0x32, 0xd2, 0xf0, 0xdc, 0x73, 0xfb,
	0x82, 0x6e, 0xe5, 0x7d, 0x58, 0xe6, 0xc2, 0x92, 0x38, 0xd9, 0x15, 0x03, 0x98, 0xc4, 0xa0, 0x39,
	0xcf, 0x6b, 0x9b, 0x8f, 0x50, 0x64, 0x45, 0x3a, 0x18, 0x30, 0x5d, 0x41, 0x42, 0x3d, 0xcf, 0x3d,
	0x17, 0xfd, 0xf5, 0xbc, 0x89, 0x12, 0x3a, 0xe2, 0xd8, 0xf4, 0x37, 0x06, 0x4a, 0xfe, 0xb8, 0x08,
	0x90, 0x34, 0x4e, 0x96, 0x63, 0x0d, 0x4a, 0x7d, 0x12, 0xf4, 0xf8, 0x79, 0xc4, 0xdf, 0x14, 0x66,
	0xf7, 0x3c, 0x37, 0xb2, 0x53, 0xf4, 0xb7, 0xe0, 0xad, 0x8d, 0x47, 0x0e, 0x8f, 0x29, 0x70, 0x6f,
	0xed, 0x6c, 0xe4, 0x08, 0x57, 0x29, 0x01, 0x8b, 0x19, 0x2e, 0x7e, 0x95, 0x6a, 0xc6, 0xb8, 0x2a,
	0x14, 0x87, 0xc1, 0x05, 0xdf, 0x6f, 0xfa, 0x53, 0xb8, 0xfd, 0xd0, 0x86, 0x25, 0xae, 0x38, 0x10,
	0x72, 0x1c, 0x5c, 0x50, 0x63, 0x78, 0x61, 0xd9, 0x2e, 0x36, 0xb2, 0xe8, 0xc1, 0x12, 0xfd, 0xa6,
	0x4d, 0x6f, 0x43, 0x21, 0xbc, 0xe2, 0x51, 0x83, 0x6d, 0x29, 0x77, 0x50, 0xfd, 0x16, 0x42, 0x21,
	0x1c, 0x03, 0x42, 0xd4, 0x11, 0x6d, 0x32, 0x15, 0xb1, 0x2a, 0xb7, 0xc9, 0x54, 0xbc, 0xb6, 0x60,
	0xe9, 0x92, 0x8c, 0x02, 0xdb, 0x73, 0xb7, 0x96, 0x99, 0xcf, 0xca, 0x3f, 0xb5, 0x23, 0x58, 0x19,
	0xda, 0x0e, 0x09, 0x42, 0xcf, 0x25, 0x6c, 0x63, 0x6a, 0xb8, 0x31, 0x6f, 0xe6, 0x3d, 0x44, 0x5c,
	0x2e, 0x9d, 0xc0, 0x71, 0xd4, 0xc1, 0xa8, 0xc5, 0x7d, 0x71, 0x8b, 0x9a, 0xb0, 0x26, 0xc1, 0xa2,
	0xf3, 0x64, 0x07, 0x82, 0x6b, 0x62, 0x76, 0x1c, 0xb6, 0x60, 0xc9, 0x27, 0xa3, 0x1e, 0xe1, 0xaa,
	0x93, 0xfa, 0xd1, 0xec, 0x53, 0xbf, 0x00, 0x6d, 0xb7, 0xd7, 0x23, 0x7e, 0xb8, 0xeb, 0xfb, 0x9e,
	0xed, 0x86, 0xec, 0x4e, 0x4d, 0xfd, 0x0a, 0x31, 0xd0, 0xbc, 0x30, 0xc4, 0xe0, 0x04, 0x3d, 0xa3,
	0xe1, 0x2b, 0x22, 0xc6, 0xc6, 0xcb, 0x08, 0xc8, 0x27, 0x6a, 0x72, 0xe9, 0xdb, 0x75, 0x58, 0xcb,
	0x0d, 0x14, 0xf8, 0xfa, 0x23, 0x58, 0xa3, 0x1a, 0x2a, 0x82, 0x45, 0x76, 0x37, 0x5f, 0xbe, 0x60,
	0x32, 0x0d, 0x98, 0x46, 0x44, 0xcb, 0xba, 0xea, 0x8f, 0x83, 0x57, 0x26, 0xb9, 0x24, 0x6e, 0x28,
	0x5e, 0xa7, 0xde, 0xc8, 0x71, 0x35, 0xee, 0xdc, 0xa4, 0xb8, 0x46, 0x8d, 0xf6, 0xc3, 0x9f, 0x68,
	0xd8, 0xfe, 0xb4, 0x00, 0x2b, 0x69, 0x0c, 0x14, 0x5b, 0x24, 0x8b, 0x7e, 0x0e, 0xd7, 0xae, 0x08,
	0x89, 0x32, 0xa2, 0x43, 0xb1, 0xb6, 0x45, 0xc6, 0xa5, 0x62, 0x86, 0x4b, 0x5f, 0x81, 0x1a, 0x6b,
	0x8c, 0x14, 0x17, 0x3b, 0x0c, 0xcb, 0x08, 0xdc, 0xe5, 0xda, 0xeb, 0x21, 0xac, 0x30, 0xa4, 0x58,
	0x87, 0xb1, 0xc3, 0xc0, 0xba, 0xc6, 0x61, 0xaf, 0x77, 0x61, 0x0d, 0xf1, 0xcd, 0xbe, 0xf7, 0xb9,
	0xcb, 0x92, 0xc0, 0x91, 0x63, 0x5e, 0x33, 0x54, 0x6c, 0xda, 0xf7, 0x3e, 0x77, 0x9b, 0x6e, 0x1f,
	0x95, 0x68, 0x3c, 0xaf, 0x80, 0x7c, 0x81, 0xc7, 0x64, 0x81, 0xcf, 0xeb, 0x94, 0x7c, 0x81, 0x57,
	0xb2, 0x84, 0x96, 0xe8, 0x8d, 0xc7, 0x74, 0x30, 0xf8, 0x72, 0x0c, 0x9b, 0xa8, 0x14, 0x46, 0xc3,
	0x98, 0x57, 0x9d, 0x71, 0xf0, 0xea, 0x35, 0x85, 0x46, 0xaf, 0xc3, 0x96, 0x9c, 0x5c, 0xe0, 0xeb,
	0x2d, 0x58, 0xdb, 0xed, 0xf7, 0x73, 0xa2, 0xf1, 0x0c, 0x4a, 0xc2, 0x2e, 0xdf, 0x9b, 0xbc, 0xcb,
	0xd8, 0x01, 0x71, 0xb1, 0x78, 0x2b, 0x47, 0x2a, 0xf0, 0xf5, 0xff, 0x50, 0xa0, 0x96, 0x82, 0x62,
	0x20, 0x82, 0x01, 0x04, 0x83, 0xca, 0x21, 0x52, 0x5f, 0x78, 0x1b, 0xca, 0xfc, 0x42, 0x18, 0xa5,
	0xae, 0x97, 0xd8, 0x75, 0x30, 0xa0, 0x0c, 0xe1, 0x99, 0x79, 0xee, 0x9d, 0x13, 0xcc, 0xc8, 0x7f,
	0x00, 0x30, 0xb2, 0x2f, 0x2d, 0x87, 0xa9, 0x80, 0x05, 0x54, 0x01, 0x32, 0x07, 0xcc, 0xa0, 0x48,
	0x38, 0xd5, 0x0a, 0xe2, 0x47, 0x05, 0x1a, 0x63, 0xbf, 0x6f, 0x85, 0x24, 0xaa, 0x09, 0xa8, 0x19,
	0x65, 0x06, 0xe8, 0x06, 0xd4, 0xea, 0x51, 0x93, 0x69, 0x85, 0xbc, 0x3a, 0x84, 0xdb, 0x57, 0xfa,
	0xad, 0x7f, 0x97, 0x7a, 0x5b, 0x31, 0x49, 0x89, 0xf1, 0xbe, 0x87, 0x2e, 0x6a, 0xbc, 0x16, 0x6e,
	0xbd, 0xa3, 0xf0, 0x67, 0xa0, 0xb7, 0x61, 0xe3, 0x0c, 0x07, 0xfa, 0x52, 0x76, 0x64, 0x1b, 0x36,
	0xa5, 0xd4, 0x02, 0x5f, 0xff, 0xff, 0xb0, 0xb6, 0x4f, 0x9c, 0xdc, 0x28, 0xd3, 0x77, 0x86, 0x6e,
	0x71, 0xbe, 0x57, 0xe0, 0xeb, 0xff, 0xa5, 0x60, 0x6a, 0x31, 0xd5, 0x20, 0x24, 0x25, 0x3f, 0x1b,
	0x93, 0xd1, 0x55, 0xea, 0x7c, 0x23, 0x04, 0xcf, 0xf7, 0x97, 0xb1, 0xd9, 0x1a, 0x94, 0x7c, 0xeb,
	0x82, 0x70, 0xbf, 0x05, 0x7f, 0x63, 0xb9, 0x83, 0x75, 0x41, 0xcc, 0xc0, 0xfe, 0x69, 0x74, 0x5a,
	0xcb, 0x14, 0x70, 0x6a, 0xff, 0x94, 0xe8, 0x1f, 0x42, 0xe5, 0xfb, 0xf1, 0x0c, 0x6a, 0xfc, 0xc3,
	0xdc, 0x75, 0x1c, 0xf5, 0x86, 0xa6, 0xc2, 0x32, 0xfb, 0xdc, 0xbb, 0x32, 0xcf, 0xec, 0xbe, 0xaa,
	0x68, 0xab, 0x50, 0x8d, 0x21, 0xdd, 0x40, 0x2d, 0xe8, 0xbf, 0xa7, 0xc0, 0x96, 0x7c, 0xb9, 0x81,
	0xaf, 0x7d, 0x00, 0x95, 0xa4, 0xc4, 0x85, 0x39, 0x05, 0xb3, 0x76, 0xab, 0x6c, 0x47, 0xc5, 0x2d,
	0xb7, 0xa1, 0xe2, 0x92, 0x2f, 0x42, 0x13, 0x97, 0xc3, 0xcf, 0x31, 0x05, 0x74, 0xf8, 0x92, 0x42,
	0x2f, 0xb4, 0x1c, 0xf4, 0xce, 0xb9, 0xce, 0x43, 0x00, 0xf5, 0xce, 0xff, 0x4d, 0x81, 0x4d, 0x83,
	0xf8, 0xde, 0x28, 0x6c, 0x38, 0x36, 0x55, 0xb7, 0xfb, 0xf4, 0x9e, 0x73, 0x41, 0x5e, 0x2b, 0xd6,
	0x99, 0x5c, 0x79, 0x8b, 0xe2, 0x95, 0x57, 0x12, 0x52, 0x2a, 0xc9, 0x42, 0x4a, 0xb7, 0xa1, 0xd2,
	0xc3, 0x49, 0x24, 0x77, 0xe3, 0x32, 0x03, 0xb4, 0xfa, 0xda, 0xfb, 0xb0, 0x19, 0x11, 0xb9, 0xb4,
	0xfb, 0xc4, 0x33, 0x13, 0x54, 0xe6, 0x6f, 0xdc, 0xe2, 0xcd, 0x1f, 0xd1, 0x56, 0xbe, 0x98, 0xbe,
	0x6e, 0xc0, 0x96, 0x7c, 0x79, 0x81, 0xaf, 0x7d, 0x5d, 0x88, 0xe4, 0x4c, 0x72, 0xc3, 0x84, 0x48,
	0x41, 0x14, 0xba, 0xd1, 0xff, 0xa0, 0x00, 0xb7, 0xf6, 0xe8, 0x9d, 0x6f, 0xb7, 0xdf, 0xdf, 0x73,
	0xac, 0xde, 0xc0, 0xe1, 0x32, 0x7b, 0x02, 0xf0, 0x92, 0x7e, 0x8b, 0x24, 0x9f, 0xca, 0xf2, 0x53,
	0xb9, 0xae, 0x4f, 0xf0, 0x03, 0xef, 0x2a, 0x15, 0x24, 0x81, 0xdb, 0x2a, 0xea, 0x8c, 0xc5, 0xb4,
	0xce, 0xa8, 0xff, 0x5c, 0x81, 0x4a, 0xdc, 0x49, 0xbe, 0x55, 0x99, 0x40, 0x7d, 0x49, 0x0c, 0x86,
	0x6d, 0x43, 0x39, 0x36, 0x4a, 0xbc, 0x2c, 0x90, 0x70, 0x5b, 0x74, 0x37, 0x5a, 0x05, 0x9e, 0x3c,
	0x1e, 0x5b, 0x47, 0x08, 0xca, 0xfd, 0x06, 0x2c, 0x8e, 0x88, 0x15, 0x78, 0x2e, 0xdf, 0x22, 0xfe,
	0xa5, 0x6f, 0xc2, 0xba, 0x64, 0x65, 0x81, 0xaf, 0x5f, 0x72, 0x6e, 0xed, 0x13, 0x27, 0xc5, 0xad,
	0xbb, 0x00, 0x23, 0xac, 0x6e, 0x35, 0xed, 0x7e, 0xc0, 0xd3, 0x81, 0x15, 0x06, 0x69, 0xf5, 0x03,
	0x6a, 0x8d, 0x47, 0xa4, 0x67, 0x39, 0x8e, 0xc9, 0x87, 0x63, 0x8e, 0xec, 0x32, 0x03, 0x1a, 0x08,
	0x4b, 0x71, 0xa8, 0x98, 0xd1, 0xaa, 0xd1, 0x84, 0xd2, 0xe3, 0x06, 0xbe, 0xfe, 0x2f, 0xac, 0xfa,
	0x5a, 0x00, 0xd2, 0x31, 0xe9, 0x94, 0xde, 0x82, 0xd5, 0x24, 0xd4, 0x2d, 0xa6, 0x29, 0x6b, 0x71,
	0xbc, 0x1b, 0x37, 0xe6, 0xc3, 0x14, 0x8b, 0x58, 0x2c, 0x31, 0x7f, 0x5a, 0xe3, 0x01, 0x58, 0xa0,
	0x20, 0x61, 0x61, 0x4a, 0xc9, 0x14, 0xd3, 0x4a, 0x26, 0xd6, 0x4a, 0x25, 0x41, 0x2b, 0x3d, 0x80,
	0x65, 0xee, 0x93, 0x24, 0x86, 0xa9, 0x62, 0x54, 0x39, 0x0c, 0x85, 0xf2, 0xcf, 0x4a, 0x58, 0xfe,
	0x90, 0x5b, 0x54, 0xe0, 0x6b, 0x86, 0x44, 0x2c, 0xdf, 0x93, 0x95, 0x0b, 0x48, 0x3a, 0xb3, 0x45,
	0xf0, 0x6f, 0x41, 0x34, 0x6f, 0xc1, 0x02, 0xea, 0x10, 0x2e, 0x59, 0xec, 0xa3, 0xfe, 0x27, 0x45,
	0xa8, 0x0a, 0x1d, 0xe8, 0x42, 0xe3, 0x2d, 0x8e, 0xea, 0x22, 0xa2, 0x1d, 0x96, 0xa8, 0xf0, 0xb4,
	0xcc, 0x16, 0xa7, 0xc9, 0x6c, 0x29, 0x2d, 0xb3, 0x1b, 0x71, 0x5e, 0x8c, 0x17, 0x6d, 0xb2, 0x2f,
	0x41, 0x58, 0x17, 0x45, 0x61, 0x15, 0x6f, 0xa7, 0x4b, 0x93, 0x6f, 0xa7, 0xe5, 0xcc, 0xed, 0xf4,
	0x11, 0x75, 0xc4, 0xdc, 0x70, 0x64, 0xf5, 0x42, 0xf1, 0x9a, 0x59, 0x31, 0x56, 0x22, 0x70, 0x52,
	0xb8, 0x76, 0x31, 0xb6, 0x1d, 0x5c, 0x36, 0xf0, 0x7b, 0x0d, 0xfd, 0x66, 0xd9, 0x14, 0xd6, 0x24,
	0x5c, 0x4e, 0x2a, 0x08, 0x39, 0xe1, 0xf9, 0xdd, 0x58, 0xa0, 0x97, 0xd3, 0x02, 0x8d, 0xae, 0x27,
	0xfe, 0xb6, 0x3d, 0xce, 0xa2, 0x1a, 0x53, 0xa3, 0x31, 0x54, 0x72, 0x7e, 0x57, 0x32, 0xe7, 0x57,
	0xbf, 0xc4, 0x3c, 0x7b, 0xef, 0x15, 0xcb, 0x91, 0x9f, 0xb7, 0xdc, 0xd4, 0xa1, 0x9c, 0x52, 0x8d,
	0xf1, 0x7f, 0x13, 0x7a, 0xfd, 0xaf, 0x14, 0xb8, 0x3d, 0x71, 0x60, 0x2c, 0xc7, 0x60, 0xc8, 0xc2,
	0xab, 0x87, 0x6f, 0xcb, 0x75, 0xa7, 0x9c, 0x00, 0x1b, 0x39, 0x7e, 0xf8, 0x50, 0x7e, 0xc9, 0x3f,
	0xeb, 0x1f, 0x40, 0x2d, 0xd5, 0x74, 0xad, 0x84, 0xfe, 0xdf, 0x29, 0xa0, 0x31, 0x67, 0x28, 0xc5,
	0xa6, 0x6b, 0x0a, 0x76, 0x9a, 0x75, 0xc5, 0xeb, 0xea, 0x8b, 0xf4, 0xb9, 0x28, 0x4d, 0x3b, 0x17,
	0x0b, 0xa9, 0x73, 0x41, 0x6f, 0x76, 0xb9, 0xd9, 0x07, 0xbe, 0x3e, 0xc6, 0x22, 0x8d, 0xb4, 0x6a,
	0xdc, 0xa3, 0x37, 0x82, 0x5f, 0xad, 0x08, 0xdc, 0xe1, 0xa2, 0x27, 0x19, 0x36, 0xf0, 0x77, 0x7e,
	0xa9, 0x80, 0x86, 0x99, 0xd8, 0xa8, 0xbe, 0x84, 0x9d, 0xa5, 0x87, 0xf0, 0x60, 0xb7, 0xd3, 0x69,
	0xff, 0xd0, 0x3c, 0x3e, 0x6b, 0x77, 0x5b, 0x66, 0xe7, 0xc8, 0x3c, 0xed, 0xee, 0x76, 0xcf, 0x4e,
	0xcd, 0xb3, 0x93, 0xd3, 0x4e, 0xb3, 0xd1, 0x3a, 0x68, 0x35, 0xf7, 0xd5, 0x1b, 0xda, 0x03, 0xb8,
	0x2b, 0x47, 0x6b, 0x9d, 0x7c, 0xd4, 0xea, 0x36, 0xf7, 0x55, 0x45, 0xd3, 0xe1, 0x9e, 0x1c, 0x65,
	0xb7, 0xd1, 0x68, 0x76, 0x28, 0x4e, 0x61, 0x32, 0x19, 0xa3, 0x79, 0x70, 0x76, 0xda, 0xdc, 0x57,
	0x8b, 0xda, 0x7d, 0xb8, 0x23, 0x47, 0x69, 0xec, 0x9e, 0x34, 0x9a, 0x6d, 0xb5, 0xb4, 0xf3, 0x09,
	0x6c, 0x34, 0xdd, 0xf1, 0x50, 0xac, 0xeb, 0xe4, 0x8b, 0x59, 0x83, 0xd5, 0xe3, 0xce, 0x91, 0x89,
	0x20, 0xb3, 0xe1, 0x78, 0x01, 0x51, 0x6f, 0x68, 0xeb, 0x70, 0x33, 0x01, 0x1a, 0x63, 0xd7, 0xb5,
	0xdd, 0x0b, 0x55, 0xd1, 0x36, 0x40, 0x4b, 0xc0, 0xc8, 0x18, 0x0a, 0x2f, 0xec, 0xfc, 0xbe, 0x02,
	0x1b, 0xac, 0x44, 0x42, 0xe4, 0x16, 0x0a, 0xca, 0x0e, 0xbc, 0xf5, 0x7c, 0xf7, 0x64, 0xbf, 0xdd,
	0x34, 0x33, 0x33, 0xec, 0xfe, 0xb0, 0xd3, 0xcc, 0x30, 0xec, 0x21, 0x3c, 0x98, 0x82, 0xcb, 0x58,
	0xa2, 0x2a, 0x33, 0xd0, 0x18, 0x57, 0xd4, 0xc2, 0xce, 0xa7, 0x71, 0x01, 0x38, 0x97, 0xd8, 0xed,
	0x69, 0x63, 0x6f, 0xc1, 0xad, 0x74, 0xf3, 0x69, 0xeb, 0xe4, 0xb0, 0xdd, 0xe4, 0x8b, 0x4e, 0xb5,
	0x74, 0x9b, 0xbb, 0xc7, 0x6a, 0x61, 0xe7, 0xcf, 0x15, 0xb8, 0x29, 0xf0, 0x94, 0xb3, 0xf3, 0x16,
	0xaf, 0x2b, 0x36, 0x3b, 0x03, 0xb3, 0xe5, 0x5e, 0x5a, 0x8e, 0xdd, 0xe7, 0xfc, 0x8c, 0xa0, 0x6d,
	0xdb, 0x1d, 0x98, 0xc7, 0x76, 0x4f, 0x55, 0xb4, 0x4d, 0x58, 0x8b, 0xc1, 0x58, 0xdb, 0x6d, 0x1e,
	0xd9, 0x8e, 0xa3, 0x16, 0x70, 0x53, 0xa2, 0x06, 0x96, 0xb7, 0x53, 0x8b, 0x29, 0x22, 0x2d, 0x37,
	0x24, 0x54, 0xbb, 0xab, 0x25, 0xba, 0xf9, 0xf1, 0xfc, 0x9a, 0x27, 0xfb, 0xe6, 0xde, 0x59, 0xd7,
	0x6c, 0xb7, 0x4e, 0x8e, 0x5a, 0x27, 0x87, 0xe6, 0x71, 0xab, 0xa1, 0x2e, 0xec, 0x7c, 0x06, 0xb5,
	0xa4, 0x98, 0x6b, 0xec, 0x84, 0xda, 0x76, 0xfc, 0x8e, 0x84, 0x01, 0x24, 0x33, 0x8d, 0x9b, 0x3e,
	0xb6, 0x5d, 0xce, 0x84, 0x14, 0xb8, 0xed, 0x05, 0xa1, 0x5a, 0xc8, 0xc3, 0x4f, 0xad, 0x21, 0x51,
	0x8b, 0x3b, 0xfd, 0xf8, 0x15, 0x46, 0x5c, 0x11, 0x7e, 0x1b, 0x36, 0x33, 0x20, 0x61, 0xd8, 0xed,
	0xd4, 0xcb, 0x16, 0x6c, 0xc4, 0xcd, 0x55, 0x15, 0x59, 0xd3, 0xf1, 0x6e, 0xb7, 0xf1, 0x5c, 0x2d,
	0xec, 0x1c, 0x40, 0x25, 0x2e, 0xd6, 0xd1, 0x00, 0x16, 0x2d, 0x0c, 0x19, 0xa9, 0x37, 0xe8, 0xef,
	0x11, 0xf9, 0x09, 0xe9, 0x85, 0xaa, 0x42, 0x7f, 0xf7, 0x89, 0x43, 0x42, 0xa2, 0x16, 0xe8, 0xef,
	0x1e, 0x66, 0x7a, 0xd4, 0xa2, 0x56, 0x81, 0x05, 0xac, 0x91, 0x51, 0x4b, 0x3b, 0x3f, 0x82, 0xcd,
	0x09, 0x59, 0x56, 0xad, 0x0c, 0xa5, 0xd6, 0x7e, 0xbb, 0xa9, 0xde, 0xa0, 0xf8, 0x7b, 0xcd, 0xc3,
	0xd6, 0x89, 0xaa, 0x50, 0x60, 0xf7, 0xc5, 0x8b, 0xb6, 0x5a, 0xa0, 0xbf, 0xda, 0xbb, 0xa7, 0x5d,
	0xb5, 0x48, 0x49, 0x77, 0xce, 0x4e, 0x5a, 0xa7, 0xcf, 0xd5, 0x12, 0xfd, 0x7d, 0xd0, 0xc2, 0xdf,
	0x0b, 0x3b, 0xdf, 0x85, 0xfa, 0xe4, 0xd4, 0x31, 0xbd, 0x91, 0x35, 0x3a, 0x47, 0xc7, 0x76, 0xcf,
	0x7c, 0xd1, 0x69, 0x9e, 0xb0, 0x4b, 0x1b, 0x07, 0x34, 0xda, 0x2f, 0x4e, 0x9b, 0xaa, 0xb2, 0x33,
	0x84, 0x0d, 0x79, 0xa2, 0x87, 0xca, 0x5a, 0x23, 0x3e, 0x8e, 0x27, 0xde, 0xc8, 0xb5, 0xe8, 0xb5,
	0x4f, 0x83, 0x95, 0x04, 0x6a, 0x58, 0xee, 0x40, 0x55, 0xd2, 0xb0, 0x91, 0xe5, 0xf6, 0xd5, 0x02,
	0xdd, 0xe9, 0x86, 0x78, 0x98, 0xe9, 0x35, 0x4e, 0x2d, 0xee, 0xf8, 0x50, 0x8e, 0x42, 0x99, 0x5a,
	0x15, 0x96, 0x92, 0x2d, 0x5a, 0x85, 0x2a, 0x0b, 0x21, 0x9a, 0xb4, 0x4d, 0x55, 0xb4, 0x15, 0x00,
	0xcc, 0xae, 0xb3, 0xef, 0x02, 0x9d, 0x7a, 0x87, 0xc5, 0x09, 0x19, 0xa4, 0x48, 0xc5, 0x38, 0x11,
	0x6b, 0x06, 0x2c, 0x51, 0x3a, 0xfb, 0xa4, 0x3f, 0x8e, 0xe8, 0x2c, 0xec, 0x1c, 0x41, 0x95, 0x72,
	0x9f, 0xab, 0x25, 0xda, 0xa9, 0x73, 0x74, 0x9c, 0x51, 0x48, 0x94, 0x76, 0x04, 0x64, 0xba, 0x68,
	0x1d, 0x6e, 0x26, 0x68, 0xa7, 0xe3, 0x5e, 0x8f, 0x04, 0x81, 0x5a, 0xd8, 0xf9, 0x19, 0x37, 0xad,
	0x91, 0xb2, 0xd7, 0xee, 0x41, 0x7d, 0xaf, 0xbd, 0xdb, 0x38, 0x6a, 0xb7, 0x4e, 0xbb, 0xb2, 0x83,
	0xbf, 0x0e, 0x37, 0x33, 0xed, 0x9d, 0x23, 0x55, 0xa1, 0xd2, 0x9a, 0x01, 0x47, 0x87, 0x4c, 0x2d,
	0xd0, 0x73, 0x9b, 0x69, 0xdc, 0x7b, 0xd1, 0x7d, 0xae, 0x16, 0x77, 0xfe, 0x42, 0x81, 0xd5, 0x78,
	0x78, 0x2e, 0x41, 0xf7, 0xe1, 0x4e, 0x82, 0x2c, 0x35, 0x14, 0xa9, 0x29, 0x72, 0x8c, 0xe6, 0xc1,
	0x41, 0xb3, 0xd1, 0x6d, 0x7d, 0x44, 0x35, 0xd0, 0x1d, 0xd8, 0xca, 0xb7, 0xff, 0xa0, 0xd3, 0x32,
	0xd0, 0x3e, 0xc8, 0x5a, 0x3f, 0xde, 0x6d, 0x75, 0x5b, 0x27, 0x87, 0x6a, 0x91, 0xaa, 0xbd, 0x5c,
	0xab, 0xd1, 0x6c, 0xec, 0xb6, 0xdb, 0xcd, 0x7d, 0xb5, 0xf4, 0xec, 0x9f, 0x75, 0xa8, 0x89, 0xd2,
	0x35, 0xd0, 0xfa, 0x98, 0x24, 0x48, 0x3f, 0xe4, 0xd4, 0x1e, 0xce, 0xf3, 0xd8, 0xf3, 0xb3, 0xfa,
	0x5b, 0xf3, 0xbd, 0x09, 0xd5, 0x6f, 0x68, 0x17, 0xa0, 0xe5, 0x1f, 0x63, 0x6a, 0xf9, 0xfe, 0xd2,
	0xc7, 0x9d, 0xf5, 0x47, 0x73, 0xe1, 0xe1, 0x40, 0x16, 0xa8, 0xd9, 0x87, 0x98, 0xda, 0x9b, 0xb2,
	0x28, 0x46, 0xf6, 0xf9, 0x67, 0xfd, 0xe1, 0x1c, 0x58, 0x38, 0xc4, 0x6f, 0x28, 0x70, 0x7b, 0xca,
	0xb3, 0x4b, 0x2d, 0x7f, 0xdf, 0x9e, 0xfe, 0x04, 0xb4, 0xfe, 0xff, 0xae, 0xd7, 0x01, 0x27, 0x71,
	0x85, 0x81, 0x1c, 0xe9, 0x33, 0x4d, 0xed, 0xab, 0x33, 0xb6, 0x25, 0xf5, 0x04, 0xb4, 0xfe, 0xee,
	0x35, 0xb0, 0x71, 0xe8, 0x20, 0x32, 0xfe, 0xd9, 0x67, 0x98, 0x5a, 0xbe, 0x0c, 0x65, 0xe2, 0x7b,
	0xd0, 0xfa, 0x3b, 0x73, 0xe3, 0xe2, 0xa0, 0x7d, 0xb8, 0x99, 0x7b, 0xb3, 0x29, 0x11, 0x53, 0xd9,
	0x0b, 0x50, 0x89, 0x98, 0xca, 0x9f, 0x7f, 0xa2, 0x98, 0xe6, 0x5f, 0x75, 0x4a, 0xc4, 0x54, 0xfa,
	0x4a, 0x54, 0x22, 0xa6, 0x13, 0x9e, 0x88, 0xde, 0xd0, 0xfc, 0xe8, 0xa5, 0x69, 0xf6, 0x48, 0xbc,
	0x3d, 0x81, 0x86, 0xe4, 0x54, 0xec, 0xcc, 0x8b, 0x1a, 0x1d, 0x8c, 0xec, 0x53, 0x11, 0xc9, 0xc1,
	0x90, 0xbc, 0x74, 0xa9, 0x3f, 0x9c, 0x03, 0x0b, 0x87, 0xf8, 0x14, 0x56, 0x33, 0xcf, 0x3e, 0xb4,
	0xaf, 0x48, 0xfa, 0x66, 0x5f, 0x95, 0xd4, 0xdf, 0x9c, 0x8d, 0x14, 0x1f, 0xbc, 0x29, 0x4f, 0x0a,
	0x24, 0x07, 0x6f, 0xfa, 0xa3, 0x09, 0xc9, 0xc1, 0x9b, 0xf1, 0x62, 0x41, 0xbf, 0xa1, 0xfd, 0x04,
	0x53, 0x52, 0xd9, 0xb7, 0x28, 0xda, 0x23, 0x19, 0x29, 0xc9, 0x53, 0x97, 0xfa, 0xe3, 0xf9, 0x10,
	0x23, 0x71, 0xcc, 0xbf, 0x27, 0xd1, 0x66, 0x69, 0x5d, 0xfe, 0x7c, 0xa5, 0xfe, 0x68, 0x2e, 0x3c,
	0x1c, 0xe8, 0xc7, 0xb0, 0x92, 0x7e, 0x75, 0xa6, 0xe9, 0xb2, 0xce, 0xe9, 0xb7, 0x6c, 0xf5, 0xaf,
	0xcc, 0xc4, 0x89, 0x38, 0x26, 0x79, 0x67, 0x20, 0xe1, 0x98, 0xfc, 0x79, 0x86, 0x84, 0x63, 0x13,
	0x9e, 0x2d, 0x30, 0xdd, 0x24, 0x2f, 0xbe, 0x97, 0xe8, 0xa6, 0x89, 0x8f, 0x04, 0x24, 0xba, 0x69,
	0x72, 0x45, 0xbf, 0x7e, 0x43, 0x3b, 0x83, 0x65, 0xb1, 0x60, 0x5c, 0xbb, 0x3f, 0x41, 0xb5, 0xc5,
	0x05, 0xf9, 0xf5, 0x07, 0x33, 0x30, 0x90, 0xec, 0x0f, 0xa0, 0x96, 0x2a, 0xe1, 0xd6, 0x1e, 0x4c,
	0x38, 0xf0, 0x49, 0xb9, 0x79, 0x5d, 0x9f, 0x85, 0x82, 0x94, 0x0d, 0xa8, 0x0a, 0x15, 0xd8, 0xda,
	0x1b, 0x92, 0x52, 0x5d, 0xb1, 0x48, 0xbc, 0x7e, 0x7f, 0x3a, 0x02, 0xd2, 0x3c, 0x80, 0x25, 0x5e,
	0xc4, 0xac, 0xdd, 0x96, 0x5b, 0x52, 0xb6, 0x9b, 0x77, 0x26, 0x37, 0x22, 0x9d, 0x4b, 0x4c, 0xc8,
	0xc8, 0x2a, 0x8d, 0xb5, 0x77, 0x64, 0xf2, 0x36, 0xa1, 0x4c, 0xba, 0xfe, 0xd5, 0xf9, 0x91, 0x71,
	0xdc, 0x36, 0x54, 0xe2, 0x1a, 0x5c, 0xed, 0xae, 0xac, 0x73, 0x22, 0xf8, 0xf7, 0xa6, 0x35, 0x47,
	0x22, 0x21, 0xd6, 0xae, 0x49, 0x44, 0x22, 0x53, 0x66, 0x27, 0x11, 0x89, 0x5c, 0xf1, 0x9b, 0x28,
	0x12, 0x9c, 0xee, 0x44, 0x91, 0x48, 0x08, 0xeb, 0xb3, 0x50, 0x04, 0x0d, 0x20, 0x14, 0xa6, 0xc9,
	0x35, 0x40, 0xba, 0x00, 0x4e, 0xae, 0x01, 0x32, 0xd5, 0x6d, 0x6c, 0xda, 0xa9, 0xda, 0x29, 0xc9,
	0xb4, 0xb3, 0x05, 0x5a, 0x75, 0x7d, 0x16, 0x4a, 0xe4, 0x16, 0xe4, 0x4a, 0x98, 0xe4, 0xde, 0x6b,
	0xae, 0x3a, 0x4a, 0xee, 0xbd, 0xe6, 0xab, 0xa1, 0xe2, 0xf9, 0x0b, 0x25, 0x2f, 0xd2, 0xf9, 0xa7,
	0x0a, 0x76, 0xe4, 0xf3, 0x4f, 0xd7, 0xe2, 0x30, 0x93, 0x99, 0xa9, 0x7b, 0x90, 0x98, 0xcc, 0x7c,
	0x09, 0x86, 0xc4, 0x64, 0xca, 0xca, 0x27, 0xd0, 0xea, 0x67, 0xf3, 0x7d, 0x12, 0xab, 0x2f, 0xa9,
	0xb1, 0xa8, 0x3f, 0x9c, 0x03, 0x0b, 0x87, 0x18, 0xc2, 0x2d, 0x59, 0x92, 0x5e, 0xcb, 0xab, 0xed,
	0x09, 0xa5, 0x01, 0xf5, 0xb7, 0xe7, 0xc4, 0x8c, 0x1d, 0xfc, 0x4c, 0xb2, 0x5e, 0xe6, 0xe0, 0xe7,
	0x4b, 0x03, 0x64, 0x0e, 0xbe, 0x2c, 0xeb, 0x8f, 0x06, 0x4b, 0x92, 0x7d, 0x96, 0x18, 0x2c, 0x79,
	0xc6, 0x5b, 0x62, 0xb0, 0x26, 0x25, 0xb3, 0x71, 0x39, 0xd9, 0xc4, 0xb4, 0x64, 0x39, 0x92, 0x8c,
	0xb7, 0x64, 0x39, 0xd2, 0x0c, 0x37, 0x6e, 0x90, 0x2c, 0xe7, 0xab, 0x3d, 0x9e, 0xb9, 0xc3, 0xd1,
	0x59, 0x7c, 0x7b, 0x4e, 0xcc, 0x68, 0x38, 0x59, 0xbe, 0x53, 0x32, 0xdc, 0x84, 0xac, 0xaf, 0x64,
	0xb8, 0x49, 0x09, 0x54, 0xa6, 0x01, 0x72, 0x49, 0x3f, 0x89, 0x06, 0x90, 0xa5, 0x3c, 0x25, 0x1a,
	0x40, 0x9e, 0x3f, 0x4c, 0x46, 0x11, 0xe3, 0xc6, 0x93, 0x46, 0xc9, 0x64, 0x19, 0x27, 0x8d, 0x92,
	0x4b, 0x0a, 0x46, 0xfe, 0x5e, 0x26, 0x07, 0x26, 0xf7, 0xf7, 0xf2, 0xa9, 0x43, 0xb9, 0xbf, 0x27,
	0x49, 0xa8, 0x31, 0x23, 0x3b, 0x21, 0x8f, 0xa1, 0xbd, 0x33, 0x7f, 0xc6, 0x43, 0x66, 0x64, 0xa7,
	0xa4, 0x47, 0x98, 0xba, 0xcb, 0x24, 0x03, 0x24, 0xea, 0x2e, 0x9f, 0xec, 0x90, 0xa8, 0x3b, 0x59,
	0x4e, 0x21, 0x72, 0xff, 0x24, 0xe1, 0x7d, 0xb9, 0xfb, 0x27, 0x4f, 0x3f, 0xd4, 0xdf, 0x99, 0x1b,
	0x97, 0x0e, 0xba, 0xf7, 0xde, 0x8f, 0xbe, 0x76, 0xe1, 0x39, 0x96, 0x7b, 0xf1, 0xe4, 0xfd, 0x67,
	0x61, 0xf8, 0xa4, 0xe7, 0x0d, 0x9f, 0xe2, 0x7f, 0x18, 0xeb, 0x79, 0xce, 0xd3, 0x80, 0x8c, 0x2e,
	0xed, 0x1e, 0x09, 0xb2, 0xff, 0x83, 0xec, 0xe5, 0x22, 0xa2, 0xbc, 0xf7, 0xbf, 0x01, 0x00, 0x00,
	0xff, 0xff, 0x84, 0x30, 0x6c, 0x22, 0xad, 0x4c, 0x00, 0x00,
}
