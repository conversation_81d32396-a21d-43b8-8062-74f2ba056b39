// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-listening-auto-play-logic_.proto

package channellisteningautoplaylogic // import "golang.52tt.com/protocol/app/channellisteningautoplaylogic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"
import channel "golang.52tt.com/protocol/app/channel"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ChannelPlayMode int32

const (
	ChannelPlayMode_chooseMode ChannelPlayMode = 0
	ChannelPlayMode_AutoMode   ChannelPlayMode = 1
	ChannelPlayMode_OrderMode  ChannelPlayMode = 2
)

var ChannelPlayMode_name = map[int32]string{
	0: "chooseMode",
	1: "AutoMode",
	2: "OrderMode",
}
var ChannelPlayMode_value = map[string]int32{
	"chooseMode": 0,
	"AutoMode":   1,
	"OrderMode":  2,
}

func (x ChannelPlayMode) String() string {
	return proto.EnumName(ChannelPlayMode_name, int32(x))
}
func (ChannelPlayMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{0}
}

type AutoPlaySwitch int32

const (
	AutoPlaySwitch_Close AutoPlaySwitch = 0
	AutoPlaySwitch_Open  AutoPlaySwitch = 1
)

var AutoPlaySwitch_name = map[int32]string{
	0: "Close",
	1: "Open",
}
var AutoPlaySwitch_value = map[string]int32{
	"Close": 0,
	"Open":  1,
}

func (x AutoPlaySwitch) String() string {
	return proto.EnumName(AutoPlaySwitch_name, int32(x))
}
func (AutoPlaySwitch) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{1}
}

type PlayStatus int32

const (
	PlayStatus_play  PlayStatus = 0
	PlayStatus_pause PlayStatus = 1
)

var PlayStatus_name = map[int32]string{
	0: "play",
	1: "pause",
}
var PlayStatus_value = map[string]int32{
	"play":  0,
	"pause": 1,
}

func (x PlayStatus) String() string {
	return proto.EnumName(PlayStatus_name, int32(x))
}
func (PlayStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{2}
}

// 自动播放的播放模式选择 顺序、单曲、随机
type PlayModeSelect int32

const (
	PlayModeSelect_ORDER_MODE   PlayModeSelect = 0
	PlayModeSelect_SINGLE_CYCLE PlayModeSelect = 1
	PlayModeSelect_RANDOM       PlayModeSelect = 2
)

var PlayModeSelect_name = map[int32]string{
	0: "ORDER_MODE",
	1: "SINGLE_CYCLE",
	2: "RANDOM",
}
var PlayModeSelect_value = map[string]int32{
	"ORDER_MODE":   0,
	"SINGLE_CYCLE": 1,
	"RANDOM":       2,
}

func (x PlayModeSelect) String() string {
	return proto.EnumName(PlayModeSelect_name, int32(x))
}
func (PlayModeSelect) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{3}
}

type WYYVipOpenDetailDto int32

const (
	WYYVipOpenDetailDto_ENUM_VIP_TYPE_NORMAL     WYYVipOpenDetailDto = 0
	WYYVipOpenDetailDto_ENUM_VIP_TYPE_BLACK_VIP  WYYVipOpenDetailDto = 6
	WYYVipOpenDetailDto_ENUM_VIP_TYPE_BLACK_SVIP WYYVipOpenDetailDto = 15
	WYYVipOpenDetailDto_ENUM_VIP_TYPE_CAR        WYYVipOpenDetailDto = 13
	WYYVipOpenDetailDto_ENUM_VIP_TYPE_WATCH      WYYVipOpenDetailDto = 16
	WYYVipOpenDetailDto_ENUM_VIP_TYPE_TV         WYYVipOpenDetailDto = 17
	WYYVipOpenDetailDto_ENUM_VIP_TYPE_RADIO      WYYVipOpenDetailDto = 18
)

var WYYVipOpenDetailDto_name = map[int32]string{
	0:  "ENUM_VIP_TYPE_NORMAL",
	6:  "ENUM_VIP_TYPE_BLACK_VIP",
	15: "ENUM_VIP_TYPE_BLACK_SVIP",
	13: "ENUM_VIP_TYPE_CAR",
	16: "ENUM_VIP_TYPE_WATCH",
	17: "ENUM_VIP_TYPE_TV",
	18: "ENUM_VIP_TYPE_RADIO",
}
var WYYVipOpenDetailDto_value = map[string]int32{
	"ENUM_VIP_TYPE_NORMAL":     0,
	"ENUM_VIP_TYPE_BLACK_VIP":  6,
	"ENUM_VIP_TYPE_BLACK_SVIP": 15,
	"ENUM_VIP_TYPE_CAR":        13,
	"ENUM_VIP_TYPE_WATCH":      16,
	"ENUM_VIP_TYPE_TV":         17,
	"ENUM_VIP_TYPE_RADIO":      18,
}

func (x WYYVipOpenDetailDto) String() string {
	return proto.EnumName(WYYVipOpenDetailDto_name, int32(x))
}
func (WYYVipOpenDetailDto) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{4}
}

// 战歌列表类型
type MusicListType int32

const (
	MusicListType_MusicListType_Default MusicListType = 0
	MusicListType_MusicListType_NC      MusicListType = 1
)

var MusicListType_name = map[int32]string{
	0: "MusicListType_Default",
	1: "MusicListType_NC",
}
var MusicListType_value = map[string]int32{
	"MusicListType_Default": 0,
	"MusicListType_NC":      1,
}

func (x MusicListType) String() string {
	return proto.EnumName(MusicListType_name, int32(x))
}
func (MusicListType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{5}
}

type SongListType int32

const (
	SongListType_ENUM_SONG_LIST_TYPE_LIKE SongListType = 0
	SongListType_ENUM_SONG_LIST_TYPE_RCMD SongListType = 1
	SongListType_ENUM_SONG_LIST_TYPE_TOP  SongListType = 2
	SongListType_ENUM_SONG_LIST_TYPE_FM   SongListType = 3
)

var SongListType_name = map[int32]string{
	0: "ENUM_SONG_LIST_TYPE_LIKE",
	1: "ENUM_SONG_LIST_TYPE_RCMD",
	2: "ENUM_SONG_LIST_TYPE_TOP",
	3: "ENUM_SONG_LIST_TYPE_FM",
}
var SongListType_value = map[string]int32{
	"ENUM_SONG_LIST_TYPE_LIKE": 0,
	"ENUM_SONG_LIST_TYPE_RCMD": 1,
	"ENUM_SONG_LIST_TYPE_TOP":  2,
	"ENUM_SONG_LIST_TYPE_FM":   3,
}

func (x SongListType) String() string {
	return proto.EnumName(SongListType_name, int32(x))
}
func (SongListType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{6}
}

type ReportChannelAutoSongProgressReq_ProgressType int32

const (
	ReportChannelAutoSongProgressReq_start ReportChannelAutoSongProgressReq_ProgressType = 0
	ReportChannelAutoSongProgressReq_end   ReportChannelAutoSongProgressReq_ProgressType = 1
)

var ReportChannelAutoSongProgressReq_ProgressType_name = map[int32]string{
	0: "start",
	1: "end",
}
var ReportChannelAutoSongProgressReq_ProgressType_value = map[string]int32{
	"start": 0,
	"end":   1,
}

func (x ReportChannelAutoSongProgressReq_ProgressType) String() string {
	return proto.EnumName(ReportChannelAutoSongProgressReq_ProgressType_name, int32(x))
}
func (ReportChannelAutoSongProgressReq_ProgressType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{13, 0}
}

// 用户进房获取播放状态
type ChannelPlayStatusReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChannelPlayStatusReq) Reset()         { *m = ChannelPlayStatusReq{} }
func (m *ChannelPlayStatusReq) String() string { return proto.CompactTextString(m) }
func (*ChannelPlayStatusReq) ProtoMessage()    {}
func (*ChannelPlayStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{0}
}
func (m *ChannelPlayStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelPlayStatusReq.Unmarshal(m, b)
}
func (m *ChannelPlayStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelPlayStatusReq.Marshal(b, m, deterministic)
}
func (dst *ChannelPlayStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelPlayStatusReq.Merge(dst, src)
}
func (m *ChannelPlayStatusReq) XXX_Size() int {
	return xxx_messageInfo_ChannelPlayStatusReq.Size(m)
}
func (m *ChannelPlayStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelPlayStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelPlayStatusReq proto.InternalMessageInfo

func (m *ChannelPlayStatusReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelPlayStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ChannelPlayStatusResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Mode                 ChannelPlayMode `protobuf:"varint,2,opt,name=mode,proto3,enum=ga.channellisteningautoplaylogic.ChannelPlayMode" json:"mode,omitempty"`
	Switch               AutoPlaySwitch  `protobuf:"varint,3,opt,name=switch,proto3,enum=ga.channellisteningautoplaylogic.AutoPlaySwitch" json:"switch,omitempty"`
	Info                 *AutoPlayInfo   `protobuf:"bytes,4,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ChannelPlayStatusResp) Reset()         { *m = ChannelPlayStatusResp{} }
func (m *ChannelPlayStatusResp) String() string { return proto.CompactTextString(m) }
func (*ChannelPlayStatusResp) ProtoMessage()    {}
func (*ChannelPlayStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{1}
}
func (m *ChannelPlayStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelPlayStatusResp.Unmarshal(m, b)
}
func (m *ChannelPlayStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelPlayStatusResp.Marshal(b, m, deterministic)
}
func (dst *ChannelPlayStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelPlayStatusResp.Merge(dst, src)
}
func (m *ChannelPlayStatusResp) XXX_Size() int {
	return xxx_messageInfo_ChannelPlayStatusResp.Size(m)
}
func (m *ChannelPlayStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelPlayStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelPlayStatusResp proto.InternalMessageInfo

func (m *ChannelPlayStatusResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChannelPlayStatusResp) GetMode() ChannelPlayMode {
	if m != nil {
		return m.Mode
	}
	return ChannelPlayMode_chooseMode
}

func (m *ChannelPlayStatusResp) GetSwitch() AutoPlaySwitch {
	if m != nil {
		return m.Switch
	}
	return AutoPlaySwitch_Close
}

func (m *ChannelPlayStatusResp) GetInfo() *AutoPlayInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type AutoPlayInfo struct {
	Menu                 *ListeningAutoPlayRcmdMenu `protobuf:"bytes,1,opt,name=menu,proto3" json:"menu,omitempty"`
	Key                  int64                      `protobuf:"varint,2,opt,name=key,proto3" json:"key,omitempty"`
	PlayTime             int64                      `protobuf:"varint,3,opt,name=play_time,json=playTime,proto3" json:"play_time,omitempty"`
	Status               PlayStatus                 `protobuf:"varint,4,opt,name=status,proto3,enum=ga.channellisteningautoplaylogic.PlayStatus" json:"status,omitempty"`
	Volume               uint32                     `protobuf:"varint,5,opt,name=Volume,proto3" json:"Volume,omitempty"`
	PlayMode             uint32                     `protobuf:"varint,6,opt,name=play_mode,json=playMode,proto3" json:"play_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *AutoPlayInfo) Reset()         { *m = AutoPlayInfo{} }
func (m *AutoPlayInfo) String() string { return proto.CompactTextString(m) }
func (*AutoPlayInfo) ProtoMessage()    {}
func (*AutoPlayInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{2}
}
func (m *AutoPlayInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AutoPlayInfo.Unmarshal(m, b)
}
func (m *AutoPlayInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AutoPlayInfo.Marshal(b, m, deterministic)
}
func (dst *AutoPlayInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AutoPlayInfo.Merge(dst, src)
}
func (m *AutoPlayInfo) XXX_Size() int {
	return xxx_messageInfo_AutoPlayInfo.Size(m)
}
func (m *AutoPlayInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AutoPlayInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AutoPlayInfo proto.InternalMessageInfo

func (m *AutoPlayInfo) GetMenu() *ListeningAutoPlayRcmdMenu {
	if m != nil {
		return m.Menu
	}
	return nil
}

func (m *AutoPlayInfo) GetKey() int64 {
	if m != nil {
		return m.Key
	}
	return 0
}

func (m *AutoPlayInfo) GetPlayTime() int64 {
	if m != nil {
		return m.PlayTime
	}
	return 0
}

func (m *AutoPlayInfo) GetStatus() PlayStatus {
	if m != nil {
		return m.Status
	}
	return PlayStatus_play
}

func (m *AutoPlayInfo) GetVolume() uint32 {
	if m != nil {
		return m.Volume
	}
	return 0
}

func (m *AutoPlayInfo) GetPlayMode() uint32 {
	if m != nil {
		return m.PlayMode
	}
	return 0
}

// 与2060接口协议一致
type MusicInfo struct {
	ClientKey            string   `protobuf:"bytes,1,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Author               string   `protobuf:"bytes,3,opt,name=author,proto3" json:"author,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	Volume               uint32   `protobuf:"varint,5,opt,name=volume,proto3" json:"volume,omitempty"`
	Key                  int64    `protobuf:"varint,6,opt,name=key,proto3" json:"key,omitempty"`
	Account              string   `protobuf:"bytes,7,opt,name=account,proto3" json:"account,omitempty"`
	IsLocal              uint32   `protobuf:"varint,8,opt,name=is_local,json=isLocal,proto3" json:"is_local,omitempty"`
	Status               uint32   `protobuf:"varint,9,opt,name=status,proto3" json:"status,omitempty"`
	Nickname             string   `protobuf:"bytes,10,opt,name=nickname,proto3" json:"nickname,omitempty"`
	MusicType            uint32   `protobuf:"varint,11,opt,name=music_type,json=musicType,proto3" json:"music_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicInfo) Reset()         { *m = MusicInfo{} }
func (m *MusicInfo) String() string { return proto.CompactTextString(m) }
func (*MusicInfo) ProtoMessage()    {}
func (*MusicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{3}
}
func (m *MusicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicInfo.Unmarshal(m, b)
}
func (m *MusicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicInfo.Marshal(b, m, deterministic)
}
func (dst *MusicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicInfo.Merge(dst, src)
}
func (m *MusicInfo) XXX_Size() int {
	return xxx_messageInfo_MusicInfo.Size(m)
}
func (m *MusicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MusicInfo proto.InternalMessageInfo

func (m *MusicInfo) GetClientKey() string {
	if m != nil {
		return m.ClientKey
	}
	return ""
}

func (m *MusicInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MusicInfo) GetAuthor() string {
	if m != nil {
		return m.Author
	}
	return ""
}

func (m *MusicInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MusicInfo) GetVolume() uint32 {
	if m != nil {
		return m.Volume
	}
	return 0
}

func (m *MusicInfo) GetKey() int64 {
	if m != nil {
		return m.Key
	}
	return 0
}

func (m *MusicInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *MusicInfo) GetIsLocal() uint32 {
	if m != nil {
		return m.IsLocal
	}
	return 0
}

func (m *MusicInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *MusicInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *MusicInfo) GetMusicType() uint32 {
	if m != nil {
		return m.MusicType
	}
	return 0
}

// 切换自动播放/点歌模式开关
type SwitchChannelPlayReq struct {
	BaseReq              *app.BaseReq   `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32         `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Switch               AutoPlaySwitch `protobuf:"varint,3,opt,name=switch,proto3,enum=ga.channellisteningautoplaylogic.AutoPlaySwitch" json:"switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SwitchChannelPlayReq) Reset()         { *m = SwitchChannelPlayReq{} }
func (m *SwitchChannelPlayReq) String() string { return proto.CompactTextString(m) }
func (*SwitchChannelPlayReq) ProtoMessage()    {}
func (*SwitchChannelPlayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{4}
}
func (m *SwitchChannelPlayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChannelPlayReq.Unmarshal(m, b)
}
func (m *SwitchChannelPlayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChannelPlayReq.Marshal(b, m, deterministic)
}
func (dst *SwitchChannelPlayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChannelPlayReq.Merge(dst, src)
}
func (m *SwitchChannelPlayReq) XXX_Size() int {
	return xxx_messageInfo_SwitchChannelPlayReq.Size(m)
}
func (m *SwitchChannelPlayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChannelPlayReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChannelPlayReq proto.InternalMessageInfo

func (m *SwitchChannelPlayReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SwitchChannelPlayReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SwitchChannelPlayReq) GetSwitch() AutoPlaySwitch {
	if m != nil {
		return m.Switch
	}
	return AutoPlaySwitch_Close
}

type SwitchChannelPlayResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SwitchChannelPlayResp) Reset()         { *m = SwitchChannelPlayResp{} }
func (m *SwitchChannelPlayResp) String() string { return proto.CompactTextString(m) }
func (*SwitchChannelPlayResp) ProtoMessage()    {}
func (*SwitchChannelPlayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{5}
}
func (m *SwitchChannelPlayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChannelPlayResp.Unmarshal(m, b)
}
func (m *SwitchChannelPlayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChannelPlayResp.Marshal(b, m, deterministic)
}
func (dst *SwitchChannelPlayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChannelPlayResp.Merge(dst, src)
}
func (m *SwitchChannelPlayResp) XXX_Size() int {
	return xxx_messageInfo_SwitchChannelPlayResp.Size(m)
}
func (m *SwitchChannelPlayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChannelPlayResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChannelPlayResp proto.InternalMessageInfo

func (m *SwitchChannelPlayResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取推荐歌曲歌单
type ChannelRcmdMusicMenuReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChannelRcmdMusicMenuReq) Reset()         { *m = ChannelRcmdMusicMenuReq{} }
func (m *ChannelRcmdMusicMenuReq) String() string { return proto.CompactTextString(m) }
func (*ChannelRcmdMusicMenuReq) ProtoMessage()    {}
func (*ChannelRcmdMusicMenuReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{6}
}
func (m *ChannelRcmdMusicMenuReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRcmdMusicMenuReq.Unmarshal(m, b)
}
func (m *ChannelRcmdMusicMenuReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRcmdMusicMenuReq.Marshal(b, m, deterministic)
}
func (dst *ChannelRcmdMusicMenuReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRcmdMusicMenuReq.Merge(dst, src)
}
func (m *ChannelRcmdMusicMenuReq) XXX_Size() int {
	return xxx_messageInfo_ChannelRcmdMusicMenuReq.Size(m)
}
func (m *ChannelRcmdMusicMenuReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRcmdMusicMenuReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRcmdMusicMenuReq proto.InternalMessageInfo

func (m *ChannelRcmdMusicMenuReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelRcmdMusicMenuReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ChannelRcmdMusicMenuResp struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Menu                 *ListeningAutoPlayRcmdMenu `protobuf:"bytes,2,opt,name=menu,proto3" json:"menu,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *ChannelRcmdMusicMenuResp) Reset()         { *m = ChannelRcmdMusicMenuResp{} }
func (m *ChannelRcmdMusicMenuResp) String() string { return proto.CompactTextString(m) }
func (*ChannelRcmdMusicMenuResp) ProtoMessage()    {}
func (*ChannelRcmdMusicMenuResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{7}
}
func (m *ChannelRcmdMusicMenuResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRcmdMusicMenuResp.Unmarshal(m, b)
}
func (m *ChannelRcmdMusicMenuResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRcmdMusicMenuResp.Marshal(b, m, deterministic)
}
func (dst *ChannelRcmdMusicMenuResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRcmdMusicMenuResp.Merge(dst, src)
}
func (m *ChannelRcmdMusicMenuResp) XXX_Size() int {
	return xxx_messageInfo_ChannelRcmdMusicMenuResp.Size(m)
}
func (m *ChannelRcmdMusicMenuResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRcmdMusicMenuResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRcmdMusicMenuResp proto.InternalMessageInfo

func (m *ChannelRcmdMusicMenuResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChannelRcmdMusicMenuResp) GetMenu() *ListeningAutoPlayRcmdMenu {
	if m != nil {
		return m.Menu
	}
	return nil
}

type ListeningAutoPlayRcmdMenu struct {
	Id                   string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Bg                   string       `protobuf:"bytes,3,opt,name=bg,proto3" json:"bg,omitempty"`
	Musics               []*MusicInfo `protobuf:"bytes,4,rep,name=musics,proto3" json:"musics,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListeningAutoPlayRcmdMenu) Reset()         { *m = ListeningAutoPlayRcmdMenu{} }
func (m *ListeningAutoPlayRcmdMenu) String() string { return proto.CompactTextString(m) }
func (*ListeningAutoPlayRcmdMenu) ProtoMessage()    {}
func (*ListeningAutoPlayRcmdMenu) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{8}
}
func (m *ListeningAutoPlayRcmdMenu) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningAutoPlayRcmdMenu.Unmarshal(m, b)
}
func (m *ListeningAutoPlayRcmdMenu) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningAutoPlayRcmdMenu.Marshal(b, m, deterministic)
}
func (dst *ListeningAutoPlayRcmdMenu) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningAutoPlayRcmdMenu.Merge(dst, src)
}
func (m *ListeningAutoPlayRcmdMenu) XXX_Size() int {
	return xxx_messageInfo_ListeningAutoPlayRcmdMenu.Size(m)
}
func (m *ListeningAutoPlayRcmdMenu) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningAutoPlayRcmdMenu.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningAutoPlayRcmdMenu proto.InternalMessageInfo

func (m *ListeningAutoPlayRcmdMenu) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ListeningAutoPlayRcmdMenu) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ListeningAutoPlayRcmdMenu) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

func (m *ListeningAutoPlayRcmdMenu) GetMusics() []*MusicInfo {
	if m != nil {
		return m.Musics
	}
	return nil
}

// 设置音量
type SetVolumeReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Volume               uint32       `protobuf:"varint,3,opt,name=volume,proto3" json:"volume,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetVolumeReq) Reset()         { *m = SetVolumeReq{} }
func (m *SetVolumeReq) String() string { return proto.CompactTextString(m) }
func (*SetVolumeReq) ProtoMessage()    {}
func (*SetVolumeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{9}
}
func (m *SetVolumeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVolumeReq.Unmarshal(m, b)
}
func (m *SetVolumeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVolumeReq.Marshal(b, m, deterministic)
}
func (dst *SetVolumeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVolumeReq.Merge(dst, src)
}
func (m *SetVolumeReq) XXX_Size() int {
	return xxx_messageInfo_SetVolumeReq.Size(m)
}
func (m *SetVolumeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVolumeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetVolumeReq proto.InternalMessageInfo

func (m *SetVolumeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetVolumeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetVolumeReq) GetVolume() uint32 {
	if m != nil {
		return m.Volume
	}
	return 0
}

type SetVolumeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetVolumeResp) Reset()         { *m = SetVolumeResp{} }
func (m *SetVolumeResp) String() string { return proto.CompactTextString(m) }
func (*SetVolumeResp) ProtoMessage()    {}
func (*SetVolumeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{10}
}
func (m *SetVolumeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVolumeResp.Unmarshal(m, b)
}
func (m *SetVolumeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVolumeResp.Marshal(b, m, deterministic)
}
func (dst *SetVolumeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVolumeResp.Merge(dst, src)
}
func (m *SetVolumeResp) XXX_Size() int {
	return xxx_messageInfo_SetVolumeResp.Size(m)
}
func (m *SetVolumeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVolumeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetVolumeResp proto.InternalMessageInfo

func (m *SetVolumeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 自动模式切歌
type CutAutoModeSongReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MusicMenuId          string       `protobuf:"bytes,3,opt,name=music_menu_id,json=musicMenuId,proto3" json:"music_menu_id,omitempty"`
	Key                  int64        `protobuf:"varint,4,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CutAutoModeSongReq) Reset()         { *m = CutAutoModeSongReq{} }
func (m *CutAutoModeSongReq) String() string { return proto.CompactTextString(m) }
func (*CutAutoModeSongReq) ProtoMessage()    {}
func (*CutAutoModeSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{11}
}
func (m *CutAutoModeSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CutAutoModeSongReq.Unmarshal(m, b)
}
func (m *CutAutoModeSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CutAutoModeSongReq.Marshal(b, m, deterministic)
}
func (dst *CutAutoModeSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CutAutoModeSongReq.Merge(dst, src)
}
func (m *CutAutoModeSongReq) XXX_Size() int {
	return xxx_messageInfo_CutAutoModeSongReq.Size(m)
}
func (m *CutAutoModeSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CutAutoModeSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_CutAutoModeSongReq proto.InternalMessageInfo

func (m *CutAutoModeSongReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CutAutoModeSongReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CutAutoModeSongReq) GetMusicMenuId() string {
	if m != nil {
		return m.MusicMenuId
	}
	return ""
}

func (m *CutAutoModeSongReq) GetKey() int64 {
	if m != nil {
		return m.Key
	}
	return 0
}

type CutAutoModeSongResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CutAutoModeSongResp) Reset()         { *m = CutAutoModeSongResp{} }
func (m *CutAutoModeSongResp) String() string { return proto.CompactTextString(m) }
func (*CutAutoModeSongResp) ProtoMessage()    {}
func (*CutAutoModeSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{12}
}
func (m *CutAutoModeSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CutAutoModeSongResp.Unmarshal(m, b)
}
func (m *CutAutoModeSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CutAutoModeSongResp.Marshal(b, m, deterministic)
}
func (dst *CutAutoModeSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CutAutoModeSongResp.Merge(dst, src)
}
func (m *CutAutoModeSongResp) XXX_Size() int {
	return xxx_messageInfo_CutAutoModeSongResp.Size(m)
}
func (m *CutAutoModeSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CutAutoModeSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_CutAutoModeSongResp proto.InternalMessageInfo

func (m *CutAutoModeSongResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 歌曲进度上报
type ReportChannelAutoSongProgressReq struct {
	BaseReq              *app.BaseReq                                  `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                                        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MusicMenuId          string                                        `protobuf:"bytes,3,opt,name=music_menu_id,json=musicMenuId,proto3" json:"music_menu_id,omitempty"`
	Key                  int64                                         `protobuf:"varint,4,opt,name=key,proto3" json:"key,omitempty"`
	Progress             ReportChannelAutoSongProgressReq_ProgressType `protobuf:"varint,5,opt,name=progress,proto3,enum=ga.channellisteningautoplaylogic.ReportChannelAutoSongProgressReq_ProgressType" json:"progress,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *ReportChannelAutoSongProgressReq) Reset()         { *m = ReportChannelAutoSongProgressReq{} }
func (m *ReportChannelAutoSongProgressReq) String() string { return proto.CompactTextString(m) }
func (*ReportChannelAutoSongProgressReq) ProtoMessage()    {}
func (*ReportChannelAutoSongProgressReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{13}
}
func (m *ReportChannelAutoSongProgressReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportChannelAutoSongProgressReq.Unmarshal(m, b)
}
func (m *ReportChannelAutoSongProgressReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportChannelAutoSongProgressReq.Marshal(b, m, deterministic)
}
func (dst *ReportChannelAutoSongProgressReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportChannelAutoSongProgressReq.Merge(dst, src)
}
func (m *ReportChannelAutoSongProgressReq) XXX_Size() int {
	return xxx_messageInfo_ReportChannelAutoSongProgressReq.Size(m)
}
func (m *ReportChannelAutoSongProgressReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportChannelAutoSongProgressReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportChannelAutoSongProgressReq proto.InternalMessageInfo

func (m *ReportChannelAutoSongProgressReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportChannelAutoSongProgressReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReportChannelAutoSongProgressReq) GetMusicMenuId() string {
	if m != nil {
		return m.MusicMenuId
	}
	return ""
}

func (m *ReportChannelAutoSongProgressReq) GetKey() int64 {
	if m != nil {
		return m.Key
	}
	return 0
}

func (m *ReportChannelAutoSongProgressReq) GetProgress() ReportChannelAutoSongProgressReq_ProgressType {
	if m != nil {
		return m.Progress
	}
	return ReportChannelAutoSongProgressReq_start
}

type ReportChannelAutoSongProgressResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReportChannelAutoSongProgressResp) Reset()         { *m = ReportChannelAutoSongProgressResp{} }
func (m *ReportChannelAutoSongProgressResp) String() string { return proto.CompactTextString(m) }
func (*ReportChannelAutoSongProgressResp) ProtoMessage()    {}
func (*ReportChannelAutoSongProgressResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{14}
}
func (m *ReportChannelAutoSongProgressResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportChannelAutoSongProgressResp.Unmarshal(m, b)
}
func (m *ReportChannelAutoSongProgressResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportChannelAutoSongProgressResp.Marshal(b, m, deterministic)
}
func (dst *ReportChannelAutoSongProgressResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportChannelAutoSongProgressResp.Merge(dst, src)
}
func (m *ReportChannelAutoSongProgressResp) XXX_Size() int {
	return xxx_messageInfo_ReportChannelAutoSongProgressResp.Size(m)
}
func (m *ReportChannelAutoSongProgressResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportChannelAutoSongProgressResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportChannelAutoSongProgressResp proto.InternalMessageInfo

func (m *ReportChannelAutoSongProgressResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 切换自动播放/点歌模式
type SetChannelPlayModeReq struct {
	BaseReq              *app.BaseReq    `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32          `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Mode                 ChannelPlayMode `protobuf:"varint,3,opt,name=mode,proto3,enum=ga.channellisteningautoplaylogic.ChannelPlayMode" json:"mode,omitempty"`
	MusicMenuId          string          `protobuf:"bytes,4,opt,name=music_menu_id,json=musicMenuId,proto3" json:"music_menu_id,omitempty"`
	Key                  int64           `protobuf:"varint,5,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SetChannelPlayModeReq) Reset()         { *m = SetChannelPlayModeReq{} }
func (m *SetChannelPlayModeReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelPlayModeReq) ProtoMessage()    {}
func (*SetChannelPlayModeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{15}
}
func (m *SetChannelPlayModeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelPlayModeReq.Unmarshal(m, b)
}
func (m *SetChannelPlayModeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelPlayModeReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelPlayModeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelPlayModeReq.Merge(dst, src)
}
func (m *SetChannelPlayModeReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelPlayModeReq.Size(m)
}
func (m *SetChannelPlayModeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelPlayModeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelPlayModeReq proto.InternalMessageInfo

func (m *SetChannelPlayModeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetChannelPlayModeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelPlayModeReq) GetMode() ChannelPlayMode {
	if m != nil {
		return m.Mode
	}
	return ChannelPlayMode_chooseMode
}

func (m *SetChannelPlayModeReq) GetMusicMenuId() string {
	if m != nil {
		return m.MusicMenuId
	}
	return ""
}

func (m *SetChannelPlayModeReq) GetKey() int64 {
	if m != nil {
		return m.Key
	}
	return 0
}

type SetChannelPlayModeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetChannelPlayModeResp) Reset()         { *m = SetChannelPlayModeResp{} }
func (m *SetChannelPlayModeResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelPlayModeResp) ProtoMessage()    {}
func (*SetChannelPlayModeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{16}
}
func (m *SetChannelPlayModeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelPlayModeResp.Unmarshal(m, b)
}
func (m *SetChannelPlayModeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelPlayModeResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelPlayModeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelPlayModeResp.Merge(dst, src)
}
func (m *SetChannelPlayModeResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelPlayModeResp.Size(m)
}
func (m *SetChannelPlayModeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelPlayModeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelPlayModeResp proto.InternalMessageInfo

func (m *SetChannelPlayModeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 播放/暂停切换
type SetChannelPlayStatusReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MusicMenuId          string       `protobuf:"bytes,3,opt,name=music_menu_id,json=musicMenuId,proto3" json:"music_menu_id,omitempty"`
	Key                  int64        `protobuf:"varint,4,opt,name=key,proto3" json:"key,omitempty"`
	Status               PlayStatus   `protobuf:"varint,5,opt,name=status,proto3,enum=ga.channellisteningautoplaylogic.PlayStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetChannelPlayStatusReq) Reset()         { *m = SetChannelPlayStatusReq{} }
func (m *SetChannelPlayStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelPlayStatusReq) ProtoMessage()    {}
func (*SetChannelPlayStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{17}
}
func (m *SetChannelPlayStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelPlayStatusReq.Unmarshal(m, b)
}
func (m *SetChannelPlayStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelPlayStatusReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelPlayStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelPlayStatusReq.Merge(dst, src)
}
func (m *SetChannelPlayStatusReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelPlayStatusReq.Size(m)
}
func (m *SetChannelPlayStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelPlayStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelPlayStatusReq proto.InternalMessageInfo

func (m *SetChannelPlayStatusReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetChannelPlayStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelPlayStatusReq) GetMusicMenuId() string {
	if m != nil {
		return m.MusicMenuId
	}
	return ""
}

func (m *SetChannelPlayStatusReq) GetKey() int64 {
	if m != nil {
		return m.Key
	}
	return 0
}

func (m *SetChannelPlayStatusReq) GetStatus() PlayStatus {
	if m != nil {
		return m.Status
	}
	return PlayStatus_play
}

type SetChannelPlayStatusResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetChannelPlayStatusResp) Reset()         { *m = SetChannelPlayStatusResp{} }
func (m *SetChannelPlayStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelPlayStatusResp) ProtoMessage()    {}
func (*SetChannelPlayStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{18}
}
func (m *SetChannelPlayStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelPlayStatusResp.Unmarshal(m, b)
}
func (m *SetChannelPlayStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelPlayStatusResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelPlayStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelPlayStatusResp.Merge(dst, src)
}
func (m *SetChannelPlayStatusResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelPlayStatusResp.Size(m)
}
func (m *SetChannelPlayStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelPlayStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelPlayStatusResp proto.InternalMessageInfo

func (m *SetChannelPlayStatusResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type SelectAutoPlayPlayModeRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PlayMode             uint32       `protobuf:"varint,5,opt,name=play_mode,json=playMode,proto3" json:"play_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SelectAutoPlayPlayModeRequest) Reset()         { *m = SelectAutoPlayPlayModeRequest{} }
func (m *SelectAutoPlayPlayModeRequest) String() string { return proto.CompactTextString(m) }
func (*SelectAutoPlayPlayModeRequest) ProtoMessage()    {}
func (*SelectAutoPlayPlayModeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{19}
}
func (m *SelectAutoPlayPlayModeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelectAutoPlayPlayModeRequest.Unmarshal(m, b)
}
func (m *SelectAutoPlayPlayModeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelectAutoPlayPlayModeRequest.Marshal(b, m, deterministic)
}
func (dst *SelectAutoPlayPlayModeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelectAutoPlayPlayModeRequest.Merge(dst, src)
}
func (m *SelectAutoPlayPlayModeRequest) XXX_Size() int {
	return xxx_messageInfo_SelectAutoPlayPlayModeRequest.Size(m)
}
func (m *SelectAutoPlayPlayModeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SelectAutoPlayPlayModeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SelectAutoPlayPlayModeRequest proto.InternalMessageInfo

func (m *SelectAutoPlayPlayModeRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SelectAutoPlayPlayModeRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SelectAutoPlayPlayModeRequest) GetPlayMode() uint32 {
	if m != nil {
		return m.PlayMode
	}
	return 0
}

type SelectAutoPlayPlayModeResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	NewPlayMode          uint32        `protobuf:"varint,5,opt,name=new_play_mode,json=newPlayMode,proto3" json:"new_play_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SelectAutoPlayPlayModeResponse) Reset()         { *m = SelectAutoPlayPlayModeResponse{} }
func (m *SelectAutoPlayPlayModeResponse) String() string { return proto.CompactTextString(m) }
func (*SelectAutoPlayPlayModeResponse) ProtoMessage()    {}
func (*SelectAutoPlayPlayModeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{20}
}
func (m *SelectAutoPlayPlayModeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelectAutoPlayPlayModeResponse.Unmarshal(m, b)
}
func (m *SelectAutoPlayPlayModeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelectAutoPlayPlayModeResponse.Marshal(b, m, deterministic)
}
func (dst *SelectAutoPlayPlayModeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelectAutoPlayPlayModeResponse.Merge(dst, src)
}
func (m *SelectAutoPlayPlayModeResponse) XXX_Size() int {
	return xxx_messageInfo_SelectAutoPlayPlayModeResponse.Size(m)
}
func (m *SelectAutoPlayPlayModeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SelectAutoPlayPlayModeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SelectAutoPlayPlayModeResponse proto.InternalMessageInfo

func (m *SelectAutoPlayPlayModeResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SelectAutoPlayPlayModeResponse) GetNewPlayMode() uint32 {
	if m != nil {
		return m.NewPlayMode
	}
	return 0
}

// 自动播放歌曲暂停/播放状态通知
type AutoSongStatusNotify struct {
	ChannelId            uint32        `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Info                 *AutoPlayInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AutoSongStatusNotify) Reset()         { *m = AutoSongStatusNotify{} }
func (m *AutoSongStatusNotify) String() string { return proto.CompactTextString(m) }
func (*AutoSongStatusNotify) ProtoMessage()    {}
func (*AutoSongStatusNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{21}
}
func (m *AutoSongStatusNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AutoSongStatusNotify.Unmarshal(m, b)
}
func (m *AutoSongStatusNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AutoSongStatusNotify.Marshal(b, m, deterministic)
}
func (dst *AutoSongStatusNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AutoSongStatusNotify.Merge(dst, src)
}
func (m *AutoSongStatusNotify) XXX_Size() int {
	return xxx_messageInfo_AutoSongStatusNotify.Size(m)
}
func (m *AutoSongStatusNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_AutoSongStatusNotify.DiscardUnknown(m)
}

var xxx_messageInfo_AutoSongStatusNotify proto.InternalMessageInfo

func (m *AutoSongStatusNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AutoSongStatusNotify) GetInfo() *AutoPlayInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 切换自动播放/点歌模式通知
type ChannelPlayModeNotify struct {
	ChannelId            uint32          `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Mode                 ChannelPlayMode `protobuf:"varint,2,opt,name=mode,proto3,enum=ga.channellisteningautoplaylogic.ChannelPlayMode" json:"mode,omitempty"`
	Info                 *AutoPlayInfo   `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
	MusicListMode        uint32          `protobuf:"varint,4,opt,name=music_list_mode,json=musicListMode,proto3" json:"music_list_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ChannelPlayModeNotify) Reset()         { *m = ChannelPlayModeNotify{} }
func (m *ChannelPlayModeNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelPlayModeNotify) ProtoMessage()    {}
func (*ChannelPlayModeNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{22}
}
func (m *ChannelPlayModeNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelPlayModeNotify.Unmarshal(m, b)
}
func (m *ChannelPlayModeNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelPlayModeNotify.Marshal(b, m, deterministic)
}
func (dst *ChannelPlayModeNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelPlayModeNotify.Merge(dst, src)
}
func (m *ChannelPlayModeNotify) XXX_Size() int {
	return xxx_messageInfo_ChannelPlayModeNotify.Size(m)
}
func (m *ChannelPlayModeNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelPlayModeNotify.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelPlayModeNotify proto.InternalMessageInfo

func (m *ChannelPlayModeNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelPlayModeNotify) GetMode() ChannelPlayMode {
	if m != nil {
		return m.Mode
	}
	return ChannelPlayMode_chooseMode
}

func (m *ChannelPlayModeNotify) GetInfo() *AutoPlayInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *ChannelPlayModeNotify) GetMusicListMode() uint32 {
	if m != nil {
		return m.MusicListMode
	}
	return 0
}

// 音量改变通知
type ChannelVolumeNotify struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Volume               uint32   `protobuf:"varint,2,opt,name=volume,proto3" json:"volume,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelVolumeNotify) Reset()         { *m = ChannelVolumeNotify{} }
func (m *ChannelVolumeNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelVolumeNotify) ProtoMessage()    {}
func (*ChannelVolumeNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{23}
}
func (m *ChannelVolumeNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelVolumeNotify.Unmarshal(m, b)
}
func (m *ChannelVolumeNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelVolumeNotify.Marshal(b, m, deterministic)
}
func (dst *ChannelVolumeNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelVolumeNotify.Merge(dst, src)
}
func (m *ChannelVolumeNotify) XXX_Size() int {
	return xxx_messageInfo_ChannelVolumeNotify.Size(m)
}
func (m *ChannelVolumeNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelVolumeNotify.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelVolumeNotify proto.InternalMessageInfo

func (m *ChannelVolumeNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelVolumeNotify) GetVolume() uint32 {
	if m != nil {
		return m.Volume
	}
	return 0
}

// 自动播放的播放模式选择推送 顺序、单曲、随机
type ChannelPlayModeSelectNotify struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PlayMode             uint32   `protobuf:"varint,2,opt,name=play_mode,json=playMode,proto3" json:"play_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelPlayModeSelectNotify) Reset()         { *m = ChannelPlayModeSelectNotify{} }
func (m *ChannelPlayModeSelectNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelPlayModeSelectNotify) ProtoMessage()    {}
func (*ChannelPlayModeSelectNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{24}
}
func (m *ChannelPlayModeSelectNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelPlayModeSelectNotify.Unmarshal(m, b)
}
func (m *ChannelPlayModeSelectNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelPlayModeSelectNotify.Marshal(b, m, deterministic)
}
func (dst *ChannelPlayModeSelectNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelPlayModeSelectNotify.Merge(dst, src)
}
func (m *ChannelPlayModeSelectNotify) XXX_Size() int {
	return xxx_messageInfo_ChannelPlayModeSelectNotify.Size(m)
}
func (m *ChannelPlayModeSelectNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelPlayModeSelectNotify.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelPlayModeSelectNotify proto.InternalMessageInfo

func (m *ChannelPlayModeSelectNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelPlayModeSelectNotify) GetPlayMode() uint32 {
	if m != nil {
		return m.PlayMode
	}
	return 0
}

type WYYUserInfo struct {
	Id                   string        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Nickname             string        `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	AvatarUrl            string        `protobuf:"bytes,3,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	Gender               uint32        `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`
	Signature            string        `protobuf:"bytes,5,opt,name=signature,proto3" json:"signature,omitempty"`
	VipDetail            []*WYYVipInfo `protobuf:"bytes,6,rep,name=vip_detail,json=vipDetail,proto3" json:"vip_detail,omitempty"`
	RedVipLevel          uint32        `protobuf:"varint,7,opt,name=red_vip_level,json=redVipLevel,proto3" json:"red_vip_level,omitempty"`
	RedVipLevelImg       string        `protobuf:"bytes,8,opt,name=red_vip_level_img,json=redVipLevelImg,proto3" json:"red_vip_level_img,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *WYYUserInfo) Reset()         { *m = WYYUserInfo{} }
func (m *WYYUserInfo) String() string { return proto.CompactTextString(m) }
func (*WYYUserInfo) ProtoMessage()    {}
func (*WYYUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{25}
}
func (m *WYYUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WYYUserInfo.Unmarshal(m, b)
}
func (m *WYYUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WYYUserInfo.Marshal(b, m, deterministic)
}
func (dst *WYYUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WYYUserInfo.Merge(dst, src)
}
func (m *WYYUserInfo) XXX_Size() int {
	return xxx_messageInfo_WYYUserInfo.Size(m)
}
func (m *WYYUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WYYUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WYYUserInfo proto.InternalMessageInfo

func (m *WYYUserInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *WYYUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *WYYUserInfo) GetAvatarUrl() string {
	if m != nil {
		return m.AvatarUrl
	}
	return ""
}

func (m *WYYUserInfo) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *WYYUserInfo) GetSignature() string {
	if m != nil {
		return m.Signature
	}
	return ""
}

func (m *WYYUserInfo) GetVipDetail() []*WYYVipInfo {
	if m != nil {
		return m.VipDetail
	}
	return nil
}

func (m *WYYUserInfo) GetRedVipLevel() uint32 {
	if m != nil {
		return m.RedVipLevel
	}
	return 0
}

func (m *WYYUserInfo) GetRedVipLevelImg() string {
	if m != nil {
		return m.RedVipLevelImg
	}
	return ""
}

type WYYVipInfo struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	ExpireTime           int64    `protobuf:"varint,2,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WYYVipInfo) Reset()         { *m = WYYVipInfo{} }
func (m *WYYVipInfo) String() string { return proto.CompactTextString(m) }
func (*WYYVipInfo) ProtoMessage()    {}
func (*WYYVipInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{26}
}
func (m *WYYVipInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WYYVipInfo.Unmarshal(m, b)
}
func (m *WYYVipInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WYYVipInfo.Marshal(b, m, deterministic)
}
func (dst *WYYVipInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WYYVipInfo.Merge(dst, src)
}
func (m *WYYVipInfo) XXX_Size() int {
	return xxx_messageInfo_WYYVipInfo.Size(m)
}
func (m *WYYVipInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WYYVipInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WYYVipInfo proto.InternalMessageInfo

func (m *WYYVipInfo) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *WYYVipInfo) GetExpireTime() int64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

type WYYSongArtist struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WYYSongArtist) Reset()         { *m = WYYSongArtist{} }
func (m *WYYSongArtist) String() string { return proto.CompactTextString(m) }
func (*WYYSongArtist) ProtoMessage()    {}
func (*WYYSongArtist) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{27}
}
func (m *WYYSongArtist) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WYYSongArtist.Unmarshal(m, b)
}
func (m *WYYSongArtist) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WYYSongArtist.Marshal(b, m, deterministic)
}
func (dst *WYYSongArtist) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WYYSongArtist.Merge(dst, src)
}
func (m *WYYSongArtist) XXX_Size() int {
	return xxx_messageInfo_WYYSongArtist.Size(m)
}
func (m *WYYSongArtist) XXX_DiscardUnknown() {
	xxx_messageInfo_WYYSongArtist.DiscardUnknown(m)
}

var xxx_messageInfo_WYYSongArtist proto.InternalMessageInfo

func (m *WYYSongArtist) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *WYYSongArtist) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type WYYSongAlbum struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WYYSongAlbum) Reset()         { *m = WYYSongAlbum{} }
func (m *WYYSongAlbum) String() string { return proto.CompactTextString(m) }
func (*WYYSongAlbum) ProtoMessage()    {}
func (*WYYSongAlbum) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{28}
}
func (m *WYYSongAlbum) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WYYSongAlbum.Unmarshal(m, b)
}
func (m *WYYSongAlbum) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WYYSongAlbum.Marshal(b, m, deterministic)
}
func (dst *WYYSongAlbum) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WYYSongAlbum.Merge(dst, src)
}
func (m *WYYSongAlbum) XXX_Size() int {
	return xxx_messageInfo_WYYSongAlbum.Size(m)
}
func (m *WYYSongAlbum) XXX_DiscardUnknown() {
	xxx_messageInfo_WYYSongAlbum.DiscardUnknown(m)
}

var xxx_messageInfo_WYYSongAlbum proto.InternalMessageInfo

func (m *WYYSongAlbum) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *WYYSongAlbum) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type WYYSongResource struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Size                 uint32   `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	Md5                  string   `protobuf:"bytes,4,opt,name=md5,proto3" json:"md5,omitempty"`
	Level                string   `protobuf:"bytes,5,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WYYSongResource) Reset()         { *m = WYYSongResource{} }
func (m *WYYSongResource) String() string { return proto.CompactTextString(m) }
func (*WYYSongResource) ProtoMessage()    {}
func (*WYYSongResource) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{29}
}
func (m *WYYSongResource) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WYYSongResource.Unmarshal(m, b)
}
func (m *WYYSongResource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WYYSongResource.Marshal(b, m, deterministic)
}
func (dst *WYYSongResource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WYYSongResource.Merge(dst, src)
}
func (m *WYYSongResource) XXX_Size() int {
	return xxx_messageInfo_WYYSongResource.Size(m)
}
func (m *WYYSongResource) XXX_DiscardUnknown() {
	xxx_messageInfo_WYYSongResource.DiscardUnknown(m)
}

var xxx_messageInfo_WYYSongResource proto.InternalMessageInfo

func (m *WYYSongResource) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *WYYSongResource) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *WYYSongResource) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *WYYSongResource) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *WYYSongResource) GetLevel() string {
	if m != nil {
		return m.Level
	}
	return ""
}

type ListeningChangePlayerReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MusicListMode        uint32       `protobuf:"varint,3,opt,name=music_list_mode,json=musicListMode,proto3" json:"music_list_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListeningChangePlayerReq) Reset()         { *m = ListeningChangePlayerReq{} }
func (m *ListeningChangePlayerReq) String() string { return proto.CompactTextString(m) }
func (*ListeningChangePlayerReq) ProtoMessage()    {}
func (*ListeningChangePlayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{30}
}
func (m *ListeningChangePlayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningChangePlayerReq.Unmarshal(m, b)
}
func (m *ListeningChangePlayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningChangePlayerReq.Marshal(b, m, deterministic)
}
func (dst *ListeningChangePlayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningChangePlayerReq.Merge(dst, src)
}
func (m *ListeningChangePlayerReq) XXX_Size() int {
	return xxx_messageInfo_ListeningChangePlayerReq.Size(m)
}
func (m *ListeningChangePlayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningChangePlayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningChangePlayerReq proto.InternalMessageInfo

func (m *ListeningChangePlayerReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListeningChangePlayerReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListeningChangePlayerReq) GetMusicListMode() uint32 {
	if m != nil {
		return m.MusicListMode
	}
	return 0
}

type ListeningChangePlayerResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MusicListMode        uint32        `protobuf:"varint,2,opt,name=music_list_mode,json=musicListMode,proto3" json:"music_list_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ListeningChangePlayerResp) Reset()         { *m = ListeningChangePlayerResp{} }
func (m *ListeningChangePlayerResp) String() string { return proto.CompactTextString(m) }
func (*ListeningChangePlayerResp) ProtoMessage()    {}
func (*ListeningChangePlayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{31}
}
func (m *ListeningChangePlayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningChangePlayerResp.Unmarshal(m, b)
}
func (m *ListeningChangePlayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningChangePlayerResp.Marshal(b, m, deterministic)
}
func (dst *ListeningChangePlayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningChangePlayerResp.Merge(dst, src)
}
func (m *ListeningChangePlayerResp) XXX_Size() int {
	return xxx_messageInfo_ListeningChangePlayerResp.Size(m)
}
func (m *ListeningChangePlayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningChangePlayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningChangePlayerResp proto.InternalMessageInfo

func (m *ListeningChangePlayerResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListeningChangePlayerResp) GetMusicListMode() uint32 {
	if m != nil {
		return m.MusicListMode
	}
	return 0
}

type ListeningGetPlayerStatusReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListeningGetPlayerStatusReq) Reset()         { *m = ListeningGetPlayerStatusReq{} }
func (m *ListeningGetPlayerStatusReq) String() string { return proto.CompactTextString(m) }
func (*ListeningGetPlayerStatusReq) ProtoMessage()    {}
func (*ListeningGetPlayerStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{32}
}
func (m *ListeningGetPlayerStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetPlayerStatusReq.Unmarshal(m, b)
}
func (m *ListeningGetPlayerStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetPlayerStatusReq.Marshal(b, m, deterministic)
}
func (dst *ListeningGetPlayerStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetPlayerStatusReq.Merge(dst, src)
}
func (m *ListeningGetPlayerStatusReq) XXX_Size() int {
	return xxx_messageInfo_ListeningGetPlayerStatusReq.Size(m)
}
func (m *ListeningGetPlayerStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetPlayerStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetPlayerStatusReq proto.InternalMessageInfo

func (m *ListeningGetPlayerStatusReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListeningGetPlayerStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ListeningGetPlayerStatusResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MusicListMode        uint32        `protobuf:"varint,2,opt,name=music_list_mode,json=musicListMode,proto3" json:"music_list_mode,omitempty"`
	Close                bool          `protobuf:"varint,3,opt,name=close,proto3" json:"close,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ListeningGetPlayerStatusResp) Reset()         { *m = ListeningGetPlayerStatusResp{} }
func (m *ListeningGetPlayerStatusResp) String() string { return proto.CompactTextString(m) }
func (*ListeningGetPlayerStatusResp) ProtoMessage()    {}
func (*ListeningGetPlayerStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{33}
}
func (m *ListeningGetPlayerStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetPlayerStatusResp.Unmarshal(m, b)
}
func (m *ListeningGetPlayerStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetPlayerStatusResp.Marshal(b, m, deterministic)
}
func (dst *ListeningGetPlayerStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetPlayerStatusResp.Merge(dst, src)
}
func (m *ListeningGetPlayerStatusResp) XXX_Size() int {
	return xxx_messageInfo_ListeningGetPlayerStatusResp.Size(m)
}
func (m *ListeningGetPlayerStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetPlayerStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetPlayerStatusResp proto.InternalMessageInfo

func (m *ListeningGetPlayerStatusResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListeningGetPlayerStatusResp) GetMusicListMode() uint32 {
	if m != nil {
		return m.MusicListMode
	}
	return 0
}

func (m *ListeningGetPlayerStatusResp) GetClose() bool {
	if m != nil {
		return m.Close
	}
	return false
}

type ListeningSearchSongByKeyReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Keyword              string       `protobuf:"bytes,3,opt,name=keyword,proto3" json:"keyword,omitempty"`
	Page                 uint32       `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListeningSearchSongByKeyReq) Reset()         { *m = ListeningSearchSongByKeyReq{} }
func (m *ListeningSearchSongByKeyReq) String() string { return proto.CompactTextString(m) }
func (*ListeningSearchSongByKeyReq) ProtoMessage()    {}
func (*ListeningSearchSongByKeyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{34}
}
func (m *ListeningSearchSongByKeyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningSearchSongByKeyReq.Unmarshal(m, b)
}
func (m *ListeningSearchSongByKeyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningSearchSongByKeyReq.Marshal(b, m, deterministic)
}
func (dst *ListeningSearchSongByKeyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningSearchSongByKeyReq.Merge(dst, src)
}
func (m *ListeningSearchSongByKeyReq) XXX_Size() int {
	return xxx_messageInfo_ListeningSearchSongByKeyReq.Size(m)
}
func (m *ListeningSearchSongByKeyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningSearchSongByKeyReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningSearchSongByKeyReq proto.InternalMessageInfo

func (m *ListeningSearchSongByKeyReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListeningSearchSongByKeyReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListeningSearchSongByKeyReq) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

func (m *ListeningSearchSongByKeyReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

type ListeningSearchSongByKeyResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MusicInfoList        []*WYYMusicInfo `protobuf:"bytes,2,rep,name=music_info_list,json=musicInfoList,proto3" json:"music_info_list,omitempty"`
	HasMore              bool            `protobuf:"varint,3,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	Count                uint32          `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListeningSearchSongByKeyResp) Reset()         { *m = ListeningSearchSongByKeyResp{} }
func (m *ListeningSearchSongByKeyResp) String() string { return proto.CompactTextString(m) }
func (*ListeningSearchSongByKeyResp) ProtoMessage()    {}
func (*ListeningSearchSongByKeyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{35}
}
func (m *ListeningSearchSongByKeyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningSearchSongByKeyResp.Unmarshal(m, b)
}
func (m *ListeningSearchSongByKeyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningSearchSongByKeyResp.Marshal(b, m, deterministic)
}
func (dst *ListeningSearchSongByKeyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningSearchSongByKeyResp.Merge(dst, src)
}
func (m *ListeningSearchSongByKeyResp) XXX_Size() int {
	return xxx_messageInfo_ListeningSearchSongByKeyResp.Size(m)
}
func (m *ListeningSearchSongByKeyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningSearchSongByKeyResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningSearchSongByKeyResp proto.InternalMessageInfo

func (m *ListeningSearchSongByKeyResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListeningSearchSongByKeyResp) GetMusicInfoList() []*WYYMusicInfo {
	if m != nil {
		return m.MusicInfoList
	}
	return nil
}

func (m *ListeningSearchSongByKeyResp) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

func (m *ListeningSearchSongByKeyResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type ListeningGetSongListByTypeReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Type                 uint32       `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	Page                 uint32       `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListeningGetSongListByTypeReq) Reset()         { *m = ListeningGetSongListByTypeReq{} }
func (m *ListeningGetSongListByTypeReq) String() string { return proto.CompactTextString(m) }
func (*ListeningGetSongListByTypeReq) ProtoMessage()    {}
func (*ListeningGetSongListByTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{36}
}
func (m *ListeningGetSongListByTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetSongListByTypeReq.Unmarshal(m, b)
}
func (m *ListeningGetSongListByTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetSongListByTypeReq.Marshal(b, m, deterministic)
}
func (dst *ListeningGetSongListByTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetSongListByTypeReq.Merge(dst, src)
}
func (m *ListeningGetSongListByTypeReq) XXX_Size() int {
	return xxx_messageInfo_ListeningGetSongListByTypeReq.Size(m)
}
func (m *ListeningGetSongListByTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetSongListByTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetSongListByTypeReq proto.InternalMessageInfo

func (m *ListeningGetSongListByTypeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListeningGetSongListByTypeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListeningGetSongListByTypeReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ListeningGetSongListByTypeReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

type ListeningGetSongListByTypeResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MusicInfoList        []*WYYMusicInfo `protobuf:"bytes,2,rep,name=music_info_list,json=musicInfoList,proto3" json:"music_info_list,omitempty"`
	Count                uint32          `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	HasMore              bool            `protobuf:"varint,4,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListeningGetSongListByTypeResp) Reset()         { *m = ListeningGetSongListByTypeResp{} }
func (m *ListeningGetSongListByTypeResp) String() string { return proto.CompactTextString(m) }
func (*ListeningGetSongListByTypeResp) ProtoMessage()    {}
func (*ListeningGetSongListByTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{37}
}
func (m *ListeningGetSongListByTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetSongListByTypeResp.Unmarshal(m, b)
}
func (m *ListeningGetSongListByTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetSongListByTypeResp.Marshal(b, m, deterministic)
}
func (dst *ListeningGetSongListByTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetSongListByTypeResp.Merge(dst, src)
}
func (m *ListeningGetSongListByTypeResp) XXX_Size() int {
	return xxx_messageInfo_ListeningGetSongListByTypeResp.Size(m)
}
func (m *ListeningGetSongListByTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetSongListByTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetSongListByTypeResp proto.InternalMessageInfo

func (m *ListeningGetSongListByTypeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListeningGetSongListByTypeResp) GetMusicInfoList() []*WYYMusicInfo {
	if m != nil {
		return m.MusicInfoList
	}
	return nil
}

func (m *ListeningGetSongListByTypeResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ListeningGetSongListByTypeResp) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

type ListeningGetSongListListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListeningGetSongListListReq) Reset()         { *m = ListeningGetSongListListReq{} }
func (m *ListeningGetSongListListReq) String() string { return proto.CompactTextString(m) }
func (*ListeningGetSongListListReq) ProtoMessage()    {}
func (*ListeningGetSongListListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{38}
}
func (m *ListeningGetSongListListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetSongListListReq.Unmarshal(m, b)
}
func (m *ListeningGetSongListListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetSongListListReq.Marshal(b, m, deterministic)
}
func (dst *ListeningGetSongListListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetSongListListReq.Merge(dst, src)
}
func (m *ListeningGetSongListListReq) XXX_Size() int {
	return xxx_messageInfo_ListeningGetSongListListReq.Size(m)
}
func (m *ListeningGetSongListListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetSongListListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetSongListListReq proto.InternalMessageInfo

func (m *ListeningGetSongListListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListeningGetSongListListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ListeningGetSongListListResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	List                 []*SongListList `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListeningGetSongListListResp) Reset()         { *m = ListeningGetSongListListResp{} }
func (m *ListeningGetSongListListResp) String() string { return proto.CompactTextString(m) }
func (*ListeningGetSongListListResp) ProtoMessage()    {}
func (*ListeningGetSongListListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{39}
}
func (m *ListeningGetSongListListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetSongListListResp.Unmarshal(m, b)
}
func (m *ListeningGetSongListListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetSongListListResp.Marshal(b, m, deterministic)
}
func (dst *ListeningGetSongListListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetSongListListResp.Merge(dst, src)
}
func (m *ListeningGetSongListListResp) XXX_Size() int {
	return xxx_messageInfo_ListeningGetSongListListResp.Size(m)
}
func (m *ListeningGetSongListListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetSongListListResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetSongListListResp proto.InternalMessageInfo

func (m *ListeningGetSongListListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListeningGetSongListListResp) GetList() []*SongListList {
	if m != nil {
		return m.List
	}
	return nil
}

type SongListList struct {
	CoverImgUrl          string   `protobuf:"bytes,1,opt,name=cover_img_url,json=coverImgUrl,proto3" json:"cover_img_url,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	LightCoverImgUrl     string   `protobuf:"bytes,4,opt,name=light_cover_img_url,json=lightCoverImgUrl,proto3" json:"light_cover_img_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SongListList) Reset()         { *m = SongListList{} }
func (m *SongListList) String() string { return proto.CompactTextString(m) }
func (*SongListList) ProtoMessage()    {}
func (*SongListList) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{40}
}
func (m *SongListList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SongListList.Unmarshal(m, b)
}
func (m *SongListList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SongListList.Marshal(b, m, deterministic)
}
func (dst *SongListList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SongListList.Merge(dst, src)
}
func (m *SongListList) XXX_Size() int {
	return xxx_messageInfo_SongListList.Size(m)
}
func (m *SongListList) XXX_DiscardUnknown() {
	xxx_messageInfo_SongListList.DiscardUnknown(m)
}

var xxx_messageInfo_SongListList proto.InternalMessageInfo

func (m *SongListList) GetCoverImgUrl() string {
	if m != nil {
		return m.CoverImgUrl
	}
	return ""
}

func (m *SongListList) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SongListList) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *SongListList) GetLightCoverImgUrl() string {
	if m != nil {
		return m.LightCoverImgUrl
	}
	return ""
}

type WYYMusicInfo struct {
	BaseInfo             *channel.MusicInfoV2 `protobuf:"bytes,1,opt,name=base_info,json=baseInfo,proto3" json:"base_info,omitempty"`
	AlbumName            string               `protobuf:"bytes,2,opt,name=album_name,json=albumName,proto3" json:"album_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *WYYMusicInfo) Reset()         { *m = WYYMusicInfo{} }
func (m *WYYMusicInfo) String() string { return proto.CompactTextString(m) }
func (*WYYMusicInfo) ProtoMessage()    {}
func (*WYYMusicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{41}
}
func (m *WYYMusicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WYYMusicInfo.Unmarshal(m, b)
}
func (m *WYYMusicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WYYMusicInfo.Marshal(b, m, deterministic)
}
func (dst *WYYMusicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WYYMusicInfo.Merge(dst, src)
}
func (m *WYYMusicInfo) XXX_Size() int {
	return xxx_messageInfo_WYYMusicInfo.Size(m)
}
func (m *WYYMusicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WYYMusicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WYYMusicInfo proto.InternalMessageInfo

func (m *WYYMusicInfo) GetBaseInfo() *channel.MusicInfoV2 {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *WYYMusicInfo) GetAlbumName() string {
	if m != nil {
		return m.AlbumName
	}
	return ""
}

type ListeningOrderSongReq struct {
	BaseReq              *app.BaseReq    `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32          `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MusicInfoList        []*WYYMusicInfo `protobuf:"bytes,3,rep,name=music_info_list,json=musicInfoList,proto3" json:"music_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListeningOrderSongReq) Reset()         { *m = ListeningOrderSongReq{} }
func (m *ListeningOrderSongReq) String() string { return proto.CompactTextString(m) }
func (*ListeningOrderSongReq) ProtoMessage()    {}
func (*ListeningOrderSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{42}
}
func (m *ListeningOrderSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningOrderSongReq.Unmarshal(m, b)
}
func (m *ListeningOrderSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningOrderSongReq.Marshal(b, m, deterministic)
}
func (dst *ListeningOrderSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningOrderSongReq.Merge(dst, src)
}
func (m *ListeningOrderSongReq) XXX_Size() int {
	return xxx_messageInfo_ListeningOrderSongReq.Size(m)
}
func (m *ListeningOrderSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningOrderSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningOrderSongReq proto.InternalMessageInfo

func (m *ListeningOrderSongReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListeningOrderSongReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListeningOrderSongReq) GetMusicInfoList() []*WYYMusicInfo {
	if m != nil {
		return m.MusicInfoList
	}
	return nil
}

type ListeningOrderSongResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MusicAddedList       []*WYYMusicInfo `protobuf:"bytes,2,rep,name=music_added_list,json=musicAddedList,proto3" json:"music_added_list,omitempty"`
	MaxMusicCount        uint32          `protobuf:"varint,3,opt,name=max_music_count,json=maxMusicCount,proto3" json:"max_music_count,omitempty"`
	CurrentMusicCount    uint32          `protobuf:"varint,4,opt,name=current_music_count,json=currentMusicCount,proto3" json:"current_music_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListeningOrderSongResp) Reset()         { *m = ListeningOrderSongResp{} }
func (m *ListeningOrderSongResp) String() string { return proto.CompactTextString(m) }
func (*ListeningOrderSongResp) ProtoMessage()    {}
func (*ListeningOrderSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{43}
}
func (m *ListeningOrderSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningOrderSongResp.Unmarshal(m, b)
}
func (m *ListeningOrderSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningOrderSongResp.Marshal(b, m, deterministic)
}
func (dst *ListeningOrderSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningOrderSongResp.Merge(dst, src)
}
func (m *ListeningOrderSongResp) XXX_Size() int {
	return xxx_messageInfo_ListeningOrderSongResp.Size(m)
}
func (m *ListeningOrderSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningOrderSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningOrderSongResp proto.InternalMessageInfo

func (m *ListeningOrderSongResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListeningOrderSongResp) GetMusicAddedList() []*WYYMusicInfo {
	if m != nil {
		return m.MusicAddedList
	}
	return nil
}

func (m *ListeningOrderSongResp) GetMaxMusicCount() uint32 {
	if m != nil {
		return m.MaxMusicCount
	}
	return 0
}

func (m *ListeningOrderSongResp) GetCurrentMusicCount() uint32 {
	if m != nil {
		return m.CurrentMusicCount
	}
	return 0
}

type ListeningGetPlayListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListeningGetPlayListReq) Reset()         { *m = ListeningGetPlayListReq{} }
func (m *ListeningGetPlayListReq) String() string { return proto.CompactTextString(m) }
func (*ListeningGetPlayListReq) ProtoMessage()    {}
func (*ListeningGetPlayListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{44}
}
func (m *ListeningGetPlayListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetPlayListReq.Unmarshal(m, b)
}
func (m *ListeningGetPlayListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetPlayListReq.Marshal(b, m, deterministic)
}
func (dst *ListeningGetPlayListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetPlayListReq.Merge(dst, src)
}
func (m *ListeningGetPlayListReq) XXX_Size() int {
	return xxx_messageInfo_ListeningGetPlayListReq.Size(m)
}
func (m *ListeningGetPlayListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetPlayListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetPlayListReq proto.InternalMessageInfo

func (m *ListeningGetPlayListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListeningGetPlayListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ListeningGetPlayListResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MusicList            []*WYYMusicInfo `protobuf:"bytes,2,rep,name=music_list,json=musicList,proto3" json:"music_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListeningGetPlayListResp) Reset()         { *m = ListeningGetPlayListResp{} }
func (m *ListeningGetPlayListResp) String() string { return proto.CompactTextString(m) }
func (*ListeningGetPlayListResp) ProtoMessage()    {}
func (*ListeningGetPlayListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{45}
}
func (m *ListeningGetPlayListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetPlayListResp.Unmarshal(m, b)
}
func (m *ListeningGetPlayListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetPlayListResp.Marshal(b, m, deterministic)
}
func (dst *ListeningGetPlayListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetPlayListResp.Merge(dst, src)
}
func (m *ListeningGetPlayListResp) XXX_Size() int {
	return xxx_messageInfo_ListeningGetPlayListResp.Size(m)
}
func (m *ListeningGetPlayListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetPlayListResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetPlayListResp proto.InternalMessageInfo

func (m *ListeningGetPlayListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListeningGetPlayListResp) GetMusicList() []*WYYMusicInfo {
	if m != nil {
		return m.MusicList
	}
	return nil
}

type ListeningGetPlayerUserInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListeningGetPlayerUserInfoReq) Reset()         { *m = ListeningGetPlayerUserInfoReq{} }
func (m *ListeningGetPlayerUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*ListeningGetPlayerUserInfoReq) ProtoMessage()    {}
func (*ListeningGetPlayerUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{46}
}
func (m *ListeningGetPlayerUserInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetPlayerUserInfoReq.Unmarshal(m, b)
}
func (m *ListeningGetPlayerUserInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetPlayerUserInfoReq.Marshal(b, m, deterministic)
}
func (dst *ListeningGetPlayerUserInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetPlayerUserInfoReq.Merge(dst, src)
}
func (m *ListeningGetPlayerUserInfoReq) XXX_Size() int {
	return xxx_messageInfo_ListeningGetPlayerUserInfoReq.Size(m)
}
func (m *ListeningGetPlayerUserInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetPlayerUserInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetPlayerUserInfoReq proto.InternalMessageInfo

func (m *ListeningGetPlayerUserInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListeningGetPlayerUserInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ListeningGetPlayerUserInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UserInfo             *WYYUserInfo  `protobuf:"bytes,2,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ListeningGetPlayerUserInfoResp) Reset()         { *m = ListeningGetPlayerUserInfoResp{} }
func (m *ListeningGetPlayerUserInfoResp) String() string { return proto.CompactTextString(m) }
func (*ListeningGetPlayerUserInfoResp) ProtoMessage()    {}
func (*ListeningGetPlayerUserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{47}
}
func (m *ListeningGetPlayerUserInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetPlayerUserInfoResp.Unmarshal(m, b)
}
func (m *ListeningGetPlayerUserInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetPlayerUserInfoResp.Marshal(b, m, deterministic)
}
func (dst *ListeningGetPlayerUserInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetPlayerUserInfoResp.Merge(dst, src)
}
func (m *ListeningGetPlayerUserInfoResp) XXX_Size() int {
	return xxx_messageInfo_ListeningGetPlayerUserInfoResp.Size(m)
}
func (m *ListeningGetPlayerUserInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetPlayerUserInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetPlayerUserInfoResp proto.InternalMessageInfo

func (m *ListeningGetPlayerUserInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListeningGetPlayerUserInfoResp) GetUserInfo() *WYYUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

type ListeningGetSongResourceReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SongId               string       `protobuf:"bytes,3,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListeningGetSongResourceReq) Reset()         { *m = ListeningGetSongResourceReq{} }
func (m *ListeningGetSongResourceReq) String() string { return proto.CompactTextString(m) }
func (*ListeningGetSongResourceReq) ProtoMessage()    {}
func (*ListeningGetSongResourceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{48}
}
func (m *ListeningGetSongResourceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetSongResourceReq.Unmarshal(m, b)
}
func (m *ListeningGetSongResourceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetSongResourceReq.Marshal(b, m, deterministic)
}
func (dst *ListeningGetSongResourceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetSongResourceReq.Merge(dst, src)
}
func (m *ListeningGetSongResourceReq) XXX_Size() int {
	return xxx_messageInfo_ListeningGetSongResourceReq.Size(m)
}
func (m *ListeningGetSongResourceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetSongResourceReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetSongResourceReq proto.InternalMessageInfo

func (m *ListeningGetSongResourceReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListeningGetSongResourceReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListeningGetSongResourceReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type ListeningGetSongResourceResp struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SongResource         *WYYSongResource `protobuf:"bytes,2,opt,name=song_resource,json=songResource,proto3" json:"song_resource,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ListeningGetSongResourceResp) Reset()         { *m = ListeningGetSongResourceResp{} }
func (m *ListeningGetSongResourceResp) String() string { return proto.CompactTextString(m) }
func (*ListeningGetSongResourceResp) ProtoMessage()    {}
func (*ListeningGetSongResourceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{49}
}
func (m *ListeningGetSongResourceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningGetSongResourceResp.Unmarshal(m, b)
}
func (m *ListeningGetSongResourceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningGetSongResourceResp.Marshal(b, m, deterministic)
}
func (dst *ListeningGetSongResourceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningGetSongResourceResp.Merge(dst, src)
}
func (m *ListeningGetSongResourceResp) XXX_Size() int {
	return xxx_messageInfo_ListeningGetSongResourceResp.Size(m)
}
func (m *ListeningGetSongResourceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningGetSongResourceResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningGetSongResourceResp proto.InternalMessageInfo

func (m *ListeningGetSongResourceResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListeningGetSongResourceResp) GetSongResource() *WYYSongResource {
	if m != nil {
		return m.SongResource
	}
	return nil
}

type ListeningReportRecordReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Action               string       `protobuf:"bytes,3,opt,name=action,proto3" json:"action,omitempty"`
	StartLogTime         uint64       `protobuf:"varint,4,opt,name=start_log_time,json=startLogTime,proto3" json:"start_log_time,omitempty"`
	SongId               string       `protobuf:"bytes,5,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	Alg                  string       `protobuf:"bytes,6,opt,name=alg,proto3" json:"alg,omitempty"`
	Time                 uint64       `protobuf:"varint,7,opt,name=time,proto3" json:"time,omitempty"`
	End                  string       `protobuf:"bytes,8,opt,name=end,proto3" json:"end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListeningReportRecordReq) Reset()         { *m = ListeningReportRecordReq{} }
func (m *ListeningReportRecordReq) String() string { return proto.CompactTextString(m) }
func (*ListeningReportRecordReq) ProtoMessage()    {}
func (*ListeningReportRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{50}
}
func (m *ListeningReportRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningReportRecordReq.Unmarshal(m, b)
}
func (m *ListeningReportRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningReportRecordReq.Marshal(b, m, deterministic)
}
func (dst *ListeningReportRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningReportRecordReq.Merge(dst, src)
}
func (m *ListeningReportRecordReq) XXX_Size() int {
	return xxx_messageInfo_ListeningReportRecordReq.Size(m)
}
func (m *ListeningReportRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningReportRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningReportRecordReq proto.InternalMessageInfo

func (m *ListeningReportRecordReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListeningReportRecordReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListeningReportRecordReq) GetAction() string {
	if m != nil {
		return m.Action
	}
	return ""
}

func (m *ListeningReportRecordReq) GetStartLogTime() uint64 {
	if m != nil {
		return m.StartLogTime
	}
	return 0
}

func (m *ListeningReportRecordReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *ListeningReportRecordReq) GetAlg() string {
	if m != nil {
		return m.Alg
	}
	return ""
}

func (m *ListeningReportRecordReq) GetTime() uint64 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *ListeningReportRecordReq) GetEnd() string {
	if m != nil {
		return m.End
	}
	return ""
}

type ListeningReportRecordResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ListeningReportRecordResp) Reset()         { *m = ListeningReportRecordResp{} }
func (m *ListeningReportRecordResp) String() string { return proto.CompactTextString(m) }
func (*ListeningReportRecordResp) ProtoMessage()    {}
func (*ListeningReportRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604, []int{51}
}
func (m *ListeningReportRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningReportRecordResp.Unmarshal(m, b)
}
func (m *ListeningReportRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningReportRecordResp.Marshal(b, m, deterministic)
}
func (dst *ListeningReportRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningReportRecordResp.Merge(dst, src)
}
func (m *ListeningReportRecordResp) XXX_Size() int {
	return xxx_messageInfo_ListeningReportRecordResp.Size(m)
}
func (m *ListeningReportRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningReportRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningReportRecordResp proto.InternalMessageInfo

func (m *ListeningReportRecordResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*ChannelPlayStatusReq)(nil), "ga.channellisteningautoplaylogic.ChannelPlayStatusReq")
	proto.RegisterType((*ChannelPlayStatusResp)(nil), "ga.channellisteningautoplaylogic.ChannelPlayStatusResp")
	proto.RegisterType((*AutoPlayInfo)(nil), "ga.channellisteningautoplaylogic.AutoPlayInfo")
	proto.RegisterType((*MusicInfo)(nil), "ga.channellisteningautoplaylogic.MusicInfo")
	proto.RegisterType((*SwitchChannelPlayReq)(nil), "ga.channellisteningautoplaylogic.SwitchChannelPlayReq")
	proto.RegisterType((*SwitchChannelPlayResp)(nil), "ga.channellisteningautoplaylogic.SwitchChannelPlayResp")
	proto.RegisterType((*ChannelRcmdMusicMenuReq)(nil), "ga.channellisteningautoplaylogic.ChannelRcmdMusicMenuReq")
	proto.RegisterType((*ChannelRcmdMusicMenuResp)(nil), "ga.channellisteningautoplaylogic.ChannelRcmdMusicMenuResp")
	proto.RegisterType((*ListeningAutoPlayRcmdMenu)(nil), "ga.channellisteningautoplaylogic.ListeningAutoPlayRcmdMenu")
	proto.RegisterType((*SetVolumeReq)(nil), "ga.channellisteningautoplaylogic.SetVolumeReq")
	proto.RegisterType((*SetVolumeResp)(nil), "ga.channellisteningautoplaylogic.SetVolumeResp")
	proto.RegisterType((*CutAutoModeSongReq)(nil), "ga.channellisteningautoplaylogic.CutAutoModeSongReq")
	proto.RegisterType((*CutAutoModeSongResp)(nil), "ga.channellisteningautoplaylogic.CutAutoModeSongResp")
	proto.RegisterType((*ReportChannelAutoSongProgressReq)(nil), "ga.channellisteningautoplaylogic.ReportChannelAutoSongProgressReq")
	proto.RegisterType((*ReportChannelAutoSongProgressResp)(nil), "ga.channellisteningautoplaylogic.ReportChannelAutoSongProgressResp")
	proto.RegisterType((*SetChannelPlayModeReq)(nil), "ga.channellisteningautoplaylogic.SetChannelPlayModeReq")
	proto.RegisterType((*SetChannelPlayModeResp)(nil), "ga.channellisteningautoplaylogic.SetChannelPlayModeResp")
	proto.RegisterType((*SetChannelPlayStatusReq)(nil), "ga.channellisteningautoplaylogic.SetChannelPlayStatusReq")
	proto.RegisterType((*SetChannelPlayStatusResp)(nil), "ga.channellisteningautoplaylogic.SetChannelPlayStatusResp")
	proto.RegisterType((*SelectAutoPlayPlayModeRequest)(nil), "ga.channellisteningautoplaylogic.SelectAutoPlayPlayModeRequest")
	proto.RegisterType((*SelectAutoPlayPlayModeResponse)(nil), "ga.channellisteningautoplaylogic.SelectAutoPlayPlayModeResponse")
	proto.RegisterType((*AutoSongStatusNotify)(nil), "ga.channellisteningautoplaylogic.AutoSongStatusNotify")
	proto.RegisterType((*ChannelPlayModeNotify)(nil), "ga.channellisteningautoplaylogic.ChannelPlayModeNotify")
	proto.RegisterType((*ChannelVolumeNotify)(nil), "ga.channellisteningautoplaylogic.ChannelVolumeNotify")
	proto.RegisterType((*ChannelPlayModeSelectNotify)(nil), "ga.channellisteningautoplaylogic.ChannelPlayModeSelectNotify")
	proto.RegisterType((*WYYUserInfo)(nil), "ga.channellisteningautoplaylogic.WYYUserInfo")
	proto.RegisterType((*WYYVipInfo)(nil), "ga.channellisteningautoplaylogic.WYYVipInfo")
	proto.RegisterType((*WYYSongArtist)(nil), "ga.channellisteningautoplaylogic.WYYSongArtist")
	proto.RegisterType((*WYYSongAlbum)(nil), "ga.channellisteningautoplaylogic.WYYSongAlbum")
	proto.RegisterType((*WYYSongResource)(nil), "ga.channellisteningautoplaylogic.WYYSongResource")
	proto.RegisterType((*ListeningChangePlayerReq)(nil), "ga.channellisteningautoplaylogic.ListeningChangePlayerReq")
	proto.RegisterType((*ListeningChangePlayerResp)(nil), "ga.channellisteningautoplaylogic.ListeningChangePlayerResp")
	proto.RegisterType((*ListeningGetPlayerStatusReq)(nil), "ga.channellisteningautoplaylogic.ListeningGetPlayerStatusReq")
	proto.RegisterType((*ListeningGetPlayerStatusResp)(nil), "ga.channellisteningautoplaylogic.ListeningGetPlayerStatusResp")
	proto.RegisterType((*ListeningSearchSongByKeyReq)(nil), "ga.channellisteningautoplaylogic.ListeningSearchSongByKeyReq")
	proto.RegisterType((*ListeningSearchSongByKeyResp)(nil), "ga.channellisteningautoplaylogic.ListeningSearchSongByKeyResp")
	proto.RegisterType((*ListeningGetSongListByTypeReq)(nil), "ga.channellisteningautoplaylogic.ListeningGetSongListByTypeReq")
	proto.RegisterType((*ListeningGetSongListByTypeResp)(nil), "ga.channellisteningautoplaylogic.ListeningGetSongListByTypeResp")
	proto.RegisterType((*ListeningGetSongListListReq)(nil), "ga.channellisteningautoplaylogic.ListeningGetSongListListReq")
	proto.RegisterType((*ListeningGetSongListListResp)(nil), "ga.channellisteningautoplaylogic.ListeningGetSongListListResp")
	proto.RegisterType((*SongListList)(nil), "ga.channellisteningautoplaylogic.SongListList")
	proto.RegisterType((*WYYMusicInfo)(nil), "ga.channellisteningautoplaylogic.WYYMusicInfo")
	proto.RegisterType((*ListeningOrderSongReq)(nil), "ga.channellisteningautoplaylogic.ListeningOrderSongReq")
	proto.RegisterType((*ListeningOrderSongResp)(nil), "ga.channellisteningautoplaylogic.ListeningOrderSongResp")
	proto.RegisterType((*ListeningGetPlayListReq)(nil), "ga.channellisteningautoplaylogic.ListeningGetPlayListReq")
	proto.RegisterType((*ListeningGetPlayListResp)(nil), "ga.channellisteningautoplaylogic.ListeningGetPlayListResp")
	proto.RegisterType((*ListeningGetPlayerUserInfoReq)(nil), "ga.channellisteningautoplaylogic.ListeningGetPlayerUserInfoReq")
	proto.RegisterType((*ListeningGetPlayerUserInfoResp)(nil), "ga.channellisteningautoplaylogic.ListeningGetPlayerUserInfoResp")
	proto.RegisterType((*ListeningGetSongResourceReq)(nil), "ga.channellisteningautoplaylogic.ListeningGetSongResourceReq")
	proto.RegisterType((*ListeningGetSongResourceResp)(nil), "ga.channellisteningautoplaylogic.ListeningGetSongResourceResp")
	proto.RegisterType((*ListeningReportRecordReq)(nil), "ga.channellisteningautoplaylogic.ListeningReportRecordReq")
	proto.RegisterType((*ListeningReportRecordResp)(nil), "ga.channellisteningautoplaylogic.ListeningReportRecordResp")
	proto.RegisterEnum("ga.channellisteningautoplaylogic.ChannelPlayMode", ChannelPlayMode_name, ChannelPlayMode_value)
	proto.RegisterEnum("ga.channellisteningautoplaylogic.AutoPlaySwitch", AutoPlaySwitch_name, AutoPlaySwitch_value)
	proto.RegisterEnum("ga.channellisteningautoplaylogic.PlayStatus", PlayStatus_name, PlayStatus_value)
	proto.RegisterEnum("ga.channellisteningautoplaylogic.PlayModeSelect", PlayModeSelect_name, PlayModeSelect_value)
	proto.RegisterEnum("ga.channellisteningautoplaylogic.WYYVipOpenDetailDto", WYYVipOpenDetailDto_name, WYYVipOpenDetailDto_value)
	proto.RegisterEnum("ga.channellisteningautoplaylogic.MusicListType", MusicListType_name, MusicListType_value)
	proto.RegisterEnum("ga.channellisteningautoplaylogic.SongListType", SongListType_name, SongListType_value)
	proto.RegisterEnum("ga.channellisteningautoplaylogic.ReportChannelAutoSongProgressReq_ProgressType", ReportChannelAutoSongProgressReq_ProgressType_name, ReportChannelAutoSongProgressReq_ProgressType_value)
}

func init() {
	proto.RegisterFile("channel-listening-auto-play-logic_.proto", fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604)
}

var fileDescriptor_channel_listening_auto_play_logic__4541ed77a4967604 = []byte{
	// 2193 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x59, 0xcd, 0x6f, 0xdb, 0xc8,
	0x15, 0x37, 0x29, 0x59, 0x16, 0x9f, 0x25, 0x85, 0x19, 0xdb, 0xb1, 0x92, 0x6c, 0xb6, 0x59, 0xa2,
	0x5d, 0x64, 0xdd, 0xb5, 0xd2, 0xf5, 0x62, 0x2f, 0x5b, 0x60, 0xb1, 0xb2, 0xec, 0xcd, 0xaa, 0x91,
	0xac, 0x80, 0x72, 0x9c, 0xba, 0x45, 0xc1, 0xa5, 0xc9, 0x09, 0x4d, 0x84, 0x22, 0x19, 0x7e, 0x38,
	0x51, 0x81, 0x9e, 0xf6, 0x50, 0xec, 0xa1, 0x05, 0x5a, 0x2c, 0x5a, 0xa0, 0xb7, 0x9e, 0x0a, 0xf4,
	0xd4, 0x53, 0xff, 0x87, 0x5e, 0x8b, 0xde, 0xda, 0x7f, 0xa0, 0xb7, 0x5e, 0x7a, 0x2f, 0xde, 0x0c,
	0x29, 0x91, 0xb2, 0xb2, 0x96, 0x12, 0x16, 0xed, 0x41, 0xc0, 0xcc, 0x70, 0xe6, 0x7d, 0xfc, 0xe6,
	0x7d, 0x8e, 0xe0, 0x9e, 0x71, 0xae, 0xbb, 0x2e, 0x75, 0x76, 0x1d, 0x3b, 0x8c, 0xa8, 0x6b, 0xbb,
	0xd6, 0xae, 0x1e, 0x47, 0xde, 0xae, 0xef, 0xe8, 0xe3, 0x5d, 0xc7, 0xb3, 0x6c, 0x43, 0x6b, 0xf9,
	0x81, 0x17, 0x79, 0xe4, 0xae, 0xa5, 0xb7, 0x92, 0xcd, 0x93, 0xbd, 0xb8, 0x15, 0x77, 0xb2, 0x8d,
	0xb7, 0xea, 0x96, 0xae, 0x9d, 0xe9, 0x21, 0xe5, 0x07, 0x6e, 0x35, 0x92, 0xdd, 0x09, 0x01, 0xe5,
	0x27, 0xb0, 0xd9, 0xe1, 0x2b, 0x8f, 0x1c, 0x7d, 0x3c, 0x8c, 0xf4, 0x28, 0x0e, 0x55, 0xfa, 0x9c,
	0xbc, 0x0b, 0x55, 0x3c, 0xa5, 0x05, 0xf4, 0x79, 0x53, 0xb8, 0x2b, 0xdc, 0x5b, 0xdf, 0x5b, 0x6f,
	0x59, 0x7a, 0x6b, 0x5f, 0x0f, 0xa9, 0x4a, 0x9f, 0xab, 0x6b, 0x67, 0x7c, 0x40, 0xee, 0x00, 0xa4,
	0x14, 0x6d, 0xb3, 0x29, 0xde, 0x15, 0xee, 0xd5, 0x55, 0x29, 0x59, 0xe9, 0x9a, 0xca, 0x6f, 0x45,
	0xd8, 0x9a, 0x43, 0x3f, 0xf4, 0xc9, 0x7b, 0x20, 0x25, 0x0c, 0x42, 0x3f, 0xe1, 0x50, 0x9b, 0x72,
	0x08, 0x7d, 0xb5, 0x7a, 0x96, 0x8c, 0xc8, 0x21, 0x94, 0x47, 0x9e, 0x49, 0x19, 0xf5, 0xc6, 0xde,
	0x07, 0xad, 0xab, 0x74, 0x6e, 0x65, 0x38, 0xf6, 0x3d, 0x93, 0xaa, 0xec, 0x38, 0xf9, 0x1c, 0x2a,
	0xe1, 0x0b, 0x3b, 0x32, 0xce, 0x9b, 0x25, 0x46, 0xe8, 0x7b, 0x57, 0x13, 0x6a, 0xc7, 0x91, 0xc7,
	0xe4, 0x66, 0xe7, 0xd4, 0xe4, 0x3c, 0xd9, 0x87, 0xb2, 0xed, 0x3e, 0xf5, 0x9a, 0x65, 0x26, 0x76,
	0x6b, 0x71, 0x3a, 0x5d, 0xf7, 0xa9, 0xa7, 0xb2, 0xb3, 0xca, 0x57, 0x22, 0xd4, 0xb2, 0xcb, 0x64,
	0x00, 0xe5, 0x11, 0x75, 0xe3, 0x04, 0x8b, 0xef, 0x5f, 0x4d, 0xb4, 0x97, 0x2e, 0xa7, 0x64, 0x54,
	0x63, 0x64, 0xf6, 0xa9, 0x1b, 0xab, 0x8c, 0x10, 0x91, 0xa1, 0xf4, 0x8c, 0x8e, 0x19, 0x6a, 0x25,
	0x15, 0x87, 0xe4, 0x36, 0x48, 0x78, 0x5c, 0x8b, 0xec, 0x11, 0x65, 0x20, 0x94, 0xd4, 0x2a, 0x2e,
	0x1c, 0xdb, 0x23, 0x4a, 0x0e, 0xa0, 0x12, 0xb2, 0xeb, 0x61, 0x6a, 0x35, 0xf6, 0xde, 0xbf, 0x5a,
	0x82, 0xcc, 0x95, 0x26, 0x67, 0xc9, 0x0d, 0xa8, 0x9c, 0x78, 0x4e, 0x3c, 0xa2, 0xcd, 0x55, 0x66,
	0x0b, 0xc9, 0x6c, 0xc2, 0x9a, 0x5d, 0x64, 0x85, 0x7d, 0x62, 0xac, 0xf1, 0x7e, 0x94, 0xdf, 0x89,
	0x20, 0xf5, 0xe3, 0xd0, 0x36, 0x18, 0x10, 0x68, 0x52, 0x8e, 0x4d, 0xdd, 0x48, 0x43, 0xf1, 0x11,
	0x0e, 0x49, 0x95, 0xf8, 0xca, 0x43, 0x3a, 0x26, 0x04, 0xca, 0xae, 0x3e, 0xe2, 0xd6, 0x20, 0xa9,
	0x6c, 0x8c, 0x5c, 0xf5, 0x38, 0x3a, 0xf7, 0x02, 0xa6, 0x95, 0xa4, 0x26, 0x33, 0x84, 0x20, 0xb6,
	0x4d, 0xa6, 0x50, 0x5d, 0xc5, 0x21, 0xee, 0xbc, 0xc8, 0xc9, 0xc7, 0x67, 0x29, 0x58, 0x95, 0x29,
	0x58, 0x4d, 0x58, 0xd3, 0x0d, 0xc3, 0x8b, 0xdd, 0xa8, 0xb9, 0xc6, 0x88, 0xa6, 0x53, 0x72, 0x13,
	0xaa, 0x76, 0xa8, 0x39, 0x9e, 0xa1, 0x3b, 0xcd, 0x2a, 0xa3, 0xb2, 0x66, 0x87, 0x3d, 0x9c, 0x22,
	0xf9, 0x04, 0x44, 0x89, 0x93, 0x4f, 0x60, 0xb9, 0x05, 0x55, 0xd7, 0x36, 0x9e, 0x31, 0xc1, 0x81,
	0x51, 0x9b, 0xcc, 0x51, 0xdf, 0x11, 0x2a, 0xaf, 0x45, 0x63, 0x9f, 0x36, 0xd7, 0xb9, 0x0b, 0xb1,
	0x95, 0xe3, 0xb1, 0x4f, 0x95, 0x3f, 0x08, 0xb0, 0xc9, 0xed, 0x2f, 0x63, 0xd6, 0xc5, 0xb9, 0x68,
	0x71, 0x6e, 0xa1, 0xec, 0xc3, 0xd6, 0x1c, 0x41, 0x97, 0xf2, 0x75, 0xe5, 0x0b, 0xd8, 0x4e, 0x4e,
	0x33, 0x6b, 0x46, 0x14, 0x98, 0x49, 0x17, 0x17, 0x92, 0x7e, 0x23, 0x40, 0x73, 0x3e, 0x8b, 0xe5,
	0xa2, 0x52, 0xea, 0xaf, 0x62, 0x41, 0xfe, 0xaa, 0x7c, 0x2d, 0xc0, 0xcd, 0x57, 0xee, 0x21, 0x0d,
	0x10, 0x6d, 0x33, 0xf1, 0x06, 0xd1, 0x36, 0xe7, 0xba, 0x41, 0x03, 0xc4, 0x33, 0x2b, 0x71, 0x01,
	0xf1, 0xcc, 0x22, 0x1d, 0xa8, 0x30, 0x3b, 0x42, 0x97, 0x2e, 0xdd, 0x5b, 0xdf, 0xfb, 0xee, 0xd5,
	0x42, 0x4e, 0xdc, 0x50, 0x4d, 0x8e, 0x2a, 0x23, 0xa8, 0x0d, 0x69, 0xc4, 0xdd, 0xb8, 0x40, 0xb3,
	0x9b, 0x3a, 0x62, 0x29, 0xeb, 0x88, 0xca, 0xc7, 0x50, 0xcf, 0xb0, 0x5b, 0xce, 0x78, 0x7e, 0x25,
	0x00, 0xe9, 0xc4, 0x11, 0x62, 0x87, 0x71, 0x65, 0xe8, 0xb9, 0x56, 0x81, 0x12, 0x2b, 0x50, 0xe7,
	0x7e, 0x8a, 0xb7, 0x85, 0x3b, 0x38, 0xd0, 0xeb, 0xa3, 0xd4, 0x82, 0xba, 0x66, 0x1a, 0x46, 0xca,
	0x93, 0x30, 0xa2, 0x7c, 0x0a, 0x1b, 0x97, 0x44, 0x5a, 0x4e, 0xab, 0x3f, 0x89, 0x70, 0x57, 0xa5,
	0xbe, 0x17, 0x44, 0x89, 0xd9, 0x22, 0x31, 0x24, 0xf4, 0x28, 0xf0, 0xac, 0x80, 0x86, 0xe1, 0xff,
	0x5a, 0x47, 0xf2, 0x0c, 0xaa, 0x7e, 0x22, 0x0b, 0x0b, 0xab, 0x8d, 0xbd, 0xc1, 0xd5, 0x96, 0x76,
	0x95, 0x4a, 0xad, 0x74, 0x8c, 0x51, 0x50, 0x9d, 0x30, 0x50, 0x14, 0xa8, 0x65, 0xbf, 0x10, 0x09,
	0x56, 0xc3, 0x48, 0x0f, 0x22, 0x79, 0x85, 0xac, 0x41, 0x89, 0xba, 0xa6, 0x2c, 0x28, 0x47, 0xf0,
	0xce, 0x15, 0xe4, 0x97, 0xbb, 0x82, 0xbf, 0x0b, 0xb0, 0x35, 0xa4, 0xd1, 0x6c, 0x5d, 0x51, 0x1c,
	0xee, 0x69, 0x89, 0x53, 0x7a, 0xb3, 0x12, 0xe7, 0xd2, 0xf5, 0x95, 0x5f, 0x79, 0x7d, 0xab, 0x53,
	0x13, 0xed, 0xc0, 0x8d, 0x79, 0xca, 0x2d, 0x07, 0xd1, 0x3f, 0x04, 0xd8, 0xce, 0x53, 0x29, 0xba,
	0x98, 0x7c, 0x4d, 0xe3, 0x9c, 0xd6, 0x35, 0xab, 0xaf, 0x5f, 0xd7, 0x28, 0x87, 0xd0, 0x9c, 0xaf,
	0xdd, 0x72, 0x28, 0x7d, 0x29, 0xc0, 0x9d, 0x21, 0x75, 0xa8, 0x11, 0xa5, 0x01, 0x3e, 0x63, 0x4c,
	0x31, 0x0d, 0xa3, 0xa2, 0xb0, 0xca, 0xd5, 0x5b, 0xab, 0x33, 0xf5, 0x96, 0x07, 0x6f, 0xbf, 0x4a,
	0x88, 0xd0, 0xf7, 0xdc, 0x90, 0x2e, 0x93, 0x07, 0x15, 0xa8, 0xbb, 0xf4, 0x85, 0x36, 0xcb, 0x6d,
	0xdd, 0xa5, 0x2f, 0x52, 0xb2, 0xca, 0x18, 0x36, 0x53, 0x17, 0xe4, 0xb8, 0x1d, 0x79, 0x91, 0xfd,
	0x74, 0x3c, 0xa3, 0x84, 0x30, 0xab, 0x44, 0x5a, 0x67, 0x8b, 0x6f, 0x50, 0x67, 0xff, 0x53, 0xc8,
	0x75, 0x20, 0x28, 0xce, 0x62, 0xcc, 0x0b, 0xea, 0x3a, 0x52, 0x1d, 0x4a, 0xaf, 0xaf, 0x03, 0x79,
	0x17, 0xae, 0x71, 0xc3, 0xc7, 0x23, 0x1c, 0x64, 0x5e, 0xd2, 0x72, 0x7f, 0xc0, 0xa2, 0x81, 0xc1,
	0xdc, 0x83, 0x8d, 0x44, 0x08, 0x9e, 0x3f, 0x17, 0x53, 0x74, 0x9a, 0x89, 0xc5, 0x5c, 0x26, 0x3e,
	0x85, 0xdb, 0x33, 0x2a, 0x71, 0xa3, 0x59, 0x8c, 0x6a, 0xce, 0x00, 0xc5, 0x19, 0x03, 0xfc, 0xa3,
	0x08, 0xeb, 0x4f, 0x4e, 0x4f, 0x1f, 0x87, 0x34, 0x60, 0x25, 0xff, 0x6c, 0x71, 0x93, 0x2d, 0x97,
	0xc5, 0xcb, 0xe5, 0xb2, 0x7e, 0xa1, 0x47, 0x7a, 0xa0, 0xc5, 0x81, 0x93, 0x84, 0x00, 0x89, 0xaf,
	0x3c, 0x0e, 0x58, 0x05, 0x6e, 0x51, 0xd7, 0xa4, 0x41, 0x02, 0x51, 0x32, 0x23, 0x6f, 0x81, 0x14,
	0xda, 0x96, 0xab, 0x47, 0x71, 0xc0, 0x4d, 0x54, 0x52, 0xa7, 0x0b, 0xe4, 0x21, 0xc0, 0x85, 0xed,
	0x6b, 0x26, 0x8d, 0x74, 0xdb, 0x69, 0x56, 0x58, 0xb5, 0xb4, 0x40, 0xa0, 0x78, 0x72, 0x7a, 0x7a,
	0x62, 0xfb, 0xec, 0xa6, 0xa4, 0x0b, 0xdb, 0x3f, 0x60, 0xc7, 0xd1, 0x23, 0x02, 0x6a, 0x6a, 0x48,
	0xd0, 0xa1, 0x17, 0xd4, 0x61, 0xfd, 0x43, 0x5d, 0x5d, 0x0f, 0xa8, 0x79, 0x62, 0xfb, 0x3d, 0x5c,
	0x22, 0xef, 0xc1, 0xf5, 0xdc, 0x1e, 0xcd, 0x1e, 0x59, 0xac, 0x99, 0x90, 0xd4, 0x46, 0x66, 0x5f,
	0x77, 0x64, 0x29, 0x6d, 0x80, 0x29, 0x1f, 0xac, 0xfb, 0x58, 0x9f, 0xc0, 0x01, 0x67, 0x63, 0xf2,
	0x2d, 0x58, 0xa7, 0x2f, 0x7d, 0x3b, 0xa0, 0xbc, 0xb3, 0xe3, 0x1d, 0x1f, 0xf0, 0x25, 0xec, 0xed,
	0x94, 0x0f, 0xa1, 0xfe, 0xe4, 0xf4, 0x14, 0xdd, 0xaf, 0x1d, 0x44, 0x76, 0x18, 0x2d, 0x52, 0x4d,
	0x2a, 0x7b, 0x50, 0x4b, 0x0f, 0x39, 0x67, 0xf1, 0x68, 0xa1, 0x33, 0x1e, 0x5c, 0x4b, 0xce, 0xa8,
	0x34, 0xf4, 0xe2, 0xc0, 0xa0, 0x97, 0x8e, 0x61, 0x4f, 0x16, 0x38, 0xc9, 0x29, 0x1c, 0x22, 0xa1,
	0xd0, 0xfe, 0x69, 0x5a, 0x08, 0xb2, 0x31, 0xee, 0x1a, 0x99, 0x1f, 0x25, 0xf9, 0x0b, 0x87, 0x64,
	0x13, 0x56, 0x39, 0x9a, 0xfc, 0xf2, 0xf8, 0x44, 0xf9, 0x4a, 0x80, 0xe6, 0xa4, 0x68, 0x46, 0x73,
	0xb5, 0x28, 0x5a, 0x2b, 0x0d, 0x0a, 0xcc, 0x3b, 0x73, 0xdc, 0xaf, 0x34, 0xcf, 0xfd, 0xdc, 0x4c,
	0xfd, 0x9e, 0x17, 0x65, 0xb9, 0xce, 0x62, 0x0e, 0x3f, 0x71, 0x1e, 0x3f, 0x13, 0x6e, 0x4f, 0xf8,
	0x3d, 0xa0, 0x11, 0x67, 0x56, 0xf8, 0x13, 0xce, 0xcf, 0x05, 0x78, 0xeb, 0xd5, 0x6c, 0xfe, 0x2b,
	0x9a, 0xe1, 0x5d, 0x1b, 0x8e, 0x17, 0x72, 0x9c, 0xab, 0x2a, 0x9f, 0x28, 0xbf, 0x16, 0x32, 0x0a,
	0x0f, 0xa9, 0x1e, 0x18, 0xe7, 0x68, 0x69, 0xfb, 0xe3, 0x87, 0xb4, 0xc8, 0x86, 0xb8, 0x09, 0x6b,
	0xcf, 0xe8, 0xf8, 0x85, 0x17, 0xa4, 0x05, 0x46, 0x3a, 0x45, 0x43, 0xf5, 0x75, 0x2b, 0x0d, 0xbe,
	0x6c, 0xac, 0xfc, 0x35, 0x0b, 0xcf, 0x25, 0xa1, 0x96, 0x83, 0xe7, 0x24, 0x85, 0x07, 0xa3, 0x3e,
	0xc3, 0xa8, 0x29, 0xb2, 0x50, 0xd4, 0x5a, 0x28, 0x14, 0x4d, 0x7b, 0x37, 0x0e, 0x27, 0x0e, 0x51,
	0x34, 0x72, 0x13, 0xaa, 0xe7, 0x7a, 0xa8, 0x8d, 0xbc, 0x20, 0x45, 0x74, 0xed, 0x5c, 0x0f, 0xfb,
	0x5e, 0xc0, 0x91, 0x66, 0x6f, 0x1c, 0x5c, 0x27, 0x3e, 0x51, 0x7e, 0x29, 0xc0, 0x9d, 0xec, 0x9d,
	0xa3, 0x46, 0x38, 0xdf, 0x1f, 0xb3, 0x62, 0xbc, 0x38, 0xac, 0xd3, 0x68, 0x56, 0xca, 0x44, 0xb3,
	0x79, 0x28, 0xff, 0x4d, 0x80, 0xb7, 0xbf, 0x49, 0xa0, 0xff, 0x0f, 0x9c, 0x27, 0x60, 0x96, 0x32,
	0x60, 0xe6, 0xd0, 0x2f, 0xe7, 0xd0, 0x9f, 0xf5, 0xe0, 0x54, 0x2b, 0xfc, 0x15, 0xe8, 0xc1, 0xbf,
	0x98, 0xf1, 0xe0, 0x3c, 0x9b, 0xe5, 0xa0, 0xdb, 0x87, 0xf2, 0x72, 0x78, 0xe5, 0x98, 0xb1, 0xb3,
	0x18, 0xb3, 0x6b, 0xd9, 0x65, 0x4c, 0x98, 0x86, 0x77, 0x41, 0x03, 0x4c, 0x82, 0x2c, 0xab, 0xf3,
	0x6c, 0xb1, 0xce, 0x16, 0xbb, 0x23, 0xeb, 0x31, 0x4f, 0x12, 0x97, 0xde, 0x3b, 0xe6, 0x59, 0xcf,
	0x2e, 0x6c, 0x38, 0xb6, 0x75, 0x1e, 0x69, 0x79, 0x8a, 0x3c, 0x91, 0xc8, 0xec, 0x53, 0x67, 0x4a,
	0x56, 0xf9, 0x31, 0x4b, 0x72, 0xd3, 0xc7, 0xc7, 0xf7, 0x13, 0x28, 0x58, 0xcd, 0xc6, 0xa1, 0xb8,
	0x86, 0x4a, 0x4e, 0x76, 0x9c, 0xec, 0x71, 0x34, 0xd2, 0xa7, 0x4a, 0x1d, 0x73, 0xa3, 0x96, 0x11,
	0x4d, 0x62, 0x2b, 0x47, 0x98, 0x0d, 0xff, 0x2c, 0xc0, 0xd6, 0x04, 0xf8, 0x41, 0x60, 0xd2, 0xa0,
	0xe0, 0x27, 0x89, 0x39, 0x86, 0x5c, 0x2a, 0xc0, 0x90, 0x95, 0x7f, 0x0b, 0x70, 0x63, 0x9e, 0xe0,
	0xcb, 0xd9, 0xca, 0x0f, 0x41, 0xe6, 0xd2, 0xe9, 0xa6, 0x49, 0xcd, 0x37, 0xf1, 0xb3, 0x06, 0xa3,
	0xd3, 0x46, 0x32, 0xcc, 0x60, 0x30, 0x8f, 0xe8, 0x2f, 0x35, 0x4e, 0x3d, 0xeb, 0x72, 0xf5, 0x91,
	0xfe, 0x92, 0x1d, 0xeb, 0x30, 0xd7, 0x6b, 0xc1, 0x86, 0x11, 0x07, 0x01, 0x75, 0xa3, 0xdc, 0x5e,
	0x1e, 0x59, 0xae, 0x27, 0x9f, 0xa6, 0xfb, 0x95, 0x2f, 0x60, 0x7b, 0x36, 0xd5, 0x15, 0xec, 0x8b,
	0x5f, 0x67, 0xeb, 0x95, 0x1c, 0x8b, 0xe5, 0xb0, 0xed, 0xa7, 0x8f, 0xc6, 0x6f, 0x80, 0xaa, 0x34,
	0x49, 0xba, 0xca, 0xd3, 0x7c, 0xbc, 0xe7, 0x39, 0x3e, 0x2d, 0xcf, 0x8b, 0xfc, 0x3f, 0x68, 0x26,
	0x8e, 0xcf, 0x32, 0x5a, 0x0e, 0x84, 0x1f, 0x80, 0x14, 0x87, 0xe8, 0xe4, 0xd3, 0x26, 0x71, 0x77,
	0x21, 0x0c, 0x26, 0x0c, 0xab, 0x71, 0x32, 0x52, 0x7e, 0x76, 0x39, 0x14, 0xa7, 0x25, 0x6c, 0x81,
	0x0e, 0xbb, 0x0d, 0x6b, 0xa1, 0xe7, 0x5a, 0xd3, 0xc7, 0x8b, 0x0a, 0x4e, 0xbb, 0xa6, 0xf2, 0xfb,
	0x39, 0x31, 0x7a, 0xca, 0x7f, 0xd9, 0xf4, 0x56, 0x67, 0x4c, 0x82, 0xe4, 0x7c, 0x02, 0xcd, 0x07,
	0x0b, 0x41, 0x93, 0x63, 0x5c, 0x0b, 0x33, 0x33, 0xe5, 0x5f, 0x59, 0xdb, 0xe5, 0xef, 0x6b, 0x2a,
	0x35, 0xbc, 0xc0, 0x2c, 0xf6, 0x59, 0x58, 0x37, 0x22, 0xdb, 0x73, 0x27, 0xff, 0xe4, 0xb0, 0x19,
	0xf9, 0x36, 0x34, 0xd8, 0x2b, 0x9f, 0xe6, 0x78, 0x16, 0xef, 0x72, 0xd0, 0x89, 0xcb, 0x6a, 0x8d,
	0xad, 0xf6, 0x3c, 0x8b, 0xfd, 0x87, 0x95, 0x81, 0x77, 0x35, 0x0b, 0x2f, 0xb6, 0x13, 0xba, 0x63,
	0xb1, 0xbf, 0x77, 0x24, 0x15, 0x87, 0x2c, 0x77, 0x20, 0x99, 0x35, 0x46, 0x86, 0x8d, 0x71, 0x17,
	0x75, 0xcd, 0xa4, 0x0d, 0xc3, 0xa1, 0xf2, 0x59, 0xa6, 0xa4, 0xcf, 0x6b, 0xbc, 0xd4, 0x95, 0xec,
	0x7c, 0x02, 0xd7, 0x66, 0x7a, 0x69, 0xd2, 0x40, 0x20, 0x3c, 0x2f, 0xa4, 0x38, 0x93, 0x57, 0x48,
	0x0d, 0xaa, 0xe9, 0x2b, 0xb1, 0x2c, 0x90, 0x3a, 0x48, 0x2c, 0xee, 0xb2, 0xa9, 0xb8, 0xf3, 0x1d,
	0x68, 0xe4, 0xff, 0x74, 0x21, 0x12, 0xac, 0x76, 0xb0, 0x2a, 0x96, 0x57, 0x48, 0x15, 0xca, 0x03,
	0x9f, 0xba, 0xb2, 0xb0, 0xf3, 0x0e, 0xc0, 0xf4, 0x6d, 0x0a, 0xd7, 0xf1, 0x6a, 0xe5, 0x15, 0xdc,
	0xec, 0xeb, 0x71, 0x48, 0x65, 0x61, 0xe7, 0x13, 0x68, 0xe4, 0xdb, 0x79, 0x14, 0x64, 0xa0, 0x1e,
	0x1c, 0xaa, 0x5a, 0x7f, 0x70, 0x70, 0x28, 0xaf, 0x10, 0x19, 0x6a, 0xc3, 0xee, 0xd1, 0x83, 0xde,
	0xa1, 0xd6, 0x39, 0xed, 0xf4, 0x0e, 0x65, 0x81, 0x00, 0x54, 0xd4, 0xf6, 0xd1, 0xc1, 0xa0, 0x2f,
	0x8b, 0x3b, 0x7f, 0x11, 0x60, 0x83, 0xb7, 0xa3, 0xc8, 0x93, 0x77, 0xbc, 0x07, 0x91, 0x47, 0x9a,
	0xb0, 0x79, 0x78, 0xf4, 0xb8, 0xaf, 0x9d, 0x74, 0x1f, 0x69, 0xc7, 0xa7, 0x8f, 0x0e, 0xb5, 0xa3,
	0x81, 0xda, 0x6f, 0xf7, 0xe4, 0x15, 0x72, 0x1b, 0xb6, 0xf3, 0x5f, 0xf6, 0x7b, 0xed, 0xce, 0x43,
	0x9c, 0xca, 0x15, 0xf2, 0x16, 0x34, 0xe7, 0x7d, 0x1c, 0xe2, 0xd7, 0x6b, 0x64, 0x0b, 0xae, 0xe7,
	0xbf, 0x76, 0xda, 0xaa, 0x5c, 0x27, 0xdb, 0xb0, 0x91, 0x5f, 0x7e, 0xd2, 0x3e, 0xee, 0x7c, 0x2e,
	0xcb, 0x64, 0x13, 0xe4, 0xfc, 0x87, 0xe3, 0x13, 0xf9, 0xfa, 0xe5, 0xed, 0x6a, 0xfb, 0xa0, 0x3b,
	0x90, 0xc9, 0xce, 0xa7, 0x50, 0xef, 0xa7, 0x21, 0x90, 0xbd, 0x25, 0xdf, 0x84, 0xad, 0xdc, 0x82,
	0x76, 0x40, 0x9f, 0xea, 0xb1, 0x13, 0xc9, 0x2b, 0x48, 0x3a, 0xff, 0xe9, 0xa8, 0x23, 0x0b, 0x3b,
	0x5f, 0x66, 0x4a, 0x19, 0x46, 0x21, 0xd5, 0x67, 0x38, 0x38, 0x7a, 0xa0, 0xf5, 0xba, 0xc3, 0x63,
	0xce, 0xb1, 0xd7, 0x7d, 0x88, 0xd0, 0xbe, 0xe2, 0xab, 0xda, 0xe9, 0x1f, 0xc8, 0xc2, 0x04, 0xa8,
	0x99, 0xaf, 0xc7, 0x83, 0x47, 0xb2, 0x48, 0x6e, 0xc1, 0x8d, 0x79, 0x1f, 0x3f, 0xeb, 0xcb, 0xa5,
	0xfd, 0x13, 0x68, 0x1a, 0xde, 0xa8, 0x35, 0xb6, 0xc7, 0x5e, 0x8c, 0x06, 0x88, 0x8d, 0x95, 0xc3,
	0xff, 0xe0, 0xff, 0xd1, 0xc7, 0x96, 0xe7, 0xe8, 0xae, 0xd5, 0xfa, 0x68, 0x2f, 0x8a, 0x5a, 0x86,
	0x37, 0xba, 0xcf, 0x96, 0x0d, 0xcf, 0xb9, 0xaf, 0xfb, 0xfe, 0xfd, 0x6f, 0x0c, 0x07, 0x67, 0x15,
	0xb6, 0xf7, 0xc3, 0xff, 0x04, 0x00, 0x00, 0xff, 0xff, 0xac, 0x8b, 0xda, 0xed, 0x90, 0x20, 0x00,
	0x00,
}
