package channellivemgr

import (
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channellivemgr"
	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"
	"math/rand"
	"os"
	"testing"
	"time"
)

var stdClient *Client

func init() {
	stdClient, _ = NewClient(grpc.WithBlock())
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
	rand.Seed(time.Now().Unix())
}

func TestChannelLiveMgr(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "Run Testing")
}

var _ = Describe("GetChannelLiveInfo", func() {
	_, err := stdClient.GetChannelLiveInfo(context.Background(), 1975746, true)

	Context("获取主播信息", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("GetChannelLiveInfoByChannelId", func() {
	_, err := stdClient.GetChannelLiveInfoByChannelId(context.Background(), 10002698)

	Context("根据房间id获取主播信息", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("GetChannelLiveStatus", func() {
	_, err := stdClient.GetChannelLiveStatus(context.Background(), pb.GetChannelLiveStatusReq{
		Uid:            1975746,
		ChannelId:      10002698,
		IgnoreMemCache: false,
	})

	Context("获取主播的直播状态信息", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("GetChannelLiveHistoryRecord", func() {
	_, err := stdClient.GetChannelLiveHistoryRecord(context.Background(), pb.GetChannelLiveHistoryRecordReq{
		Uid:       1975746,
		BeginTime: 0,
		EndTime:   1702976441,
	})

	Context("获取主播直播记录", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("BatchGetChannelLiveStatusSimple", func() {
	_, err := stdClient.BatchGetChannelLiveStatusSimple(context.Background(), pb.BatchGetChannelLiveStatusSimpleReq{
		ChannelList: []uint32{10002698, 10091459},
	})

	Context("批量获取直播间的简单直播信息", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("GetChannelLivePKRecord", func() {
	_, err := stdClient.GetChannelLivePKRecord(context.Background(), pb.GetChannelLivePKRecordReq{
		Uid: 1975746,
	})

	Context("批量获取主播的pk记录", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("GetChannelLiveRankUser", func() {
	_, err := stdClient.GetChannelLiveRankUser(context.Background(), pb.GetChannelLiveRankUserReq{
		ChannelId: 10002698,
	})

	Context("获取主播间的送礼排行榜", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("GetHeartBeatTimeOut", func() {
	_, err := stdClient.GetHeartBeatTimeOut(context.Background(), 1639904441, true)

	Context("获取心跳过期的直播间", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})

})

var _ = Describe("GetChannelLiveWatchTimeRankUser", func() {
	_, err := stdClient.GetChannelLiveWatchTimeRankUser(context.Background(), pb.GetChannelLiveWatchTimeRankUserReq{
		ChannelId: 10002698,
	})

	Context("获取直播间的观看时长排行榜", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("GetChannelLiveData", func() {
	_, err := stdClient.GetChannelLiveData(context.Background(), pb.GetChannelLiveDataReq{
		Uid:       1975746,
		BeginTime: 1639893641,
		EndTime:   1639904441,
		ChannelId: 10002698,
	})

	Context("获取直播间的数据统计", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})

})

var _ = Describe("BatchGetChannelLiveRecord", func() {
	_, err := stdClient.BatchGetChannelLiveRecord(context.Background(), pb.BatchGetChannelLiveRecordReq{
		UidList:   []uint32{1975746, 1975455},
		BeginTime: 1639893641,
		EndTime:   1639904441,
	})

	Context("批量获取主播的直播记录", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})

})

var _ = Describe("GetChannelLiveTotalData", func() {
	_, err := stdClient.GetChannelLiveTotalData(context.Background(), pb.GetChannelLiveTotalDataReq{
		Uid:       1975746,
		BeginTime: 1639893641,
		EndTime:   1639904441,
	})

	Context("获取主播详细的直播数据", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("GetChannelLiveTOPN", func() {
	_, err := stdClient.GetChannelLiveTOPN(context.Background(), pb.GetChannelLiveTOPNReq{
		Uid:   1975746,
		Off:   0,
		Count: 10,
	})

	Context("获取主播最近的直播数据", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("GetChannelLiveAnchorScore", func() {
	_, err := stdClient.GetChannelLiveAnchorScore(context.Background(), 1975746)

	Context("获取主播的直播积分", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("BatchGetChannelLiveTotalData", func() {
	_, err := stdClient.BatchGetChannelLiveTotalData(context.Background(), &pb.BatchGetChannelLiveTotalDataReq{
		UidList:   []uint32{1975746},
		BeginTime: 1639893641,
		EndTime:   1639904441,
	})

	Context("批量获取主播的详细直播数据", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("GetAllAnchor", func() {
	_, err := stdClient.GetAllAnchor(context.Background(), &pb.GetAllAnchorReq{})

	Context("获取所有主播", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})

})

var _ = Describe("GetChannelLiveAnchorScoreLog", func() {
	_, err := stdClient.GetChannelLiveAnchorScoreLog(context.Background(), &pb.GetChannelLiveAnchorScoreLogReq{
		Uid: 1975746,
		SourceList: []uint32{
			uint32(pb.AddChannelLiveAnchorScoreReq_DayAnchorIncomeMission),
			uint32(pb.AddChannelLiveAnchorScoreReq_DayAnchorTimeMission),
			uint32(pb.AddChannelLiveAnchorScoreReq_WeekAnchorIncomeMission),
			uint32(pb.AddChannelLiveAnchorScoreReq_WeekAnchorTimeMission),
			uint32(pb.AddChannelLiveAnchorScoreReq_ScoreExchange),
			uint32(pb.AddChannelLiveAnchorScoreReq_ScoreExchangeReturn),
			uint32(pb.AddChannelLiveAnchorScoreReq_GuildChangePrivate),
			uint32(pb.AddChannelLiveAnchorScoreReq_GuildQuit),
			uint32(pb.AddChannelLiveAnchorScoreReq_GuildOfficalRecycle),
			uint32(pb.AddChannelLiveAnchorScoreReq_GuildExchange),
		},
		Begin: 0,
		Limit: 10,
	})

	Context("获取主播直播积分变化log", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})

})

var _ = Describe("GetUserPushCnt", func() {
	_, err := stdClient.GetUserPushCnt(context.Background(), &pb.GetUserPushCntReq{
		Uid:       1971336,
		LimitCnt:  10,
		AnchorUid: 1974825,
		UidList:   []uint32{1967227, 1969210},
	})

	Context("获取每天推送限制", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})

})

var _ = Describe("BatchGetGroupFansGiftValue", func() {
	_, err := stdClient.BatchGetGroupFansGiftValue(context.Background(), &pb.BatchGetGroupFansGiftValueReq{
		AnchorUid: 1971336,
		UidList:   []uint32{1967227, 1969210},
	})

	Context("获取主播的粉团送礼值", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("DelChannelLiveInfo", func() {
	_, err := stdClient.DelChannelLiveInfo(context.Background(), 1974825, "test")

	Context("回收主播权限", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("SetAppointPkInfo", func() {
	pkRivalInfoList := make([]*pb.PkRivalInfo, 0)
	pkRivalInfoList = append(pkRivalInfoList, &pb.PkRivalInfo{
		Uid:       uint32(rand.Intn(10000)),
		PkBeginTs: 1639367656,
	})

	_, err := stdClient.AddAppointPkInfo(context.Background(), &pb.AddAppointPkInfoReq{
		Info: &pb.AppointPkInfo{
			Uid:       uint32(rand.Intn(10000)),
			BeginTs:   1639364056,
			EndTs:     1639371256,
			RivalList: pkRivalInfoList,
			Operator:  "lijinan",
		},
	})

	Context("增加指定PK配置", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("GetAppointPkInfoList", func() {
	_, err := stdClient.GetAppointPkInfoList(context.Background(), &pb.GetAppointPkInfoListReq{
		QueryType: uint32(pb.GetAppointPkInfoListReq_Query_All),
		Page:      0,
		PageSize:  10,
	})

	Context("获取指定pk信息列表", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("AcceptAppointPk", func() {
	_, err := stdClient.AcceptAppointPk(context.Background(), &pb.AcceptAppointPkReq{
		MyUid:     2212797,
		OtherUid:  2185821,
		ChannelId: 10158631,
	})

	Context("接受指定pk", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("ConfirmAppointPkPush", func() {
	_, err := stdClient.ConfirmAppointPkPush(context.Background(), &pb.ConfirmAppointPkPushReq{
		MyUid:    2212797,
		OtherUid: 2185821,
	})

	Context("确定收到指定pk推送", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})

})

var _ = Describe("GetAppointPkInfo", func() {
	_, err := stdClient.GetAppointPkInfo(context.Background(), &pb.GetAppointPkInfoReq{
		Uid: 2212797,
	})

	Context("进房获取指定pk信息", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})

})

var _ = Describe("GetAnchorByUidList", func() {
	_, err := stdClient.GetAnchorByUidList(context.Background(), &pb.GetAnchorByUidListReq{
		UidList: []uint32{2212797, 2185821},
	})

	Context("根据uid获取主播信息", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})

})

var _ = Describe("CheckIsAnchorInBackList", func() {
	_, err := stdClient.CheckIsAnchorInBackList(context.Background(), 2212797)

	Context("判断用户是否在主播黑名单中", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("GetPKMatchInfo", func() {
	_, err := stdClient.GetPKMatchInfo(context.Background(), 2212797, 10158631)

	Context("获取主播pk匹配信息", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("BatchGetAnchorTotalData", func() {
	_, err := stdClient.BatchGetAnchorTotalData(context.Background(), []uint32{2212797, 2185821})

	Context("获取主播直播数据", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})

})

var _ = Describe("GetApplyList", func() {
	_, err := stdClient.GetApplyList(context.Background(), &pb.GetApplyListReq{
		Uid:       2212797,
		ChannelId: 10158631,
	})

	Context("获取PK申请列表", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

var _ = Describe("SearchAnchor", func() {
	_, err := stdClient.SearchAnchor(context.Background(), &pb.SearchAnchorReq{
		Uid: 1975746,
	})

	Context("搜索主播信息", func() {
		It(fmt.Sprint(err), func() {
			Expect(err).To(BeNil())
		})
	})
})

type DealTokenData struct {
	Ver     int64  `json:"ver"`
	Encrypt string `json:"encrypt"`
	Data    string `json:"data"`
}

type DealToken struct {
	TradeNo    string `json:"tradeNo"`
	OrderID    string `json:"orderID"`
	Sign       string `json:"sign"`
	Ctime      string `json:"ctime"`
	ServerName string `json:"serverName"`
	BuyerID    int64  `json:"buyerId"`
	TotalPrice int64  `json:"totalPirce"`
	PrevToken  string `json:"prevToken"` //前一个节点的dealToken的json.Marshaler
	PrevMd5    string `json:"prevMd5"`   //PrevToken的md5
}

func traveTokenLink(root *DealToken) {
	for {
		if nil == root {
			break
		}

		if len(root.PrevToken) == 0 {
			break
		}

		err := json.Unmarshal([]byte(root.PrevToken), root)
		if nil != err {
			break
		}

	}
}

func getMD5Encode(data string) string {
	h := md5.New()
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

func getSign(dt *DealToken) string {
	return fmt.Sprintf("%v#%v#%v#%v", dt.TradeNo, dt.BuyerID, dt.TotalPrice, dt.Ctime)
}

// 加上UNiFilepay节点的dealtoken
func addTokenDecode(encode, orderID string) (string, error) {
	dt := &DealToken{}

	err := json.Unmarshal([]byte(encode), dt)
	if nil != err {
		return "", err
	}

	dt.OrderID = orderID

	preToke, _ := json.Marshal(dt)
	token := &DealToken{
		TradeNo:    dt.TradeNo,
		OrderID:    orderID,
		Sign:       "",
		Ctime:      time.Now().Format("2006-01-02 15:04:05"),
		ServerName: "unified-pay",
		BuyerID:    dt.BuyerID,
		TotalPrice: 0,
		PrevToken:  string(preToke),
		PrevMd5:    getMD5Encode(string(preToke)),
	}

	token.Sign = getMD5Encode(getSign(token))

	newToken, err := json.Marshal(token)

	return string(newToken), err
}

func dealTokenDecode(encode string) (*DealToken, error) {
	// 1 decode to json
	decode, err := base64.StdEncoding.DecodeString(encode)

	if nil != err {
		return nil, err
	}

	fmt.Printf("\n decode:%v \n", string(decode))

	dtData := &DealTokenData{}
	err = json.Unmarshal(decode, dtData)
	if nil != err {
		log.Errorf("dealTokenDecode Unmarshal DealTokenData err:%v", err)
		return nil, err
	}

	// 2 decode to struct
	dt := &DealToken{}
	data, err := base64.StdEncoding.DecodeString(dtData.Data)
	if nil != err {
		return nil, err
	}

	fmt.Printf("\n %v \n", string(data))

	err = json.Unmarshal(data, dt)
	if nil != err {
		return nil, err
	}

	//3 check sign
	sign := getSign(dt)
	md5Code := getMD5Encode(sign)

	fmt.Printf("\nsign:%v md5Code:%v dt.Sign:%v \n", sign, md5Code, dt.Sign)

	if md5Code != dt.Sign {
		return nil, errors.New("check not pass")
	}

	return dt, nil
}

func TestGetHelloWorld(t *testing.T) {

	td, err := dealTokenDecode("eyJ2ZXIiOjEsImRhdGEiOiJleUowY21Ga1pVNXZJam9pTWpBeU1UQTFNalV4TmpRMU16WTRORFF3TURBd01ERXlaamd4SWl3aWMybG5iaUk2SWpVeU5EUXlOakU1TlRBNU1Ua3haR1poTVRFMU9UQTNZelV3TWpnNE1qWTBJaXdpWTNScGJXVWlPaUl5TURJeExUQTFMVEkxSURFMk9qUTFPak0zSWl3aWMyVnlkbVZ5VG1GdFpTSTZJblJpWldGdUlpd2lkRzkwWVd4UWFYSmpaU0k2TVRBc0ltSjFlV1Z5U1dRaU9qSXhPVFE0TXpkOSIsImVuY3J5cHQiOiJub25lIn0=")
	fmt.Printf("\n td:%+v   err:%v \n", td, err)

	tdData, _ := json.Marshal(td)
	fmt.Printf("\n tdData:%v \n", string(tdData))

	newData, _ := addTokenDecode(string(tdData), "bbbb")

	fmt.Printf("\n newData:%v \n", newData)

	newTd := &DealToken{}
	err = json.Unmarshal([]byte(newData), newTd)

	traveTokenLink(newTd)

	fmt.Println("TestGetHelloWorld end")
}
