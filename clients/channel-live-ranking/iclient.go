package channelliveranking

import (
	"context"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channel-live-ranking"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetRankingList(ctx context.Context, opUid uint32, req *pb.GetRankingListReq) (*pb.GetRankingListResp, protocol.ServerError)
	GetAnchorHonorNameplate(ctx context.Context, opUid, actorUid uint32) (*pb.GetAnchorHonorNameplateResp, protocol.ServerError)
	BatchGetAnchorHonorNameplate(ctx context.Context, opUid uint32, actorUidList []uint32) (*pb.BatchGetAnchorHonorNameplateResp, protocol.ServerError)
	GiveAnchorHonorNameplate(ctx context.Context, opUid, actorUid, honorId, expiredTs uint32) (*pb.GiveAnchorHonorNameplateResp, protocol.ServerError)
	GetLiveFansWeekRank(ctx context.Context, req *pb.GetLiveFansWeekRankReq) (*pb.GetLiveFansWeekRankResp, protocol.ServerError)
	HandleFansWeekRank(ctx context.Context, req *pb.HandleFansWeekRankReq) (*pb.HandleFansWeekRankResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
